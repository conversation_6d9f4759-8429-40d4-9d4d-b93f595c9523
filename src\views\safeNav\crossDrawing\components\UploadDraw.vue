<template>
  <div v-loading.fullscreen.lock="fullscreenLoading" element-loading-text="正在上传中...">
    <el-dialog
      :before-close="resetModule"
      :close-on-click-modal="false"
      :visible.sync="visible"
      append-to-body
      title="上传图纸"
      width="750px"
    >
      <el-form ref="form" :model="projectInfo" :rules="rules" inline label-width="130px" size="small">
        <!--<el-form-item label="所属目录:">-->
        <!--  {{ projectInfo.name }}-->
        <!--</el-form-item>-->
      </el-form>
      <div v-if="showFormData" class="text item">
        <fm-generate-form
          :ref="'generateForm'"
          :data="formStruct"
          :remote="remoteFunc"
          :value="formData"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="resetModule">取消</el-button>
        <el-button type="primary" @click="submit">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import { GenerateForm } from '@/components/VueFormMaking'
import 'form-making/dist/FormMaking.css'

Vue.component(GenerateForm.name, GenerateForm)
import { getToken } from '@/utils/auth'
import extendTpl from '@/api/system/extendTpl'
import { drawing } from '@/api/safeNav/omAssetAffiliated';

export default {
  name: 'ProjectForm',
  data() {
    return {
      fullscreenLoading: false,
      rules: {},
      processStructureValue: {},
      formStruct: {},
      formData: {},
      showFormData: false,
      remoteFunc: {
        funcGetToken(resolve) {
          resolve(getToken())
        }
      },
      visible: false,
      projectInfo: {
        id: undefined
      },
      bindId: '',
      allProject: []

    }
  },
  methods: {
    init(data) {
      this.visible = true;
      // 图纸上传的模版ID
      const { otherInfo } = this.$config.draw_upload_key;
      this.getInitExtendTpl(otherInfo)
      // this.getExtendTpl(this.bindId);
    },
    // 获取模板信息
    // getExtendTpl(id) {
    //   const data = { id, enabled: 1 }
    //   extendBindTpl.get(data).then(res => {
    //     if (res && res.content && res.content.length) {
    //       this.processStructureValue = res.content[0];
    //       const FormStruct = JSON.parse(this.processStructureValue.extendTpl.formStruct);
    //       this.formStruct = FormStruct;
    //     }
    //     this.showFormData = true;
    //   });
    // },

    // 获取模版
    getInitExtendTpl(id) {
      extendTpl.get({ enabled: 1, id }).then(res => {
        if (res && res.content && res.content.length) {
          this.processStructureValue = res.content[0];
          const FormStruct = JSON.parse(this.processStructureValue.formStruct);
          this.formStruct = FormStruct;
        }
        this.showFormData = true;
      })
    },
    // 关闭弹框
    resetModule() {
      this.processStructureValue = {};
      this.formStruct = {};
      this.formData = {};
      this.showFormData = false;
      this.$refs.form.resetFields();
      this.visible = false;
    },
    // 提交
    submit() {
      this.$refs['form'].validate((valid) => {
        if (!valid) {
          return false;
        } else {
          this.checkModule().then(res => {
            if (!res.flag) {
              return false;
            } else {
              this.fullscreenLoading = true;
              const subData = res.subData;
              console.log(subData, '<===>', 'subData')
              drawing(subData).then(response => {
                this.resetModule();
                this.$emit('success', this.projectInfo, subData);
                this.$notify({
                  title: '操作成功',
                  type: 'success',
                  duration: 2500
                })
              }).catch((e) => {
                console.log(e);
              }).finally(() => {
                this.fullscreenLoading = false;
              })
            }
          })
        }
      })
    },
    async checkModule() {
      const subData = {
        bindId: this.$config.corss_draw_key.bindId,
        formData: '',
        formStruct: JSON.stringify(this.formStruct),
        formBindToVar: true,
        relation: this.processStructureValue.relation,
        enabled: 1
      };
      const p = await this.$refs['generateForm'].getData().then(values => {
        subData.formData = JSON.stringify(values);
        return {
          flag: true,
          subData
        };
      }).catch(() => {
        return {
          flag: false
        };
      })
      return p;
    }
  }
}

</script>
