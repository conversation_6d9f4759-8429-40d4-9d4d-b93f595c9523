<template>
  <div class="app-container">
    <div class="config-detail">
      <el-tabs v-if="type == 1" :stretch="true" style="height: 200px;" tab-position="left">
        <el-tab-pane v-for="(item,index) in list" :key="index" :label="item.name">
          <component :is="item.com" :ref="`${item.com}`" style="background: #fff;" />
        </el-tab-pane>
      </el-tabs>
      <div v-if="type === 2">
        <simple-config ref="simpleConfig" />
      </div>
      <div class="btn-box">
        <el-button class="sub-btn" type="primary" @click="submitForm">提交</el-button>
        <el-button class="sub-btn" type="info" @click="goBack">返回</el-button>
      </div>
    </div>

  </div>
</template>

<script>
import configurationConfig from './components/configurationConfig.vue';
import debugConfig from './components/debugConfig.vue';
import ferretConfig from './components/ferretConfig.vue';
import filterConfig from './components/filterConfig.vue';
import headlessConfig from './components/headlessConfig.vue';
import inputConfig from './components/inputConfig.vue';
import outputConfig from './components/outputConfig.vue';
import rateLimitConfig from './components/rateLimitConfig.vue';
import scopeConfig from './components/scopeConfig.vue';
import uniqueConfig from './components/uniqueConfig.vue';
import simpleConfig from './components/simpleConfig.vue';
import crudJob from '@/api/system/timing'

export default {
  name: 'ConfigDetail',
  components: {
    configurationConfig,
    debugConfig,
    ferretConfig,
    filterConfig,
    headlessConfig,
    inputConfig,
    outputConfig,
    rateLimitConfig,
    scopeConfig,
    uniqueConfig,
    simpleConfig
  },
  data() {
    return {
      list: [
        { name: '输入配置', com: 'inputConfig', key: 'input' },
        { name: '输出配置', com: 'outputConfig', key: 'output' },
        { name: '详细配置', com: 'configurationConfig', key: 'configuration' },
        { name: '调试配置', com: 'debugConfig', key: 'debug' },
        { name: '无头浏览器配置', com: 'headlessConfig', key: 'headless' },
        { name: '范围配置', com: 'scopeConfig', key: 'scope' },
        { name: '过滤器配置', com: 'filterConfig', key: 'filter' },
        { name: '速率限制配置', com: 'rateLimitConfig', key: 'rate_limit' },
        { name: '脚本配置', com: 'ferretConfig', key: 'ferret_query' },
        { name: '去重配置', com: 'uniqueConfig', key: 'unique' }
      ],
      detailInfo: {
        input: '',
        configuration: '',
        debug: '',
        headless: '',
        scope: '',
        filter: '',
        output: '',
        unique: '',
        rate_limit: '',
        ferret_query: ''
      },
      id: '',
      detail: {},
      type: 1
    };
  },
  created() {
    this.type = this.$route.query.type;
    // this.getDetail();
  },

  mounted() {
    this.id = this.$route.query.id;
    this.getDetail();
  },

  methods: {
    getDetail() {
      const params = {
        id: this.id,
        enabled: 1
      }
      crudJob.get(params).then(res => {
        console.log('res==>', res);
        this.detail = res.content[0]
        this.detailInfo = JSON.parse(this.detail.params)
        if (this.detailInfo.hasOwnProperty('params') && this.type == 2) {
          console.log('this.detailInfo ==>', this.detailInfo);
          this.$refs.simpleConfig.formData = this.detailInfo
          this.$refs.simpleConfig.valueOne = this.detailInfo.text
          this.$refs.simpleConfig.valueTwo = this.detailInfo.params.content.text
        } else if (this.type == 1 && this.detailInfo.hasOwnProperty('debug')) {
          this.list.forEach(item => {
            const childComponent = this.$refs[item.com][0];
            if (childComponent) {
              if (item.key === 'unique') {
                childComponent.formData = { unique: this.detailInfo[item.key] }
              } else if (item.key === 'ferret_query') {
                childComponent.formData = this.detailInfo[item.key]
                childComponent.value = this.detailInfo[item.key].text
              } else {
                childComponent.formData = this.detailInfo[item.key]
              }
            }
          });
        }
      })
    },
    submitForm() {
      if (this.type === 1) {
        const formData = {};
        this.list.forEach(item => {
          const childComponent = this.$refs[item.com][0];
          if (childComponent && childComponent.getFormData) {
            const childFormData = childComponent.getFormData();
            Object.assign(formData, childFormData);
          }
        });
        console.log(formData);
        if (formData.input?.urls && formData.input.urls[0]) {
          // 拿到数据并至少填写一个链接
          crudJob.edit({ ...this.detail, params: JSON.stringify(formData), enabled: 1 }).then(res => {
            this.$router.push({ name: 'Timing' })
          })
        } else {
          this.$message({
            message: '请至少填写一个目标URL的链接',
            type: 'error'
          })
        }
      } else if (this.type === 2) {
        const formData = this.$refs.simpleConfig.getFormData();
        crudJob.edit({ ...this.detail, params: JSON.stringify(formData), enabled: 1 }).then(res => {
          this.$router.push({ name: 'Timing' })
        })
      }
    },
    goBack() {
      this.$router.push({ name: 'Timing' })
    }
  }
};
</script>

<style lang="scss" scoped>
.config-detail {
	min-width: calc(100vh - 120px);
	background: #fff;
	padding: 50px 0 20px;
	position: relative;

	::v-deep.el-tabs--left {
		height: auto !important;
	}

	.btn-box {
		position: absolute;
		right: 20px;
		top: 10px;
	}
}
</style>
