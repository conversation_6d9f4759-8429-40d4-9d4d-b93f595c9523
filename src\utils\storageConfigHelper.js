/**
 * 存储平台配置助手
 * 用于查看和修改存储平台配置
 */

import { findOptions } from '@/api/system/globalConfig'

/**
 * 获取所有存储平台配置
 */
export async function getStoragePlatforms() {
  try {
    const res = await findOptions({ type: 'STORAGE', enabled: 1 })
    if (res && res.STORAGE) {
      return res.STORAGE
    }
    return {}
  } catch (error) {
    console.error('获取存储平台配置失败:', error)
    return {}
  }
}

/**
 * 查找本地存储平台
 */
export async function findLocalStoragePlatform() {
  const platforms = await getStoragePlatforms()

  // 查找包含 'local' 关键字的平台
  for (const [key, config] of Object.entries(platforms)) {
    const platformValue = config.extend?.data?.platform || ''
    if (platformValue.toLowerCase().includes('local')) {
      return {
        key,
        platform: platformValue,
        config
      }
    }
  }

  // 如果没找到，查找其他可能的本地存储标识
  for (const [key, config] of Object.entries(platforms)) {
    const platformValue = config.extend?.data?.platform || ''
    // 检查是否包含本地存储相关关键字
    if (platformValue.includes('localhost') ||
        platformValue.includes('127.0.0.1') ||
        key.toLowerCase().includes('local')) {
      return {
        key,
        platform: platformValue,
        config
      }
    }
  }

  return null
}

/**
 * 获取表单配置中的存储平台信息
 */
export function getFormStorageInfo(formStruct) {
  try {
    const struct = typeof formStruct === 'string' ? JSON.parse(formStruct) : formStruct
    const storageFields = []

    if (struct.list) {
      struct.list.forEach(field => {
        if ((field.type === 'imgupload' || field.type === 'ossfile') && field.options?.platform) {
          storageFields.push({
            name: field.name,
            type: field.type,
            platform: field.options.platform,
            action: field.options.action,
            field
          })
        }
      })
    }

    return storageFields
  } catch (error) {
    console.error('解析表单配置失败:', error)
    return []
  }
}

/**
 * 修改表单配置中的存储平台
 */
export function updateFormStoragePlatform(formStruct, fieldName, newPlatform) {
  try {
    const struct = typeof formStruct === 'string' ? JSON.parse(formStruct) : formStruct

    if (struct.list) {
      struct.list.forEach(field => {
        if (field.name === fieldName && (field.type === 'imgupload' || field.type === 'ossfile')) {
          field.options.platform = newPlatform
          console.log(`已将字段 "${fieldName}" 的存储平台修改为: ${newPlatform}`)
        }
      })
    }

    return JSON.stringify(struct)
  } catch (error) {
    console.error('修改表单配置失败:', error)
    return formStruct
  }
}

/**
 * 打印存储平台配置信息（用于调试）
 */
export async function printStorageInfo() {
  console.log('=== 存储平台配置信息 ===')

  const platforms = await getStoragePlatforms()
  console.log('所有存储平台:', platforms)

  const localPlatform = await findLocalStoragePlatform()
  if (localPlatform) {
    console.log('本地存储平台:', localPlatform)
  } else {
    console.log('未找到本地存储平台')
  }

  console.log('=== 配置信息结束 ===')
}

/**
 * 检查并建议存储平台配置
 */
export async function checkStorageConfig(formData) {
  const storageFields = getFormStorageInfo(formData.formStruct)
  const localPlatform = await findLocalStoragePlatform()

  console.log('=== 存储配置检查结果 ===')
  console.log('表单中的存储字段:', storageFields)

  if (localPlatform) {
    console.log('建议的本地存储平台标识符:', localPlatform.platform)

    storageFields.forEach(field => {
      if (field.platform !== localPlatform.platform) {
        console.log(`字段 "${field.name}" 当前使用: ${field.platform}`)
        console.log(`建议修改为: ${localPlatform.platform}`)
      }
    })
  } else {
    console.log('警告: 未找到本地存储平台配置')
  }

  console.log('=== 检查结束 ===')
}

export default {
  getStoragePlatforms,
  findLocalStoragePlatform,
  getFormStorageInfo,
  updateFormStoragePlatform,
  printStorageInfo,
  checkStorageConfig
}
