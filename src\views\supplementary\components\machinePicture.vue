<template>
  <div v-if="currentSelects.id" v-loading="loading" :style="currentSelects.customStyle" class="topology-box">
    <div :style="backgroundStyle" class="topology-content">
      <span
        v-for="(item,index) in dataList"
        :key="index"
        :class="(item.mark==='long')?'long-text':''"
        :style="{...item.fv4Style,backgroundColor:item.color}"
      >
        {{ item.fv4 }}
      </span>
    </div>
  </div>
</template>

<script>
import {
  legend
} from '../utils/fileds'
import { getOmAssetAutoPilot } from '@/api/parts/assets';

export default {
  name: 'MachinePicture',
  data() {
    return {
      legend,
      loading: false,
      currentSelects: {},
      dataList: [],
      opacityFlag: 0
    }
  },
  computed: {
    backgroundStyle() {
      // 确保 currentSelects.img 有正确的值，并且对应的图片文件存在
      return {
        backgroundImage: `url(${require(`@/assets/images/${this.currentSelects.img}.png`)})`,
        width: this.currentSelects.bgStyle.width,
        height: this.currentSelects.bgStyle.height,
        opacity: this.opacityFlag
      };
    }
  },

  created() {
  },
  methods: {
    initData(data) {
      this.currentSelects = data;
      this.processMachineList()
    },
    formatStatus(status) {
      const defaultColor = legend.find(item => item.title === '其他').color;
      const matchedItem = legend.find(item => item.title === status);

      return matchedItem ? matchedItem.color : defaultColor;
    },
    async processMachineList(query) {
      this.dataList = []
      this.opacityFlag = 0;
      const { bindId } = this.$config.auto_pilot3_key;
      const json = {
        page: 0,
        size: 99999,
        bindId,
        enabled: [1],
        'type.fieldName': 'fv1',
        'type.values': '路口施工进度',
        'tagName': this.currentSelects.tagName
      };
      try {
        this.loading = true;
        const res = await getOmAssetAutoPilot(json);
        const apiData = res.content;

        // 遍历并匹配接口数据
        this.dataList = this.currentSelects.dataList.map(item => {
          const matchedData = apiData.find(apiItem => apiItem.fv4 === item.fv4);
          if (matchedData) {
            const key = Object.keys(matchedData.tagMap).find(k => k.includes(this.currentSelects.tagName));
            const status = key ? matchedData.tagMap[key] : '其他';
            const color = this.formatStatus(status);
            return {
              ...item,
              status,
              color
            };
          } else {
            return {
              ...item,
              status: '其他',
              color: this.formatStatus('其他')
            };
          }
        });
        this.opacityFlag = 1;
        this.loading = false;
      } catch (error) {
        console.error('Failed to fetch data:', error);
      }
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.topology-box {
	width: 1920px;
	height: 1080px;
	//padding: 56px 58px;
	position: relative;
	transform: scale(0.75);
	transform-origin: top left; /* 确保缩放基点为左上角 */
	.topology-content {
		//background: url("../../../assets/images/topology1.png") no-repeat center;
		background-repeat: no-repeat;
		background-position: center;
		background-size: 100%;

		> span {
			display: block;
			width: 76px;
			height: 42px;
			border-radius: 4px;
			text-align: center;
			background-color: #fff;
			position: absolute;
			left: 0;
			top: 0;
			font-family: Source Han Sans SC;
			font-weight: 500;
			font-size: 20px;
			color: #FFFFFF;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		> .long-text {
			font-size: 18px !important;
		}
	}
}
</style>
