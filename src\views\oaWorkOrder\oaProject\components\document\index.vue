<template>
  <div class="document-info">
    <!--文件数据-->
    <div class="head-container">
      <e-header :permission="permission" />
      <crudOperation :permission="permission">
        <update-button
          v-if="bindId"
          slot="right"
          :bind-id="bindId"
          :enabled="[1]"
          :permission="permission"
        />
        <el-button
          slot="right"
          v-permission="permission.manyEdit"
          class="filter-item"
          icon="el-icon-refresh-left"
          size="mini"
          type="warning"
          @click="moveFile()"
        >移动文件
        </el-button>
      </crudOperation>
    </div>
    <div class="body-box">
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="tableData"
        row-key="id"
        style="width: 100%;"
        @select="crud.selectChange"
        @select-all="crud.selectAllChange"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column :fixed="true" :reserve-selection="true" type="selection" width="55" />
        <el-table-column
          v-for="item in tableHeader"
          :key="item.prop"
          :align="item.align || 'center'"
          :fixed="item.fixed || false"
          :label="item.label"
          :prop="item.prop"
          :show-overflow-tooltip="true"
          :width="item.width"
        >
          <template slot-scope="scope">
            <template v-if="item.prop == 'filePre'">
              <file-thumb
                :file-ext="scope.row.fileExt"
                :preview-list="[scope.row.url]"
                :url="scope.row.thUrl"
                @preView="preView"
              />
            </template>
            <template v-else-if="item.label == '状态'">
              <el-tag :type="scope.row.status == '发布' ? 'success' :'info'">{{ scope.row.status }}</el-tag>
            </template>
            <span v-else>{{ scope.row[item.prop] }}</span>
          </template>
        </el-table-column>
        <!--   编辑与删除   -->
        <el-table-column
          v-if="checkPer(setOperateShow)"
          align="center"
          class="fixed-oper-right"
          fixed="right"
          label="操作"
          width="180"
        >
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
            <min-crud-operation
              :handle-many="handleMany"
              :many-option="manyOption"
              :scope="scope"
              title="更多操作"
            />

          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>

    <!--文件移动-->
    <move-file ref="moveFileRef" :selections="crud.selections" @success="successMove" />
    <!--修改文件名-->
    <edit-file-name ref="editFileNameRef" @success="successEditName" />
  </div>
</template>

<script>
import eHeader from './header.vue';
import oaDocument from '@/api/oaWorkOrder/oaDocument';
import CRUD, { presenter } from '@crud/crud';
import crudOperation from '@crud/CRUD.operation.vue';
import pagination from '@crud/Pagination.vue';
import { downloadUrl, bytesToMBRounded, isImage } from '@/utils';
import updateButton from '@/components/UpdateButton/index.vue'
import { mapGetters } from 'vuex'
import udOperation from '@crud/UD.operation.vue';
import MoveFile from '@/views/oaWorkOrder/oaProject/components/document/moveFile.vue';
import EditFileName from '@/views/oaWorkOrder/oaProject/components/document/editFileName.vue';
import minCrudOperation from '@crud/UD.operation.mini.vue';
// 页面中所有的权限
const permission = {
  list: ['admin', 'oaDocument:list'],
  del: ['admin', 'oaDocument:del'],
  add: ['admin', 'oaDocument:add'],
  manyEdit: ['admin', 'oaDocument:edit'],
  updateT: ['admin', 'oaPm:updateFormStruct'],
  updateR: ['admin', 'oaPm:updateRelation']
}
export default {
  name: 'OaProject',
  components: { minCrudOperation, udOperation, eHeader, crudOperation, pagination, updateButton, MoveFile, EditFileName },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: false,
      title: '文档',
      url: 'api/oaDocument/small',
      sort: ['createTime,desc'],
      query: { enabled: 1 },
      crudMethod: { ...oaDocument },
      optShow: {
        add: false,
        edit: false,
        del: true,
        download: false,
        reset: false,
        rightGroup: false
      }
    })
  },
  mixins: [presenter()],
  data() {
    return {
      permission,
      manyOption: [
        { name: '修改文件名', command: '1', permission: permission.manyEdit, fun: 'editFileName', always: true },
        { name: '下载', command: '2', permission: permission.list, fun: 'download', always: true }
      ],
      tableData: [],
      tableHeader: [],
      bindId: ''
    }
  },
  computed: {
    ...mapGetters([
      'preViewUrl'
    ]),
    setOperateShow() {
      return this.$setArrPermission(['list', 'del', 'manyEdit'], this.permission)
    }
  },
  watch: {
    'crud.data': {
      handler(val) {
        this.formatterTableHeader(val)
        this.formatterTableData(val)
      },
      deep: true
    }
  },
  methods: {

    handleMany(data) {
      const { item } = data
      typeof this[item.fun] === 'function' && this[item.fun](data)
    },
    editFileName({ row }) {
      this.$refs.editFileNameRef.initForm(row);
    },
    // 修改当前文件的目录
    moveFile() {
      const { selections } = this.crud
      console.log(selections, '<===>', 'selections')
      if (selections.length > 0) {
        this.$refs.moveFileRef.initForm();
      } else {
        this.$notify({
          title: '提示',
          message: '请选择要修改目录的文件',
          type: 'info'
        })
      }
    },
    successMove() {
      this.$refs.table.clearSelection();
      this.toQuery();
    },
    successEditName() {
      this.toQuery();
    },
    toQuery() {
      this.crud.toQuery();
    },
    initData() {
      const { docId } = this.$route.query
      this.bindId = docId;
      // this.crud.query.bindId = docId;
    },
    [CRUD.HOOK.beforeRefresh]() {
      // const { docId } = this.$route.query
      // this.bindId = docId;
    },
    download({ row }) {
      downloadUrl(row.url, row.fileName)
    },
    preView(url) {
      window.open(`${this.preViewUrl}/onlinePreview?url=${encodeURIComponent(btoa(url))}`);
    },
    async upDataTableData(data) {
      this.initData();
      const { id = this.$route.query.projectId } = data || {};
      // 设置query的pmId,如果data.id存在,则使用data.id,否则使用$route.query.projectId
      this.$set(this.crud.query, 'pmId', id);
      // 肯定会有 避免第一次保存目录的时候报错
      if (this.crud.query.pmId) {
        this.crud.refresh();
      }
    },
    /**
		 * 格式化表格数据
		 * @param {Array} val - 表格数据数组
		 */
    formatterTableData(val) {
      if (val && val.length) {
        let tableData = [];
        tableData = val.map(item => {
          const json = item.extend.data || {};
          json.id = item.id;
          // json.fv1 = item.fv1;
          json.createBy = item.createBy;
          json.createTime = item.createTime;
          const transMap = item.transMap;
          json.pmTreeName = transMap.pmTreeName;
          json.pmId = transMap.pmTreeId;
          const ft1 = item.ft1 ? JSON.parse(item.ft1) : '';
          json.ft1 = ft1;
          const ft1Data = ft1.response ? ft1.response : ft1.raw.response;
          if (ft1Data) {
            const { originalFilename, createBy, url, size, ext } = ft1Data;
            json.fileName = originalFilename;
            json.fileCreateBy = createBy;
            json.url = url;
            json.fileExt = ext;
            json.thUrl = isImage(ext) ? `${url}?x-oss-process=image/resize,h_100,m_lfit` : '';
            json.size = `${bytesToMBRounded(size)}M`;
          }
          return json;
        });
        this.tableData = tableData;
      } else {
        this.tableData = [];
      }
    },
    /**
		 * 格式化表头
		 * @param {Array} val - 表格数据数组
		 */
    formatterTableHeader(val) {
      const tableHeader = [
        { prop: 'pmTreeName', label: '目录名称', align: 'left' },
        { prop: 'fileName', label: '文档名称', width: 200, align: 'left' },
        { prop: 'filePre', label: '预览', align: 'center', width: 60 },
        { prop: 'size', label: '文档大小', width: 80 },
        { prop: 'fileCreateBy', label: '上传人', width: 60 },
        { prop: 'createTime', label: '上传时间' }
      ]
      const otherHeader = [];
      this.tableHeader = [...tableHeader, ...otherHeader];
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
::v-deep .vue-treeselect__control, ::v-deep .vue-treeselect__placeholder, ::v-deep .vue-treeselect__single-value {
	height: 30px;
	line-height: 30px;
}

//.fixed-oper-right {
//
//	background-color: red;
//}
</style>
