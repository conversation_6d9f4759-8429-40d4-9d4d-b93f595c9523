<template>
  <!-- 绑定模板 -->
  <el-dialog v-loading="loading" append-to-body :show-close="false" :close-on-click-modal="false" :visible="isShow" title="绑定模板" width="500px">
    <el-form ref="form" :rules="formRules" size="small" label-width="80px">
      <el-form-item label="模版" prop="extendTpl.id">
        <el-select v-model="form.extendTpl.id" filterable placeholder="请选择模版" style="width: 100%" :clearable="true">
          <el-option
            v-for="item in templates"
            :key="item.id"
            :label="item.title"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="bindTplId" label="绑定的ID">
        <el-input v-model="bindTplId" placeholder="" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="text" @click="bindCancel()">取消</el-button>
      <el-button type="primary" @click="submitted()">确认</el-button>
      <el-button type="danger" @click="deleteTpl()">删除</el-button>
    </span>
  </el-dialog>
</template>

<script>
import extendBindTpl from '@/api/system/extendBindTpl'
import extendTpl from '@/api/system/extendTpl'
import crudMenu from '@/api/system/menu'
export default {
  data() {
    return {
      form: {
        id: undefined,
        extendTpl: { id: undefined },
        enabled: 1,
        type: 0,
        menu: { id: undefined },
        relation: { relation: '', entityAttribute: '' }
      },
      formRules: {
        'extendTpl.id': { required: true, message: '至少绑定一个模板', trigger: 'blur' }
      },
      isShow: false,
      title: '绑定模板',
      templates: [],
      isEdit: false, // 是否编辑
      bindTplId: undefined, // 编辑时需要绑定这条数据的id
      menuData: {},
      loading: false
    }
  },
  methods: {
    // 初始化数据
    initData(id, data) {
      this.isShow = true;
      this.form.menu = { id: id };
      this.menuData = data;
      this.getList();
      this.getOldTpl();
    },
    // 点击取消
    bindCancel() {
      this.isShow = false;
      this.form.extendTpl.id = undefined;
      this.form.menu = { id: undefined };
      this.isEdit = false;
      this.bindTplId = undefined;
    },
    // 获取所有模板
    getList() {
      const data = {
        size: 999,
        enabled: 1,
        sort: 'id,desc'
      };
      extendTpl.get(data).then(response => {
        this.templates = response.content;
      })
    },
    // 获取已经绑定的模板
    getOldTpl() {
      this.loading = true;
      const data = {
        menuId: this.form.menu.id,
        enabled: 1,
        size: 999,
        page: 0
      };
      extendBindTpl.get(data).then(response => {
        this.loading = false;
        if (response.content.length > 0) {
          this.form.extendTpl.id = response.content[0].extendTpl.id;
          this.isEdit = true;
          this.bindTplId = response.content[0].id;
        }
      }).catch(e => {
        this.loading = false;
      })
    },
    // 确定绑定模板
    submitted() {
      if (!this.form.extendTpl.id) {
        this.$notify({
          title: '请绑定一个模板',
          type: 'info',
          duration: 2500
        });
        return;
      }
      let title = '绑定成功';
      if (this.isEdit) {
        this.form.id = this.bindTplId;
        title = '修改成功';
        extendBindTpl.edit(this.form).then(() => {
          this.$notify({
            title: title,
            type: 'success',
            duration: 2500
          });
          this.bindCancel();
        })
      } else {
        this.form.id = undefined;
        extendBindTpl.add(this.form).then((res) => {
          this.editMenuPathQuery(res);
          this.$notify({
            title: title,
            type: 'success',
            duration: 2500
          });
        })
      }
    },
    // 自动给菜单的path加上query的绑定id
    editMenuPathQuery(res) {
      let path = this.menuData.path;
      if (this.menuData.path.indexOf('?') != -1) {
        path = this.menuData.path.split('?')[0];
      }
      const json = { ...this.menuData, 'path': `${path}?id=${res.id}` }
      crudMenu.edit(json).then(() => {
        this.bindCancel();
        this.$emit('fatherMethod');
      })
    },
    deleteTpl() {
      if (!this.bindTplId) {
        this.$notify({
          title: '暂无绑定模板可删除',
          type: 'info',
          duration: 2500
        });
        return;
      }
      this.$confirm('此操作将永久删除该菜单的模板绑定关系, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        extendBindTpl.del([this.bindTplId]).then((res) => {
          let path = this.menuData.path;
          if (this.menuData.path.indexOf('?') != -1) {
            path = this.menuData.path.split('?')[0];
          }
          const json = { ...this.menuData, 'path': path }
          crudMenu.edit(json).then(() => {
            this.$emit('fatherMethod');
          })
          this.bindCancel();
          this.$notify({
            title: '删除成功',
            type: 'success',
            duration: 2500
          });
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    }

  }
}
</script>
