<template>
  <div
    v-loading="pageLoading"
    class="app-container oa-pm"
    element-loading-spinner="el-icon-loading"
    element-loading-text="更新中"
  >
    <!--工具栏-->
    <div class="head-container">
      <search-header :category-list="categoryList" :dict="dict" :permission="permission" />
      <crudOperation :permission="permission">
        <!--一系列更新按钮-->
        <custom-action-button
          slot="right"
          :bind-id="bindId"
          :current-crud="crud"
          :permission="permission"
          @importProject="importProject"
          @successUpdateInfo="successUpdateInfo"
        />
        <el-button
          slot="right"
          class="filter-item"
          icon="el-icon-location"
          size="mini"
          type="primary"
          @click="handleShowMapView"
        >
          地图展示
        </el-button>
      </crudOperation>
    </div>
    <!--表格渲染-->
    <div ref="projectBody" class="project-table-content">
      <Hamburger :is-active="operateShow" class="hamburger-container" @toggleClick="toggleSideBar" />
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="tableData"
        :header-cell-style="tableRowClassName"
        :load="getSubProjects"
        :max-height="650"
        :summary-method="(param) => getSummaries(param)"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        class="table-fixed"
        lazy
        row-key="id"
        show-summary
        @select="crud.selectChange"
        @cell-dblclick="dbClickCell"
        @select-all="crud.selectAllChange"
        @selection-change="crud.selectionChangeHandler"
        @sort-change="sortChangeHandler"
      >
        <el-table-column :selectable="checkboxT" type="selection" width="55" />
        <el-table-column
          v-for="(header, initIndex) in tableHeaders"
          :key="header.label + initIndex"
          :align="header.align"
          :fixed="header.fixed"
          :label="header.label"
          :prop="header.prop"
          :show-overflow-tooltip="getShowTip(header)"
          :sortable="header.sortable || false"
          :width="header.width"
        >
          <template slot-scope="scope">
            <edit-cell
              v-if="editingConfig.cell === `${scope.row.id}-${header.prop}`"
              :ref="`editElForm${scope.row.id}`"
              :category-list="categoryList"
              :current-crud="crud"
              :current-scope="scope"
              :dict="dict"
              :editing-config="editingConfig"
              :header="header"
              :permission="permission"
            />
            <see-cell
              v-else
              :current-cell="scope"
              :header="header"
              :permission="permission"
              @handleOperateLog="handleOperateLog"
              @handleOperateRemarks="handleOperateRemarks"
            />
          </template>
        </el-table-column>
        <transition name="fade">
          <el-table-column v-if="checkPer(setOperateShow) && operateShow" fixed="right" label="操作" width="240">
            <template slot-scope="scope">
              <!--<udOperation-->
              <!--  v-if="showButton(scope.row)"-->
              <!--  :data="scope.row"-->
              <!--  :permission="permission"-->
              <!--  msg="确定删除吗,如果存在下级节点则一并删除，此操作不能撤销！"-->
              <!--/>-->
              <udOperation
                v-if="showButton(scope.row)"
                :data="scope.row"
                :msg="`确定删除 <span style='color:red'>${scope.row.name}</span> 这个项目吗？ 如果存在下级节点则一并删除，此操作不能撤销！`"
                :permission="permission"
              />
              <min-crud-operation
                :handle-many="handleMany"
                :many-option="manyOption"
                :scope="scope"
                :show-item="showItem"
                title="项目操作"
              />
            </template>
          </el-table-column>
        </transition>
      </el-table>

      <!--分页组件-->
      <pagination />
    </div>
    <!--选择项目成员-->
    <select-user v-if="hasSelectUser" ref="selectUser" @succeSubmit="sureSelect" />
    <!--为项目成员配置权限-->
    <select-project v-if="hasSelectFile" ref="selectFile" @succeSubmit="sureSelect" />
    <!--去派单-->
    <dispatch ref="dispatch" />
    <!--导入项目-->
    <upload-excel ref="uploadExcel" @getlist="crud.toQuery()" />
    <!--项目批注-->
    <remarks
      v-if="remarksShow"
      ref="remarksRef"
      :permission="permission"
      @concelForm="remarksShow = false"
      @toRefresh="crud.refresh()"
    />

    <!--项目日志-->
    <projectLog
      v-if="projectLogShow"
      ref="logRef"
      :permission="permission"
      @concelForm="projectLogShow = false"
      @toRefresh="crud.refresh()"
    />

    <!--地图弹窗-->
    <map-dialog ref="mapDialog" />
  </div>
</template>

<script>
import oaPmTree from '@/api/oaWorkOrder/oaPmTree'
import { getCatalogTree } from '@/api/oaWorkOrder/oaPmProgress'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination';
import minCrudOperation from '@crud/UD.operation.mini'
import UploadExcel from '@/views/oaWorkOrder/oaPm/components/uploadExcel.vue'
import SelectProject from '@/views/oaWorkOrder/oaPm/components/selectProject.vue';
import SelectUser from '@/views/oaWorkOrder/oaPm/components/selectUser.vue';
import Dispatch from '@/views/oaWorkOrder/oaPm/components/dispatch.vue';
import Remarks from '@/views/oaWorkOrder/oaPm/components/remarks.vue';
import EditCell from '@/views/oaWorkOrder/oaPm/components/editCell.vue';
import SeeCell from '@/views/oaWorkOrder/oaPm/components/seeCell.vue';
import CustomActionButton from '@/views/oaWorkOrder/oaPm/components/customActionButton.vue';
import SearchHeader from '../oaPm/module/header.vue'
import projectLog from '@/views/oaWorkOrder/oaPm/components/projectLog.vue';
import MapDialog from '@/views/oaWorkOrder/oaPm/components/mapDialog.vue';
import { mapGetters } from 'vuex'
import Hamburger from '@/components/Hamburger'
import {
  tableRowClassName,
  formatterTableData,
  formatterTableHeader,
  operateRemarks,
  operateLog,
  handleAuthFun,
  handleUserFun,
  handleSummaries,
  projectLeaderOrRoles,
  getCategoryList,
  toggleSideBarFun, sortChangeHandlerFun, addProjectFun, sureSelectFun, onDispatchFun
} from '@/views/oaWorkOrder/oaPm/utils/commonFun'
import {
  permission,
  manyOption
} from '@/views/oaWorkOrder/oaPm/utils/field'
// crud交由presenter持有
const defaultForm = {
  id: null
}
export default {
  name: 'OaPm',
  components: {
    UploadExcel,
    Remarks,
    projectLog,
    SearchHeader,
    Dispatch,
    SelectUser,
    SelectProject, crudOperation, udOperation, pagination,
    minCrudOperation,
    Hamburger,
    EditCell,
    CustomActionButton,
    SeeCell,
    MapDialog
  },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: false,
      title: '项目',
      url: 'api/oaPmTree/small',
      sort: [],
      query: { enabled: 1, fv1: '项目', fv2: '', fv3: [], fv4: [], ft4: [], pidIsNull: true, sort: ['createTime,desc'] },
      crudMethod: { ...oaPmTree },
      optShow: {
        add: true,
        edit: false,
        del: false,
        download: false,
        reset: true,
        rightGroup: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['project_status', 'project_info_stage', 'project_info_status', 'department', 'project_info_type'],
  data() {
    return {
      editingConfig: {
        cell: null, // 当前编辑的单元格
        currentEditData: {},
        currentEditHeader: {},
        dictList: []
      }, // 编辑配置
      tableHeight: 0,
      operateShow: false, // 操作列是否显示
      remarksShow: false,
      projectLogShow: false,
      pageLoading: false,
      tableData: [],
      hasSelectUser: false,
      hasSelectFile: false,
      menus: [],
      permission,
      rules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        path: [
          { required: true, message: '请输入地址', trigger: 'blur' }
        ]
      },
      bindId: '',
      manyOption,
      tableHeaders: [],
      projectRoles: this.$config['roles_keys'],
      sortField: {},
      allProject: [],
      categoryList: [],
      isBlurred: false
      // stageExecHeaders: [],
      // stageInitHeaders: [],
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ]),
    showItem({ user, projectRoles }) {
      return (item, row) => {
        if (row.noTop && !item.always) {
          return false;
        }
        return projectLeaderOrRoles(user, projectRoles, row)
      };
    },
    setOperateShow() {
      return this.$setArrPermission(['add', 'del', 'addMember'], this.permission)
    },
    showButton({ user }) {
      return (row) => {
        return projectLeaderOrRoles(user, this.$config['pm_edit_roles'], row)
      };
    }
  },
  watch: {
    'crud.data': {
      async handler(val, oldVal) {
        this.allProject = []
        this.tableHeaders = formatterTableHeader(val)
        this.tableData = formatterTableData(val)
        this.allProject = [...this.tableData]
        await this.$nextTick()
        this.$refs.table.doLayout()
      },
      deep: true
    }
  },
  async created() {
    this.crud.operToggle = false
    const { bindId } = this.$config.projects_keys
    this.bindId = bindId;
    this.crud.query.bindId = bindId
    this.crud.toQuery();
    this.categoryList = await getCategoryList()
  },
  methods: {
    // 显示地图弹窗
    async handleShowMapView() {
      try {
        // 检查是否选择了项目
        if (!this.crud.selections || this.crud.selections.length === 0) {
          this.$message.warning('请先选择项目')
          return
        }

        // 显示加载状态
        const loading = this.$loading({
          lock: true,
          text: '正在获取项目点位数据...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        // 获取选中项目的ID列表
        const projectIds = this.crud.selections.map(project => project.id)
        console.log('选中的项目ID列表:', projectIds)

        // 依次调用API获取每个项目的目录树数据
        const allLocationPoints = []

        for (const projectId of projectIds) {
          try {
            console.log(`正在获取项目 ${projectId} 的目录树数据...`)
            const response = await getCatalogTree(projectId)

            if (response.treeList) {
              // 递归遍历树结构，筛选出type=='point'的节点
              const pointNodes = this.extractPointNodes(response.treeList)

              // 转换为地图组件需要的格式并添加项目信息
              const projectPoints = pointNodes.map(point => ({
                name: point.name || point.title || '未命名点位',
                status: point.status || 1, // 默认为1，表示未开始
                longitude: point.longitude || point.lng || point.lon,
                latitude: point.latitude || point.lat,
                id: point.id,
                projectId: projectId, // 添加项目ID标识
                projectName: this.crud.selections.find(p => p.id === projectId)?.name || '未知项目',
                ...point // 保留原始数据
              })).filter(point => point.longitude && point.latitude) // 过滤掉没有坐标的点位

              allLocationPoints.push(...projectPoints)
              console.log(`项目 ${projectId} 获取到 ${projectPoints.length} 个点位`)
            } else {
              console.warn(`项目 ${projectId} 未获取到目录树数据`)
            }
          } catch (error) {
            console.error(`获取项目 ${projectId} 的目录树数据失败:`, error)
            // 继续处理下一个项目，不中断整个流程
          }
        }

        // 关闭加载状态
        loading.close()

        if (allLocationPoints.length === 0) {
          this.$message.warning('选中的项目中没有找到有效的点位数据')
          return
        }

        console.log(`总共获取到 ${allLocationPoints.length} 个点位数据`)

        // 传递合并后的点位数据到地图组件
        this.$refs.mapDialog.show(allLocationPoints)

        // 显示成功消息
        this.$message.success(`成功加载 ${allLocationPoints.length} 个点位数据`)
      } catch (error) {
        console.error('获取项目点位数据失败:', error)
        this.$message.error('获取项目点位数据失败: ' + (error.message || '未知错误'))
      }
    },

    // 递归提取type=='point'的节点
    extractPointNodes(nodes) {
      const pointNodes = []

      const traverse = (nodeList) => {
        if (!Array.isArray(nodeList)) return

        nodeList.forEach(node => {
          // 如果节点类型是'point'，添加到结果数组
          if (node.type === 'point') {
            pointNodes.push(node)
          }

          // 如果有子节点，递归遍历
          if (node.children && Array.isArray(node.children)) {
            traverse(node.children)
          }
        })
      }

      traverse(nodes)
      return pointNodes
    },
    // 点击表格箭头-获取子项目
    getSubProjects(tree, treeNode, resolve) {
      const params = { pid: tree.id, enabled: 1, fv1: '子项目', size: 999 }
      setTimeout(() => {
        oaPmTree.getPmTreeSmall(params).then(res => {
          const data = res.content.map(item => ({ ...item, noTop: true }));
          const newTableChilerData = formatterTableData(data)
          this.allProject = [...this.allProject, ...newTableChilerData]
          resolve(newTableChilerData)
        })
      }, 100)
    },
    // 成功更新之后的操作
    successUpdateInfo(type) {
      if (type == 1) {
        this.pageLoading = true
      } else if (type == 2) {
        this.pageLoading = false
      } else {
        this.pageLoading = false
        this.crud.toQuery();
      }
    },
    getShowTip(header) {
      const flag = header.showTip !== undefined ? header.showTip : true;
      return flag
    },
    async dbClickCell(row, column, cell, event) {
      if (!projectLeaderOrRoles(this.user, this.$config['pm_edit_roles'], row)) {
        return
      } else {
        this.editingConfig.currentEditHeader = this.tableHeaders.find(item => item.prop === column.property)
        const { type, dict } = this.editingConfig.currentEditHeader
        if (!type) {
          this.editingConfig.currentEditHeader = {}
          return
        } else {
          this.editingConfig.currentEditData = row
          const currentRow = this.allProject.find(item => item.id === row.id);
          if (type == 'select') {
            this.editingConfig.dictList = this.dict[dict]
          }
          await this.$nextTick();
          this.editingConfig.currentEditHeader.showTip = false;
          this.editingConfig.cell = `${currentRow.id}-${column.property}`;
          // await this.$nextTick()
          // const str = `editElForm${currentRow.id}`
          // const editElForm = this.$refs[str]
          // // 需要根据dom结构的改变而改变
          // if (editElForm) {
          //   editElForm.forEach(item => {
          //     const inputElements = item.$el.querySelectorAll('input');
          //     inputElements.forEach(input => {
          //       input.focus();
          //     })
          //   })
          // }
        }
      }
    },
    // 导入项目
    importProject() {
      const { bindId, categoryId } = this.$config.projects_keys
      this.$refs.uploadExcel.init({ bindId, categoryId });
    },
    // 金额合计
    getSummaries(param) {
      const data = {
        firstValue: '合计',
        data: param.data,
        columns: param.columns,
        targetProperties: ['fv14', 'fv7', 'fv21', 'fv8', 'fv9', 'fv11', 'fv12', 'fv13', 'fv23', 'fv24', 'fv25']
      }
      return handleSummaries(data)
    },

    [CRUD.HOOK.beforeRefresh]() {
      this.crud.query.bindId = this.bindId
    },
    [CRUD.HOOK.beforeResetQuery]() {
      this.$refs.table.clearSort()
    },
    [CRUD.HOOK.beforeToAdd]() {
      this.$router.push({ name: 'AddOaPmForm', query: { bindId: this.bindId, command: 1, fv1: '项目' }})
    },
    [CRUD.HOOK.beforeToEdit](crud, form) {
      const { id, bindId, fv1 } = form
      this.$router.push({ name: 'AddOaPmForm', query: { id, bindId, fv1 }})
    },
    // 确认选择 项目成员和目录
    sureSelect(info) {
      sureSelectFun(info, this)
    },
    // 点击项目派单
    onDispatch(data) {
      onDispatchFun(data, this)
    },
    // 点击添加项目
    addProject(data) {
      addProjectFun(data, this)
    },
    // 表头颜色函数
    tableRowClassName,
    // 点击批注
    async handleOperateRemarks(data) {
      await operateRemarks(data, this)
    },
    // 点击项目日志
    async handleOperateLog(data) {
      await operateLog(data, this)
    },
    // 打开项目成员抽屉
    async handleUsers(data) {
      await handleUserFun(data, this)
    },
    // 打开项目成员权限抽屉
    async handleAuth(data) {
      await handleAuthFun(data, this)
    },
    // 点击操作列头
    async toggleSideBar() {
      await toggleSideBarFun(this)
    },
    // 表格排序
    sortChangeHandler(data) {
      sortChangeHandlerFun(data, this)
    },

    composeValue(item, row) {
      return {
        command: Number(item.command),
        row,
        item
      }
    },
    handleMany(data) {
      const { item } = data
      typeof this[item.fun] === 'function' && this[item.fun](data)
    },
    // 控制表格行是否可以被选择
    checkboxT(row, rowIndex) {
      // 可以根据需要添加选择条件，比如某些状态的项目不能被选择
      // 这里默认所有行都可以被选择
      return true
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
//操作按钮相关
.operate-button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;

}

.project-table-content {
  position: relative;

  .hamburger-container {
    position: absolute;
    right: 0;
    top: 0;
    z-index: 8;
    line-height: 50px;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color: transparent;
    background: rgba(0, 0, 0, .09);

    &:hover {
      background: rgba(0, 0, 0, .19)
    }
  }
}

::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

::v-deep .vue-treeselect__control,
::v-deep .vue-treeselect__placeholder,
::v-deep .vue-treeselect__single-value {
  height: 30px;
  line-height: 30px;
}

.table-fixed {

  //max-height: 760px;
  // 表格合计样式
  ::v-deep.el-table__footer {
    tr {
      td {
        font-size: 14px;
        color: #606266;
        font-weight: 700;
        text-align: right;
        font-family: Sans-serif;

        &:first-child {
          text-align: left;
        }
      }
    }
  }

  ::v-deep .el-table__fixed-body-wrapper {
    height: calc(100% - 130px) !important;
    padding-bottom: 17px;
    box-sizing: content-box;
  }
}
</style>
