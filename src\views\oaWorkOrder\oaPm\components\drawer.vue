<template>
  <el-drawer
    size="85%"
    :title="title"
    :visible.sync="visible"
    :direction="direction"
    :before-close="beforeClose"
    :with-header="false"
    :destroy-on-close="true"
    :wrapper-closable="false"
    :close-on-press-escape="false"
  >
    <div class="drawer-content">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="立项阶段" name="first">
          <el-card class="box-card" style="margin-top: 10px">
            <div slot="header" class="clearfix">
              <span>立项阶段表单信息</span>
            </div>
            <div v-if="stageInit.showFormData" class="text item">
              <fm-generate-form
                :ref="'generateForm_stageInit'"
                :data="stageInit.formStruct"
                :remote="remoteFunc"
                :value="stageInit.formData"
                :preview="stageInit.viewOrEdit"
              />
            </div>
            <hr style="background-color: #d9d9d9; border:0; height:1px;">
            <div class="text item" style="text-align: center;margin-top:18px">
              <el-button v-if="!stageInit.viewOrEdit" type="primary" :disabled="stageInit.submitDisabled" @click="submitAction('stageInit')">提交</el-button>
              <el-button @click="concelForm">取消并关闭</el-button>
            </div>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="实施阶段" name="second">
          <el-card class="box-card" style="margin-top: 10px">
            <div slot="header" class="clearfix">
              <span>实施阶段表单信息</span>
            </div>
            <div v-if="stageExec.showFormData" class="text item">
              <fm-generate-form
                :ref="'generateForm_stageExec'"
                :data="stageExec.formStruct"
                :remote="remoteFunc"
                :value="stageExec.formData"
                :preview="stageExec.viewOrEdit"
              />
            </div>
            <hr style="background-color: #d9d9d9; border:0; height:1px;">
            <div class="text item" style="text-align: center;margin-top:18px">
              <el-button v-if="!stageExec.viewOrEdit" type="primary" :disabled="stageExec.submitDisabled" @click="submitAction('stageExec')">提交</el-button>
              <el-button @click="concelForm">取消并关闭</el-button>
            </div>
          </el-card>
        </el-tab-pane>
      </el-tabs>

    </div>
  </el-drawer>
</template>

<script>
import Vue from 'vue'
import { GenerateForm } from '@/components/VueFormMaking'
import 'form-making/dist/FormMaking.css'
Vue.component(GenerateForm.name, GenerateForm)
import extendBindTpl from '@/api/system/extendBindTpl'
import oaPmStageInit from '@/api/oaWorkOrder/oaPmStageInit'
import oaPmStageExec from '@/api/oaWorkOrder/oaPmStageExec'
import { getConfig } from '@/utils/getConfigData.js'
import crudDictDetail from '@/api/system/dictDetail'
export default {
  props: {
    direction: {
      type: String,
      default: 'rtl' // 设置默认方向，可以根据需要修改
    }
  },
  data() {
    return {
      visible: false,
      title: '标题',
      activeName: 'first',
      pmId: '',
      // 立项阶段
      stageInit: {
        submitDisabled: false,
        processStructureValue: {},
        showFormData: false,
        formStruct: {},
        formData: {},
        viewOrEdit: false, // 默认是编辑
        bindId: '',
        jsonData: {},
        request: oaPmStageInit
      },
      // 实施阶段
      stageExec: {
        submitDisabled: false,
        processStructureValue: {},
        showFormData: false,
        formStruct: {},
        formData: {},
        viewOrEdit: false, // 默认是编辑
        bindId: '',
        jsonData: {},
        request: oaPmStageExec
      },
      remoteFunc: {
        getDictDetail(resolve, dictName) {
          crudDictDetail.get(dictName).then(response => {
            const options = response.content
            resolve(options)
          })
        },
        isStageInit(resolve) {
          // 项目类型
          this.getDictDetail(resolve, 'is_stage_init');
        }
      }
    }
  },
  methods: {
    init(option) {
      this.visible = true;
      this.pmId = option.pmId;
      this.getInitData();
    },
    getInitData() {
      this.getStageInit()
      this.getStageExec();
    },
    async getStageInit() {
      this.stageInit.bindId = await this.getConfigData('stage_init');
      await this.getProcessNodeList(this.stageInit.bindId, 'stageInit');// 立项
      await this.getContent('stageInit');
    },
    async getStageExec() {
      this.stageExec.bindId = await this.getConfigData('stage_exec');
      await this.getProcessNodeList(this.stageExec.bindId, 'stageExec');// 实施
      await this.getContent('stageExec');
    },
    getContent(name) {
      this[name].request.get({ pmId: this.pmId }).then(res => {
        if (res && res.content && res.content.length) {
          const jsonData = res.content[0];
          this[name].formData = JSON.parse(jsonData.formData);
          this[name].jsonData = jsonData;
          this[name].showFormData = true;
        } else {
          this[name].showFormData = true;
        }
      }).catch(e => {
        this[name].showFormData = true;
      })
    },
    getProcessNodeList(bindId, name) {
      const data = { id: bindId, enabled: 1 }
      extendBindTpl.get(data).then(res => {
        if (res && res.content && res.content.length) {
          this[name].processStructureValue = res.content[0];
          const FormStruct = JSON.parse(this[name].processStructureValue.extendTpl.formStruct);
          this[name].formStruct = FormStruct;
        }
        // this[name].showFormData = true;
      });
    },
    doRequest(values, name) {
      const subData = {
        pmId: this.pmId,
        bindId: this[name].bindId,
        enabled: 1,
        formData: JSON.stringify(values),
        formStruct: JSON.stringify(this[name].formStruct),
        formBindToVar: 1,
        relation: this[name].processStructureValue.relation
      };
      let request = this[name].request.add;
      let requestData = subData
      if (this[name].jsonData && this[name].jsonData.id) {
        subData.id = this[name].jsonData.id;
        request = this[name].request.edit;
        requestData = subData
      }
      request(requestData).then(response => {
        this.$notify({
          title: '保存成功',
          type: 'success',
          duration: 2500
        });
        this[name].viewOrEdit = true;
        if (this.stageInit.viewOrEdit && this.stageExec.viewOrEdit) {
          setTimeout(() => {
            this.concelForm();
          }, 1500)
        }
      }).catch(() => {
        this.submitDisabled = false
      })
    },
    submitAction(name) {
      let isGenerateFormValid = false;
      this.$refs['generateForm_' + name].getData().then(values => {
        isGenerateFormValid = true;
        // 如果 generateForm 校验通过
        if (isGenerateFormValid) {
          this.doRequest(values, name);
        }
      }).catch(() => {
        isGenerateFormValid = false;
        this[name].submitDisabled = false;
      });
    },
    async getConfigData(value) {
      const data = {
        key: value
      }
      return await getConfig(data).then(res => {
        return res.extend.data.bindId;
      })
    },
    concelForm() {
      this.beforeClose();
    },
    handleClick() {

    },
    beforeClose() {
      // 恢复data中的初始值
      this.$emit('succeSubmit');
      this.selectVisible = false;
      Object.assign(this.$data, this.$options.data.call(this));
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.drawer-content{
  padding: 20px;
}
</style>
