<template>
  <el-breadcrumb separator="/">
    <el-breadcrumb-item>{{ treeData.fv16 }} {{ treeData.name }}</el-breadcrumb-item>
    <el-breadcrumb-item v-if="treeData.fv13">{{ treeData.fv13 }}</el-breadcrumb-item>
    <el-breadcrumb-item v-if="treeData.fv11">{{ treeData.fv11 }}</el-breadcrumb-item>
    <el-breadcrumb-item>{{ treeData.createBy }}</el-breadcrumb-item>
    <span v-if="treeData.fv12" :style="{ color: statusColor(treeData) }" class="node-label">
      ({{ treeData.fv12 }})
    </span>
  </el-breadcrumb>
</template>

<script>

export default {
  name: '',
  components: {},

  props: {
    treeData: {
      type: Object,
      default: () => {
      }
    }
  },
  computed: {

    statusColor() {
      // const colors = ['red', '#1890ff'];
      const colors = {
        '已审核': '#1890ff',
        '逾期': 'red'
      }
      return data => colors[data.fv12] || 'red';
    }
  }

}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
