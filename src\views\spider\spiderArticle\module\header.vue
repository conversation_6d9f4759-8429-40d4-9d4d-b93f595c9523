<template>
  <div
    v-if="crud.props.searchToggle"
  >
    <el-input
      v-model="query.fv1"
      class="filter-item"
      clearable
      placeholder="请输入项目名称搜索"
      size="small"
      style="width: 200px;"
    />
    <el-input
      v-model="query.fv2"
      class="filter-item"
      clearable
      placeholder="请输入项目编号搜索"
      size="small"
      style="width: 200px;"
    />
    <el-input
      v-model="query.fv5"
      class="filter-item"
      clearable
      placeholder="请输入采购单位搜索"
      size="small"
      style="width: 200px;"
    />
    <el-input
      v-model="query.fv12"
      class="filter-item"
      clearable
      placeholder="请输入中标单位搜索"
      size="small"
      style="width: 200px;"
    />
    <el-input
      v-model="query.fv13"
      class="filter-item"
      clearable
      placeholder="请输入标签搜索"
      size="small"
      style="width: 200px;"
    />
    <el-select
      v-model="query.fv3"
      class="filter-item"
      clearable
      placeholder="请选择公告类型"
      style="width: 200px"
    >
      <el-option
        v-for="item in announcementType"
        :key="item.id"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <date-range-picker v-model="query.fv11" class="date-item" />
    <rrOperation>
      <el-button
        slot="right"
        v-permission="permission.count"
        class="filter-item"
        icon="el-icon-monitor"
        size="mini"
        type="primary"
        @click="openStatistics()"
      >信息统计
      </el-button>
    </rrOperation>
  </div>
</template>

<script>
import { header } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import DateRangePicker from '@/components/DateRangePicker'

export default {
  components: { rrOperation, DateRangePicker },
  mixins: [header()],
  props: {
    permission: {
      type: Object,
      required: true
    },
    announcementType: {
      typeof: Array,
      required: true
    }
  },
  methods: {
    openStatistics() {
      this.$emit('openStatistics')
    }

  }
}
</script>
