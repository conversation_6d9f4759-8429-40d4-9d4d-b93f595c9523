export function findTopLevelId(tree, nodeId) {
  function findNodeInTree(node, idToFind) {
    if (node.id === idToFind) {
      return node;
    }
    if (node.children) {
      for (const child of node.children) {
        const foundNode = findNodeInTree(child, idToFind);
        if (foundNode) {
          return foundNode;
        }
      }
    }
    return null;
  }

  for (const node of tree) {
    const foundNode = findNodeInTree(node, nodeId);
    if (foundNode) {
      return node.id;
    }
  }
  return null; // 未找到匹配的id
}

