<template>
  <div class="app-container">
    <el-card class="box-card" style="margin-top: 10px">
      <div slot="header" class="clearfix">
        <span>调整目录</span>
        <el-button size="mini" style="float: right" type="primary" @click="saveData">保存</el-button>
      </div>
      <el-form ref="elForm" :model="elForm" :rules="elFormRules" label-width="130px">

        <el-row>
          <el-col :span="12">
            <el-form-item label="原目录模板" prop="fv1">
              <el-select
                v-model="elForm.fv1"
                placeholder="请选择"
                @change="(val)=>changeTemplate(val, 'oldSelectList')"
              >
                <el-option
                  v-for="item in catalogueList"
                  :key="item.id"
                  :disabled="item.id == elForm.fv2"
                  :label="item.title"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="新目录模版" prop="fv2">
              <el-select
                v-model="elForm.fv2"
                placeholder="请选择"
                @change="(val)=>changeTemplate(val, 'newSelectList')"
              >
                <el-option
                  v-for="item in catalogueList"
                  :key="item.id"
                  :disabled="item.id == elForm.fv1"
                  :label="item.title"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="模板详细信息" prop="ft1">
          <el-table :data="elForm.ft1" border style="width: 100%">
            <el-table-column fixed width="50">
              <template slot="header">
                <i
                  class="el-icon-circle-plus"
                  style="font-size: 25px; color: #409EFF; cursor: pointer;"
                  @click="addSubformCol"
                />
              </template>
              <template slot-scope="scope">
                <i
                  class="el-icon-remove"
                  style="font-size: 25px; color: red; cursor: pointer;"
                  @click="delSubformCol(scope.$index)"
                />
              </template>
            </el-table-column>
            <el-table-column label="原序号" prop="oldIndexSort" />
            <el-table-column label="原名称" prop="oldName">
              <template slot-scope="scope">
                <el-select
                  v-model="scope.row.oldIndexSort"
                  clearable
                  placeholder="请选择原名称"
                  @change="(val) => changeSelect(val, scope.$index, 'old')"
                >
                  <el-option
                    v-for="item in oldSelectList"
                    :key="item.indexSort"
                    :label="item.name"
                    :value="item.indexSort"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="新序号" prop="newIndexSort" />
            <el-table-column label="新名称" prop="newName">
              <template slot-scope="scope">
                <el-select
                  v-model="scope.row.newIndexSort"
                  clearable
                  placeholder="请选择新名称"
                  @change="(val) => changeSelect(val, scope.$index, 'new')"
                >
                  <el-option
                    v-for="item in newSelectList"
                    :key="item.indexSort"
                    :label="item.name"
                    :value="item.indexSort"
                  />
                </el-select>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>

        <el-row>
          <el-col :span="12">
            <el-form-item label="选中项目" prop="ft2">
              <self-select
                :disabled="elForm.ft3.length > 0"
                :filterable="true"
                :options="allFt2ProjectList"
                :select-value.sync="elForm.ft2"
                :tags="2"
                clearable
                custom-label="name"
                custom-value="id"
                placeholder="请选择要修改的项目"
                size="small"
                style="width: 100%;"
                @selectChange="(val)=>selectChange(val,'ft2')"
              />
            </el-form-item>
            <el-form-item label="选中的项目">
              <ul>
                <li v-for="item in selectFt2ProjectList" :key="item.id">{{ item.name }}({{ item.fv1 }})</li>
              </ul>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排除项目">
              <self-select
                :disabled="elForm.ft2.length > 0"
                :filterable="true"
                :options="allFt3ProjectList"
                :select-value.sync="elForm.ft3"
                :tags="2"
                clearable
                custom-label="name"
                custom-value="id"
                placeholder="请选择要修改的项目"
                size="small"
                style="width: 100%;"
                @selectChange="(val)=>selectChange(val,'ft3')"
              />
            </el-form-item>
            <el-form-item label="排除的项目">
              <ul>
                <li v-for="item in selectFt3ProjectList" :key="item.id">{{ item.name }}({{ item.fv1 }})</li>
              </ul>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

    </el-card>
  </div>
</template>

<script>
import { getOaPmCatalogData } from '@/views/oaWorkOrder/catalogue/utils/catalogue';
import {
  findSelectList,
  findSelectObj,
  getAllproject,
  clearArrObjProps,
  updateOptionsDisabled,
  updateOptionsDisabledFalse
} from '@/views/oaWorkOrder/catalogOper/utils/catalogOper'
import oaPmCatalogRelation from '@/api/oaWorkOrder/oaPmCatalogRelation';

export default {
  name: 'CatalogueForm',
  components: {},

  props: {},
  data() {
    return {
      elForm: {
        fv1: '', // 旧的模版ID
        fv2: '', // 要修改的模版的ID
        enabled: 1,
        status: 0, // 没有执行
        ft1: [], // 是修改的具体
        ft2: [], // 是要修改的项目
        ft3: [] // 是要不修改的项目
      },
      elFormRules: {
        fv1: [{ required: true, message: '请输入原目录模版', trigger: ['blur', 'change'] }],
        fv2: [{ required: true, message: '请输入新目录模版', trigger: ['blur', 'change'] }]
        // ft2: [{ required: true, message: '请选择要修改的项目', trigger: ['blur', 'change'] }],
        // ft3: [{ required: true, message: '请选择要排除修改的项目', trigger: ['blur', 'change'] }]
      },
      catalogueList: [],
      oldSelectList: [],
      newSelectList: [],
      allFt2ProjectList: [],
      allFt3ProjectList: [],
      selectFt2ProjectList: [],
      selectFt3ProjectList: []
    }
  },
  computed: {},
  watch: {},
  async created() {
    const { id } = this.$route.query
    this.initData()
    const allProjectList = await getAllproject()
    this.allFt3ProjectList = JSON.parse(JSON.stringify(allProjectList))
    this.allFt2ProjectList = JSON.parse(JSON.stringify(allProjectList))
    if (id) {
      // 编辑
      this.editInitData(id)
    } else {
      // 添加
    }
  },
  methods: {
    async initData() {
      const queryResult = await getOaPmCatalogData({ sort: 'createTime,desc', enabled: 1, size: 9999 })
      this.catalogueList = JSON.parse(JSON.stringify(queryResult)) || []
    },
    // 编辑对应关系初始化
    async editInitData(id) {
      const queryResult = await oaPmCatalogRelation.getSmall({ id })
      const { content } = queryResult
      const data = content[0]
      const ft1 = JSON.parse(data.ft1)
      const newFt1 = ft1.map(item => {
        return {
          oldIndexSort: item.o1,
          oldName: item.o2,
          newIndexSort: item.n1,
          newName: item.n2
        }
      })
      this.elForm = {
        ...data,
        ft1: newFt1,
        ft2: data.ft2 ? JSON.parse(data.ft2) : [],
        ft3: data.ft3 ? JSON.parse(data.ft3) : [],
        fv1: Number(data.fv1),
        fv2: Number(data.fv2)
      }
      this.changeTemplate(this.elForm.fv1, 'oldSelectList', 1)
      this.changeTemplate(this.elForm.fv2, 'newSelectList', 1)
      this.selectChange(this.elForm.ft2, 'ft2')
      this.selectChange(this.elForm.ft3, 'ft3')
    },
    addSubformCol(data) {
      const obj = {
        oldIndexSort: '',
        oldName: '',
        newIndexSort: '',
        newName: ''
      }
      this.elForm.ft1.push(obj)
    },
    delSubformCol(index) {
      this.elForm.ft1.splice(index, 1)
    },
    changeTemplate(val, arr, type) {
      this[arr] = findSelectList(this.catalogueList, val)
      if (type) {
        return
      }
      // 清空指定属性
      const props = arr === 'oldSelectList' ? ['oldIndexSort', 'oldName'] : ['newIndexSort', 'newName']
      this.elForm.ft1 = clearArrObjProps(this.elForm.ft1, props);
    },
    changeSelect(val, index, type) {
      let obj = {}
      if (type === 'old') {
        obj = findSelectObj(this.oldSelectList, val)
        this.$set(this.elForm.ft1[index], 'oldIndexSort', val && obj.indexSort ? obj.indexSort : '');
        this.$set(this.elForm.ft1[index], 'oldName', val && obj.name ? obj.name : '');
      } else {
        obj = findSelectObj(this.newSelectList, val)
        this.$set(this.elForm.ft1[index], 'newIndexSort', val && obj.indexSort ? obj.indexSort : '');
        this.$set(this.elForm.ft1[index], 'newName', val && obj.name ? obj.name : '');
      }
    },
    selectChange(val, type) {
      this.elForm[type] = val
      const updateSelectedList = (val, selectList, allList) => {
        if (val.length > 0) {
          const filteredArray = this[allList].filter(item => val.includes(item.id));
          this[selectList] = filteredArray;
          this[allList] = updateOptionsDisabled(val, this[allList]);
        } else {
          this[selectList] = [];
          this[allList] = updateOptionsDisabledFalse(this[allList]);
        }
      };

      if (type === 'ft2') {
        updateSelectedList(val, 'selectFt2ProjectList', 'allFt3ProjectList');
      } else if (type === 'ft3') {
        updateSelectedList(val, 'selectFt3ProjectList', 'allFt2ProjectList');
      }
    },
    saveData() {
      this.$refs.elForm.validate(async valid => {
        if (!valid) {
          return
        } else {
          const ft1 = this.elForm.ft1.map(item => {
            return {
              o1: item.oldIndexSort,
              o2: item.oldName,
              n1: item.newIndexSort,
              n2: item.newName
            }
          })
          const data = {
            ...this.elForm,
            ft1: JSON.stringify(ft1),
            ft2: JSON.stringify(this.elForm.ft2),
            ft3: JSON.stringify(this.elForm.ft3)
          }
          let title = '添加'
          let request = oaPmCatalogRelation.add

          if (this.elForm.id) {
            title = '编辑'
            request = oaPmCatalogRelation.edit
          }
          request(data).then(res => {
            this.$message({
              message: `${title}成功`,
              type: 'success'
            })
            this.goBack()
          })
        }
      })
    },
    goBack() {
      this.$router.push({ name: 'OperCatalog' });
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
