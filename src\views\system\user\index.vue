<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--侧边部门数据-->
      <el-col :lg="4" :md="5" :sm="6" :xl="4" :xs="9">
        <div class="head-container">
          <el-input
            v-model="deptName"
            class="filter-item"
            clearable
            placeholder="输入部门名称搜索"
            prefix-icon="el-icon-search"
            size="small"
            @input="getDeptDatas"
          />
        </div>
        <el-tree
          :data="deptDatas"
          :expand-on-click-node="false"
          :load="getDeptDatas"
          :props="defaultProps"
          lazy
          @node-click="handleNodeClick"
        />
      </el-col>
      <!--用户数据-->
      <el-col :lg="20" :md="19" :sm="18" :xl="20" :xs="15">
        <!--工具栏-->
        <div class="head-container">
          <div v-if="crud.props.searchToggle">
            <!-- 搜索 -->
            <el-input
              v-model="query.blurry"
              class="filter-item"
              clearable
              placeholder="输入名称或者邮箱搜索"
              size="small"
              style="width: 200px;"
              @keyup.enter.native="crud.toQuery"
            />
            <date-range-picker v-model="query.createTime" class="date-item" />
            <el-select
              v-model="query.enabled"
              class="filter-item"
              clearable
              placeholder="状态"
              size="small"
              style="width: 90px"
              @change="crud.toQuery"
            >
              <el-option
                v-for="item in enabledTypeOptions"
                :key="item.key"
                :label="item.display_name"
                :value="item.key"
              />
            </el-select>
            <rrOperation />
          </div>
          <crudOperation :permission="permission" show="" />
        </div>
        <!--表单渲染-->
        <el-dialog
          :before-close="crud.cancelCU"
          :close-on-click-modal="false"
          :title="crud.status.title"
          :visible.sync="crud.status.cu > 0"
          append-to-body
          width="570px"
        >
          <el-form ref="form" :inline="true" :model="form" :rules="rules" label-width="66px" size="small">
            <el-form-item label="用户名" prop="username">
              <el-input v-model="form.username" clearable placeholder="用户名" style="width: 178px" />
            </el-form-item>
            <el-form-item label="电话" prop="phone">
              <el-input v-model.number="form.phone" clearable placeholder="电话" style="width: 178px" />
            </el-form-item>
            <el-form-item label="姓名" prop="nickName">
              <el-input v-model="form.nickName" clearable placeholder="姓名" style="width: 178px" />
            </el-form-item>
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" clearable placeholder="邮箱" style="width: 178px" />
            </el-form-item>
            <el-form-item label="部门" prop="dept.id">
              <treeselect
                v-model="form.dept.id"
                :load-options="loadDepts"
                :options="depts"
                placeholder="选择部门"
                style="width: 178px"
              />
            </el-form-item>
            <el-form-item label="角色" prop="roles" style="margin-bottom: 0;">
              <el-select
                v-model="roleDatas"
                multiple
                placeholder="请选择"
                style="width: 178px"
                @change="changeRole"
                @remove-tag="deleteTag"
              >
                <el-option
                  v-for="item in roles"
                  :key="item.name"
                  :disabled="level !== 1 && item.level <= level"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="岗位" prop="jobs">
						<el-select
							v-model="jobDatas"
							style="width: 178px"
							multiple
							placeholder="请选择"
							@remove-tag="deleteTag"
							@change="changeJob"
						>
							<el-option
								v-for="item in jobs"
								:key="item.name"
								:label="item.name"
								:value="item.id"
							/>
						</el-select>
					</el-form-item> -->
            <el-form-item label="性别">
              <el-radio-group v-model="form.gender" style="width: 178px">
                <el-radio label="男">男</el-radio>
                <el-radio label="女">女</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="状态">
              <el-radio-group v-model="form.enabled" :disabled="form.id === user.id">
                <el-radio
                  v-for="item in dict.user_status"
                  :key="item.id"
                  :label="item.value"
                >{{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="text" @click="crud.cancelCU">取消</el-button>
            <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
          </div>
        </el-dialog>
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :data="crud.data"
          style="width: 100%;"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column :selectable="checkboxT" type="selection" width="55" />
          <el-table-column :show-overflow-tooltip="true" fixed="left" label="用户名" prop="username" />
          <el-table-column :show-overflow-tooltip="true" fixed="left" label="姓名" prop="nickName" />
          <el-table-column :show-overflow-tooltip="true" label="手机号" prop="phone" />
          <el-table-column :show-overflow-tooltip="true" label="邮箱" prop="email" />
          <el-table-column
            :show-overflow-tooltip="true"
            label="角色"
            prop="roles"
          >
            <template slot-scope="scope">
              {{
                scope.row.roles.map((itme) => {
                  return itme.name
                }).join(',')
              }}
            </template>
          </el-table-column>
          <el-table-column label="性别" prop="gender" />
          <el-table-column :show-overflow-tooltip="true" label="部门" prop="dept">
            <template slot-scope="scope">
              <div>{{ scope.row.dept ? scope.row.dept.name : '暂无部门' }}</div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="状态" prop="enabled">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.enabled"
                :disabled="user.id === scope.row.id"
                active-color="#409EFF"
                inactive-color="#F56C6C"
                @change="changeEnabled(scope.row, scope.row.enabled)"
              />
            </template>
          </el-table-column>
          <el-table-column :show-overflow-tooltip="true" label="创建日期" prop="createTime" width="135" />
          <el-table-column
            v-if="checkPer(['admin','user:edit','user:del'])"
            align="center"
            fixed="right"
            label="操作"
            width="240"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :disabled-dle="scope.row.id === user.id"
                :permission="permission"
              />
              <el-button
                v-permission=" ['admin', 'user:resetPasswd']"
                size="mini"
                type="success"
                @click="restPassword(scope.row)"
              >重置密码
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <pagination />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import crudUser from '@/api/system/user';
import { isvalidPhone } from '@/utils/validate';
import { getDepts, getDeptSuperior } from '@/api/system/dept';
import { getAll, getLevel } from '@/api/system/role';
import { getAllJob } from '@/api/system/job';
import CRUD, { crud, form, header, presenter } from '@crud/crud';
import rrOperation from '@crud/RR.operation';
import crudOperation from '@crud/CRUD.operation';
import udOperation from '@crud/UD.operation';
import pagination from '@crud/Pagination';
import DateRangePicker from '@/components/DateRangePicker';
import Treeselect, { LOAD_CHILDREN_OPTIONS } from '@riophae/vue-treeselect';
import { mapGetters } from 'vuex';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';

let userRoles = []
let userJobs = []
const defaultForm = {
  id: null,
  username: null,
  nickName: null,
  gender: '男',
  email: '',
  enabled: 'false',
  roles: [],
  jobs: [],
  dept: { id: null },
  phone: null
}
export default {
  name: 'User',
  components: { Treeselect, crudOperation, rrOperation, udOperation, pagination, DateRangePicker },
  cruds() {
    return CRUD({ title: '用户', url: 'api/users', crudMethod: { ...crudUser }})
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据字典
  dicts: ['user_status'],
  data() {
    // 自定义验证
    const validPhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入电话号码'))
      } else if (!isvalidPhone(value)) {
        callback(new Error('请输入正确的11位手机号码'))
      } else {
        callback()
      }
    }
    return {
      height: document.documentElement.clientHeight - 180 + 'px;',
      deptName: '', depts: [], deptDatas: [], jobs: [], level: 3, roles: [],
      jobDatas: [], roleDatas: [], // 多选时使用
      defaultProps: { children: 'children', label: 'name', isLeaf: 'leaf' },
      permission: {
        add: ['admin', 'user:add'],
        edit: ['admin', 'user:edit'],
        del: ['admin', 'user:del']
      },
      enabledTypeOptions: [
        { key: 'true', display_name: '激活' },
        { key: 'false', display_name: '锁定' }
      ],
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '请输入用户昵称', trigger: 'blur' },
          { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        phone: [
          { required: true, trigger: 'blur', validator: validPhone }
        ]
      }
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  watch: {
    // 'crud.data': {
    //   handler(val, oldVal) {
    //     val.map(item => {
    //       if (item.remarks) {
    //         item.remarks = JSON.parse(item.remarks)
    //       }
    //     })
    //   }
    // },
    // deep: true
  },
  created() {
    this.crud.msg.add = '新增成功，默认密码：tifJovAccomRals8'
    this.crud.optShow.download = false;
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 180 + 'px;'
    }
  },
  methods: {
    restPassword(row) {
      crudUser.resetPasswd(row).then(() => {
        this.$notify({
          title: '重置成功',
          type: 'success',
          duration: 2500
        });
      });
    },
    changeRole(value) {
      userRoles = []
      value.forEach(function(data, index) {
        const role = { id: data }
        userRoles.push(role)
      })
    },
    changeJob(value) {
      userJobs = []
      value.forEach(function(data, index) {
        const job = { id: data }
        userJobs.push(job)
      })
    },
    deleteTag(value) {
      userRoles.forEach(function(data, index) {
        if (data.id === value) {
          userRoles.splice(index, value)
        }
      })
    },
    // 新增与编辑前做的操作
    [CRUD.HOOK.afterToCU](crud, form) {
      this.getRoles()
      if (form.id == null) {
        this.getDepts()
      } else if (form.dept.id == null) {
        this.getDepts()
      } else {
        this.getSupDepts(form.dept.id)
      }
      this.getDepts()
      this.getRoleLevel()
      this.getJobs()
      form.enabled = form.enabled.toString()
      // form.unionId = form.unionId == null ? undefined : form.unionId
      // form.appId = form.appId == null ? undefined : form.appId
    },
    // 新增前将多选的值设置为空
    [CRUD.HOOK.beforeToAdd]() {
      this.jobDatas = []
      this.roleDatas = []
    },
    // 初始化编辑时候的角色与岗位
    [CRUD.HOOK.beforeToEdit](crud, form) {
      this.getJobs(this.form.dept.id)
      this.jobDatas = []
      this.roleDatas = []
      userRoles = []
      userJobs = []
      const _this = this
      form.roles.forEach(function(role, index) {
        _this.roleDatas.push(role.id)
        const rol = { id: role.id }
        userRoles.push(rol)
      })
      form.jobs.forEach(function(job, index) {
        _this.jobDatas.push(job.id)
        const data = { id: job.id }
        userJobs.push(data)
      })
    },
    // 提交前做的操作
    [CRUD.HOOK.afterValidateCU](crud) {
      if (!crud.form.dept.id) {
        this.$message({
          message: '部门不能为空',
          type: 'warning'
        })
        return false
      } else if (this.roleDatas.length === 0) {
        this.$message({
          message: '角色不能为空',
          type: 'warning'
        })
        return false
      }
      // else if (this.jobDatas.length === 0) {
      //   this.$message({
      //     message: '岗位不能为空',
      //     type: 'warning'
      //   })
      //   return false
      // }
      crud.form.roles = userRoles
      crud.form.jobs = userJobs
      return true
    },
    // 获取左侧部门数据
    getDeptDatas(node, resolve) {
      const sort = 'id,desc'
      const params = { sort: sort, enabled: 1 }
      if (typeof node !== 'object') {
        if (node) {
          params['name'] = node
        }
      } else if (node.level !== 0) {
        params['pid'] = node.data.id
      }
      setTimeout(() => {
        getDepts(params).then(res => {
          if (resolve) {
            resolve(res.content)
          } else {
            this.deptDatas = res.content
          }
        })
      }, 100)
    },
    getDepts() {
      getDepts({ enabled: true }).then(res => {
        this.depts = res.content.map(function(obj) {
          if (obj.hasChildren) {
            obj.children = null
          }
          return obj
        })
      })
    },
    getSupDepts(deptId) {
      getDeptSuperior(deptId).then(res => {
        const date = res.content
        this.buildDepts(date)
        this.depts = date
      })
    },
    buildDepts(depts) {
      depts.forEach(data => {
        if (data.children) {
          this.buildDepts(data.children)
        }
        if (data.hasChildren && !data.children) {
          data.children = null
        }
      })
    },
    // 获取弹窗内部门数据
    loadDepts({ action, parentNode, callback }) {
      if (action === LOAD_CHILDREN_OPTIONS) {
        getDepts({ enabled: true, pid: parentNode.id }).then(res => {
          parentNode.children = res.content.map(function(obj) {
            if (obj.hasChildren) {
              obj.children = null
            }
            return obj
          })
          setTimeout(() => {
            callback()
          }, 200)
        })
      }
    },
    // 切换部门
    handleNodeClick(data) {
      if (data.pid === 0) {
        this.query.deptId = null
      } else {
        this.query.deptId = data.id
      }
      this.crud.toQuery()
    },
    // 改变状态
    changeEnabled(data, val) {
      this.$confirm('此操作将 "' + this.dict.label.user_status[val] + '" ' + data.username + ', 是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        crudUser.edit(data).then(res => {
          this.crud.notify(this.dict.label.user_status[val] + '成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
        }).catch(() => {
          data.enabled = !data.enabled
        })
      }).catch(() => {
        data.enabled = !data.enabled
      })
    },
    // 获取弹窗内角色数据
    getRoles() {
      getAll().then(res => {
        this.roles = res
      }).catch(() => {
      })
    },
    // 获取弹窗内岗位数据
    getJobs() {
      getAllJob().then(res => {
        this.jobs = res.content
      }).catch(() => {
      })
    },
    // 获取权限级别
    getRoleLevel() {
      getLevel().then(res => {
        this.level = res.level
      }).catch(() => {
      })
    },
    checkboxT(row, rowIndex) {
      return row.id !== this.user.id
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
::v-deep .vue-treeselect__control, ::v-deep .vue-treeselect__placeholder, ::v-deep .vue-treeselect__single-value {
	height: 30px;
	line-height: 30px;
}
</style>
