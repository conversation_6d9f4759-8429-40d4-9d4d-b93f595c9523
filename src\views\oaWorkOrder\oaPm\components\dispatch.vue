<template>
  <div>
    <el-dialog
      :before-close="handleClose"
      :close-on-click-modal="false"
      :visible.sync="dispatchVisible"
      append-to-body
      title="选择流程"
      width="500px"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" size="small">
        <el-form-item label="流程类型" prop="classify">
          <el-select v-model="form.classify" placeholder="请选择分类">
            <el-option
              v-for="item in processClassification"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="流程" prop="id">
          <el-select v-model="form.id" :placeholder="form.classify ? '请选择流程' : '请先选择分类'">
            <el-option
              v-for="item in process"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="handleClose">取消</el-button>
        <el-button :loading="loading" type="primary" @click="onsubmit()">去派单</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import crudCategory from '@/api/system/category'
import { processList } from '@/api/oaWorkOrder/process';

export default {
  name: 'Dispatch',
  data() {
    return {
      dispatchVisible: false,
      loading: false,
      form: {
        classify: '',
        id: '',
        pmId: ''
      },
      rules: {
        classify: [
          { required: true, message: '请选择分类', trigger: ['blur', 'change'] }
        ],
        id: [
          { required: true, message: '请选择流程', trigger: ['blur', 'change'] }
        ]
      },
      processClassification: [],
      process: []
    }
  },
  watch: {
    'form.classify'(newValue, oldValue) {
      if (newValue) {
        this.getProcess(newValue)
      }
    }
  },

  methods: {
    init(data) {
      const { id, categoryId } = data;
      this.form.pmId = id;
      this.dispatchVisible = true;
      this.getClassifyList(categoryId);
    },
    handleClose() {
      this.dispatchVisible = false;
      this.$refs['form'].resetFields();
    },
    // 获取分类
    getClassifyList(categoryId) {
      crudCategory.getCategory({ enabled: 1, pid: categoryId }).then(response => {
        this.processClassification = response.content
      })
    },
    // 获取流程
    getProcess(classify) {
      if (classify) {
        processList({ classify, enabled: 1 }).then(res => {
          this.process = res.content || []
        })
      }
    },
    onsubmit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.$router.push({ name: 'ProcessForm', query: this.form });
          this.handleClose();
        }
      });
    }
  }
}
</script>
