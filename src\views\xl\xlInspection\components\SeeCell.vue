<template>
  <span class="see-cell">
    <template v-if="header.prop === 'title'">
      <span style="cursor: pointer;color: #2476F8" @click="goDetail(currentScope.row)">
        {{ currentScope.row[header.prop] }}
      </span>
    </template>
    <span v-else>{{ currentScope.row[header.prop] }}</span>
  </span>
</template>

<script>
export default {
  name: 'SeeCell',
  components: {},

  props: {
    header: {
      type: Object,
      default: () => {
        return {}
      }
    },
    currentScope: {
      type: Object,
      default: () => {
        return {}
      }
    },
    permission: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  computed: {},
  watch: {},
  created() {
  },
  methods: {
    // 去资产详情页
    goDetail(data) {
      this.$emit('toDetail', data)
    }
  }
}
</script>

<style currentScoped lang="scss" rel="stylesheet/scss">

</style>
