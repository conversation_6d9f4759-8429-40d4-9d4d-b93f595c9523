<template>
  <div>
    <!--周报-->
    <weekly-list v-if="isListFlag" ref="weeklyListRef" @sendData="handleData" @updateListFlag="handleListFlagUpdate" />
    <weekly-form
      v-else
      ref="weeklyFormRef"
      :current-time="currentTime"
      :weekly-data="weeklyData"
      @updateListFlag="handleListFlagUpdate"
    />
  </div>
</template>

<script>
import WeeklyList from '@/views/oaWorkOrder/oaProject/components/weeklyList/weeklyList.vue';
import WeeklyForm from '@/views/oaWorkOrder/oaProject/components/weeklyList/weeklyForm.vue';

export default {
  name: 'Weekly',
  components: { WeeklyList, WeeklyForm },
  data() {
    return {
      isListFlag: true,
      currentTime: {}, // 左侧的时间
      weeklyData: {} // 用于存储从 weekly-list 接收到的数据
    }
  },
  methods: {
    handleListFlagUpdate(newFlag) {
      this.isListFlag = newFlag;
    },
    handleData(data) {
      this.weeklyData = data.dateList;
      this.currentTime = data.currentTime
      this.isListFlag = false; // 切换到 weekly-form 组件
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
