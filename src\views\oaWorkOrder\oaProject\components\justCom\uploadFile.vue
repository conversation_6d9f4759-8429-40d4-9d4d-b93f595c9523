<template>
  <div v-loading.fullscreen.lock="fullscreenLoading" element-loading-text="正在上传中...">
    <el-dialog
      :before-close="resetModule"
      :close-on-click-modal="false"
      :visible.sync="visible"
      append-to-body
      title="上传文件"
      width="750px"
    >
      <el-form ref="form" :model="projectInfo" :rules="rules" inline label-width="130px" size="small">
        <el-form-item label="所属目录:">
          {{ projectInfo.name }}
          <!--<treeselect-->
          <!--  v-model="projectInfo.id"-->
          <!--  :options="allProject"-->
          <!--  placeholder="选择项目"-->
          <!--  style="width: 178px"-->
          <!--/>-->
        </el-form-item>
      </el-form>
      <div v-if="showFormData" class="text item">
        <fm-generate-form
          :ref="'generateForm'"
          :data="formStruct"
          :remote="remoteFunc"
          :value="formData"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="resetModule">取消</el-button>
        <el-button type="primary" @click="submit">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import { GenerateForm } from '@/components/VueFormMaking'
import 'form-making/dist/FormMaking.css'

Vue.component(GenerateForm.name, GenerateForm)
import { getToken } from '@/utils/auth'
import { getUser } from '@/api/system/user'
import extendBindTpl from '@/api/system/extendBindTpl'
import extendTpl from '@/api/system/extendTpl'
import oaDocument from '@/api/oaWorkOrder/oaDocument';
import oaPmTree from '@/api/oaWorkOrder/oaPmTree';
import { mapGetters } from 'vuex';
// import Treeselect from '@riophae/vue-treeselect';
// import '@riophae/vue-treeselect/dist/vue-treeselect.css';

export default {
  name: 'ProjectForm',
  // components: { Treeselect },
  data() {
    return {
      fullscreenLoading: false,
      rules: {
        // id: [
        //   { required: true, message: '请选择项目', trigger: 'blur' }
        // ]
      },
      processStructureValue: {},
      formStruct: {},
      formData: {},
      showFormData: false,
      remoteFunc: {
        funcGetToken(resolve) {
          resolve(getToken())
        },
        // 获取用户列表
        userList(resolve) {
          getUser().then(response => {
            const options = response.content
            resolve(options)
          })
        },
        // 对级联选择器数据进行处理
        handleData(option) {
          option.map(item => {
            item.value = item.name;
            if (item.hasChildren) {
              this.handleData(option.children);
            }
          });
        }
      },
      visible: false,
      projectInfo: {
        id: undefined
      },
      bindId: '',
      allProject: [],
      treeNode: ''

    }
  },
  computed: {
    ...mapGetters([
      'isAllUpload'
    ])
  },
  methods: {
    init(data, node) {
      if (node) {
        this.treeNode = this.getDirectoriesFromNode(node);
        // console.log(this.treeNode, '<===>', 'this.treeNode')
      }
      if (data) {
        // 项目上传
        this.bindId = this.$route.query.docId
        this.projectInfo = data;
      } else {
        // 文档上传
        this.bindId = this.$route.query.id
        this.projectInfo = { id: undefined }
      }
      // this.getAllProjectTree();
      this.visible = true;
      // this.getInitExtendTpl(51)
      this.getExtendTpl(this.bindId);
    },

    // 获取所有树节点
    getAllProjectTree() {
      oaPmTree.getAllPmTreeLists({ enabled: 1 }).then(res => {
        this.allProject = res.content
      })
    },
    // 获取模板信息
    getExtendTpl(id) {
      const data = { id, enabled: 1 }
      extendBindTpl.get(data).then(res => {
        if (res && res.content && res.content.length) {
          this.processStructureValue = res.content[0];
          const FormStruct = JSON.parse(this.processStructureValue.extendTpl.formStruct);
          // 设置自定义上传路径
          this.setCustomUploadPath(FormStruct);
          // 设置自定义上传路径
          this.formStruct = FormStruct;
        }
        this.showFormData = true;
      });
    },
    // 从树节点递归获取完整路径
    getDirectoriesFromNode(node) {
      if (!node) return [];

      // 存储路径，从当前节点向上递归
      const paths = [];

      // 递归函数，从当前节点向上查找父节点
      const findParentPath = (currentNode) => {
        // 如果当前节点为空或者没有 label，则停止递归
        if (!currentNode || !currentNode.label) {
          return;
        }

        // 将当前节点的 label 添加到路径开头
        paths.unshift(currentNode.label);

        // 如果有父节点，继续递归
        if (currentNode.parent) {
          findParentPath(currentNode.parent);
        }
      };

      // 开始递归查找
      findParentPath(node);

      return paths;
    },
    // 设置自定义上传路径
    setCustomUploadPath(formStruct) {
      // 获取项目名称和其他需要的信息
      const projectName = this.$route.query.name;

      console.log(projectName)
      // 递归处理表单结构，为所有上传组件添加自定义路径
      const processFormItems = (items) => {
        if (!items || !Array.isArray(items)) return;

        items.forEach(item => {
          // 处理 grid 类型
          if (item.type === 'grid' && item.columns) {
            item.columns.forEach(col => {
              if (col.list) {
                processFormItems(col.list);
              }
            });
          }

          // 处理 ossfile 类型
          if (item.type === 'ossfile') {
            // 确保 options 对象存在
            if (!item.options) {
              item.options = {};
            }

            // 设置自定义路径，格式为：项目名/目录/目录...
            // 例如：国门商务区科研配套集体职工宿舍7#/采购/设备/成交公告
            const directories = this.treeNode || [];

            // 方式一：设置完整的自定义路径
            item.options.customPath = `${projectName}/${directories.join('/')}`;

            // 方式二：分别设置项目名和目录结构
            // item.options.projectName = projectName;
            // item.options.directories = directories;
          }
        });
      };

      // 处理表单中的所有项
      if (formStruct.list) {
        processFormItems(formStruct.list);
      }
    },

    // 获取模版
    getInitExtendTpl(id) {
      extendTpl.get({ enabled: 1, id }).then(res => {
        if (res && res.content && res.content.length) {
          this.processStructureValue = res.content[0];
          console.log(this.processStructureValue, '<===>', 'this')
          const FormStruct = JSON.parse(this.processStructureValue.formStruct);
          this.formStruct = FormStruct;
        }
        this.showFormData = true;
      })
    },
    // 关闭弹框
    resetModule() {
      this.processStructureValue = {};
      this.formStruct = {};
      this.formData = {};
      this.showFormData = false;
      this.$refs.form.resetFields();
      this.visible = false;
    },
    // 提交
    submit() {
      this.$refs['form'].validate((valid) => {
        if (!valid) {
          return false;
        } else {
          this.checkModule().then(res => {
            if (!res.flag) {
              return false;
            } else {
              this.fullscreenLoading = true;
              const subData = res.subData;
              console.log(subData, '<===>', 'subData')
              oaDocument.add(subData).then(response => {
                this.resetModule();
                this.$emit('success', this.projectInfo, subData);
                this.$notify({
                  title: '操作成功',
                  type: 'success',
                  duration: 2500
                })
              }).catch((e) => {
                console.log(e);
              }).finally(() => {
                this.fullscreenLoading = false;
              })
            }
          })
        }
      })
    },
    async checkModule() {
      const subData = {
        bindId: this.bindId,
        formData: '',
        formStruct: JSON.stringify(this.formStruct),
        formBindToVar: true,
        relation: this.processStructureValue.relation,
        enabled: 1,
        pmId: this.projectInfo.id,
        itemName: this.$route.query.name
      };
      const p = await this.$refs['generateForm'].getData().then(values => {
        if (this.isAllUpload) {
          const fileList = [...values.file, ...values.folder]
          const allFilesUploaded = fileList.every(file => file.status === 'success');
          if (allFilesUploaded) {
            subData.formData = JSON.stringify(values);
            return {
              flag: true,
              subData
            };
          }
        } else {
          this.$notify({
            title: '有文件正在上传中，请稍后再试',
            type: 'warning',
            duration: 2500
          })
          return {
            flag: false
          };
        }
      }).catch(() => {
        return {
          flag: false
        };
      })
      return p;
    }
  }
}

</script>
