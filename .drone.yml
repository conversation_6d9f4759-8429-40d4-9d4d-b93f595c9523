# 生产
# drone 云部署
kind: pipeline
type: docker
name: om_platform_web
# drone构建步骤
steps:
  - name: restore-cache
    image: drillster/drone-volume-cache
    settings:
      restore: true
      mount:
        - ./.npm-cache
        - ./node_modules
    volumes:
      - name: cache
        path: /cache

  - name: npm-install
    image: node:16.20.2-alpine
    commands:
#      - npm config set unsafe-perm true
#      - npm config set sass_binary_site=https://npmmirror.com/mirrors/node-sass
      - npm config set cache ./.npm-cache --global
      - npm install --registry=https://registry.npmmirror.com --force

  - name: build-dist
    image: node:16.20.2-alpine
    commands:
      - npm run build:prod

  - name: rebuild-cache
    image: drillster/drone-volume-cache
    settings:
      rebuild: true
      mount:
        - ./.npm-cache  1
        - ./node_modules
    volumes:
      - name: cache
        path: /cache
    # 2.Docker 制作镜像，推送到私有仓库
  - name: docker build
    image: plugins/docker
    pull: if-not-exists
    volumes:
      - name: docker
        path: /var/run/docker.sock
    settings:
      username:
        from_secret: HARBOR_USER
      password:
        from_secret: HARBOR_PASSWD
      #    from_secret: dockerHub_password
      tags:
        - ${DRONE_TAG=latest}
      insecure: true
      repo: **************:10086/library/om_platform_web
      registry: **************:10086
      dockerfile: Dockerfile
  # 2.使用ssh访问主机运行最新版容器
  - name: docker deploy
    pull: if-not-exists
    image: appleboy/drone-ssh
    settings:
      host: **************
      username:
        from_secret: DEPLOY_USER
      password:
        from_secret: DEPLOY_PASSWD
      #    from_secret: ssh_password
      port: 22
      script:
        - echo =======暂停容器=======
        - sudo docker stop `sudo docker ps -a | grep om_platform_web | awk '{print $1}'`
        - echo =======暂停旧容器=======
        - sudo docker rm  `sudo docker ps -a | grep om_platform_web | awk '{print $1}'`
        - echo =======开始部署应用=======
        - sudo docker run -d -p 10017:80 --name om_platform_web --restart=always  **************:10086/library/om_platform_web:${DRONE_TAG=latest}
        - echo =======部署成功=======
  # 挂载的主机卷，可以映射到docker容器中
volumes:
  - name: cache
    host:
      path: /tmp/cache
  - name: docker
    host:
      path: /var/run/docker.sock
# drone执行触发器
trigger:
  event:
    - tag
