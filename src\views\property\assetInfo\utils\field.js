// 项目列表所有权限
export const permission = {
  add: ['admin', 'amBasicData:add'],
  edit: ['admin', 'amBasicData:edit'],
  del: ['admin', 'amBasicData:del'],
  updateT: ['admin', 'amBasicData:updateFormStruct'],
  updateR: ['admin', 'amBasicData:updateRelation'],
  importXlsWithRule: ['admin', 'amBasicData:importXlsWithRule'],
  addOrder: ['admin', 'amOrder:add']
  // updateFieldsByRule: ['admin', 'amBasicData:updateFieldsByRule'],
  // exportProject: ['admin', 'amBasicData:exportProject'],
  // previewProject: ['admin', 'amBasicData:previewProject'],
}

// 项目列表表头
export const tableHeader = [
  { label: '资产编号', prop: 'basicNo', fixed: 'left', align: 'center', width: 200 },
  { label: '项目名称', prop: 'pmName', align: 'center', width: 280 },
  { label: '库房名称', prop: 'depotName', align: 'center', width: 200 },
  { label: '设备名称', prop: 'deviceName', align: 'center', width: 200 },
  { label: '品牌名称', prop: 'brandName', width: 200, align: 'center' },
  { label: '型号', prop: 'modelName', width: 200, align: 'center' }
]

// 定义按钮组
export const updateButtonsLists = [
  // {
  //   id: '1',
  //   label: '导入',
  //   permission: permission.importXlsWithRule,
  //   fun: 'importProject',
  //   size: 'mini',
  //   className: [],
  //   icon: 'plus',
  //   type: 'primary'
  // },
  // {
  //   id: '6',
  //   label: '导出',
  //   permission: permission.exportProject,
  //   fun: 'exportProject',
  //   size: 'mini',
  //   className: [],
  //   icon: 'download',
  //   type: 'primary',
  //   query: {
  //     fileType: 'xls'
  //   }
  // },
  // {
  //   id: '7',
  //   label: '预览',
  //   permission: permission.previewProject,
  //   fun: 'exportProject',
  //   size: 'mini',
  //   className: [],
  //   icon: 'view',
  //   type: 'primary',
  //   query: {
  //     fileType: 'html'
  //   }
  // }
]

