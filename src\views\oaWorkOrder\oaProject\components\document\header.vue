<template>
  <div
    v-if="crud.props.searchToggle"
  >
    <el-input
      v-model="query.ft1"
      class="filter-item"
      clearable
      placeholder="输入文件名称"
      size="small"
      style="width: 200px;"
    />
    <date-range-picker v-model="query.createTime" class="date-item" />
    <rrOperation />
  </div>
</template>

<script>
import { header } from '@crud/crud';
import rrOperation from '@crud/RR.operation.vue';
import DateRangePicker from '@/components/DateRangePicker/index.vue';

export default {
  components: { rrOperation, DateRangePicker },
  mixins: [header()],
  props: {
    permission: {
      type: Object,
      required: true
    }
  }
}
</script>
