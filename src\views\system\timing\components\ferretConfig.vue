<template>
  <div class="">
    <el-form ref="form" v-loading="!isYamlReady" :model="formData" label-width="120px">
      <el-form-item label="分类ID">
        <el-input v-model="formData.params.category_id" placeholder="分类ID" />
      </el-form-item>
      <el-form-item label="绑定ID">
        <el-input v-model="formData.params.bind_id" placeholder="绑定ID" />
      </el-form-item>
      <el-form-item label="内容">
        <Yaml v-if="isYamlReady" ref="yaml" :height="height" :value="value" style="width: 100%;" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import Yaml from '@/components/YamlEdit/index'
import { delArrInvalid } from '@/views/system/timing/utils/formate'

export default {
  name: 'FerretConfig',
  components: { Yaml },
  data() {
    return {
      height: document.documentElement.clientHeight - 210 + 'px',
      value: null,
      formData: {
        text: null,
        params: {
          category_id: null,
          bind_id: null
        }
      },
      isYamlReady: false
    };
  },

  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 210 + 'px'
    }
    setTimeout(() => {
      this.isYamlReady = true;
    }, 2000);
  },

  methods: {
    getFormData() {
      this.formData.text = this.$refs.yaml.getValue()
      return {
        ferret_query: delArrInvalid(this.formData)
      };
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
