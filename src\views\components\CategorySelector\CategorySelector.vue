<template>
  <div class="category-selector">
    <div class="category-selector-main">
      <div class="category-group">
        <div v-for="category in categoryList" :key="category.id" class="category-item">
          <template v-if="isDateCategory(category)">
            <div class="date-category">
              <span class="date-title">
                {{ category.name }}
              </span>
              <el-date-picker
                v-model="dateRanges[category.id]"
                value-format="yyyy-MM-d"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                class="date-picker"
                @change="handleDateRangeChange(category)"
              />
            </div>
          </template>
          <template v-else>
            <el-checkbox-group v-model="localSelectedCategories" class="category-checkbox-group">
              <el-checkbox :label="category.id" @change="handleCategoryCheckboxChange(category)">
                <span :style="{ paddingLeft: '10px',color: (category.isOpen && category.children) ? '#1890ff' : '' }" @click.prevent="handleCategoryClick(category)">
                  {{ category.name }}
                  <template v-if="category.children && category.children.length">
                    <i :class="isCategorySelected(category) ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" />
                  </template>
                </span>
              </el-checkbox>
              <el-checkbox-group v-if="isCategorySelected(category)" v-model="localSelectedCategories" class="subcategory-checkbox-group">
                <el-checkbox
                  v-for="subCategory in category.children"
                  :key="subCategory.id"
                  :label="subCategory.id"
                >
                  <span :style="{ paddingLeft: '10px' }" @click.prevent="handleSubCategoryClick(subCategory)">
                    {{ subCategory.name }}
                  </span>
                </el-checkbox>
              </el-checkbox-group>
            </el-checkbox-group>
          </template>
        </div>
      </div>
      <div v-if="localSelectedCategories.length || hasSelectedDates" class="selected-tags">
        <el-button type="warning" round style="display: block;margin-bottom: 10px;" size="mini" @click="resetTag">重置</el-button>
        <el-tag
          v-for="item in selectedTags"
          :key="item.id"
          style="margin-right:8px;margin-bottom:6px;"
          :closable="true"
          @close="removeTag(item.id)"
        >
          <template v-if="item.dateRange">
            {{ item.name }}: {{ item.dateRange[0] }} 至 {{ item.dateRange[1] }}
          </template>
          <template v-else>
            {{ formatTag(item) }}
          </template>
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CategorySelector',
  props: {
    categoryList: {
      type: Array,
      required: true
    },
    categoryBaseInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      localSelectedCategories: [],
      selectedCategory: null,
      selectedTags: [],
      dateRanges: {},
      isResetting: false // 用于控制 watch 的标志位
    };
  },
  computed: {
    hasSelectedDates() {
      return Object.keys(this.dateRanges).length > 0;
    }
  },
  watch: {
    localSelectedCategories: {
      handler(newVal) {
        // console.log('复选框值变化了')
        this.updateSelectedTagsAndEmit();
      },
      deep: true
    },
    dateRanges: {
      handler(newVal) {
        // this.updateSelectedTagsAndEmit();
      },
      deep: true
    }
  },
  created() {
    this.initializeCategoryOpenState(this.categoryList);
  },
  methods: {
    /** 初始化isOpen是否打开所有父级标签 */
    initializeCategoryOpenState(categories) {
      categories.forEach(category => {
        this.$set(category, 'isOpen', true);
        if (category.children && category.children.length) {
          this.initializeCategoryOpenState(category.children);
        }
      });
    },
    /** 点击一级菜单，展开合并，没有子集的直接触发复选框 */
    handleCategoryClick(category) {
      if (category.children && category.children.length) {
        category.isOpen = !category.isOpen;
      } else {
        // 当没有子集时，触发复选框选择
        this.toggleCategorySelection(category);
      }
    },
    /** 选择二级文字的时候也触发复选框的处理并且处理父级的全选和全不选 */
    handleSubCategoryClick(subCategory) {
      this.toggleCategorySelection(subCategory);
      this.updateParentCheckboxState(subCategory);
    },
    /** 根据二级的同一级的选中情况，处理一级的全选或者全不选的状态 */
    updateParentCheckboxState(subCategory) {
      const parentCategory = this.findParentCategory(this.categoryList, subCategory);
      if (parentCategory) {
        const allChildrenSelected = parentCategory.children.every(child => this.localSelectedCategories.includes(child.id));
        const parentIndex = this.localSelectedCategories.indexOf(parentCategory.id);
        if (allChildrenSelected) {
          if (parentIndex === -1) {
            this.localSelectedCategories.push(parentCategory.id);
          }
        } else {
          if (parentIndex !== -1) {
            this.localSelectedCategories.splice(parentIndex, 1);
          }
        }
      }
    },
    findParentCategory(categories, targetCategory) {
      for (const category of categories) {
        if (category.children && category.children.some(child => String(child.id) === String(targetCategory.id))) {
          return category;
        }
        if (category.children) {
          const result = this.findParentCategory(category.children, targetCategory);
          if (result) {
            return result;
          }
        }
      }
      return null;
    },
    /** 点击复选框，把选项存入localSelectedCategories */
    toggleCategorySelection(category) {
      const index = this.localSelectedCategories.indexOf(category.id);
      if (index === -1) {
        this.localSelectedCategories.push(category.id);
      } else {
        this.localSelectedCategories.splice(index, 1);
      }
    },
    /** 点击一级菜单的复选框，触发是全选和全不选 */
    handleCategoryCheckboxChange(category) {
      if (this.localSelectedCategories.includes(category.id)) {
        this.selectAllChildren(category);
      } else {
        this.deselectAllChildren(category);
      }
    },
    /** 全选 */
    selectAllChildren(category) {
      if (category.children && category.children.length) {
        category.children.forEach(child => {
          if (!this.localSelectedCategories.includes(child.id)) {
            this.localSelectedCategories.push(child.id);
          }
          this.selectAllChildren(child);
        });
      }
    },
    /** 全不选 */
    deselectAllChildren(category) {
      if (category.children && category.children.length) {
        category.children.forEach(child => {
          const index = this.localSelectedCategories.indexOf(child.id);
          if (index !== -1) {
            this.localSelectedCategories.splice(index, 1);
          }
          this.deselectAllChildren(child);
        });
      }
    },
    isCategorySelected(category) {
      return category.isOpen;
    },
    /** 处理返回数据 */
    getLeafSelectedCategories(categories) {
      const leafSelectedCategories = [];
      const leafSelectedObjects = [];
      const leafSelectedNames = [];
      const categoryMap = this.buildCategoryMap(this.categoryList);

      const findTopCategory = (category) => {
        let topCategory = category;
        while (topCategory.parentId && categoryMap[topCategory.parentId]) {
          topCategory = categoryMap[topCategory.parentId];
        }
        return topCategory;
      };

      const isLeafSelected = (category) => {
        if (!category.children || category.children.length === 0) {
          return categories.includes(category.id);
        }
        for (const child of category.children) {
          if (categories.includes(child.id)) {
            return false;
          }
        }
        return categories.includes(category.id);
      };

      categories.forEach(categoryId => {
        const category = categoryMap[categoryId];
        if (category && isLeafSelected(category)) {
          const topCategory = findTopCategory(category);
          leafSelectedCategories.push(categoryId);
          leafSelectedNames.push(category.name);
          leafSelectedObjects.push({
            ...category,
            topCategoryId: topCategory.id,
            topName: topCategory.name
          });
        }
      });

      return [leafSelectedCategories, leafSelectedNames, leafSelectedObjects];
    },
    buildCategoryMap(categories) {
      const categoryMap = {};
      const traverse = (categoryList, parentId = null) => {
        categoryList.forEach(category => {
          categoryMap[category.id] = { ...category, parentId };
          if (category.children) {
            traverse(category.children, category.id);
          }
        });
      };
      traverse(categories);
      return categoryMap;
    },
    /** 删除标签 */
    removeTag(tagId) {
      // 删除 localSelectedCategories 中的标签
      this.localSelectedCategories = this.localSelectedCategories.filter(id => String(id) !== String(tagId));

      // 删除 dateRanges 中的标签
      if (this.dateRanges.hasOwnProperty(tagId)) {
        this.$delete(this.dateRanges, String(tagId));
      }
    },
    /** 清空选中项 */
    resetLocalSelectedCategories() {
      this.isResetting = true;
      this.localSelectedCategories = [];
      this.dateRanges = {};
      this.$emit('update:selectedCategories', [], [], true);
    },
    handleDateRangeChange(category) {
      this.$nextTick(() => {
        if (!this.dateRanges[category.id] || !this.dateRanges[category.id].length) {
          this.removeTag(category.id);
        }
        this.updateSelectedTagsAndEmit();
      });
    },
    /** 标签改变时候触发 */
    updateSelectedTagsAndEmit() {
      if (this.isResetting) {
        this.isResetting = false;
        return;
      }
      // eslint-disable-next-line no-unused-vars
      const [selectedIds, selectedNames, selectedObjects] = this.getLeafSelectedCategories(this.localSelectedCategories);

      const dateSelectedObjects = Object.entries(this.dateRanges).map(([id, range]) => {
        const dateRangesCategory = this.getCategoryById(id);
        return {
          id,
          topName: dateRangesCategory?.name,
          name: dateRangesCategory?.name,
          dateRange: range,
          extend: dateRangesCategory
        };
      });
      const dateSelectedNames = Object.entries(this.dateRanges).map(([id, range]) => (`${this.getCategoryById(id)?.name}:${range}`));

      this.selectedTags = [...selectedObjects, ...dateSelectedObjects];
      const newSelectNames = [...selectedNames, ...dateSelectedNames];
      // console.log(newSelectNames, this.selectedTags)
      this.$emit('update:selectedCategories', newSelectNames, this.selectedTags, this.isResetting);
    },
    /** 根据id获取整条数据 */
    getCategoryById(id) {
      return this.categoryList.find(cat => String(cat.id) === String(id));
    },
    /** 是否是日期选择器 */
    isDateCategory(category) {
      try {
        const formData = JSON.parse(category.formData);
        return formData.isDate === '是';
      } catch (error) {
        return false;
      }
    },
    formatTag(item) {
      if (item.name == '/') {
        return `${item.topName} | ${item.name}`
      } else {
        return item.name;
      }
    },
    resetTag() {
      this.localSelectedCategories = [];
      this.dateRanges = {};
    }
  }
};
</script>

<style scoped lang="scss">
.category-selector {
  padding: 10px 0 15px 0;
}
.category-selector-main {
  display: flex;
}

.category-group {
  padding-right: 10px;
  border-right: 1px solid #e8e8e8;
}
.subcategory-group {
  margin-top: 10px;

}
.subcategory-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  padding-left: 25px;
}
::v-deep .category-checkbox {
  margin-bottom: 8px !important;
}
.selected-tags {
  padding-left: 10px;
}
.category-group,.selected-tags{
  width: 50%;
  overflow-y: auto;
  height: calc(100vh - 80px);
}
::v-deep .category-checkbox-group .el-checkbox__label,
::v-deep .category-checkbox-group2 .el-checkbox__label {
  padding-left: 0 !important;
}
::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #606266;
}
.category-item {
  margin-bottom: 8px;
}
.date-category {
  display: flex;
  /* align-items: center; */
  .date-title{
    color: #606266;
    font-weight: 500;
    line-height: 32px;
    margin-right: 15px;
  }
}

</style>
