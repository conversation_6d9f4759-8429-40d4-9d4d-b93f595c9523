import store from '@/store'
import spiderReadAttention from '@/api/spider/spiderReadAttention';
import router from '@/router/routers'
import spiderArticleApi from '@/api/spider/spiderArticle';

/**
 * @description: 格式化表格数据
 * @param val 表格数据数组
 * @returns {*|*[]|*[]}
 */
export function formatterTableData(val) {
  let tableData = [];
  if (!val && !val.length) {
    return tableData
  }
  const mapItem = (item) => {
    const label = item.label ? JSON.parse(item.label) : []
    const newLabel = label.map((subItem, index) => `${subItem.label}${index !== label.length - 1 ? '/' : ''}`).join('');
    return {
      ...item,
      newLabel,
      projectNameR: item.projectName
    }
  };
  tableData = val.map(mapItem);
  return tableData
}

/**
 * @description: 格式化表格头部
 * @param val
 * @returns {[{prop: string, width: number, fixed: string, label: string, align: string},{headerAlign: string, prop: string, width: number, fixed: string, label: string, align: string},{headerAlign: string, prop: string, width: number, label: string, align: string},{headerAlign: string, prop: string, width: number, label: string, align: string},{headerAlign: string, prop: string, width: number, label: string, align: string},null,null,null,...*[]]}
 */
export function formatterTableHeader(val) {
  let tableHeader = [
    { prop: 'projectNo', label: '项目编号', align: 'center', fixed: 'left', width: 200 },
    { prop: 'projectName', label: '项目名称', align: 'left', headerAlign: 'left', fixed: 'left', width: 200 },
    { prop: 'purchasingUnit', label: '采购单位', align: 'left', headerAlign: 'left', width: 150 },
    { prop: 'type', label: '公告类型', align: 'left', headerAlign: 'left', width: 100 },
    { prop: 'date', label: '公告日期', align: 'left', headerAlign: 'left', width: 100 },
    { prop: 'amount', label: '金额(万元)', align: 'right', headerAlign: 'center', width: 150 },
    { prop: 'biddingUnit', label: '中标单位', align: 'left', headerAlign: 'left', width: 150 },
    { prop: 'newLabel', label: '标签', align: 'left', headerAlign: 'left', width: 200 }
  ]
  const otherHeader = [];
  tableHeader = [...tableHeader, ...otherHeader];
  return tableHeader
}

export function formatterIntentTableHeader(val) {
  let tableHeader = [
    // { prop: 'projectNo', label: '项目编号', align: 'center', fixed: 'left', width: 200 },
    { prop: 'projectName', label: '项目名称', align: 'left', headerAlign: 'left', fixed: 'left', width: 200 },
    { prop: 'purchasingUnit', label: '采购单位', align: 'left', headerAlign: 'left', width: 150 },
    { prop: 'type', label: '公告类型', align: 'left', headerAlign: 'left', width: 100 },
    { prop: 'date', label: '公告日期', align: 'left', headerAlign: 'left', width: 100 },
    { prop: 'amount', label: '金额(万元)', align: 'right', headerAlign: 'center', width: 150 },
    { prop: 'biddingUnit', label: '中标单位', align: 'left', headerAlign: 'left', width: 150 },
    { prop: 'newLabel', label: '标签', align: 'left', headerAlign: 'left', width: 200 }
  ]
  const otherHeader = [];
  tableHeader = [...tableHeader, ...otherHeader];
  return tableHeader
}

export function formatterRelevTableHeader(val) {
  let tableHeader = [
    { prop: 'projectNo', label: '项目编号', align: 'center', fixed: 'left' },
    { prop: 'projectName', label: '项目名称', align: 'left', headerAlign: 'left', fixed: 'left' },
    { prop: 'projectNameR', label: '意向项目名称', align: 'left', headerAlign: 'left', fixed: 'left' },
    { prop: 'purchasingUnit', label: '采购单位', align: 'left', headerAlign: 'left' }

  ]
  const otherHeader = [];
  tableHeader = [...tableHeader, ...otherHeader];
  return tableHeader
}

/**
 * @description: 关注和取消关注参数
 * @type {{fv2: *, enabled: number, status: string}}
 */
const user = store.getters.user;
const statusMessage = {
  unAttention: '关注成功',
  attention: '取关成功'
}

/**
 * @description: 关注/取关
 * @param row 当前行的数据
 */
export async function follow(row, otherQuery, callback) {
  let followQuery = {
    fv1: row.projectName,
    fv2: user.username
  }
  if (otherQuery) {
    followQuery = {
      ...followQuery,
      ...otherQuery
    }
  }
  await spiderReadAttention.cancel(followQuery)
  if (typeof callback === 'function') {
    callback(statusMessage[row.status]);
  }
}

function setAndNavigate(data, destination) {
  sessionStorage.setItem('projectInfo', JSON.stringify(data));
  store.dispatch('currentProject/setCurrentProject', data);
  router.push(destination);
}

/**
 * 详情
 * @param data
 * @param detailType
 */
export function toDetail(data, detailType) {
  setAndNavigate(data, { name: 'SpiderArticleDetail', query: { detailType }});
}

/**
 * @description: 确认信息
 * @param data
 */
export function sureInfo(data) {
  setAndNavigate(data, { name: 'ConfirmInfo' });
}

/**
 * @description: 关联信息
 * @param data
 */
export function toRelevance(data) {
  setAndNavigate(data, { name: 'Relevancy' });
}

export function publicChangeHandler({ prop, order }, obj) {
  const sortTransMap = {
    ascending: 'asc',
    descending: 'desc'
  };
  if (!order || obj[prop] === order) {
    obj[prop] = null;
  } else {
    obj[prop] = order;
  }
  const newArr = []
  Object.entries(obj).forEach(([key, value]) => {
    if (value !== null) {
      newArr.push(`${key},${sortTransMap[value]}`)
    }
  });
  return newArr
}

/**
 * 获取项目请求、信息确认的基础信息
 */

export async function getBaseProject(info, otherQuery) {
  const data = {
    fv1: info.fv1,
    ...otherQuery
  }
  const res = await spiderArticleApi.getSpiderArticleBase(data);
  return res;
}

