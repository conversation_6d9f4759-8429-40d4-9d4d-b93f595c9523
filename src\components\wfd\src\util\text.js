/**
 * 计算字符串的长度
 * @param {string} str 指定的字符串
 * @return {number} 字符串长度
 */
export const calcStrLen = str => {
  let len = 0
  for (let i = 0; i < str.length; i++) {
    if (str.charCodeAt(i) > 0 && str.charCodeAt(i) < 128) {
      len++
    } else {
      len += 2
    }
  }
  return len
}

/**
 * 计算显示的字符串
 * @param {string} str 要裁剪的字符串
 * @param {number} maxWidth 最大宽度
 * @param {number} fontSize 字体大小
 * @return {string} 显示的字符串
 */
export const fittingString = (str, maxWidth, fontSize) => {
  const fontWidth = fontSize * 1.3 // 字号+边距
  maxWidth = maxWidth * 2 // 需要根据自己项目调整
  const width = calcStrLen(str) * fontWidth
  const ellipsis = '…'
  if (width > maxWidth) {
    const actualLen = Math.floor((maxWidth - 10) / fontWidth)
    const result = str.substring(0, actualLen) + ellipsis
    return result
  }
  return str
}
