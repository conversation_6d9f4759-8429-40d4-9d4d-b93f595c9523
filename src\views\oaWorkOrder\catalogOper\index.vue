<template>
  <div class="app-container">
    <div class="head-container">
      <!--<e-header :permission="permission" />-->
      <crudOperation :permission="permission" />
    </div>
    <!-- 表格 -->
    <div class="body-box">
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="tableData"
        style="width: 100%;"
        @select="crud.selectChange"
        @select-all="crud.selectAllChange"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column
          v-for="(item) in tableHeader"
          :key="item.prop"
          :align="item.align"
          :fixed="item.fixed || false"
          :label="item.label"
          :min-width="item.minWidth"
          :prop="item.prop"
          :width="item.width"
        >
          <template slot-scope="scope">
            <template v-if="item.prop == 'filePre'">
              <file-thumb
                :file-ext="scope.row.fileExt"
                :preview-list="[scope.row.url]"
                :url="scope.row.thUrl"
                @preView="preView"
              />
            </template>
            <!--<template v-else-if="item.prop == 'status'">-->
            <!--  <el-tag :type="scope.row[item.prop] == '1' ? 'success' : 'danger'">-->
            <!--    {{ scope.row[item.prop] == '1' ? '是' : '否' }}-->
            <!--  </el-tag>-->
            <!--</template>-->
            <span v-else>{{ scope.row[item.prop] }}</span>
          </template>
        </el-table-column>

        <el-table-column
          fixed="right"
          label="操作"
          width="240"
        >
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
              msg="确定删除吗,此操作不能撤销！"
            />
            <el-button
              size="mini"
              type="success"
              @click="handleEdit(scope.row)"
            >执行
            </el-button>
          </template>
        </el-table-column>

      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
// import eHeader from './module/header';
import CRUD, { form, presenter } from '@crud/crud';
import crudOperation from '@crud/CRUD.operation';
import pagination from '@crud/Pagination';
import oaPmCatalogRelation from '@/api/oaWorkOrder/oaPmCatalogRelation';
import {
  formatterTableHeader,
  permission,
  formatterTableData
} from '@/views/oaWorkOrder/catalogOper/utils/catalogOper';

import udOperation from '@crud/UD.operation.vue';

const defaultForm = { id: null }
export default {
  name: 'Catalogue',
  components: { udOperation, crudOperation, pagination },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: true,
      title: '目录操作记录',
      url: 'api/oaPmCatalogRelation/small',
      sort: [],
      query: { enabled: 1, sort: 'createTime,desc' },
      crudMethod: { ...oaPmCatalogRelation },
      optShow: {
        add: true,
        edit: false,
        del: false,
        download: false,
        reset: true,
        rightGroup: false
      },
      page: {
        // 页码
        page: 0,
        // 每页数据条数
        size: 20,
        // 总数据条数
        total: 0
      }
    })
  },

  mixins: [presenter(), form(defaultForm)],
  data() {
    return {
      permission,
      tableData: [],
      tableHeader: [],
      websock: ''
    };
  },
  computed: {
    setOperateShow() {
      return this.$setArrPermission(['edit', 'del'], this.permission)
    }
  },
  watch: {
    'crud.data': {
      handler(val) {
        this.tableData = formatterTableData(val)
        this.tableHeader = formatterTableHeader(val)
      },
      deep: true
    }
  },
  created() {
  },
  methods: {
    [CRUD.HOOK.beforeToAdd]() {
      this.$router.push({ name: 'CatalogueOperDetail' })
    },
    [CRUD.HOOK.beforeToEdit](crud, form) {
      this.$router.push({ name: 'CatalogueOperDetail', query: { id: form.id }})
    },
    handleEdit(data) {
      oaPmCatalogRelation.modifiedCatalog({ relationId: data.id }).then(res => {
        this.initWebSocket()
      })
    },
    initWebSocket() {
      const wsUri = process.env.VUE_APP_WS_API + '/webSocket/modifiedCatalog'
      this.websock = new WebSocket(wsUri)
      this.websock.onerror = this.webSocketOnError
      this.websock.onopen = this.websocketonopen;
      this.websock.onmessage = this.webSocketOnMessage
      // console.log(this.websock.readyState);
    },
    webSocketOnError(e) {
      this.$notify({
        title: 'WebSocket连接发生错误',
        type: 'error',
        duration: 5500
      })
    },
    websocketonopen(e) {
      // console.log(e, '连接成功');
      // console.log(this.websock.readyState);
    },
    webSocketOnMessage(e) {
      if (e == 'ping') {
        return
      }
      const data = JSON.parse(e.data)
      if (data.msgType === 'INFO') {
        this.$notify({
          title: '',
          message: data.msg,
          type: 'success',
          dangerouslyUseHTMLString: true,
          duration: 5500
        })
      } else if (data.msgType === 'ERROR') {
        this.$notify({
          title: '',
          message: data.msg,
          dangerouslyUseHTMLString: true,
          type: 'error',
          duration: 5500
        })
      }
      // setTimeout(() => {
      //   this.crud.refresh()
      // }, 200)
    },
    webSocketSend(agentData) {
      this.websock.send(agentData)
    }
  }
};
</script>

<style lang="scss" rel="stylesheet/scss" scoped>

</style>
