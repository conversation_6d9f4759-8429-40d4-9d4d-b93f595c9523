<!-- MultiTagSelect.vue -->
<template>
  <div>
    <CategorySelector
      v-for="(category, index) in categories"
      :key="index"
      :ref="'categorySelector_' + index"
      :category-base-info="category.categoryBaseInfo"
      :category-list="category.categoryList"
      @update:selectedCategories="handleSelectedCategories(index, ...arguments)"
    />
  </div>
</template>

<script>
import CategorySelector from './CategorySelector.vue';

export default {
  name: 'MultiCategorySelector',
  components: { CategorySelector },
  props: {
    categories: {
      type: Array,
      required: true
    }
  },
  methods: {
    handleSelectedCategories(index, selectedIds, selectedObjects, isFromClear) {
      this.$emit('update:selectedCategories', index, selectedIds, selectedObjects, isFromClear);
    },
    resetLocalSelectedCategories(index) {
      const refName = 'categorySelector_' + index;
      const categorySelector = this.$refs[refName][0];
      if (categorySelector && typeof categorySelector.resetLocalSelectedCategories === 'function') {
        categorySelector.resetLocalSelectedCategories();
      } else {
        console.error(`Component with ref ${refName} does not have a method resetLocalSelectedCategories`);
      }
    },
    resetAllCategories() {
      for (let i = 0; i < this.categories.length; i++) {
        this.resetLocalSelectedCategories(i);
      }
    }
  }
};
</script>

<style scoped>
/* Add any additional styles if needed */
</style>
