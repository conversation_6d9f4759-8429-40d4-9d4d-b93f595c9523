<template>
  <div class="search-box">
    <el-input v-model="query.title" clearable size="small" placeholder="输入路口名称" style="width: 200px;" class="filter-item" />
    <span style="padding-left:10px;padding-right:10px;">
      <el-button class="filter-item" size="small" type="success" icon="el-icon-search" @click="searchMap">搜索</el-button>
    </span>
    <el-select
      v-model="query.category"
      clearable
      size="small"
      placeholder="请选择部件"
      class="filter-item"
      style="width: 200px"
      @change="searchMap"
    >
      <el-option
        v-for="item in assetsList"
        :key="item.id"
        :label="item.label"
        :value="item.id"
      />
    </el-select>
  </div>
</template>
<script>
/* eslint-disable */
import { getOmAssetSmall } from '@/api/parts/assets';
import crudCategory from '@/api/system/category';
import { getConfig } from '@/utils/getConfigData';
import MultiMarker from '../SupportingFile/MultiMarker';
// import ClusterBubble from '../SupportingFile/DOMOverlay';
export default {
  name: 'markerCluster',
  props: {
    map: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      markerLayer: null,
      infoWindow: null,
      markerCluster:null,
      query: {
        title: '',
        category: ''
      },
      markList: [],
      assetsList: []
    };
  },
  watch: {
    map(val, old) {
      this.initData();
    }
  },
  created() {

  },
  mounted() {
    this.getParts();
  },
  beforeDestroy() {
    this.markList = [];
  },
  methods: {
    initData() {
      // 初始化marker
      this.setMaker();
      // 信息弹窗
      this.infoWin();
    },
    getParts() {
      const data = { key: 'Asset_class' }
      getConfig(data).then(res => {
        const pid = res.extend.data.categoryId
        const bindId = res.extend.data.bindId
        crudCategory.getCategory({ pid, bindId }).then(resCategory => {
          this.assetsList = resCategory.content;
          this.query.category = this.assetsList[0].id;
        })
      })
    },
    async setMaker() {
      let markList = await this.getSmallMap();
      //this.markerLayer = new TMap.MultiMarker(MultiMarker(this, this.markList));
      this.clusterMarker(markList);
      
      // this.markerLayer.setGeometries(this.markList);
      // 监听marker点击事件
      //this.markerLayer.on('click', this.clickHandler);
    },
    /** 点击点位打开信息窗 */
    clickHandler(evt) {
      var content = `<div style="padding:3px;">${evt.geometry.properties.title}</div>`;
      this.infoWindow.open(); // 打开信息窗
      this.infoWindow.setPosition(evt.geometry.position); // 设置信息窗位置
      this.infoWindow.setContent(content); // 设置信息窗内容
    },
    /** 创建信息窗口 */
    infoWin() {
      this.infoWindow = new TMap.InfoWindow({
        map: this.map,
        position: new TMap.LatLng(0, 0),
        offset: { x: -12, y: -32 } // 设置信息窗相对position偏移像素，为了使其显示在Marker的上方
      });
      this.infoWindow.close(); // 初始关闭信息窗关闭
    },
    /** 获取点位列表 */
    async getSmallMap(resolve) {
      return await getOmAssetSmall({
        title: this.query.title,
        categoryId: this.query.category
      }).then(res => {
        const arr = res.content;
        var zoom = this.map.getZoom().toFixed(0) < 13 ? '1' : '';
        return arr.map(item => {
          let fv2 = '';
          let fv3 = '';
          if (item.fv2 && item.fv2 !== 'null' && item.fv2 !== 'undefined') {
            fv2 = item.fv2;
          }
          if (item.fv3 && item.fv3 !== 'null' && item.fv3 !== 'undefined') {
            fv3 = item.fv3;
          }
          let str=this.styleIcon(item.status || '', this.query.category, zoom, (item.inspect && item.inspect.fv2) ? item.inspect.fv2 : '');
          return {
            id: item.id,
            position: new TMap.LatLng(fv3, fv2),
            properties: {
              title: item.title,
              type: this.query.category,
              backstageId: item.id,
              styleId: str,
            },
            styleId:  str,
          }
        });
      }).catch(e => { console.log(e) })
    },
    async searchMap() {
      await this.getSmallMap();
      this.markerLayer.setGeometries(this.markList);
    },
    /** marker点位的icon */
    styleIcon(state, type, zoom, level) {
      var str = '';
      var z = zoom;
      if (type === 63) {
        // 路口
        str = (state === '正常' ? 'iconGreen' : state !== '正常' ? this.styleIddang(level, type) : 'iconYellow') + z;
      } else if (type === 64) {
        // 摄像头
        str = (state === '正常' ? 'cameraB' : state !== '正常' ? this.styleIddang(level, type) : 'cameraH') + z;
      } else if (type === 65) {
        // 监控杆
        str = (state === '正常' ? 'booksB' : state !== '正常' ? this.styleIddang(level, type) : 'booksH') + z;
      }
      return str;
    },
    /** 故障等级的图标id */
    styleIddang(level, type) {
      var str = '';
      if (type === 63) {
        str = level === '极高' ? 'errorjigao' : level === '高' ? 'errorgao' : level === '中' ? 'errorzhong' : 'iconRed';
      } else if (type === 64) {
        str = level === '极高' ? 'cameraRiga' : level === '高' ? 'cameraGao' : level === '中' ? 'cameraRhona' : 'cameraR';
      } else if (type === 65) {
        str = level === '极高' ? 'booksJIgao' : level === '高' ? 'booksGao' : level === '中' ? 'booksZhong' : 'booksR';
      }
      return str;
    },
    /** 点聚合 */
    clusterMarker(markerList){
      class ClusterBubble extends TMap.DOMOverlay {
        constructor(options) {
          super(options)
          this.enableClick = true
        }

        onInit(options) {
          this.content = options.content
          this.position = options.position
        }

        // 销毁时需要删除监听器
        onDestroy() {
          this.dom.removeEventListener('click', this.onClick)
          this.removeAllListeners()
        }

        // 点击事件
        onClick(e) {
          this.emit('click', e)
        }

        // 创建气泡DOM元素
        createDOM() {
          var dom = document.createElement('div')
          // 设置DOM样式
          dom.classList.add('clusterBubble');
          dom.innerText = this.content;
          dom.style.cssText = [
            'width: ' + (40 + parseInt(this.content) * 2) + 'px;',
            'height: ' + (40 + parseInt(this.content) * 2) + 'px;',
            'line-height: ' + (40 + parseInt(this.content) * 2) + 'px;',
          ].join(' ');

          // 监听点击事件，实现zoomOnClick
          this.onClick = this.onClick.bind(this)
          // pc端注册click事件，移动端注册touchend事件
          dom.addEventListener('click', this.onClick)
          return dom
        }

        // 更新
        updateDOM() {
          if (!this.map) {
            return
          }
          // 经纬度坐标转容器像素坐标
          const pixel = this.map.projectToContainer(this.position)

          // 使文本框中心点对齐经纬度坐标点
          const left = pixel.getX() - this.dom.clientWidth / 2 + 'px'
          const top = pixel.getY() - this.dom.clientHeight / 2 + 'px'
          this.dom.style.transform = `translate(${left}, ${top})`

          this.emit('dom_updated')
        }
      }
      let options={
        id: 'marker-cluster', // 图层id
        map: this.map,
        enableDefaultStyle: false, // 关闭默认样式
        minimumClusterSize: 3,
        zoomOnClick: true,
        gridSize: 60,
        averageCenter: false,
        geometries:markerList
      }
      this.markerCluster = new TMap.MarkerCluster(options);
      var clusterBubbleList = [];
      var markerGeometries = [];
      var marker = null;
      let _this=this;
      // 监听聚合簇变化
      this.markerCluster.on('cluster_changed', function (e) {
        // 销毁旧聚合簇生成的覆盖物
        if (clusterBubbleList.length) {
          clusterBubbleList.forEach(function (item) {
            item.destroy();
          })
          clusterBubbleList = [];
        }
        markerGeometries = [];

        // 根据新的聚合簇数组生成新的覆盖物和点标记图层
        var clusters = _this.markerCluster.getClusters();
        clusters.forEach(function (item) {
          if (item.geometries.length > 1) {
            let clusterBubble = new ClusterBubble({
              map:_this.map,
              position: item.center,
              content: item.geometries.length,
            });
            clusterBubble.on('click', () => {
              _this.map.fitBounds(item.bounds);
            });
            clusterBubbleList.push(clusterBubble);
          } else {
            markerGeometries.push({
              position: item.center
            });
            // let data=item.geometries[0];
            // // console.log(data,11,item)
            // markerGeometries.push({
            //   position:item.center,
            //   styleId:data.styleId
            // });
          }
        });
        if (marker) {
          console.log(markerGeometries,908)
        // 已创建过点标记图层，直接更新数据
          marker.setGeometries(markerGeometries);
        } else {
          // 创建点标记图层
          console.log(markerGeometries,909)
          marker = new TMap.MultiMarker(MultiMarker(this, markerGeometries));
          // marker = new TMap.MultiMarker({
          //   map:_this.map,
          //   styles: {
          //     default: new TMap.MarkerStyle({
          //       'width': 34,
          //       'height': 42,
          //       'anchor': {
          //         x: 17,
          //         y: 21,
          //       },
          //       'src': 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/marker_blue.png',
          //     }),
          //   },
          //   geometries: markerGeometries
          // });
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.search-box{
  position: absolute;
  width:100%;
  left:20px;
  top:20px;
  z-index: 2000;
  padding: 10px 0 0 10px;
}
</style>
