import globalConfig from '@/api/system/globalConfig'

export function getConfig({ enabled = 1, type = 'NORMAL', key, ...otherQuery }) {
  return new Promise(resolve => {
    globalConfig.get({ enabled, type, key, ...otherQuery }).then((res) => {
      if (res?.content?.length) {
        const data = res.content[0]
        resolve(data)
      }
    });
  })
}

export async function getBulidenConfig(currentRouteName = '') {
  const res = await getConfig({ key: 'build_progess' });
  const arr = res.extend.data.configInfo;
  const configData = arr.find(item => item.key === currentRouteName);
  const configDataNew = { ...configData, ...JSON.parse(configData.otherInfo) };
  if (currentRouteName) {
    return configDataNew;
  } else {
    return arr
  }
}
