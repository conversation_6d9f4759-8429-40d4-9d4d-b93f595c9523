<template>
  <div v-if="crud.props.searchToggle" class="">
    <el-input
      v-model="query.fv4OrTitle"
      class="filter-item"
      clearable
      placeholder="请输入编号或路口名称"
      size="small"
      style="width: 180px;"
      @keyup.enter.native="crud.toQuery"
    />
    <!--<el-input-->
    <!--  v-model="query.fv6"-->
    <!--  class="filter-item"-->
    <!--  clearable-->
    <!--  placeholder="请输入实际整改人"-->
    <!--  size="small"-->
    <!--  style="width: 180px;"-->
    <!--  @keyup.enter.native="crud.toQuery"-->
    <!--/>-->
    <!--<self-select-->
    <!--  :options="dict.inspection_unit"-->
    <!--  :select-value.sync="query.inspectionUnit"-->
    <!--  :tags="2"-->
    <!--  class="filter-item"-->
    <!--  clearable-->
    <!--  placeholder="请选择检查单位"-->
    <!--  size="small"-->
    <!--  style="width: 180px;"-->
    <!--  @selectChange="(val)=>selectChange(val, 'inspectionUnit')"-->
    <!--/>-->
    <self-select
      :options="dict.partition_type"
      :select-value.sync="query.region"
      :tags="2"
      class="filter-item"
      clearable
      placeholder="请选择分区"
      size="small"
      style="width: 180px;"
      @selectChange="(val)=>selectChange(val, 'region')"
    />
    <el-select
      v-model="query.status"
      class="filter-item"
      clearable
      placeholder="请选择整改状态"
      size="small"
      style="width: 180px"
      @change="crud.toQuery"
    >
      <el-option
        v-for="item in dict.rectificate_status"
        :key="item.id"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <date-range-picker
      v-model="query.fv2With69"
      class="date-item"
      end-placeholder="接受案件结束日期"
      start-placeholder="接受案件开始日期"
      value-format="yyyy/MM/dd"
    />
    <date-range-picker
      v-model="query.revDate"
      class="date-item"
      end-placeholder="处置截至结束日期"
      start-placeholder="处置截至开始日期"
      value-format="yyyy/MM/dd"
    />
    <rrOperation />
  </div>
</template>

<script>
import { header } from '@crud/crud';
import rrOperation from '@crud/RR.operation';
import DateRangePicker from '@/components/DateRangePicker/index.vue';

export default {
  components: { DateRangePicker, rrOperation },
  mixins: [header()],
  props: {
    permission: {
      type: Object,
      required: true
    },
    dict: {
      type: Object,
      required: true
    }
  },
  data() {
    return {}
  },

  computed: {},
  watch: {},
  created() {
  },
  methods: {
    selectChange(val, type) {
      this.crud.query[type] = val;
      this.crud.toQuery();
    }
  }
}
</script>
