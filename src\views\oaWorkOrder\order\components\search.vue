<template>
  <div>
    <el-form-item label="表单标题">
      <el-input
        v-model.trim="listQuery.title"
        placeholder="请输入表单标题"
        clearable
        size="small"
        style="width: 180px"
        @keyup.enter.native="getList"
      />
    </el-form-item>
    <el-form-item label="项目">
      <el-select
        v-model="listQuery.pmId"
        filterable
        clearable
        remote
        size="small"
        reserve-keyword
        placeholder="请选择项目"
        :loading="loading"
        style="width: 180px"
        @change="getList"
      >
        <el-option
          v-for="item in pmList"
          :key="item.id"
          :label="item.title"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item v-if="genre == 'all'" label="流程类型">
      <el-select
        v-model="listQuery.classify"
        filterable
        clearable
        remote
        size="small"
        reserve-keyword
        placeholder="请选择流程类型"
        :loading="loading"
        style="width: 180px"
        @change="getList"
      >
        <el-option
          v-for="item in processType"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    <!--<el-form-item v-if="genre !== 'my-create'" label="申请人">-->
    <!--<el-select-->
    <!--v-model="listQuery.creator"-->
    <!--filterable-->
    <!--clearable-->
    <!--remote-->
    <!--size="small"-->
    <!--reserve-keyword-->
    <!--placeholder="请输入当前申请人"-->
    <!--:loading="loading"-->
    <!--style="width: 150px"-->
    <!--@change="getList"-->
    <!--&gt;-->
    <!--<el-option-->
    <!--v-for="item in UserOptions"-->
    <!--:key="item.id"-->
    <!--:label="item.nickName"-->
    <!--:value="item.id"-->
    <!--/>-->
    <!--</el-select>-->
    <!--</el-form-item>-->
    <el-form-item v-if="genre !== 'upcoming'" label="当前处理人">
      <el-select
        v-model="listQuery.current"
        filterable
        clearable
        remote
        size="small"
        reserve-keyword
        placeholder="请输入当前处理人"
        :loading="loading"
        style="width: 150px"
        @change="getList"
      >
        <el-option
          v-for="item in UserOptions"
          :key="item.id"
          :label="item.nickName"
          :value="item.nickName"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="优先级">
      <el-select v-model="listQuery.priority" placeholder="请选择优先级" size="small" clearable style="width: 130px" @change="getList">
        <el-option label="一般" :value="1" />
        <el-option label="紧急" :value="2" />
        <el-option label="非常紧急" :value="3" />
      </el-select>
    </el-form-item>
    <el-form-item v-if="genre !== 'upcoming'" label="是否结束">
      <el-select v-model="listQuery.isEnd" placeholder="请选择状态" size="small" clearable style="width: 130px" @change="getList">
        <el-option label="是" :value="1" />
        <el-option label="否" :value="0" />
      </el-select>
    </el-form-item>
    <el-form-item label="创建时间">
      <date-range-picker v-model="listQuery.createTime" class="date-item" @change="getList" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" icon="el-icon-search" size="small" @click="getList">搜索</el-button>
      <el-button class="filter-item" size="mini" type="warning" icon="el-icon-refresh-left" @click="resetQuery()">重置</el-button>
    </el-form-item>
  </div>
</template>

<script>
import { getUser } from '@/api/system/user';
import { getCategory } from '@/api/system/category';
import DateRangePicker from '@/components/DateRangePicker/index.vue';
import { getManySmall } from '@/api/oaWorkOrder/oaPm';

const LCFL_ID = '68'
export default {
  name: 'WorkOrderSearch',
  components: { DateRangePicker },
  // eslint-disable-next-line vue/require-prop-types
  props: ['genre'],
  data() {
    return {
      loading: false,
      listQuery: {
        // title: undefined,
        // creator: undefined,
        // processor: undefined,
        // priority: undefined,
        // isEnd: undefined,
        // createTime: undefined
      },
      UserOptions: [],
      processType: [],
      pmList: []
    }
  },

  created() {
    this.remoteUserList();
    this.getProcessType();
    this.getAllProject();
  },
  methods: {
    getList() {
      Object.keys(this.listQuery).length !== 0 && Object.keys(this.listQuery).forEach(item => {
        if (this.listQuery[item] === null || this.listQuery[item] === '') this.listQuery[item] = undefined
      })
      this.$emit('handleSearch', this.listQuery)
    },
    remoteUserList(query) {
      getUser({
        pageSize: 999999
      }).then(res => {
        this.UserOptions = res.content;
      })
    },
    getProcessType() {
      getCategory({ size: 999, pid: LCFL_ID, enabled: 1 }).then(res => {
        this.processType = res.content || [];
      })
    },
    getAllProject() {
      const params = {
        size: 9999,
        bindId: 35
      }
      getManySmall(params).then(res => {
        this.pmList = res.content || []
      })
    },
    resetQuery() {
      this.listQuery = {};
      this.$emit('reset')
    }
  }
}
</script>

<style scoped>

</style>
