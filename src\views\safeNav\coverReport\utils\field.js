// 项目列表所有权限
export const permission = {
  add: ['admin', 'omAssetAffiliated:add'],
  edit: ['admin', 'omAssetAffiliated:edit'],
  del: ['admin', 'omAssetAffiliated:del'],
  upload: ['admin', 'omAssetAffiliated:importXlsWithRule'],
  updateT: ['admin', 'omAssetAffiliated:updateFormStruct'],
  updateR: ['admin', 'omAssetAffiliated:updateRelation']
  // updateG: ['admin', 'omAssetAffiliated:toRedisGeoIndex']
}

// 项目列表表头
export const tableHeader = [
  { label: '编号', prop: 'fv4', width: 80, align: 'left', fixed: 'left' },
  { label: '路口名称', prop: 'title', width: 180, align: 'left', fixed: 'left' },
  { label: '杆体编号', prop: 'fv5', width: 150, align: 'left' },
  { label: '位置描述', prop: 'fv1', width: 150, align: 'left' },
  { label: '遮挡照片', prop: 'ft1File', width: 280, align: 'left' },
  { label: '状态', prop: 'fv9', width: 160, align: 'center' },
  { label: '描述', prop: 'ft2', width: 150, align: 'center' },
  { label: '上传人', prop: 'createBy', width: 150, align: 'center' },
  { label: '上传日期', prop: 'createTime', width: 150, align: 'center' }

]

// 定义按钮组
export const updateButtonsLists = [
  {
    id: '1',
    label: '导入',
    permission: permission.upload,
    fun: 'importProject',
    size: 'mini',
    className: [],
    icon: 'plus',
    type: 'primary'
  },
  {
    id: '2',
    label: '预览',
    permission: permission.upload,
    fun: 'exportProject',
    size: 'mini',
    className: [],
    icon: 'view',
    type: 'success',
    query: {
      fileType: 'html'
    }
  },
  {
    id: '3',
    label: '导出',
    permission: permission.upload,
    fun: 'exportProject',
    size: 'mini',
    className: [],
    icon: 'download',
    type: 'primary',
    query: {
      fileType: 'xls'
    }
  }
]

// 联合key
export const allKey = {
  '1': {
    target: '2',
    comType: 'fv4',
    targetType: 'title',
    list: 'pointList'
  },
  '2': {
    target: '1',
    comType: 'title',
    targetType: 'fv4',
    list: 'positionList'
  }
}
