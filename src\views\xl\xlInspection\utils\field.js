// 项目列表所有权限
export const permission = {
  add: ['admin', 'omInspect:add'],
  edit: ['admin', 'omInspect:edit'],
  del: ['admin', 'omInspect:del']
  // upload: ['admin', 'omInspect:importXlsWithRule'],
  // updateT: ['admin', 'omInspect:updateFormStruct'],
  // updateR: ['admin', 'omInspect:updateRelation']
  // updateG: ['admin', 'omAsset:toRedisGeoIndex']
}

// 项目列表表头
export const tableHeader = [
  { label: '点位编号', prop: 'fv4', fixed: 'left', align: 'left', width: 80 },
  { label: '立杆位置', prop: 'title', align: 'left', width: 200 },
  { label: '巡检日期', prop: 'createTime', align: 'left', width: 180 },
  { label: '巡检人员', prop: 'fv15', width: 120, align: 'left' },
  { label: '整体状态', prop: 'status', width: 120, align: 'left' },
  { label: '设备箱状态', prop: 'fv10', width: 120, align: 'left' },
  { label: '总摄像头数', prop: 'fv1', width: 120, align: 'left' },
  { label: '前端设备状态', prop: 'fv11', width: 120, align: 'left' },
  { label: '供电状态', prop: 'fv12', width: 120, align: 'left' },
  { label: '图像有无遮挡', prop: 'fv13', width: 120, align: 'left' },
  { label: '网络线路状态', prop: 'fv14', width: 120, align: 'left' },
  { label: '派出所', prop: 'fv8', width: 120, align: 'left' },
  { label: '融合后链路', prop: 'fv9', width: 120, align: 'left' }
]

// 定义按钮组
// 定义按钮组
export const updateButtonsLists = [

]

// 详情表头
export const tableHeaderDetail = [
  // { label: '摄像头', prop: 'fv5', fixed: 'left', align: 'left', width: 80 },
  // { label: '设备厂商', prop: 'fv10', align: 'left', width: 100 },
  // { label: '设备型号', prop: 'fv11', align: 'left', width: 180 },
  // { label: '产权单位', prop: 'fv12', width: 120, align: 'left' },
  // { label: '融合后链路', prop: 'fv9', width: 120, align: 'left' },
  // { label: '状态', prop: 'fv13', width: 120, align: 'left' }
]
