// 项目列表所有权限
export const permission = {
  add: ['admin', 'omAssetAffiliated:add'],
  edit: ['admin', 'omAssetAffiliated:edit'],
  del: ['admin', 'omAssetAffiliated:del'],
  upload: ['admin', 'omAssetAffiliated:importXlsWithRule'],
  updateT: ['admin', 'omAssetAffiliated:updateFormStruct'],
  updateR: ['admin', 'omAssetAffiliated:updateRelation']
  // updateG: ['admin', 'omAssetProgress:toRedisGeoIndex']
}
// 项目列表表头
export const tableHeader = [
  { label: '路口编号', prop: 'fv1', align: 'left', width: 100 },
  { label: '路口名称', prop: 'title', width: 180, align: 'left' },
  { label: '杆体编号', prop: 'fv4', width: 150 }
]

// 定义按钮组
export const updateButtonsLists = [
  {
    id: '1',
    label: '导入',
    permission: permission.upload,
    fun: 'importProject',
    size: 'mini',
    className: [],
    icon: 'plus',
    type: 'primary'
  },
  {
    id: '2',
    label: '预览',
    permission: permission.upload,
    fun: 'exportProject',
    size: 'mini',
    className: [],
    icon: 'view',
    type: 'success',
    query: {
      fileType: 'html'
    }
  },
  {
    id: '3',
    label: '导出',
    permission: permission.upload,
    fun: 'exportProject',
    size: 'mini',
    className: [],
    icon: 'download',
    type: 'primary',
    query: {
      fileType: 'xls'
    }
  }
]

// 联合key
export const allKey = {
  '1': {
    target: '2',
    comType: 'fv4',
    targetType: 'title',
    list: 'pointList'
  },
  '2': {
    target: '1',
    comType: 'title',
    targetType: 'fv4',
    list: 'positionList'
  }
}

export const tagDrawerOptions = {
  title: '',
  direction: 'rtl',
  size: '40%'
}
export const tagSelectOptions = {
  'type.fieldName': 'fv1',
  'type.values': '',
  'top.fieldName': 'fv10'
}
