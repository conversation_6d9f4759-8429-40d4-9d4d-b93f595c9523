<template>
  <span :default="{row}" class="see-cell">
    <template v-if="header.prop === 'title'">
      <span style="cursor: pointer;color: #2476F8" @click="goDetail(row)">
        {{ row[header.prop] }}
      </span>
    </template>
    <template v-else-if="isDynamicHeader(header.prop) && row[header.prop]">
      <!--<span class="tag-list">-->
      <!--  &lt;!&ndash;{{ row[header.prop] }}&ndash;&gt;-->
      <!--  <el-tag-->
      <!--    class="custom-tag"-->
      <!--    style="margin-right:8px;width: auto;"-->
      <!--    type="success"-->
      <!--  >{{ row[header.prop] }}</el-tag>-->
      <!--</span>-->
      <span class="tag-item">{{ showTag(row[header.prop]) }}</span>
    </template>
    <span v-else>{{ row[header.prop] }}</span>
  </span>
</template>

<script>

export default {
  name: 'SeeCell',
  components: {},

  props: {
    header: {
      type: Object,
      default: () => {
        return {}
      }
    },
    row: {
      type: Object,
      default: () => {
        return {}
      }
    },
    crud: {
      type: Object,
      default: () => {
        return {}
      }
    },
    permission: {
      type: Object,
      default: () => {
        return {}
      }
    },
    isShowTag: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  computed: {
    getDescribe() {
      return (data) => {
        return data.join(',')
      }
    },
    isDynamicHeader(prop) {
      const dynamicHeader = this.crud?.metaData?.tableHeader || [];
      return (prop) => {
        return !!dynamicHeader.find(element => element.prop === prop);
      }
    },
    showTag() {
      return (value) => {
        // 正则表达式匹配只包含数字或者形如5.00的字符串
        const numberPattern = /^\d+(\.\d{2})?$/;
        if (numberPattern.test(value)) {
          // 如果匹配，则进行parseFloat处理
          return parseFloat(value);
        } else {
          // 如果不匹配，返回原始字符串
          return value;
        }
      }
    }
  },
  watch: {},
  created() {
  },
  methods: {
    // 去资产详情页
    goDetail(data) {
      // const query = {
      //   name: 'AutoPilot3Form',
      //   query: {
      //     id: data.id,
      //     type: 1
      //   }
      // }
      // this.$router.push(query)
    }
  }
}
</script>

<style currentScoped lang="scss" rel="stylesheet/scss">
.tag-list {
	display: flex;
	flex-wrap: wrap;

	.custom-tag {
		margin-right: 8px;
		padding: 2px;
		text-align: center;
		white-space: normal; /* 允许文本折行 */
		word-break: break-all; /* 在任意字符间折行，适用于没有自然折行点的长单词或URL等 */
		/* 或者使用 word-break: break-word; 保持英文单词和中文句子的完整性 */
		min-width: 100px;
		vertical-align: top;
		margin-bottom: 3px;
	}

}

.tag-item {
	//display: inline-block;
	//background-color: #e7faf0;
	color: #13ce66;
	//border-radius: 4px;
	//border: 1px solid #d0f5e0;
	font-size: 12px;
	padding: 4px;
	width: 100px;
	text-align: center;
	overflow: hidden;
}
</style>
