import store from '@/store'

/**
 * @param {Array} value
 * @returns {Boolean}
 * @example see @/views/permission/directives.vue
 */
export default {
  install(Vue) {
    Vue.prototype.checkPer = (value) => {
      if (value && value instanceof Array && value.length > 0) {
        const roles = store.getters && store.getters.roles
        const permissionRoles = value
        return roles.some(role => {
          return permissionRoles.includes(role)
        })
      } else {
        console.error(`need roles! Like v-permission="['admin','editor']"`)
        return false
      }
    }
  }
}

/**
 * 获取所有权限
 * @type {Set<any>}
 */
const mergedPermissionSet = new Set();

/**
 * 获取所有的权限
 * @param options
 * @returns {*[]}
 */
export function getAllpermission(options) {
  mergedPermissionSet.clear();
  options.forEach(item => {
    item.permission.forEach(permission => {
      mergedPermissionSet.add(permission);
    });
  });
  const mergedPermissionArray = Array.from(mergedPermissionSet);
  return mergedPermissionArray
}

/**
 * 获取想要格式的权限数据
 * @param needKey
 * @param permission
 * @returns {*[]}
 */
export function setArrPermission(needKey, permission) {
  const allPermission = []
  needKey.map(item => {
    allPermission.push({ permission: permission[item] })
  })
  return getAllpermission(allPermission)
}

export function isHasPermission(value) {
  if (value && value instanceof Array && value.length > 0) {
    const roles = store.getters && store.getters.roles
    const permissionRoles = value
    return roles.some(role => {
      return permissionRoles.includes(role)
    })
  } else {
    console.error(`need roles! Like v-permission="['admin','editor']"`)
    return false
  }
}
