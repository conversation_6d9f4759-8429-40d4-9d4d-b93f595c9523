<template>
  <el-dialog
    v-dialog-drag
    :before-close="concelForm"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :title="title"
    :visible.sync="visible"
    append-to-body
    width="600px"
  >
    <el-form ref="elForm" :model="elForm" :rules="elRule" label-width="88px" size="small">
      <el-form-item label="新目录" prop="pmId">
        <treeselect
          v-model="elForm.pmId"
          :load-options="lazyLoadTrees"
          :options="directoryList"
          placeholder="请选择要加入的目录"
          style="width:100%"
        />
      </el-form-item>
    </el-form>
    <el-table
      :data="selections"
      style="width: 100%"
    >
      <el-table-column
        v-for="item in tableHeader"
        :key="item.prop"
        :align="item.align || 'center'"
        :fixed="item.fixed || false"
        :label="item.label"
        :prop="item.prop"
        :show-overflow-tooltip="true"
        :width="item.width"
      >
        <template slot-scope="scope">

          <span>{{ scope.row[item.prop] }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        fixed="right"
        label="操作"
        width="100"
      >
        <template slot-scope="scope">

          <el-button
            size="mini"
            style="margin-left: 10px"
            type="warning"
            @click="handleClose(scope.row)"
          >移除
          </el-button>

        </template>
      </el-table-column>
    </el-table>
    <div slot="footer" class="dialog-footer">
      <el-button type="text" @click="concelForm">取消</el-button>
      <el-button
        :disabled="submitDisabled"
        type="primary"
        @click="submitAction"
      >
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getTreeLists, lazyTrees } from '@/utils/getTrees'
import oaPmTree from '@/api/oaWorkOrder/oaPmTree';
import Treeselect from '@riophae/vue-treeselect';
import oaDocument from '@/api/oaWorkOrder/oaDocument';

export default {
  name: 'MoveFile',
  components: { Treeselect },
  props: {
    selections: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      submitDisabled: false,
      visible: false,
      title: '移动文件',
      directoryList: [],
      tableHeader: [
        { prop: 'pmTreeName', label: '目录名称' },
        { prop: 'fileName', label: '文档名称' },
        { prop: 'fileCreateBy', label: '上传人' },
        { prop: 'createTime', label: '上传时间' }
      ],
      elForm: {
        pmId: undefined
      },
      elRule: {
        pmId: [
          { required: true, message: '请选择要移入的新目录', trigger: 'change' }
        ]
      }
    }
  },
  created() {
  },
  methods: {
    initForm() {
      this.visible = true;
      const { bindId } = this.$config.task_keys;
      const { projectId } = this.$route.query
      const queryParams = {
        enabled: '1',
        fv1: '目录',
        sort: 'sort,asc',
        size: 99999,
        pid: projectId,
        bindId: bindId
      }
      getTreeLists({ apiMethod: oaPmTree.getPmTreeSmall, queryParams }).then(res => {
        this.directoryList = res || []
      })
    },
    lazyLoadTrees(params) {
      const { bindId } = this.$config.task_keys;
      const otherParams = {
        sort: 'sort,asc',
        enabled: '1',
        fv1: '目录',
        size: 99999,
        bindId: bindId,
        pidNotNull: true
      }
      lazyTrees({ ...params, apiMethod: oaPmTree.getPmTreeSmall, otherParams })
    },
    // 移除
    handleClose(tag) {
      if (this.selections.length <= 1) {
        this.$notify({
          type: 'warning',
          title: '提示',
          message: '至少要有一个文件'
        });
      } else {
        // 找到要移除的元素的索引
        const indexToRemove = this.selections.findIndex(item => item.id === tag.id);

        if (indexToRemove !== -1) {
          // 根据索引移除元素
          this.selections.splice(indexToRemove, 1);
        }
      }
    },
    concelForm() {
      this.visible = false
      this.$refs['elForm'].resetFields();
    },

    handleFinalData() {
      const { pmId } = this.elForm; // 获取pmId
      // 遍历并处理this.selections数组
      return this.selections.map(obj => {
        return {
          id: obj.id,
          pmId
        };
      });
    },
    submitAction() {
      this.$refs['elForm'].validate((valid) => {
        if (valid) {
          const data = this.handleFinalData()
          oaDocument.edit(data).then(response => {
            this.$notify({
              title: `移入成功`,
              type: 'success',
              duration: 2500
            })
            this.concelForm();
            this.$emit('success')
          }).catch((e) => {
            console.log(e);
          })
        } else {
          this.submitDisabled = false
          return false
        }
      });
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
