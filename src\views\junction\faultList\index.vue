<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input v-model="query.title" clearable size="small" placeholder="输入故障地点" style="width: 200px;" class="filter-item" />
        <el-select
          v-model="query.fv1"
          clearable
          size="small"
          placeholder="请选择大类"
          class="filter-item"
          style="width: 200px"
        >
          <el-option
            v-for="item in dict.fault_category"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-select
          v-model="query.fv2"
          clearable
          size="small"
          placeholder="请选择来源"
          class="filter-item"
          style="width: 200px"
        >
          <el-option
            v-for="item in dict.fault_origin"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-input v-model="query.fv3" clearable size="small" placeholder="输入小类" style="width: 200px;" class="filter-item" />
        <el-input v-model="query.fv4" clearable size="small" placeholder="输入报障次数" style="width: 200px;" class="filter-item" />
        <date-range-picker v-model="query.fv5" value-format="yyyy-MM-dd" class="date-item" />
        <rrOperation />
      </div>
      <crudOperation :permission="permission">
        <el-button
          slot="left"
          v-permission="permission.add"
          class="filter-item"
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="addTable"
        >
          新建
        </el-button>
        <el-button
          slot="left"
          v-permission="permission.upload"
          size="mini"
          icon="el-icon-plus"
          type="success"
          class="filter-item"
          @click="addFile"
        >上传</el-button>
        <update-button
          slot="left"
          :permission="permission"
          :bind-id="bindId"
          :enabled="[1]"
        />
      </crudOperation>
    </div>
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="已匹配" name="已匹配" />
      <el-tab-pane label="未匹配" name="未匹配">
        <div style="text-align:right;">
          <el-button
            v-permission="permission.updateM"
            size="mini"
            type="warning"
            class="filter-item"
            @click="manualMatch"
          >匹配</el-button>
        </div>
      </el-tab-pane>
    </el-tabs>
    <el-table
      ref="table"
      v-loading="crud.loading"
      :data="tableData"
      row-key="id"
      @select="crud.selectChange"
      @select-all="crud.selectAllChange"
      @selection-change="crud.selectionChangeHandler"
    >
      <el-table-column v-if="activeName==='未匹配'" type="selection" width="55" />
      <el-table-column
        v-for="item in tableHeader"
        :key="item.prop"
        :show-overflow-tooltip="true"
        :prop="item.prop"
        :label="item.label"
        :width="item.width || '150'"
        :align="item.align || 'center'"
      >
        <template slot-scope="scope">
          <template v-if="item.prop.indexOf('img') !='-1'">
            <template v-for="(item,index) in scope.row[item.prop]">
              <el-image
                v-if="index == 0"
                :key="item.id"
                :src="item.url"
                :preview-src-list="[item.url]"
                fit="contain"
                lazy
                class="el-avatar"
              >
                <div slot="error">
                  <i class="el-icon-document" />
                </div>
              </el-image>
            </template>

          </template>
          <!-- <template v-else-if="item.prop.indexOf('cascader') !='-1'">
            <span>{{ scope.row[item.prop] ? scope.row[item.prop].join('-') : '' }}</span>
          </template> -->
          <span v-else>{{ scope.row[item.prop] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="250" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button v-permission="permission.edit" type="primary" size="mini" @click="editItem(scope.row)">编辑</el-button>
          <el-popconfirm
            confirm-button-text="确认"
            :hide-icon="true"
            cancel-button-text="取消"
            icon-color="red"
            title="确认要删除该条数据？"
            @confirm="deleteItem(scope.row)"
          >
            <el-button slot="reference" v-permission="permission.del" type="danger" size="mini">删除</el-button>
          </el-popconfirm>
          <el-button type="success" size="mini" @click="detail(scope.row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination />
    <uploadExcel ref="uploadExcel" @getlist="getChildList" />
    <match ref="match" @getlist="getChildList" />
  </div>
</template>

<script>
import uploadExcel from './components/uploadExcel'
import match from './components/match'
import crudTable from '@/api/parts/fault'
import CRUD, { presenter, header, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import DateRangePicker from '@/components/DateRangePicker'
import updateButton from '@/components/UpdateButton/index'
export default {
  name: 'Fault',
  components: { crudOperation, rrOperation, pagination, uploadExcel, match, DateRangePicker, updateButton },
  cruds() {
    return CRUD({ title: '错误报告', url: 'api/omFaultReport/small', query: { enabled: 1 }, crudMethod: { ...crudTable }})
  },
  mixins: [presenter(), header(), crud()],
  dicts: ['fault_category', 'fault_origin'],
  data() {
    return {
      nofistLoad: false,
      tableData: [],
      tableHeader: [],
      permission: {
        add: ['admin', 'omFaultReport:add'],
        edit: ['admin', 'omFaultReport:edit'],
        del: ['admin', 'omFaultReport:del'],
        upload: ['admin', 'omFaultReport:importXlsWithRule'],
        updateT: ['admin', 'omFaultReport:updateFormStruct'],
        updateR: ['admin', 'omFaultReport:updateRelation'],
        updateA: ['admin', 'omFaultReport:updateAssetId'],
        updateM: ['admin', 'omAlias:updateAssetId']
      },
      props: {
        value: 'id',
        label: 'label',
        emitPath: false
      },
      bindId: '',
      activeName: '已匹配'
    }
  },
  watch: {
    'crud.data': {
      handler(val, oldVal) {
        if (val && val.length && val.length > 0) {
          const newVal = val;
          const publicfilters = ['text_', 'divider_', 'editor_', 'subform'];
          const uniqueFilters = ['file_'];
          const publicLable = ['栅格布局']
          const uniqueLable = ['序号']
          const filterTableHeader = [...publicfilters, ...uniqueFilters]
          const filterLable = [...publicLable, ...uniqueLable]
          const tableHeader = newVal[0].extend.tableHeader.filter((item) => {
            return filterLable.every(subItem => item.label !== subItem) && filterTableHeader.every(subItem => item.prop.indexOf(subItem) == '-1')
          });
          const otherTableHeader = [
            // { prop: 'assetTitle', label: '匹配路口名' }
            // { prop: 'createTime', label: '发布时间' }
          ];
          this.activeName == '已匹配' && otherTableHeader.push({ prop: 'assetTitle', label: '匹配路口名' })
          this.tableHeader = [...tableHeader, ...otherTableHeader];
          let tableData = [];
          tableData = newVal.map(item => {
            const json = item.extend.data;
            json.id = item.id;
            json.title = item.title;
            json.assetTitle = item?.asset?.title || '';
            return json;
          });
          this.tableData = tableData;
        } else {
          this.tableHeader = [];
          this.tableData = [];
        }
      },
      deep: true
    }
  },
  created() {
    this.crud.optShow = { add: false, edit: false, del: false, download: false, reset: true }
  },
  activated() {
    if (this.nofistLoad) {
      this.crud.refresh();
    }
    this.nofistLoad = false
  },
  methods: {
    [CRUD.HOOK.beforeRefresh]() {
      this.crud.query.bindId = this.$route.query.id;
      this.bindId = this.$route.query.id;
      this.crud.query.status = this.activeName;
    },
    addFile() {
      this.$refs.uploadExcel.init();
    },
    addTable() {
      this.$router.push({ name: 'FaultCreate', query: { id: this.$route.query.id }});
      this.nofistLoad = true;
    },
    editItem(row) {
      this.$router.push({ name: 'FaultCreate', query: { id: this.$route.query.id, rowId: row.id, type: 'edit' }});
      this.nofistLoad = true;
    },
    detail(row) {
      this.$router.push({ name: 'FaultCreate', query: { id: this.$route.query.id, rowId: row.id, type: 'see' }})
    },
    deleteItem(row) {
      crudTable.del([row.id]).then(response => {
        this.crud.refresh();
        this.$notify({
          title: '数据已删除',
          type: 'success',
          duration: 2500
        })
      })
    },
    getJson(item) {
      const json = {}
      item.forEach(i => {
        for (const name in i) {
          json[name] = i[name];
        }
      })
      return json;
    },
    getArr(item) {
      const arr = [];
      item.forEach(i => {
        i.forEach(j => {
          arr.push(j)
        })
      })
      return arr;
    },
    handleClick() {
      this.crud.refresh();
    },
    manualMatch() {
      const selection = this.crud.selections;
      if (selection && selection.length) {
        this.$refs.match.init(this.crud.selections);
      } else {
        this.$notify({
          title: '请先勾选列表数据',
          type: 'warning',
          duration: 2500
        })
      }
    },
    getChildList() {
      this.activeName = '已匹配';
      this.crud.refresh();
    }
  }
}
</script>
<style>
.tableImg {
  width: 50px;height: 50px;
}
</style>
