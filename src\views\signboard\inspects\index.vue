<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-select v-if="filterAssetShow" v-model="query.assetId" clearable filterable placeholder="请选择部件名称" class="filter-item">
          <el-option
            v-for="item in partsList"
            :key="item.id"
            :label="item.title"
            :value="item.id"
            style="width: 200px"
          />
        </el-select>
        <el-select v-model="query.status" clearable filterable placeholder="请选择巡检结果" class="filter-item">
          <el-option
            v-for="item in inspectsRlist"
            :key="item.id"
            :label="item.label"
            :value="item.label"
            style="width: 200px"
          />
        </el-select>
        <el-input v-model="query.fv6" clearable size="small" placeholder="请输入情况描述" style="width: 200px;" class="filter-item" />
        <date-range-picker v-model="query.createTime" type="datetimerange" style="width:400px !important" class="date-item" />
        <rrOperation>
          <el-button
            slot="right"
            class="filter-item"
            type="primary"
            icon="el-icon-eleme"
            size="mini"
            @click="preView"
          >
            预览
          </el-button>
          <el-button
            slot="right"
            class="filter-item"
            type="primary"
            icon="el-icon-download"
            size="mini"
            @click="downLoad"
          >
            导出
          </el-button>
        </rrOperation>
      </div>
      <crudOperation :permission="permission">
        <el-button
          slot="left"
          v-permission="permission.add"
          class="filter-item"
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="addTable"
        >
          新建
        </el-button>
        <update-button
          slot="left"
          :permission="permission"
          :bind-id="bindId"
          :enabled="[1]"
        />
      </crudOperation>
    </div>
    <el-table
      ref="table"
      v-loading="crud.loading"
      :data="tableData"
      row-key="id"
      @select="crud.selectChange"
      @select-all="crud.selectAllChange"
      @selection-change="crud.selectionChangeHandler"
    >
      <el-table-column
        v-for="item in tableHeader"
        :key="item.prop"
        :show-overflow-tooltip="true"
        :prop="item.prop"
        :label="item.label"
        :width="item.width || ''"
        :align="item.align || 'center'"
      >
        <template slot-scope="scope">
          <template v-if="item.prop.indexOf('img') !='-1'">
            <template v-for="(item,index) in formatImageArr(scope.row[item.prop])">
              <el-image
                v-if="index == 0"
                :key="item.id"
                :src="item.url"
                :preview-src-list="[item.url]"
                fit="contain"
                class="el-avatar"
              >
                <div slot="error">
                  <i class="el-icon-document" />
                </div>
              </el-image>
            </template>
          </template>
          <template v-else-if="item.label =='部件名称'">
            <span class="table-colume-title" @click="detail(scope.row)">{{ scope.row[item.prop] }}</span>
          </template>
          <span v-else>{{ scope.row[item.prop] }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="checkPer(['admin','omInspect:edit','omInspect:del'])" label="操作" width="250" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button v-permission="permission.edit" type="primary" size="mini" @click="editItem(scope.row)">编辑</el-button>
          <el-popconfirm
            confirm-button-text="确认"
            :hide-icon="true"
            cancel-button-text="取消"
            icon-color="red"
            title="确认要删除该条数据？"
            @confirm="deleteItem(scope.row)"
          >
            <el-button slot="reference" v-permission="permission.del" type="danger" size="mini">删除</el-button>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import crudTable from '@/api/parts/inspect'
import CRUD, { presenter, header, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import DateRangePicker from '@/components/DateRangePicker'
import { getConfig } from '@/utils/getConfigData'
import omAsset from '@/api/parts/assets'
import crudDictDetail from '@/api/system/dictDetail'
import { downloadUrl } from '@/utils/index'
import updateButton from '@/components/UpdateButton/index'
export default {
  name: 'SignInspects',
  components: { crudOperation, rrOperation, pagination, DateRangePicker, updateButton },
  cruds() {
    return CRUD({
      title: '资产管理',
      url: 'api/omInspect/small',
      query: { enabled: 1 },
      crudMethod: { ...crudTable },
      optShow: { add: false, edit: false, del: false, download: false, reset: true }
    })
  },

  mixins: [presenter(), header(), crud()],
  dicts: ['inspects_results'],
  data() {
    return {
      enabledTypeOptions: [
        { key: 'true', display_name: '激活' },
        { key: 'false', display_name: '锁定' }
      ],
      nofistLoad: false,
      tableData: [],
      tableHeader: [],
      permission: {
        add: ['admin', 'omInspect:add'],
        edit: ['admin', 'omInspect:edit'],
        del: ['admin', 'omInspect:del'],
        upload: ['admin', 'omInspect:importXlsWithRule'],
        updateT: ['admin', 'omInspect:updateFormStruc'],
        updateR: ['admin', 'omInspect:updateRelation']
      },
      props: {
        value: 'id',
        label: 'label',
        emitPath: false
      },
      bindId: '',
      partsList: [],
      inspectsRlist: [],
      filterAssetShow: true,
      omAssetTitle: '',
      partsListInfo: {
        page: -1,
        size: 200,
        categoryId: '',
        bindId: '',
        enabled: 1
      }
    }
  },
  computed: {
    ...mapGetters([
      'baseApi',
      'omInspectAPi'
    ])
  },
  watch: {
    'crud.data': {
      handler(val, oldVal) {
        if (val && val.length && val.length > 0) {
          const newVal = val;
          const publicfilters = ['text_', 'divider_', 'editor_', 'subform'];
          const uniqueFilters = ['file_'];
          const publicLable = ['栅格布局']
          const uniqueLable = ['部件名称']
          const filterTableHeader = [...publicfilters, ...uniqueFilters]
          const filterLable = [...publicLable, ...uniqueLable]
          const tableHeader = newVal[0].extend.tableHeader.filter((item) => {
            return filterLable.every(subItem => item.label !== subItem) && filterTableHeader.every(subItem => item.prop.indexOf(subItem) == '-1')
          });
          const otherTableHeader = [
            { prop: 'assetTitle', label: '部件名称' },
            { prop: 'createTime', label: '巡检时间' },
            { prop: 'status', label: '巡检结果' }
            // { prop: 'fv2', label: '故障等级' }
          ];
          this.tableHeader = [...otherTableHeader, ...tableHeader];
          let tableData = [];
          tableData = newVal.map(item => {
            const json = item.extend.data;
            json.id = item.id;
            json.assetTitle = item.asset.title;
            json.createTime = item.createTime;
            json.status = item.status;
            // json.fv2 = item.fv2;
            return json;
          });
          this.tableData = tableData;
        } else {
          this.tableHeader = [];
          this.tableData = [];
        }
      },
      deep: true
    }
  },
  created() {
    this.getPartsNames();
    this.getInspectsResults();
  },
  activated() {
    if (this.nofistLoad) {
      this.crud.refresh();
    }
    this.nofistLoad = false
  },
  methods: {
    [CRUD.HOOK.beforeRefresh]() {
      this.crud.query.bindId = this.$route.query.id;
      this.crud.query.categoryId = this.$route.query.category;
      if (this.$route.query.omAssetID) {
        this.crud.query.assetId = this.$route.query.omAssetID
      }
      if (this.$route.query.omAssetTitle) {
        this.omAssetTitle = this.$route.query.omAssetTitle
      }
      this.filterAssetShow = !this.$route.query.omAssetID
      this.bindId = this.$route.query.id;
    },
    getTime() {
      if (this.query.createTime) {
        return this.query.createTime
      }
      const end = `${this.$dayJS().format('YYYY-MM-DD')} 12:00:00`
      const start = `${this.$dayJS().subtract(1, 'day').format('YYYY-MM-DD')} 12:00:00`
      return [start, end]
    },
    preView() {
      // const params = qs.stringify(data, { indices: false })
      const params = `page=0&size=99999&inspect.createTime=${this.getTime()[0]}&inspect.createTime=${this.getTime()[1]}&inspect.status=${this.query.status || ''}&asset.status=${this.query.status || '故障'}&inspect.enabled=1`
      const url = `${this.omInspectAPi}/index/html?${params}`;
      window.open(url)
    },
    downLoad() {
      const params = `page=0&size=99999&inspect.createTime=${this.getTime()[0]}&inspect.createTime=${this.getTime()[1]}&inspect.status=${this.query.status || ''}&asset.status=${this.query.status || '故障'}&inspect.enabled=1&fileName=交通信号灯运维日报`
      const url = `${this.omInspectAPi}/index/doc?${params}`;
      downloadUrl(url)
    },
    getInspectsResults() {
      crudDictDetail.get('inspects_results').then(response => {
        this.inspectsRlist = response.content
      })
    },
    async getPartsNames() {
      const data = { key: 'signboard_list' }
      await getConfig(data).then(res => {
        this.partsListInfo.categoryId = res.extend.data.categoryId
        this.partsListInfo.bindId = res.extend.data.bindId
        this.getPartsList();
      });
    },
    getPartsList() {
      this.partsListInfo.page++;
      omAsset.getOmAssetSmall(this.partsListInfo).then(res => {
        this.partsList = this.partsList.concat(res.content);
        if (res.totalElements == this.partsList.length) {
          return
        } else {
          this.getPartsList();
        }
      })
    },
    addTable() {
      const query = {
        id: this.$route.query.id,
        omAssetID: this.$route.query.omAssetID || '',
        categoryID: this.$route.query.category,
        omAssetTitle: this.omAssetTitle
      }
      this.$router.push({ name: 'SignInspectsCreate', query });
      this.nofistLoad = true;
    },
    editItem(row) {
      this.$router.push({ name: 'SignInspectsCreate', query: { id: this.$route.query.id, rowId: row.id, omAssetTitle: this.omAssetTitle, type: 'edit' }});
      this.nofistLoad = true;
    },
    detail(row) {
      this.$router.push({ name: 'SignInspectsCreate', query: { id: this.$route.query.id, rowId: row.id, omAssetTitle: this.omAssetTitle, type: 'see' }})
    },
    deleteItem(row) {
      crudTable.del([row.id]).then(res => {
        this.crud.refresh();
        this.$notify({
          title: '数据已删除',
          type: 'success',
          duration: 2500
        })
      })
    },
    getJson(item) {
      const json = {}
      item.forEach(i => {
        for (const name in i) {
          json[name] = i[name];
        }
      })
      return json;
    },
    getArr(item) {
      const arr = [];
      item.forEach(i => {
        i.forEach(j => {
          arr.push(j)
        })
      })
      return arr;
    },
    formatImageArr(val) {
      if (typeof val === 'string') {
        return JSON.parse(val);
      } else {
        return val;
      }
    }
  },
  [CRUD.HOOK.beforeRefresh]() {
  }
}
</script>

<style scoped>
.table-colume-title {
  cursor: pointer;
  color: #2476F8;
}
</style>
