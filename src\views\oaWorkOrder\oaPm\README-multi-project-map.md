# 多项目地图展示功能

## 功能概述

已成功实现多个项目ID依次调用 `getCatalogTree` API，筛选出所有 `type=='point'` 的节点，并将结果合并为一个数组传递给 `mapDialog` 组件。

## 主要修改

### 1. 引入API

```javascript
import { getCatalogTree } from '@/api/oaWorkOrder/oaPmProgress'
```

### 2. 重构 handleShowMapView() 方法

- **原来**: 使用硬编码的模拟数据
- **现在**: 支持多项目选择，依次调用API获取真实数据并合并

### 3. 核心实现逻辑

```javascript
async handleShowMapView() {
  try {
    // 1. 检查是否选择了项目
    if (!this.crud.selections || this.crud.selections.length === 0) {
      this.$message.warning('请先选择项目')
      return
    }

    // 2. 显示加载状态
    const loading = this.$loading({...})

    // 3. 获取选中项目的ID列表
    const projectIds = this.crud.selections.map(project => project.id)

    // 4. 依次调用API获取每个项目的目录树数据
    const allLocationPoints = []
    
    for (const projectId of projectIds) {
      const response = await getCatalogTree(projectId)
      if (response) {
        const pointNodes = this.extractPointNodes(response)
        const projectPoints = pointNodes.map(point => ({
          name: point.name || point.title || '未命名点位',
          longitude: point.longitude || point.lng || point.lon,
          latitude: point.latitude || point.lat,
          id: point.id,
          projectId: projectId, // 添加项目ID标识
          projectName: this.crud.selections.find(p => p.id === projectId)?.name,
          ...point
        })).filter(point => point.longitude && point.latitude)

        allLocationPoints.push(...projectPoints)
      }
    }

    // 5. 传递合并后的点位数据到地图组件
    this.$refs.mapDialog.show(allLocationPoints)
  } catch (error) {
    // 错误处理
  }
}
```

## 功能特点

### ✅ 多项目支持
- 支持同时选择多个项目
- 依次调用每个项目的API
- 自动合并所有项目的点位数据

### ✅ 数据增强
- 为每个点位添加 `projectId` 和 `projectName` 字段
- 便于在地图上区分不同项目的点位

### ✅ 错误处理
- 单个项目API失败不影响其他项目
- 完善的用户提示和错误日志

### ✅ 用户体验
- 显示加载状态和进度提示
- 成功后显示获取到的点位数量
- 没有有效点位时给出友好提示

### ✅ 性能优化
- 使用 `for...of` 循环顺序执行API调用
- 避免并发请求过多导致服务器压力

## 数据流程

```
用户选择多个项目
       ↓
获取项目ID列表 [1, 2, 3]
       ↓
依次调用 getCatalogTree(1), getCatalogTree(2), getCatalogTree(3)
       ↓
每个API返回树形结构数据
       ↓
递归遍历每个树，提取 type=='point' 的节点
       ↓
转换数据格式并添加项目信息
       ↓
合并所有项目的点位数据
       ↓
传递给 mapDialog 组件显示
```

## 数据结构示例

### 输入：选中的项目
```javascript
[
  { id: 1, name: '北京市教育设施建设项目' },
  { id: 2, name: '上海市基础设施改造项目' }
]
```

### 输出：合并后的点位数据
```javascript
[
  {
    id: 103,
    name: '监测点BJ-A1',
    longitude: '116.816054',
    latitude: '40.154257',
    projectId: 1,
    projectName: '北京市教育设施建设项目',
    description: '北京教学楼A区监测点'
  },
  {
    id: 203,
    name: '监测点SH-R1',
    longitude: '121.473701',
    latitude: '31.230416',
    projectId: 2,
    projectName: '上海市基础设施改造项目',
    description: '上海道路改造监测点1'
  }
]
```

## 使用方式

1. 在项目列表中选择一个或多个项目（通过复选框）
2. 点击"地图展示"按钮
3. 系统会依次获取每个选中项目的点位数据
4. 所有点位数据合并后在地图上显示
5. 地图上的点位会标识所属项目信息

## 测试

可以使用 `test-multi-project-map.js` 文件来测试多项目数据获取和合并的逻辑。

## 注意事项

- 确保选择的项目都有有效的ID
- API调用失败时会继续处理其他项目
- 没有坐标信息的点位会被自动过滤
- 建议不要同时选择过多项目，避免API调用时间过长
