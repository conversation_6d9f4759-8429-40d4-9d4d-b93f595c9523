<template>
  <el-dialog
    v-dialog-drag
    :before-close="concelForm"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :title="title"
    :visible.sync="visible"
    append-to-body
    width="500px"
  >
    <el-form ref="elForm" :model="elForm" :rules="elRule" label-width="130px">
      <el-form-item label="负责人:" prop="createBy">
        <el-select
          v-model="elForm.createBy"
          :loading="selectLoading"
          :remote-method="remoteSelectUsers"
          class="filter-item"
          clearable
          debounce="500"
          filterable
          placeholder="请输入处理人"
          remote
          reserve-keyword
          size="small"
          style="width: 100%"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.username"
            :value="item.username"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="开始时间:" prop="fv13">
        <el-date-picker
          v-model="elForm.fv13"
          :picker-options="pickerOptionsE"
          clearable
          format="yyyy-MM-dd HH:mm:ss"
          placeholder="请选择预计开始时间"
          popper-class="no-atTheMoment"
          style="width: 100%"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
        />
      </el-form-item>
      <el-form-item label="结束时间:" prop="fv11">
        <el-date-picker
          v-model="elForm.fv11"
          :append-to-body="false"
          :picker-options="pickerOptionsS"
          class="date-picker"
          clearable
          format="yyyy-MM-dd HH:mm:ss"
          placeholder="请选择预计结束时间"
          popper-class="no-atTheMoment"
          style="width: 100%"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
        />
      </el-form-item>
      <el-form-item label="状态:" prop="fv12">
        <el-select
          v-model="elForm.fv12"
          class="filter-item"
          clearable
          filterable
          placeholder="请选择状态"
          size="small"
          style="width: 100%"
        >
          <el-option
            v-for="item in stateList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="text" @click="concelForm">取消</el-button>
      <el-button
        :disabled="submitDisabled"
        type="primary"
        @click="submitAction"
      >
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import crudUser from '@/api/system/user';
import oaPmTree from '@/api/oaWorkOrder/oaPmTree';

export default {
  name: 'ProgressForm',
  components: {},
  data() {
    return {
      elForm: {
        fv18: '',
        formBindToVar: 0,
        createBy: '', // 负责人
        fv13: '', // 预计开始时间
        fv11: '', // 预计结束时间
        fv12: '' // 状态
      },
      elRule: {
        fv13: [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        fv11: [
          { required: true, message: '请选择结束时间', trigger: 'change' }
        ]
      },
      stateList: [
        { value: '已审核', label: '已审核' },
        { value: '逾期', label: '逾期' }
      ],
      pickerOptionsS: {
        disabledDate: (time) => {
          if (!this.elForm.fv13) {
            return;
          }
          return time.getTime() < new Date(this.elForm.fv13).valueOf();
        }
      },
      pickerOptionsE: {
        disabledDate: (time) => {
          if (!this.elForm.fv11) {
            return;
          }
          return time.getTime() > new Date(this.elForm.fv11).valueOf();
        }
      },
      visible: false,
      title: '进度查询',
      submitDisabled: false,
      selectLoading: false,
      userList: [] // 处理人

    }
  },

  computed: {},
  watch: {},
  created() {
  },
  methods: {
    initForm(data) {
      this.visible = true;
      this.elForm = data;
      this.elForm.fv18 = this.$route.query.name;
    },
    async concelForm() {
      this.visible = false
      this.$refs['elForm'].resetFields();
      this.$refs['elForm'].clearValidate();
    },
    // 处理人搜索
    remoteSelectUsers(query) {
      if (query !== '') {
        this.selectLoading = true;
        const data = {
          enabled: 1,
          userName: query,
          size: 99
        }
        crudUser.getByName(data).then(res => {
          if (res) {
            this.userList = res || [];
            this.selectLoading = false;
          } else {
            this.userList = [];
          }
        })
      } else {
        this.userList = [];
      }
    },
    submitAction() {
      this.$refs['elForm'].validate((valid) => {
        if (valid) {
          oaPmTree.edit(this.elForm).then(response => {
            this.$notify({
              title: `修改成功`,
              type: 'success',
              duration: 2500
            })
            this.concelForm();
            this.$emit('success')
          }).catch((e) => {
            console.log(e);
          })
        } else {
          this.submitDisabled = false
          return false
        }
      });
    }
  }

}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
