<template>
  <div class="app-container">
    <div class="head-container">
      <e-header :permission="permission" />
      <crudOperation :permission="permission">
        <!--<update-button-->
        <!--  slot="right"-->
        <!--  :bind-id="bindId"-->
        <!--  :enabled="[1]"-->
        <!--  :permission="permission"-->
        <!--/>-->
      </crudOperation>
    </div>
    <!-- 表格 -->
    <div class="body-box">
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="tableData"
        :span-method="arraySpanMethod"
        style="width: 100%;"
        @select="crud.selectChange"
        @select-all="crud.selectAllChange"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column
          v-for="(item, index) in tableHeader"
          :key="item.prop"
          :align="item.align"
          :fixed="item.fixed || false"
          :label="item.label"
          :min-width="item.minWidth"
          :prop="item.prop"
          :width="item.width"
        >
          <template slot-scope="scope">
            <template v-if="item.prop == 'filePre'">
              <file-thumb
                :file-ext="scope.row.fileExt"
                :preview-list="[scope.row.url]"
                :url="scope.row.thUrl"
                @preView="preView"
              />
            </template>
            <template v-else-if="item.prop == 'dataName'">
              <span style="cursor: pointer;color: #2476F8" @click="goDetail(scope.row)">
                {{ scope.row[item.prop] }}
              </span>
            </template>
            <template v-else-if="item.prop == 'createBy'">
              <span
                v-for="(path,pathIndex) in scope.row[item.prop]"
                :key="`${index}-${pathIndex}`"
                style="cursor: pointer;color: #2476F8"
                @click="goDetail(scope.row,path)"
              >
                {{ path }}
              </span>
            </template>
            <span v-else>{{ scope.row[item.prop] }}</span>
          </template>
        </el-table-column>

      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import oaDocument from '@/api/oaWorkOrder/oaDocument';
import eHeader from './module/header';
import CRUD, { form, presenter } from '@crud/crud';
import crudOperation from '@crud/CRUD.operation';
import pagination from '@crud/Pagination';
import { downloadUrl } from '@/utils/index';
import { mapGetters } from 'vuex'
// import { getUser } from '@/api/system/user'

const defaultForm = { id: null }
export default {
  name: 'CountDoc',
  components: { eHeader, crudOperation, pagination },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: false,
      title: '文档统计',
      url: 'api/oaDocument/stat',
      sort: [],
      query: { enabled: 1, createTime: [] },
      crudMethod: { ...oaDocument },
      optShow: {
        add: false,
        edit: false,
        del: false,
        download: false,
        reset: true,
        rightGroup: false
      },
      page: {
        // 页码
        page: 0,
        // 每页数据条数
        size: 20,
        // 总数据条数
        total: 0
      }
    })
  },

  mixins: [presenter(), form(defaultForm)],
  data() {
    return {
      permission: {
        del: ['admin', 'oaDocument:del'],
        add: ['admin', 'oaDocument:add'],
        updateT: ['admin', 'oaPm:updateFormStruct'],
        updateR: ['admin', 'oaPm:updateRelation']
      },
      tableData: [],
      tableHeader: [],
      bindId: '34',
      currentIndex: '',
      currentColumnIndex: ''
    };
  },
  computed: {
    ...mapGetters([
      'preViewUrl'
    ])
  },
  watch: {
    'crud.data': {
      handler(val) {
        this.formatterTableHeader(val)
        this.formatterTableData(val)
      },
      deep: true
    }
  },
  created() {
    this.crud.page.size = 30
    this.crud.refresh();
    // this.getUserlist()
  },
  methods: {
    // getUserlist() {
    //   getUser().then(res => {
    //     console.log(res, '<===>', 'res')
    //   })
    // },
    [CRUD.HOOK.beforeRefresh]() {
      if (!this.crud.query.createTime) {
        this.crud.query.startTime = ''
        this.crud.query.endTime = ''
      } else {
        if (this.crud.query.createTime && this.crud.query.createTime.length === 0) {
          const formattedDate = this.$dayJS().format('YYYY-MM-DD');
          this.crud.query.createTime = [`${formattedDate} 00:00:00`, `${formattedDate} 23:59:59`];
        }
        this.crud.query.startTime = this.crud.query.createTime[0]
        this.crud.query.endTime = this.crud.query.createTime[1]
      }
    },
    download(row) {
      downloadUrl(row.url, row.fileName)
    },
    preView(url) {
      window.open(`${this.preViewUrl}/onlinePreview?url=${encodeURIComponent(btoa(url))}`);
    },
    /**
		 * 格式化表格数据
		 * @param {Array} val - 表格数据数组
		 */
    formatterTableData(val) {
      if (!val && !val.length) {
        this.tableData = [];
        return
      }
      const mapItem = (item) => {
        return {
          ...item,
          createBy: item.createBy.split(',')
        };
      };
      this.tableData = val.map(mapItem);
    },
    /**
		 * 格式化表头
		 * @param {Array} val - 表格数据数组
		 */
    formatterTableHeader(val) {
      const tableHeader = [
        { prop: 'dataName', label: '项目名称', align: 'left', fixed: 'left', minWidth: 380 },
        // { prop: 'fileExt', label: '后缀', align: 'left' },
        { prop: 'count', label: '总数', align: 'center', width: 100 },
        { prop: 'orderName', label: '目录', align: 'center', width: 200 },
        { prop: 'total', label: '数量', align: 'center', width: 100 },
        { prop: 'createBy', label: '上传人', align: 'center', width: 200 },
        { prop: 'createTime', label: '上传时间', width: 150 }
      ]
      const otherHeader = [];
      this.tableHeader = [...tableHeader, ...otherHeader];
    },

    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      // 行数据 列参数 行下标  列下标你这个是行列都得合并？
      this.addTotal()
      const arr = this.getSpan(this.tableData)
      if (columnIndex < 2) {
        const row = arr[rowIndex].row
        const col = arr[rowIndex].col
        return {
          rowspan: row,
          colspan: col
        }
      }
    },
    getSpan(list) {
      const newArr = []
      const obj = {}
      for (let i = 0; i < list.length; i++) {
        if (i === 0) {
          obj.row = 1
          obj.col = 1
          newArr.push(obj)
        } else {
          if (list[i].dataName === list[i - 1].dataName) {
            newArr.push({ row: 0, col: 0 })
            const index = list.findIndex(item => {
              return item.dataName === list[i - 1].dataName
            })
            newArr[index].row++
          } else {
            newArr.push({ row: 1, col: 1 })
          }
        }
      }
      return newArr
    },
    addTotal() {
      const idTotals = {}
      this.tableData.forEach(item => {
        const { dataName, total } = item
        if (idTotals[dataName]) {
          idTotals[dataName] += total
        } else {
          idTotals[dataName] = total
        }
      })

      this.tableData.forEach(item => {
        const { dataName } = item
        item.count = idTotals[dataName]
      })
    },
    goDetail(row, name) {
      const { dataName } = row
      const { bindId } = this.$config.docm_keys
      const query = {
        id: String(bindId),
        type: 'countDoc',
        createTime: this.crud.query.createTime
      }
      if (name) {
        query.createBy = name
      } else {
        query.dataName = dataName
      }
      this.$router.push({
        name: 'Document',
        query
      })
    }
    // handleCellMouseEnter(row, column, cell, event) {
    //   this.currentIndex = row.dataName;
    //   this.currentColumnIndex = column.label;
    // },
    // handleCellMouseLeave() {
    //   this.currentIndex = '';
    //   this.currentColumnIndex = '';
    // },
    // tableRowClassName({ row }) {
    //   const flag =
    // 			row.dataName == this.currentIndex &&
    // 			(this.currentColumnIndex == '项目名称' || this.currentColumnIndex == '总数');
    //   return flag ? 'quotatemplate-my-hover-row' : '';
    // },
    // cellStyle({ row, column, rowIndex, columnIndex }) {
    //   const isHighlighted =
    // 			row.dataName === this.currentIndex &&
    // 			!['项目名称', '总数'].includes(this.currentColumnIndex) &&
    // 			[0, 1].includes(columnIndex);
    //
    //   return isHighlighted ? 'background: #d1d2d3' : '';
    // }

  }
};
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
::v-deep.el-table .quotatemplate-my-hover-row {
	background: #d1d2d3 !important;
}
</style>
