<template>
  <span class="oper-buttons">
    <update-button
      v-if="bindId"
      :bind-id="bindId"
      :enabled="[1]"
      :other-params="otherParams"
      :permission="permission"
    />
    <el-button
      v-for="(item) in updateButtonsLists"
      :key="item.label"
      v-permission="item.permission"
      :icon="`el-icon-${item.icon}`"
      :size="item.size || 'mini'"
      :type="item.type || 'primary'"
      class="filter-item"
      @click="handelClick(item)"
    >
      {{ item.label }}
    </el-button>
  </span>
</template>

<script>
import updateButton from '@/components/UpdateButton/index.vue';
import oaPmTree from '@/api/oaWorkOrder/oaPmTree'
import { getToken } from '@/utils/auth';
import { downloadUrl } from '@/utils';
import { mapGetters } from 'vuex';
import { updateButtonsLists } from '@/views/oaWorkOrder/oaPm/utils/field'

export default {
  name: '',
  components: { updateButton },
  props: {
    bindId: {
      type: String,
      default: ''
    },
    permission: {
      type: Object,
      default: () => {
        return {}
      }
    },
    currentCrud: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    return {
      otherParams: { fv1: ['项目', '子项目'] },
      updateButtonsLists
    }
  },

  computed: {
    ...mapGetters([
      'omOaAPi'
    ])
  },
  watch: {},
  created() {
  },
  methods: {
    handelClick(item) {
      this[item.fun](item.query);
    },
    importProject() {
      this.$emit('importProject')
    },
    uploadExcelSuccess() {
      this.$emit('uploadExcelSuccess')
    },
    // 通过key 更新项目
    updateProjectKeys() {
      const { bindId } = this.$config.projects_keys
      this.$emit('successUpdateInfo', 1)
      const data = {
        sourceKeys: ['5', '6'],
        targetKey: '40',
        bindId: Number(bindId),
        fv1: ['项目', '子项目'],
        size: 9999
      }

      oaPmTree.updateCateNames(data).then(res => {
        this.$notify({
          title: '',
          message: '更新成功',
          type: 'success'
        })
        this.$emit('successUpdateInfo')
      }).finally(() => {
        this.$emit('successUpdateInfo', 2)
      })
    },
    // 更新项目权限
    updateProjectAuth() {
      this.$emit('successUpdateInfo', 1)
      const data = {
        size: 9999,
        fv1: ['项目', '子项目']
      }
      oaPmTree.addToMemberAndAuth(data).then(res => {
        this.$notify({
          message: '更新成功',
          type: 'success'
        })
        this.$emit('successUpdateInfo')
      }).finally(() => {
        this.$emit('successUpdateInfo', 2)
      })
    },
    // 更新子项目
    updateSubProjects() {
      this.$emit('successUpdateInfo', 1)
      const data = {
        size: 9999,
        fv1: ['项目', '子项目']
      }
      oaPmTree.oneLevelSubproject(data).then(res => {
        this.$notify({
          message: '更新成功',
          type: 'success'
        })
        this.$emit('successUpdateInfo')
      }).finally(() => {
        this.$emit('successUpdateInfo', 2)
      })
    },
    // 更新项目金额
    updateProjectMoneys() {
      this.$emit('successUpdateInfo', 1)
      const data = {
        size: 9999,
        fv1: ['项目', '子项目'],
        enabled: 1
      }
      oaPmTree.updateFieldsByRule(data).then(res => {
        this.$notify({
          message: '更新成功',
          type: 'success'
        })
        this.$emit('successUpdateInfo')
      }).finally(() => {
        this.$emit('successUpdateInfo', 2)
      })
    },
    exportProject({ fileType }) {
      const { query } = this.currentCrud
      const { bindId } = this.$config.projects_keys
      console.log(this.query, '<===>', 'this.query')
      const fixedParams = {
        page: 0,
        size: 9999999,
        sort: 'createTime,desc',
        fileName: '项目列表',
        'pmTree.enabled': 1,
        'pmTree.fv1': '项目',
        'pmTree.pidIsNull': true,
        'pmTree.bindId': bindId
      };

      const variableParams = {
        'pmTree.name': query.name,
        'pmTree.createBy': query.createBy,
        'pmTree.ft4': query.ft4,
        'pmTree.fv5': query.fv5,
        'pmTree.fv6': query.fv6,
        'pmTree.fv3': query.fv3,
        'pmTree.fv4': query.fv4,
        'pmTree.ft2': query.ft2
      };
      const filteredVariableParams = this.filterParams(variableParams);

      const params = { ...fixedParams, ...filteredVariableParams };
      this.downloadFile(fileType, params);
    },
    downloadFile(fileType, params) {
      const queryString = Object.keys(params)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&');

      const url = `${this.omOaAPi}/project/${fileType}?${queryString}`;
      let message = '处理中，'
      if (fileType === 'html') {
        message = '预览中，';
      } else if (fileType === 'xls') {
        message = '下载中，';
      }
      this.$message({
        message: message + `请稍后查看...`,
        type: 'success'
      })
      fetch(url, {
        headers: {
          'Authorization': `${getToken()}`
        }
      })
        .then(response => {
          if (!response.ok) {
            throw new Error('Network response was not ok');
          }
          return response.blob();
        })
        .then(blob => {
          const newUrl = window.URL.createObjectURL(blob);

          if (fileType === 'html') {
            window.open(newUrl, '_blank');
          } else if (fileType === 'xls') {
            downloadUrl(newUrl, '项目列表');
          }
        })
        .catch(error => {
          console.error('There was a problem with your fetch operation:', error);
        });
    },
    // 专门用于过滤参数
    filterParams(variableParams) {
      return Object.entries(variableParams).reduce((acc, [key, value]) => {
        if (this.isNotEmpty(value)) {
          acc[key] = typeof value === 'string' ? value.trim() : value;
        }
        return acc;
      }, {});
    },
    // 检查给定值是否为空
    isNotEmpty(value) {
      const type = typeof value;
      switch (type) {
        case 'string':
          return value.trim() !== '';
        case 'object':
          return value !== null && (
            Array.isArray(value) ? value.length > 0 : Object.keys(value).length > 0
          );
        default:
          return value !== null && value !== undefined;
      }
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
