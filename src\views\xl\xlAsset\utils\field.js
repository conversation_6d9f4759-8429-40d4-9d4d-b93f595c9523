// 项目列表所有权限
export const permission = {
  add: ['admin', 'omAsset:add'],
  edit: ['admin', 'omAsset:edit'],
  del: ['admin', 'omAsset:del'],
  upload: ['admin', 'omAsset:importXlsWithRule'],
  updateT: ['admin', 'omAsset:updateFormStruct'],
  updateR: ['admin', 'omAsset:updateRelation']
  // updateG: ['admin', 'omAsset:toRedisGeoIndex']
}

// 项目列表表头
export const tableHeader = [
  { label: '序号', prop: '0', fixed: 'left', align: 'left', width: 80 },
  { label: '融合后点位', prop: 'fv4', align: 'left', width: 100 },
  { label: '立杆位置', prop: 'title', align: 'left', width: 180 },
  { label: '总点位数量', prop: 'fv6', width: 120, align: 'left' },
  { label: '摄像机类型', prop: 'fv5', width: 120, align: 'left' },
  { label: '类型对应数量', prop: 'fv7', width: 120, align: 'left' },
  { label: '设备厂商', prop: 'fv10', width: 120, align: 'left' },
  { label: '设备型号', prop: 'fv11', width: 120, align: 'left' },
  { label: '派出所', prop: 'fv8', width: 120, align: 'left' },
  { label: '产权单位', prop: 'fv12', width: 120, align: 'left' },
  { label: '融合后链路', prop: 'fv9', width: 120, align: 'left' },
  { label: '盘点情况', prop: 'fv13', width: 120, align: 'left' },
  { label: '经度', prop: 'fv2', width: 120, align: 'left' },
  { label: '维度', prop: 'fv3', width: 120, align: 'left' },
  { label: '创建日期', prop: 'createTime', width: 150 }
]

// 定义按钮组
export const updateButtonsLists = [
  {
    id: '1',
    label: '导入资产',
    permission: permission.upload,
    fun: 'importProject',
    size: 'mini',
    className: [],
    icon: 'plus',
    type: 'primary'
  },
  {
    id: '2',
    label: '预览',
    permission: permission.upload,
    fun: 'exportProject',
    size: 'mini',
    className: [],
    icon: 'view',
    type: 'success',
    query: {
      fileType: 'html'
    }
  },
  {
    id: '3',
    label: '导出',
    permission: permission.upload,
    fun: 'exportProject',
    size: 'mini',
    className: [],
    icon: 'download',
    type: 'primary',
    query: {
      fileType: 'xls'
    }
  }
]

// 详情表头
export const tableHeaderDetail = [
  { label: '摄像头', prop: 'fv5', fixed: 'left', align: 'left', width: 80 },
  { label: '设备厂商', prop: 'fv10', align: 'left', width: 100 },
  { label: '设备型号', prop: 'fv11', align: 'left', width: 180 },
  { label: '产权单位', prop: 'fv12', width: 120, align: 'left' },
  { label: '融合后链路', prop: 'fv9', width: 120, align: 'left' },
  { label: '状态', prop: 'fv13', width: 120, align: 'left' }
]
// 联合key
export const allKey = {
  '1': {
    target: '2',
    comType: 'fv4',
    targetType: 'title',
    list: 'pointList'
  },
  '2': {
    target: '1',
    comType: 'title',
    targetType: 'fv4',
    list: 'positionList'
  }
}
