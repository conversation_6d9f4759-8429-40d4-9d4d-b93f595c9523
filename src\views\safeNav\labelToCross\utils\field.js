// 项目列表所有权限
export const permission = {
  add: ['admin', 'omAssetTag:add'],
  edit: ['admin', 'omAssetTag:edit'],
  del: ['admin', 'omAssetTag:del'],
  upload: ['admin', 'omAssetTag:importXlsWithRule'],
  updateT: ['admin', 'omAssetTag:updateFormStruct'],
  updateR: ['admin', 'omAssetTag:updateRelation']
  // updateG: ['admin', 'omAssetTag:toRedisGeoIndex']
}

// 项目列表表头
export const tableHeader = [
  // { label: '序号', prop: '0', fixed: 'left', align: 'left', width: 80 },
  { label: '顶级', prop: 'fv1', align: 'left', width: 100 },
  { label: '标签', prop: 'fv2', width: 100, align: 'left' },
  { label: '路口编号', prop: 'fv3', align: 'left', width: 100 },
  { label: '路口名称', prop: 'fv4', width: 180, align: 'left' },
  { label: '创建日期', prop: 'createTime', width: 150 }
]

// 定义按钮组
export const updateButtonsLists = [
  // {
  //   id: '1',
  //   label: '导入',
  //   permission: permission.upload,
  //   fun: 'importProject',
  //   size: 'mini',
  //   className: [],
  //   icon: 'plus',
  //   type: 'primary'
  // },
  // {
  //   id: '2',
  //   label: '预览',
  //   permission: permission.upload,
  //   fun: 'exportProject',
  //   size: 'mini',
  //   className: [],
  //   icon: 'view',
  //   type: 'success',
  //   query: {
  //     fileType: 'html'
  //   }
  // },
  // {
  //   id: '3',
  //   label: '导出',
  //   permission: permission.upload,
  //   fun: 'exportProject',
  //   size: 'mini',
  //   className: [],
  //   icon: 'download',
  //   type: 'primary',
  //   query: {
  //     fileType: 'xls'
  //   }
  // }
]

