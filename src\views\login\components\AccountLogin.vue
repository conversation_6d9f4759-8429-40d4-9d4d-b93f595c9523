<template>
  <div class="account-login">
    <!--<div class="account-login-title">账号登录</div>-->
    <el-form
      ref="loginForm"
      :model="loginForm"
      :rules="loginRules"
      class="login-form"
      label-position="left"
      label-width="0px"
    >
      <el-form-item prop="username">
        <el-input
          v-model="loginForm.username"
          auto-complete="off"
          placeholder="账号"
          type="text"
        >
          <svg-icon
            slot="prefix"
            class="el-input__icon input-icon"
            icon-class="user"
          />
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          auto-complete="off"
          placeholder="密码"
          show-password
          type="password"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon
            slot="prefix"
            class="el-input__icon input-icon"
            icon-class="password"
          />
        </el-input>
      </el-form-item>
      <el-form-item prop="code">
        <el-input
          v-model="loginForm.code"
          auto-complete="off"
          placeholder="验证码"
          style="width: 63%"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon
            slot="prefix"
            class="el-input__icon input-icon"
            icon-class="validCode"
          />
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode">
        </div>
      </el-form-item>
      <el-checkbox v-model="loginForm.rememberMe" style="margin:0 0 25px 0;">
        记住我
      </el-checkbox>
      <el-form-item style="width:100%;">
        <el-button
          :loading="loading"
          size="medium"
          style="width:100%;"
          type="primary"
          @click.native.prevent="handleLogin"
        >
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { getCodeImg } from '@/api/login';
import Cookies from 'js-cookie';
import { encrypt } from '@/utils/rsaEncrypt';
import Config from '@/settings';

export default {
  name: 'AccountLogin',
  data() {
    return {
      loginForm: {
        username: '',
        password: '',
        rememberMe: false,
        code: '',
        uuid: ''
      },
      loginRules: {
        username: [
          { required: true, trigger: 'blur', message: '用户名不能为空' }
        ],
        password: [
          { required: true, trigger: 'blur', message: '密码不能为空' }
        ],
        code: [{ required: true, trigger: 'change', message: '验证码不能为空' }]
      },
      loading: false,
      codeUrl: ''
    }
  },
  created() {
    // 获取验证码
    this.getCode();
    // 获取用户名密码等Cookie
    this.getCookie();
    // token 过期提示
    this.point();
  },
  methods: {
    getCode() {
      getCodeImg().then(res => {
        this.codeUrl = res.img;
        this.loginForm.uuid = res.uuid;
      });
    },
    getCookie() {
      const username = Cookies.get('username');
      let password = Cookies.get('password');
      const rememberMe = Cookies.get('rememberMe');
      // 保存cookie里面的加密后的密码
      this.cookiePass = password === undefined ? '' : password;
      password = password === undefined ? this.loginForm.password : password;
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password: password,
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
        code: ''
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        const user = {
          username: this.loginForm.username,
          password: this.loginForm.password,
          rememberMe: this.loginForm.rememberMe,
          code: this.loginForm.code,
          uuid: this.loginForm.uuid
        };
        if (user.password !== this.cookiePass) {
          user.password = encrypt(user.password);
        }
        if (valid) {
          this.loading = true;
          if (user.rememberMe) {
            Cookies.set('username', user.username, {
              expires: Config.passCookieExpires
            });
            Cookies.set('password', user.password, {
              expires: Config.passCookieExpires
            });
            Cookies.set('rememberMe', user.rememberMe, {
              expires: Config.passCookieExpires
            });
          } else {
            Cookies.remove('username');
            Cookies.remove('password');
            Cookies.remove('rememberMe');
          }
          this.$store
            .dispatch('Login', user)
            .then(() => {
              this.loading = false;
              this.$router.push({ path: this.redirect || '' });
            })
            .catch(() => {
              this.loading = false;
              this.getCode();
            });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    point() {
      const point = Cookies.get('point') !== undefined;
      if (point) {
        this.$notify({
          title: '提示',
          message: '当前登录状态已过期，请重新登录！',
          type: 'warning',
          duration: 5000
        });
        Cookies.remove('point');
      }
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.account-login {

	.account-login-title {
		width: 320px;
		margin: 0 auto 15px;
		font-size: 16px;
		text-align: center;
		color: #707070;
	}

	.login-form {
		//width: 320px;
	}

	.login-code {
		width: 33%;
		display: inline-block;
		height: 38px;
		float: right;

		img {
			cursor: pointer;
			vertical-align: middle;
		}
	}
}
</style>
