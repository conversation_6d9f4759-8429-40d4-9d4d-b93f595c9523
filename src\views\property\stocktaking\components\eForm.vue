<template>
  <div class="task-dialog">
    <el-dialog
      :before-close="cancelForm"
      :close-on-click-modal="false"
      title="盘库信息"
      :visible.sync="visible"
      append-to-body
      width="800px"
    >

      <div v-if="showFormData" class="text item task-content">
        <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="130px">
          <el-form-item label="库房名称">
            <span>{{ displayBasicData && displayBasicData.depot && displayBasicData.depot.title }}</span>
          </el-form-item>
          <el-form-item label="项目名称">
            <span>{{ displayBasicData && displayBasicData.pm && displayBasicData.pm.name }}</span>
          </el-form-item>
          <el-form-item label="设备">
            <span>{{ displayBasicData && displayBasicData.device && displayBasicData.device.name }}</span>
          </el-form-item>
          <el-form-item label="品牌">
            <span>{{ displayBasicData && displayBasicData.brand && displayBasicData.brand.name }}</span>
          </el-form-item>
          <el-form-item label="型号">
            <span>{{ displayBasicData && displayBasicData.model && displayBasicData.model.name }}</span>
          </el-form-item>
          <el-form-item label="盘库数量" prop="stockCount">
            <el-input-number
              v-model="ruleForm.stockCount"
              :disabled="viewOrEdit"
              :min="0"
              :precision="0"
              :step="1"
              controls-position="right"
              placeholder="请输入盘库数量"
              style="width: 220px"
            />
          </el-form-item>
        </el-form>
        <!-- <fm-generate-form
          :ref="'generateForm'"
          :data="formStruct"
          :preview="viewOrEdit"
          :remote="remoteFunc"
          :value="formData"
        /> -->
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="cancelForm">取消</el-button>
        <el-button
          v-if="!viewOrEdit"
          :disabled="submitDisabled"
          type="primary"
          @click="submitAction"
        >
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth';
import { mapGetters } from 'vuex';
import amStockCount from '@/api/property/amStockCount'
export default {
  components: {},
  data() {
    return {
      loading: false,
      visible: false,
      ruleForm: {
        stockCount: 0
      },
      displayBasicData: {},
      rules: {
        stockCount: [
          { required: true, message: '请输入盘库数量', trigger: 'blur' }
        ]
      },
      submitDisabled: false,
      viewOrEdit: false,
      showFormData: false,
      processStructureValue: {},
      formStruct: {},
      formData: {},
      remoteFunc: {
        funcGetToken(resolve) {
          resolve(getToken())
        }
      }
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  methods: {
    init(info) {
      this.visible = true;
      const { id, type } = info;
      if (type == '2') {
        this.viewOrEdit = true;
      } else {
        this.viewOrEdit = false;
      }
      this.getBasicData(id)
    },
    async getBasicData(id) {
      await amStockCount.get({ id, enabled: 1 }).then(res => {
        if (res && res.content) {
          const jsonData = res.content[0];
          // this.processStructureValue = jsonData;
          // this.formStruct = JSON.parse(jsonData.formStruct);
          // this.formData = JSON.parse(jsonData.formData);

          this.ruleForm = jsonData;
          this.displayBasicData = jsonData.basicData
          this.showFormData = true;
        }
      })
    },
    submitAction() {
      this.$refs['ruleForm'].validate((valid) => {
        if (!valid) {
          return false;
        } else {
          const subData = { ...this.ruleForm };
          const request = amStockCount.edit;
          this.submitDisabled = true
          request(subData).then(response => {
            this.$notify({
              title: `修改盘库成功`,
              type: 'success',
              duration: 2500
            })
            this.cancelForm();
            this.$emit('successAction')
          }).catch((e) => {
            console.log(e);
          }).finally(() => {
            this.submitDisabled = false
          })
        }
      })
    },
    // submitAction() {
    //   this.$refs['ruleForm'].validate((valid) => {
    //     if (!valid) {
    //       return false;
    //     } else {
    //       this.checkModule().then(res => {
    //         if (!res.flag) {
    //           return false;
    //         } else {
    //           const subData = { ...this.ruleForm, ...res.subData };
    //           const request = amStockCount.edit;
    //           this.submitDisabled = true
    //           request(subData).then(response => {
    //             this.$notify({
    //               title: `修改盘库成功`,
    //               type: 'success',
    //               duration: 2500
    //             })
    //             this.cancelForm();
    //             this.$emit('successAction')
    //           }).catch((e) => {
    //             console.log(e);
    //           }).finally(() => {
    //             this.submitDisabled = false
    //           })
    //         }
    //       })
    //     }
    //   })
    // },
    // async checkModule() {
    //   const subData = {
    //     bindId: this.$config.stocktaking_key.bindId,
    //     formData: null,
    //     formStruct: null,
    //     formBindToVar: false,
    //     relation: this.processStructureValue.relation
    //   };
    //   if (this.showFormData) {
    //     const p = await this.$refs['generateForm'].getData().then(values => {
    //       subData.formData = JSON.stringify(values);
    //       subData.formStruct = JSON.stringify(this.formStruct);
    //       return {
    //         flag: true,
    //         subData
    //       };
    //     }).catch(() => {
    //       return {
    //         flag: false
    //       };
    //     })
    //     return p;
    //   } else {
    //     return Promise.resolve({
    //       subData,
    //       flag: true
    //     });
    //   }
    // },
    cancelForm() {
      this.visible = false;
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.el-picker-panel__footer .el-button--text.el-picker-panel__link-btn {
  display: none;
}
</style>

<style lang="scss" rel="stylesheet/scss">

.no-atTheMoment {
  .el-picker-panel__footer .el-button--text.el-picker-panel__link-btn {
    display: none;
  }
}
</style>
