<template>
  <div class="app-container">
    <el-form ref="form" :model="formData" label-width="270px">
      <el-form-item label="要使用的并发抓取器数量">
        <el-input-number v-model="formData.concurrency" :min="0" label="要使用的并发抓取器数量" style="width: 200px" />
      </el-form-item>
      <el-form-item label="要处理的并发输入数量">
        <el-input-number v-model="formData.parallelism" :min="0" label="要处理的并发输入数量" style="width: 200px" />
      </el-form-item>
      <el-form-item label="每个请求之间的请求延迟（以秒为单位）">
        <el-input-number v-model="formData.delay" :min="0" label="每个请求之间的请求延迟（以秒为单位）" style="width: 200px" />
      </el-form-item>
      <el-form-item label="每秒发送的最大请求数">
        <el-input-number v-model="formData.rate_limit" :min="0" label="每秒发送的最大请求数" style="width: 200px" />
      </el-form-item>
      <el-form-item label="每分钟发送的最大请求数">
        <el-input-number v-model="formData.rate_limit_minute" :min="0" label="每分钟发送的最大请求数" style="width: 200px" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { delArrInvalid } from '@/views/system/timing/utils/formate'
export default {
  name: 'RateLimitConfig',

  data() {
    return {
      formData: {
        'concurrency': null,
        'parallelism': null,
        'delay': null,
        'rate_limit': null,
        'rate_limit_minute': null
      }
    };
  },

  mounted() {

  },

  methods: {
    getFormData() {
      return {
        rate_limit: delArrInvalid(this.formData)
      };
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
