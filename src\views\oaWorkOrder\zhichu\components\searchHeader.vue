<template>
  <div v-if="crud.props.searchToggle">
    <!-- 搜索 -->
    <el-input
      v-model="query.name"
      class="filter-item"
      clearable
      placeholder="请输入项目名称搜索"
      size="small"
      style="width: 180px;"
      @keyup.enter.native="crud.toQuery"
    />
    <!-- <el-select
      v-model="query.createBy"
      :loading="selectLoading"
      :remote-method="remoteSelectUsers"
      class="filter-item"
      clearable
      debounce="500"
      filterable
      placeholder="请输入项目负责人"
      remote
      reserve-keyword
      size="small"
      style="width: 180px"
    >
      <el-option
        v-for="item in userList"
        :key="item.id"
        :label="item.username"
        :value="item.username"
      />
    </el-select>
    <self-select
      :options="dict.department"
      :select-value="query.ft4"
      :tags="1"
      class="filter-item"
      clearable
      placeholder="请选择所属部门"
      size="small"
      style="width: 200px;"
      @selectChange="(val)=>selectChange(val, 'ft4')"
    />
    <el-input
      v-model="query.fv5"
      class="filter-item"
      clearable
      placeholder="请输入年份"
      size="small"
      style="width: 180px;"
      @keyup.enter.native="crud.toQuery"
    />
    <el-select
      v-model="query.fv2"
      class="filter-item"
      clearable
      placeholder="请选择项目类型"
      size="small"
      style="width: 180px"
      @change="crud.toQuery"
    >
      <el-option
        v-for="item in dict.project_info_type"
        :key="item.key"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <el-cascader
      v-model="categoryId"
      :options="categoryList"
      class="filter-item"
      clearable
      placeholder="请选择项目类别"
      size="small"
      style="width: 180px;"
      @change="changeCateGory"
    />
    <self-select
      :options="dict.project_info_stage"
      :select-value="query.fv3"
      :tags="2"
      class="filter-item"
      clearable
      placeholder="请选择项目阶段"
      size="small"
      style="width: 180px;"
      @selectChange="(val)=>selectChange(val, 'fv3')"
    />

    <self-select
      :options="dict.project_info_status"
      :select-value.sync="query.fv4"
      :tags="2"
      class="filter-item"
      clearable
      placeholder="请选择项目状态"
      size="small"
      style="width: 180px;"
      @selectChange="(val)=>selectChange(val, 'fv4')"
    />

    <el-select
      v-model="query.ft2"
      class="filter-item"
      clearable
      placeholder="请选择整改类型"
      size="small"
      style="width: 180px"
    >
      <el-option label="整改要求" value="0" />
      <el-option label="整改合格" value="1" />
      <el-option label="整改回复" value="2" />
    </el-select> -->
    <rrOperation />
  </div>
</template>

<script>
// import CRUD, { header } from '@crud/crud';
import { header } from '@crud/crud';
import rrOperation from '@crud/RR.operation';
// import { getByName } from '@/api/system/user';

export default {
  components: { rrOperation },
  mixins: [header()],
  props: {
    // permission: {
    //   type: Object,
    //   required: true
    // },
    // dict: {
    //   type: Object,
    //   required: true
    // },
    // categoryList: {
    //   type: Array,
    //   required: true
    // }
  },

  data() {
    return {
      userList: [],
      selectLoading: false,
      categoryId: ''
    }
  },
  computed: {
    // collapse() {
    //   return function(value) {
    //     if (value && value.length > 2) {
    //       return true
    //     }
    //     return false
    //   }
    // }
  },
  methods: {
    // selectChange(val, type) {
    //   this.crud.query[type] = val;
    //   this.crud.toQuery();
    // },
    // [CRUD.HOOK.beforeResetQuery]() {
    //   this.categoryId = ''
    // },
    // remoteSelectUsers(query) {
    //   if (query !== '') {
    //     this.selectLoading = true;
    //     const data = {
    //       enabled: 1,
    //       userName: query,
    //       size: 99
    //     }
    //     getByName(data).then(res => {
    //       if (res) {
    //         this.userList = res || [];
    //         this.selectLoading = false;
    //       } else {
    //         this.userList = [];
    //       }
    //     })
    //   } else {
    //     this.userList = [];
    //   }
    // },
    // changeCateGory(val) {
    //   this.query.fv6 = val[val.length - 1];
    // }
  }
}
</script>
<style lang="scss" rel="stylesheet/scss" scoped>
::v-deep .vue-treeselect__placeholder {
	line-height: 28px;
	font-size: 14px;
	margin-left: 5px;
}

::v-deep .vue-treeselect__control {
	height: 28px;

	.vue-treeselect__single-value {
		margin-left: 5px;
		line-height: 28px;
		color: #606266;
		font-size: 14px;
	}
}

::v-deep .vue-treeselect__input-container {
	line-height: 30px;
	height: 28px;
}

</style>
