<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input
          v-model="query.jobName"
          class="filter-item"
          clearable
          placeholder="输入任务名称搜索"
          size="small"
          style="width: 200px;"
          @keyup.enter.native="toQuery"
        />
        <date-range-picker v-model="query.createTime" class="date-item" />
        <rrOperation />
      </div>
      <crudOperation :permission="permission">
        <!-- 任务日志 -->
        <el-button
          slot="right"
          class="filter-item"
          icon="el-icon-tickets"
          size="mini"
          type="info"
          @click="doLog"
        >日志
        </el-button>
        <el-button
          slot="right"
          class="filter-item"
          icon="el-icon-tickets"
          size="mini"
          type="success"
          @click="doRun"
        >测试脚本
        </el-button>
      </crudOperation>
      <Log ref="log" />
      <!-- 任务日志 -->

      <scriptRun ref="scriptRun" />
    </div>
    <!--Form表单-->
    <el-dialog
      :before-close="crud.cancelCU"
      :close-on-click-modal="false"
      :title="crud.status.title"
      :visible.sync="crud.status.cu > 0"
      append-to-body
      width="730px"
    >
      <el-form ref="form" :inline="true" :model="form" :rules="rules" label-width="100px" size="small">
        <el-form-item label="任务名称" prop="jobName">
          <el-input v-model="form.jobName" style="width: 220px;" />
        </el-form-item>
        <el-form-item label="任务描述" prop="description">
          <el-input v-model="form.description" style="width: 220px;" />
        </el-form-item>
        <el-form-item label="Bean名称" prop="beanName">
          <el-input v-model="form.beanName" style="width: 220px;" />
        </el-form-item>
        <el-form-item label="执行方法" prop="methodName">
          <el-input v-model="form.methodName" style="width: 220px;" />
        </el-form-item>
        <el-form-item label="Cron表达式" prop="cronExpression">
          <el-input v-model="form.cronExpression" style="width: 220px;" />
        </el-form-item>
        <el-form-item label="子任务ID">
          <el-input v-model="form.subTask" placeholder="多个用逗号隔开，按顺序执行" style="width: 220px;" />
        </el-form-item>
        <el-form-item label="任务负责人" prop="personInCharge">
          <el-input v-model="form.personInCharge" style="width: 220px;" />
        </el-form-item>
        <el-form-item label="告警邮箱" prop="email">
          <el-input v-model="form.email" placeholder="多个邮箱用逗号隔开" style="width: 220px;" />
        </el-form-item>
        <el-form-item label="失败后暂停">
          <el-radio-group v-model="form.pauseAfterFailure" style="width: 220px">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="任务状态">
          <el-radio-group v-model="form.isPause" style="width: 220px">
            <el-radio :label="false">启用</el-radio>
            <el-radio :label="true">暂停</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="参数内容">
          <el-input v-model="form.params" rows="10" style="width: 556px;" type="textarea" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </div>
    </el-dialog>
    <!--表格渲染-->
    <el-table
      ref="table"
      v-loading="crud.loading"
      :data="crud.data"
      style="width: 100%;"
      @selection-change="crud.selectionChangeHandler"
    >
      <el-table-column :selectable="checkboxT" type="selection" width="55" />
      <el-table-column :show-overflow-tooltip="true" label="任务ID" prop="id" />
      <el-table-column :show-overflow-tooltip="true" label="任务名称" prop="jobName" />
      <el-table-column :show-overflow-tooltip="true" label="Bean名称" prop="beanName" />
      <el-table-column :show-overflow-tooltip="true" label="执行方法" prop="methodName" />
      <el-table-column :show-overflow-tooltip="true" label="参数" prop="params" />
      <el-table-column :show-overflow-tooltip="true" label="cron表达式" prop="cronExpression" />
      <el-table-column :show-overflow-tooltip="true" label="状态" prop="isPause" width="90px">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isPause ? 'warning' : 'success'">{{ scope.row.isPause ? '已暂停' : '运行中' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" label="描述" prop="description" width="150px" />
      <el-table-column :show-overflow-tooltip="true" label="创建日期" prop="createTime" width="136px" />
      <el-table-column :show-overflow-tooltip="true" align="center" label="当前配置" prop="type" width="136px">
        <template slot-scope="scope">
          {{ currrentP(scope.row.type) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="checkPer(['admin','timing:edit','timing:del'])"
        align="center"
        fixed="right"
        label="操作"
        width="260px"
      >
        <template slot-scope="scope">
          <el-button
            v-permission="['admin','timing:edit']"
            size="mini"
            style="margin-left: -2px"
            type="text"
            @click="goDetail(scope.row,1)"
          >详情配置
          </el-button>
          <el-button
            v-permission="['admin','timing:edit']"
            size="mini"
            style="margin-left: -2px"
            type="text"
            @click="goDetail(scope.row,2)"
          >简单配置
          </el-button>
          <el-button
            v-permission="['admin','timing:edit']"
            size="mini"
            style="margin-left: 3px;"
            type="text"
            @click="crud.toEdit(scope.row)"
          >编辑
          </el-button>
          <el-button
            v-permission="['admin','timing:edit']"
            size="mini"
            style="margin-left: -2px"
            type="text"
            @click="execute(scope.row.id)"
          >执行
          </el-button>
          <el-button
            v-permission="['admin','timing:edit']"
            size="mini"
            style="margin-left: 3px"
            type="text"
            @click="updateStatus(scope.row.id,scope.row.isPause ? '恢复' : '暂停')"
          >
            {{ scope.row.isPause ? '恢复' : '暂停' }}
          </el-button>
          <el-popover
            :ref="scope.row.id"
            v-permission="['admin','timing:del']"
            placement="top"
            width="200"
          >
            <p>确定停止并删除该任务吗？</p>
            <div style="text-align: right; margin: 0">
              <el-button size="mini" type="text" @click="$refs[scope.row.id].doClose()">取消</el-button>
              <el-button :loading="delLoading" size="mini" type="primary" @click="delMethod(scope.row.id)">确定</el-button>
            </div>
            <el-button slot="reference" size="mini" type="text">删除</el-button>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination />
  </div>
</template>

<script>
import crudJob from '@/api/system/timing'
import Log from './log'
import scriptRun from './components/scriptRun.vue'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import DateRangePicker from '@/components/DateRangePicker'

const defaultForm = {
  id: null,
  jobName: null,
  subTask: null,
  beanName: null,
  methodName: null,
  params: null,
  cronExpression: null,
  pauseAfterFailure: true,
  isPause: false,
  personInCharge: null,
  email: null,
  description: null
}
export default {
  name: 'Timing',
  components: { Log, pagination, crudOperation, rrOperation, DateRangePicker, scriptRun },
  cruds() {
    return CRUD({ title: '定时任务', url: 'spider/api/jobs', crudMethod: { ...crudJob }})
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      delLoading: false,
      permission: {
        add: ['admin', 'timing:add'],
        edit: ['admin', 'timing:edit'],
        del: ['admin', 'timing:del']
      },
      tableData: [],
      rules: {
        jobName: [
          { required: true, message: '请输入任务名称', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入任务描述', trigger: 'blur' }
        ],
        beanName: [
          { required: true, message: '请输入Bean名称', trigger: 'blur' }
        ],
        methodName: [
          { required: true, message: '请输入方法名称', trigger: 'blur' }
        ],
        cronExpression: [
          { required: true, message: '请输入Cron表达式', trigger: 'blur' }
        ],
        personInCharge: [
          { required: true, message: '请输入负责人名称', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    currrentP() {
      return (type) => {
        const typeList = ['暂无配置', '详细配置', '简单配置'];
        return typeList[type]
      }
    }
  },
  watch: {
    'crud.data': {
      handler(val, oldVal) {
        if (val && val.length && val.length > 0) {
          const newVal = val;
          let tableData = [];
          tableData = newVal.map(item => {
            let params = {}
            if (item.params) {
              params = JSON.parse(item.params)
            }
            if (params.hasOwnProperty('params')) {
              item.type = 2;
            } else if (params.hasOwnProperty('debug')) {
              item.type = 1;
            } else {
              item.type = 0
            }
            return item;
          });
          this.tableData = tableData;
        } else {
          this.tableHeader = [];
          this.tableData = [];
        }
      },
      deep: true
    }
  },
  methods: {
    // 执行
    execute(id) {
      crudJob.execution(id).then(res => {
        this.crud.notify('执行成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log(err.response.data.message)
      })
    },
    // 改变状态
    updateStatus(id, status) {
      if (status === '恢复') {
        this.updateParams(id)
      }
      crudJob.updateIsPause(id).then(res => {
        this.crud.toQuery()
        this.crud.notify(status + '成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log(err.response.data.message)
      })
    },
    updateParams(id) {
      console.log(id)
    },
    delMethod(id) {
      this.delLoading = true
      crudJob.del([id]).then(() => {
        this.delLoading = false
        this.$refs[id].doClose()
        this.crud.dleChangePage(1)
        this.crud.delSuccessNotify()
        this.crud.toQuery()
      }).catch(() => {
        this.delLoading = false
        this.$refs[id].doClose()
      })
    },
    // 显示日志
    doLog() {
      this.$refs.log.dialog = true
      this.$refs.log.doInit()
    },
    doRun() {
      this.$refs.scriptRun.dialog = true
    },
    checkboxT(row, rowIndex) {
      return row.id !== 1
    },
    goDetail(data, type) {
      const query = {
        id: data.id,
        type
      }
      this.$router.push({ name: 'ConfigDetail', query })
    }
  }
}
</script>
