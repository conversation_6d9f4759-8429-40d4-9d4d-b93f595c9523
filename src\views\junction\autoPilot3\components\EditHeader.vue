<template>
  <div class="edit-header">
    <el-input
      v-model="header.prop"
      v-auto-focus
      class="edit-cell"
      size="mini"
      @blur="handleInputBlur"
      @change="() => handleCellChange(header.prop)"
    />
  </div>
</template>

<script>
export default {
  name: '',
  components: {},

  props: {
    header: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {}
  },

  computed: {},
  watch: {},
  created() {
  },
  methods: {
    handleInputBlur() {

    },
    handleCellChange() {

    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.edit-header {
	position: relative;

	.edit-cell {
		position: absolute;
		z-index: 999;
		width: 180px;
		text-align: left;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
	}
}

</style>
