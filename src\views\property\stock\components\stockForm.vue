<template>
  <div class="task-dialog">
    <el-dialog
      :before-close="cancelForm"
      :close-on-click-modal="false"
      title="修改库存"
      :visible.sync="visible"
      append-to-body
      width="800px"
    >

      <div v-if="showFormData" class="text item task-content">
        <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="130px">
          <el-form-item label="库房名称">
            <span>{{ displayBasicData && displayBasicData.depot && displayBasicData.depot.title }}</span>
          </el-form-item>
          <el-form-item label="项目名称">
            <span>{{ displayBasicData && displayBasicData.pm && displayBasicData.pm.name }}</span>
          </el-form-item>
          <el-form-item label="设备">
            <span>{{ displayBasicData && displayBasicData.device && displayBasicData.device.name }}</span>
          </el-form-item>
          <el-form-item label="品牌">
            <span>{{ displayBasicData && displayBasicData.brand && displayBasicData.brand.name }}</span>
          </el-form-item>
          <el-form-item label="型号">
            <span>{{ displayBasicData && displayBasicData.model && displayBasicData.model.name }}</span>
          </el-form-item>
          <el-form-item label="库存数量" prop="stockAmount">
            <el-input-number
              v-model="ruleForm.stockAmount"
              :min="0"
              :precision="0"
              :step="1"
              controls-position="right"
              placeholder="请输入库存数量"
              style="width: 220px"
            />
          </el-form-item>
        </el-form>
        <!-- <fm-generate-form
          :ref="'generateForm'"
          :data="formStruct"
          :preview="viewOrEdit"
          :remote="remoteFunc"
          :value="formData"
        /> -->
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="cancelForm">取消</el-button>
        <el-button
          :disabled="submitDisabled"
          type="primary"
          @click="submitAction"
        >
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth';
import { mapGetters } from 'vuex';
import amStock from '@/api/property/amStock'
export default {
  components: {},
  data() {
    return {
      visible: false,
      ruleForm: {
        stockAmount: 0
      },
      displayBasicData: {},
      rules: {
        stockAmount: [
          { required: true, message: '请输入库存数量', trigger: 'blur' }
        ]
      },
      submitDisabled: false,
      viewOrEdit: false,
      showFormData: false,
      remoteFunc: {
        funcGetToken(resolve) {
          resolve(getToken())
        }
      }
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  methods: {
    init(info) {
      this.visible = true;
      const { id } = info;
      this.bindId = this.$config.stock_key.bindId
      this.getBasicData(id)
    },
    async getBasicData(id) {
      await amStock.get({ id, enabled: 1 }).then(res => {
        if (res && res.content) {
          const jsonData = res.content[0];
          this.ruleForm = jsonData;
          this.displayBasicData = jsonData.basicData
          this.showFormData = true;
        }
      })
    },
    submitAction() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          const subData = { ...this.ruleForm };
          const request = amStock.edit;
          this.submitDisabled = true
          request(subData).then(response => {
            this.$notify({
              title: `修改库存成功`,
              type: 'success',
              duration: 2500
            })
            this.cancelForm();
            this.$emit('successAction')
          }).catch((e) => {
            console.log(e);
          }).finally(() => {
            this.submitDisabled = false
          })
        } else {
          this.submitDisabled = false
          return false
        }
      });
    },
    cancelForm() {
      this.visible = false;
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.el-picker-panel__footer .el-button--text.el-picker-panel__link-btn {
  display: none;
}
</style>

<style lang="scss" rel="stylesheet/scss">

.no-atTheMoment {
  .el-picker-panel__footer .el-button--text.el-picker-panel__link-btn {
    display: none;
  }
}
</style>
