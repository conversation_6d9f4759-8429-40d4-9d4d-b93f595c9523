<template>
  <div class="app-container">
    <el-card class="box-card" style="margin-top: 10px">
      <div slot="header" class="clearfix">
        <span>雪亮巡检信息</span>
      </div>
      <el-form ref="elFormRef" :model="elForm" :rules="elRules" label-width="150px">
        <el-form-item label="点位编号" prop="fv4">
          <template v-if="viewOrEdit">
            <!-- <el-input v-model="elForm.fv4" disabled /> -->
            <el-autocomplete
              v-model="elForm.fv4"
              :debounce="800"
              :disabled="disabledAsset"
              :fetch-suggestions="querySearchAsync"
              placeholder="请输入点位编号"
              style="width:600px"
              @select="handleSelect"
            />
          </template>
          <template v-else>
            <span>{{ elForm.fv4 }}</span>
          </template>
        </el-form-item>
        <el-form-item label="立杆位置" prop="title">
          <template v-if="viewOrEdit">
            <el-input v-model="elForm.title" disabled style="width:600px" />
          </template>
          <template v-else>
            <span>{{ elForm.title }}</span>
          </template>
        </el-form-item>
        <el-divider />
        <el-form-item>
          <template v-if="viewOrEdit">
            <el-radio-group v-model="selectAllStatus" @change="selectAll">
              <el-radio :label="'正常'">全选正常</el-radio>
              <el-radio :label="'异常'">全选异常</el-radio>
            </el-radio-group>
          </template>
        </el-form-item>
        <el-row>
          <el-col v-for="(item, index) in inspectionItems" :key="index" :span="24">
            <el-form-item :label="itemLabel(item)" :prop="item.field">
              <template v-if="!item.frontDevices">
                <template v-if="viewOrEdit">
                  <el-radio-group v-model="elForm[item.field]" @change="resetSelectAllStatus">
                    <el-radio :label="'正常'">正常&emsp;&emsp;</el-radio>
                    <el-radio :label="'异常'">异常&emsp;&emsp;</el-radio>
                  </el-radio-group>
                </template>
                <template v-else>
                  <span>{{ elForm[item.field] }}</span>
                </template>
              </template>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="巡检照片">
          <div style="display: flex; flex-wrap: wrap;">
            <template v-if="viewOrEdit">
              <PhotoUpload v-model="elForm.ft1" title="点位整体" :required="true" style="margin-bottom: 10px; margin-right: 10px;" :disabled="viewOrEdit" pname="ft1" :multiple="false" @update:fileList="handleFileListUpdate('ft1', $event)" />
              <PhotoUpload v-model="elForm.ft2" title="设备箱外" :required="true" style="margin-bottom: 10px; margin-right: 10px;" :disabled="viewOrEdit" pname="ft2" :multiple="false" @update:fileList="handleFileListUpdate('ft2', $event)" />
              <PhotoUpload v-model="elForm.ft3" title="设备箱内" :required="true" style="margin-bottom: 10px; margin-right: 10px;" :disabled="viewOrEdit" pname="ft3" :multiple="false" @update:fileList="handleFileListUpdate('ft3', $event)" />
              <PhotoUpload v-model="elForm.ft4" title="巡检人员" :required="true" style="margin-bottom: 10px; margin-right: 10px;" :disabled="viewOrEdit" pname="ft4" :multiple="false" @update:fileList="handleFileListUpdate('ft4', $event)" />
            </template>
            <template v-else>
              <div v-for="(photoArray, index) in [{ title: '点位整体', photos: elForm.ft1 }, { title: '设备箱外', photos: elForm.ft2 }, { title: '设备箱内', photos: elForm.ft3 }, { title: '巡检人员', photos: elForm.ft4 }]" :key="index" style="margin-bottom: 10px; margin-right: 10px; text-align: center;">
                <template v-if="photoArray.photos[0] && (photoArray.photos[0].url || (photoArray.photos[0].response && photoArray.photos[0].response.url))">
                  <img :src="photoArray.photos[0].url || photoArray.photos[0].response.url" style="width: 108px;height:108px; margin-bottom: 5px;">
                  <div>{{ photoArray.title }}</div>
                </template>
              </div>
            </template>
          </div>
        </el-form-item>
        <el-form-item label="异常（如有）">
          <template v-if="viewOrEdit">
            <PhotoUpload v-model="elForm.ft5" title="异常（如有）" :required="false" style="width: 100%;" :disabled="viewOrEdit" pname="ft5" :multiple="true" @update:fileList="handleFileListUpdate('ft5', $event)" />
          </template>
          <template v-else>
            <div style="display: flex; flex-wrap: wrap;">
              <div v-for="(photo, index) in elForm.ft5" :key="index" style="margin-bottom: 10px; margin-right: 10px;">
                <template v-if="photo.url || (photo.response && photo.response.url)">
                  <img :src="photo.url || photo.response.url" style="width: 108px;height:108px;">
                </template>
              </div>
            </div>
          </template>
        </el-form-item>
        <el-form-item label="情况描述" prop="ft6">
          <template v-if="viewOrEdit">
            <el-input v-model="elForm.ft6" type="textarea" :autosize="{ minRows: 5, maxRows: 20}" />
          </template>
          <template v-else>
            <span>{{ elForm.ft6 }}</span>
          </template>
        </el-form-item>
        <el-form-item label="巡检人" prop="fv15">
          <template v-if="viewOrEdit">
            <el-input v-model="elForm.fv15" />
          </template>
          <template v-else>
            <span>{{ elForm.fv15 }}</span>
          </template>
        </el-form-item>
      </el-form>
      <hr style="background-color: #d9d9d9; border:0; height:1px;">
      <div class="text item" style="text-align: center; margin-top: 18px;">
        <el-button v-if="viewOrEdit" :disabled="submitDisabled" type="primary" @click="submitAction">提交</el-button>
        <el-button @click="concelForm">{{ viewOrEdit ? '取 消' : '返回' }}</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import omInspect from '@/api/parts/inspect'
import PhotoUpload from './components/PhotoUpload.vue';
import extendBindTpl from '@/api/system/extendBindTpl';
import { getInspectionWithX, getOmAssetSmall } from '@/api/parts/assets'
export default {
  name: 'XlInspectsForm',
  components: { PhotoUpload },

  props: {},
  data() {
    return {
      formStruct: {},
      formData: {},
      remoteFunc: {},
      submitDisabled: false,
      viewOrEdit: false,
      showFormData: false,
      processStructureValue: {},
      elForm: { // 名字避开count、detail、fv4、fv6、fv8、fv9、location、title 因为inspectionWithX接口获取到的数据要和form合并就会重名
        fv4: '', // 点位编号
        title: '', // 立杆位置
        fv10: '', // 设备箱
        fv11: '', // 前端设备状态
        fv12: '', // 供电状态
        fv13: '', // 图像有无遮挡
        fv14: '', // 网络线路状态
        fv15: '', // 巡检人
        ft1: [],
        ft2: [],
        ft3: [],
        ft4: [],
        ft5: [],
        ft6: ''// 情况描述
      },
      elRules: {
        fv4: [{ required: true, message: '请输入点位编号', trigger: 'blur' }],
        title: [{ required: true, message: '请输入立杆位置', trigger: 'blur' }],
        fv10: [{ required: true, message: '请选择设备箱状态', trigger: 'change' }],
        fv11: [{ required: true, message: '请选择前端设备状态', trigger: 'change' }],
        fv12: [{ required: true, message: '请选择供电状态', trigger: 'change' }],
        fv13: [{ required: true, message: '请选择图像有无遮挡状态', trigger: 'change' }],
        fv14: [{ required: true, message: '请选择网络线路状态', trigger: 'change' }],
        fv15: [{ required: true, message: '请输入巡检人', trigger: 'blur' }],
        ft1: [{ required: true, validator: this.validateUpload, trigger: 'change' }],
        ft2: [{ required: true, validator: this.validateUpload, trigger: 'change' }],
        ft3: [{ required: true, validator: this.validateUpload, trigger: 'change' }],
        ft4: [{ required: true, validator: this.validateUpload, trigger: 'change' }]
      },
      initialInspectionItems: [
        { name: '设备箱', field: 'fv10', count: 0 },
        { name: '总摄像头数', count: 0, frontDevices: true },
        { name: '前端设备状态', field: 'fv11', count: 0 },
        { name: '供电状态', field: 'fv12', count: 0 },
        { name: '图像有无遮挡', field: 'fv13', count: 0 },
        { name: '网络线路状态', field: 'fv14', count: 0 }
      ],
      inspectionItems: [],
      dynamicInspectionItems: [], // 动态生成的部分
      selectAllStatus: '', // 用于全选状态的变量
      assetJson: {},
      totalCamerasText: '总摄像头数',
      omAssetID: null,
      overallStatus: null,
      disabledAsset: false,
      asset: {}
    }
  },
  computed: {
    totalCameras() {
      return this.dynamicInspectionItems.reduce((total, item) => {
        return total + item.count;
      }, 0);
    }
  },
  watch: {},
  created() {
    this.init()
  },
  methods: {
    itemLabel(item) {
      // 总摄像头数
      if (item.name === this.totalCamerasText) {
        return `${item.name}（${this.totalCameras}）`;
      } else {
        return item.count > 0 ? `${item.name}（${item.count}）` : item.name;
      }
    },
    selectAll() {
      // 全选处理
      const status = this.selectAllStatus;
      this.inspectionItems.forEach(item => {
        if (!item.frontDevices) {
          this.elForm[item.field] = status;
        }
      });
      // this.updateTotalCamerasStatus();
    },
    init() {
      // 重置 inspectionItems
      this.inspectionItems = [...this.initialInspectionItems];
      const { rowId, omAssetID, fv4, type } = this.$route.query
      this.viewOrEdit = type !== '1'; // 转换为布尔值
      this.omAssetID = omAssetID;
      if (fv4) {
        this.disabledAsset = true;// 有点位编号的时候
      }
      if (rowId) {
        // 编辑或者查看
        this.getContent(rowId)
      } else {
        // 添加
        this.getProcessNodeList();
        this.getAssetsJson(omAssetID, fv4);
      }
    },
    getAssetsJson(omAssetID, fv4) {
      // 获取点位信息
      this.assetJson = {};
      const { bindId, categoryId } = this.$config.xl_asset_key
      getInspectionWithX({ id: omAssetID, fv4: fv4, bindId, categoryId }).then(res => {
        if (res && res.content && res.content[0]) {
          this.assetJson = res.content[0];
          this.elForm.fv4 = this.assetJson.fv4;// 点位编号
          this.elForm.title = this.assetJson.title;// 立杆位置
          this.updateDynamicInspectionItems(this.assetJson.detail);
        }
      })
    },
    updateDynamicInspectionItems(assetDetail) {
      // 处理前端设备数据，根据接口获取，动态插入到总摄像头数后面
      let detailArray = [];
      this.dynamicInspectionItems = [];
      if (assetDetail) {
        try {
          detailArray = JSON.parse(assetDetail);
        } catch (e) {
          console.error('Failed to parse assetDetail:', e);
          detailArray = [];
        }
      } else {
        console.warn('assetDetail is undefined or empty');
      }

      this.dynamicInspectionItems = detailArray.map(detail => {
        return {
          name: detail.fv5,
          count: parseInt(detail.fv7),
          frontDevices: true
        };
      });
      // 重置 inspectionItems
      this.inspectionItems = [...this.initialInspectionItems];

      // 插入动态项到总摄像头数后面
      const totalCameraIndex = this.inspectionItems.findIndex(item => item.name === this.totalCamerasText);
      if (totalCameraIndex !== -1) {
        this.inspectionItems.splice(totalCameraIndex + 1, 0, ...this.dynamicInspectionItems);
      }
    },
    getContent(id) {
      omInspect.get({ id, enabled: 1 }).then(res => {
        if (res && res.content && res.content.length) {
          const jsonData = res.content[0];
          this.processStructureValue = jsonData;

          // 安全解析 JSON 数据的函数
          const safeParseJSON = (jsonString, defaultValue = []) => {
            try {
              return JSON.parse(jsonString) || defaultValue;
            } catch (e) {
              console.error('Failed to parse JSON:', e);
              return defaultValue;
            }
          };

          // 设置表单基本信息
          this.elForm = {
            ...jsonData,
            ft1: safeParseJSON(jsonData.ft1),
            ft2: safeParseJSON(jsonData.ft2),
            ft3: safeParseJSON(jsonData.ft3),
            ft4: safeParseJSON(jsonData.ft4),
            ft5: safeParseJSON(jsonData.ft5)
          };

          // 处理动态检查项
          this.updateDynamicInspectionItems(jsonData.ft9);
        }
      });
    },
    getProcessNodeList() {
      const data = { id: this.$config.xl_inspection_key.bindId, enabled: 1 }
      extendBindTpl.get(data).then(res => {
        if (res && res.content && res.content.length) {
          this.processStructureValue = res.content[0];
          const FormStruct = JSON.parse(this.processStructureValue.extendTpl.formStruct);
          this.formStruct = FormStruct;
        }
        // this.showFormData = true;
      });
    },
    submitAction() {
      this.$refs['elFormRef'].validate((valid) => {
        if (!valid) {
          return false;
        } else {
          this.checkModule().then(res => {
            if (!res.flag) {
              return false;
            } else {
              // const subData = { ...this.elForm, ...res.subData };
              const subData = { ...res.subData };
              let request = omInspect.add;
              let title = '添加'
              if (subData.id) {
                request = omInspect.edit;
                title = '编辑'
              }
              request(subData).then(response => {
                this.$notify({
                  title: `${title}成功`,
                  type: 'success',
                  duration: 2500
                })
                this.concelForm();
              }).catch((e) => {
                console.log(e);
              })
            }
          })
        }
      })
    },
    async checkModule() {
      const isEditing = !!this.elForm.id; // 判断是否是编辑模式
      let subData = {
        enabled: 1,
        bindId: this.$config.xl_inspection_key.bindId,
        categoryId: this.$config.xl_inspection_key.categoryId,
        formData: null,
        formStruct: null,
        formBindToVar: 2,
        relation: this.processStructureValue.relation,
        ...this.elForm,
        status: this.updateOverallStatus(), // 整体状态
        asset: {
          id: this.omAssetID
        }
      };

      if (isEditing) {
        // 编辑模式下，直接使用 this.elForm 数据
        subData = {
          ...this.elForm,
          status: this.updateOverallStatus() // 整体状态
        };
      } else {
        // 添加模式下，处理添加时需要的参数
        const { count, detail, location, fv4, fv6, fv8, fv9 } = this.assetJson;
        const newAssetJson = {
          fv1: count,
          ft9: detail, // 前端设备数量
          fv3: location,
          fv4: fv4,
          fv6: fv6,
          fv8: fv8,
          fv9: fv9
        };
        subData = {
          ...subData,
          ...newAssetJson
        };
      }

      return Promise.resolve({
        subData,
        flag: true
      });
    },
    updateOverallStatus() {
      // 根据每个选项的状态，处理整体状态
      const overallStatus = this.inspectionItems
        .filter(item => !item.frontDevices) // 排除掉frontDevices: true的项
        .some(item => this.elForm[item.field] !== '正常') // 检查是否有异常状态
        ? '异常' : '正常';
      return overallStatus;
    },
    concelForm() {
      const query = {
        bindId: this.$config.xl_inspection_key.bindId,
        categoryId: this.$config.xl_inspection_key.categoryId
      }
      this.$router.push({ name: 'XlInspects', query });
    },
    handleFileListUpdate(name, fileList) {
      // 处理上传图片组件返回的参数
      this.elForm[name] = fileList;
    },
    querySearchAsync(queryString, cb) {
      // 输入搜索下拉，设置点位编号和立杆位置
      if (queryString) {
        const data = {
          categoryId: this.$config.xl_asset_key.categoryId,
          enabled: 1,
          fv4: queryString
        }
        getOmAssetSmall(data).then(resOmAsset => {
          let options = resOmAsset.content || [];
          if (options && options.length) {
            options = options.map(item => {
              item.value = item.fv4;
              this.omAssetID = item.id;
              this.getAssetsJson(this.omAssetID, item.fv4);
              return item;
            })
          } else {
            this.ruleForm.fv4 = '';
            this.elForm.title = '';
            this.asset = {};
          }
          cb(options)
        });
      }
    },
    handleSelect(item) {
      this.asset = item;
    },
    validateUpload(rule, value, callback) {
      if (value.length === 0) {
        callback(new Error('请上传图片'));
      } else {
        callback();
      }
    },
    resetSelectAllStatus() {
      // 根据每项单选检查全选状态
      const allNormal = this.initialInspectionItems.every(item => {
        if (item.frontDevices) return true;
        return this.elForm[item.field] === '正常';
      });

      const allAbnormal = this.initialInspectionItems.every(item => {
        if (item.frontDevices) return true;
        return this.elForm[item.field] === '异常';
      });

      if (allNormal) {
        this.selectAllStatus = '正常';
      } else if (allAbnormal) {
        this.selectAllStatus = '异常';
      } else {
        this.selectAllStatus = '';
      }
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
