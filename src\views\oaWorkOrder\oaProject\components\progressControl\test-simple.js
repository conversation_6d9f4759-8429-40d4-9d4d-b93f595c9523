// 简单测试：验证只统计直接子级节点的逻辑

// 测试数据：目录-子目录-节点 的结构
const testData = [
  {
    id: 1,
    name: '根目录',
    type: 'catalog',
    children: [
      {
        id: 2,
        name: '子目录A',
        type: 'catalog',
        children: [
          {
            id: 3,
            name: '节点1',
            type: 'point'
          },
          {
            id: 4,
            name: '节点2',
            type: 'point'
          }
        ]
      },
      {
        id: 5,
        name: '子目录B',
        type: 'catalog',
        children: [
          {
            id: 6,
            name: '节点3',
            type: 'point'
          }
        ]
      }
    ]
  }
]

// 统计目录直接子级中的节点数量
function countNodesInCatalog(catalogNode) {
  if (!catalogNode.children || !Array.isArray(catalogNode.children)) {
    return 0
  }

  let count = 0
  catalogNode.children.forEach(child => {
    if (child.type && child.type !== 'catalog') {
      count++
    }
  })

  return count
}

// 处理树形数据
function processTreeData(treeData) {
  const processNode = (node) => {
    const processedNode = { ...node }

    if (processedNode.children && Array.isArray(processedNode.children)) {
      processedNode.children = processedNode.children.map(child => processNode(child))

      if (processedNode.type === 'catalog') {
        const nodeCount = countNodesInCatalog(processedNode)
        if (nodeCount > 0) {
          processedNode.name = `${processedNode.name} (${nodeCount})`
        }
      }
    }

    return processedNode
  }

  return treeData.map(node => processNode(node))
}

// 测试
console.log('原始数据:')
console.log(JSON.stringify(testData, null, 2))

const result = processTreeData(testData)
console.log('\n处理后的数据:')
console.log(JSON.stringify(result, null, 2))

console.log('\n验证结果:')
console.log('根目录（直接子级都是目录，不显示数量）:', result[0].name)
console.log('子目录A（直接子级有2个节点）:', result[0].children[0].name)
console.log('子目录B（直接子级有1个节点）:', result[0].children[1].name)
