import crudMenu from '@/api/system/menu'
export function getBindUrl(path) {
  return new Promise(resolve => {
    crudMenu.getMenus({ blurry: path }).then((res) => {
      let ID;
      if (res?.content?.length) {
        console.log(res.content[0]);
        const path = res.content[0].path
        if (path.indexOf('?') != -1) {
          ID = path.split('?')[1].split('=')[1];
        }
        resolve(ID)
      }
    });
  })
}
