<template>
  <div class="just-com">

    <div class="just-contant">
      <div class="just-com-left">
        <project-trees
          ref="projectRef"
          :current-planning="currentPlanning"
          @addTask="addTask"
          @onClickLabel="onClickLabel"
          @onDispatch="onDispatch"
          @uploadFile="onClickUploadFile"
        />
      </div>
      <div class="just-com-right"><!--文件数据-->
        <document-list v-if="currentPlanning==3" ref="documentRef" />
        <!--任务列表-->
        <task-list v-if="currentPlanning==1" ref="taskListRef" @toEdit="addTask" />
      </div>

    </div>

    <!--树直接上传文件-->
    <upload-file ref="uploadRef" @success="successAction" />
    <!--派单选择  -->
    <dispatch ref="dispatch" />
    <!--任务弹框-->
    <task-dialog
      v-if="taskDialogVisible"
      ref="taskDialogRef"
      @successAction="successAction"
    />
  </div>
</template>

<script>
import TaskDialog from '@/views/oaWorkOrder/oaProject/components/task/taskDialog.vue';
import uploadFile from '@/views/oaWorkOrder/oaProject/components/justCom/uploadFile.vue';
import DocumentList from '@/views/oaWorkOrder/oaProject/components/document/index.vue';
import ProjectTrees from '@/views/oaWorkOrder/oaProject/components/directory/projectTrees.vue';
import dispatch from '@/views/oaWorkOrder/oaProject/components/justCom/dispatch.vue';
import TaskList from '@/views/oaWorkOrder/oaProject/components/task/TaskList.vue';

export default {
  name: 'JustCom',
  components: { dispatch, ProjectTrees, DocumentList, TaskList, uploadFile, TaskDialog },

  props: {
    currentPlanning: {
      type: Number,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      taskDialogVisible: false,
      currentDirectory: {},
      currentNode: {}
    }
  },

  computed: {},
  watch: {
    'currentPlanning': {
      handler(val, oldVal) {
        if (val === 1 || val === 3) {
          console.log(555, '<===>', '555')
          this.loadTrees()
        }
      }
    }
  },
  methods: {
    async addTask(info) {
      this.taskDialogVisible = true
      await this.$nextTick()
      this.$refs.taskDialogRef.init(info)
    },
    onClickUploadFile(data, node) {
      this.currentNode = node
      this.$refs.uploadRef.init(data, node);
    },
    // 上传成功与添加任务成功
    successAction(data, subData) {
      console.log(data, subData, '<===>', 'data')
      if (this.currentPlanning == 1) {
        this.taskDialogVisible = false;
        if (data.type === 3) {
          data = this.currentDirectory
        }
        this.loadTrees();
        this.$refs.taskListRef && this.$refs.taskListRef.upDataTableData(data)
      } else if (this.currentPlanning == 3) {
        // this.loadTrees();
        this.isUpdateDirectory(subData)
        this.$refs.documentRef && this.$refs.documentRef.upDataTableData(data)
      }
    },
    // 是否更新目录
    isUpdateDirectory(data) {
      const formData = JSON.parse(data.formData);
      if (formData.folder && formData.folder.length > 0) {
        this.currentNode.loaded = false;
        this.currentNode.expand();
      }
    },
    onClickLabel(data) {
      if (this.currentPlanning == 1) {
        this.currentDirectory = data
        this.$refs.taskListRef.upDataTableData(data)
      } else if (this.currentPlanning == 3) {
        this.currentDirectory = {}
        this.$refs.documentRef.upDataTableData(data)
      }
    },
    onDispatch(data) {
      this.$refs.dispatch.init(data.id)
    },
    loadTrees() {
      this.$refs.projectRef.loadProjectNode()
    }

  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
//.just-com {
//	position: relative;
//}
.just-contant {
	position: relative !important;
	display: flex;
	justify-content: space-between;
	max-height: calc(100vh - 240px);

}

.just-com-left {
	overflow-x: hidden;
	overflow-y: auto;
	width: 40%;
	margin-right: 20px;
}

.just-com-right {
	overflow-y: auto;
	width: 60%;
	position: sticky;
	top: 0;
	right: 0;
}
</style>
