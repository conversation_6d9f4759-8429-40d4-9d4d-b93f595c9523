// import { tableHeader } from './field'
import { isArray } from '@/utils/is'
import crudCategory from '@/api/system/category';
const getTagApi = 'api/omAsset/findAssetWithTagAndTop';
const getSmallApi = 'api/omAsset/autoPilot';
import { tableHeader } from './field'

/**
 * 格式化表格数据
 * @param {Array} val - 表格数据数组
 */
export function formatterTableData(val) {
  if (val && val.length) {
    let tableData = [];
    tableData = val.map(item => {
      const json = item.extend.data || {};
      const tagMap = item?.tagMap;
      let fv7 = ''
      try {
        // 尝试解析 item.fv7，如果解析失败会抛出异常
        fv7 = JSON.parse(item.fv7);
      } catch (error) {
        // 如果 JSON.parse 失败，意味着 fv7 可能已经是字符串了，直接赋值
        fv7 = item.fv7;
      }

      // 判断 fv7 是否为数组
      if (isArray(fv7)) {
        // 如果是数组，将其转换成逗号分隔的字符串
        fv7 = fv7.join(',');
      }

      return {
        ...json,
        ...item,
        fv7,
        ...tagMap
      };
    });
    return tableData;
  } else {
    return [];
  }
}

/**
 * 格式化表头
 * @param {Array} val - 表格数据数组
 */
export function formatterTableHeader(val, dynamicHeader) {
  const otherHeader = [];
  return [...tableHeader, ...dynamicHeader, ...otherHeader];
}

/**
 * 点击是否显示操作列
 * @param vueInstance
 * @returns {Promise<void>}
 */
export async function toggleSideBarFun(vueInstance) {
  vueInstance.operateShow = !vueInstance.operateShow;
  await vueInstance.$nextTick()
  vueInstance.$refs.table.doLayout()
}

export function mergeCell({ row, column, rowIndex, columnIndex, tableData }) {
  if (columnIndex === 1 || columnIndex === 2 || columnIndex === 3) {
    const arr = getSpanArray(tableData, 'fv4');
    return getSpan(arr, rowIndex);
  } else if (columnIndex === 4 || columnIndex === 5) {
    const arr = getSpanArray(tableData, 'fv4', 'fv5');
    return getSpan(arr, rowIndex);
  }
  return { rowspan: 1, colspan: 1 };
}

function getSpanArray(list, key1, key2) {
  const spanArray = [];
  for (let i = 0; i < list.length; i++) {
    if (i === 0) {
      spanArray.push({ row: 1, col: 1 });
    } else {
      const isSame = key2
        ? list[i][key1] === list[i - 1][key1] && list[i][key2] === list[i - 1][key2]
        : list[i][key1] === list[i - 1][key1];
      if (isSame) {
        spanArray.push({ row: 0, col: 0 });
        const index = spanArray.findIndex((_, idx) => list[idx][key1] === list[i][key1] && (!key2 || list[idx][key2] === list[i][key2]));
        spanArray[index].row++;
      } else {
        spanArray.push({ row: 1, col: 1 });
      }
    }
  }
  return spanArray;
}

function getSpan(arr, rowIndex) {
  return {
    rowspan: arr[rowIndex].row,
    colspan: arr[rowIndex].col
  };
}

/**
 * 获取级联分类数据
 */
export async function getCategoryList(categoryId, bindId) {
  const fetchChildren = async(categoryId) => {
    const json = {
      page: 0,
      size: 9999999999,
      sort: 'id, desc',
      pidIsNull: true,
      enabled: 1,
      bindId: bindId,
      pid: categoryId

    }
    const res = await crudCategory.getCategory(json);
    const data = res.content;

    if (!data || data.length === 0) {
      return [];
    }

    const children = await Promise.all(data.map(async(item) => {
      item.value = item.id;
      if (item.hasChildren) {
        item.children = await fetchChildren(item.id);
      }
      return item;
    }));

    return children;
  };

  try {
    return await fetchChildren(categoryId);
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

/**
 * 检查调用哪个接口然后调用
 */
export function checkAndTagToQuery(crud, selectedCategories, fieldsToReset) {
  const hasSelectedCategories = selectedCategories && selectedCategories.length > 0;
  if (hasSelectedCategories) {
    crud.url = getTagApi;
    resetAssetQuery(crud, fieldsToReset);
    crud.toQuery();
  } else {
    clearTagAssetToQuery(crud);
    crud.toQuery();
  }
}

/**
 * 清空标签的一大堆参数 使用资产接口调用
 */
export function clearTagAssetToQuery(crud) {
  const keysToClear = [
    'tag.fieldName', 'tag.values',
    'top.fieldName', 'top.values',
    // 'type.fieldName', 'type.values',
    'date.fieldName', 'date.values',
    'dateFormat', 'indexName'
  ];

  keysToClear.forEach(key => {
    if (Array.isArray(crud.query[key])) {
      crud.query[key] = [];
    } else {
      delete crud.query[key];
    }
  });

  // 清空其它的键值对
  for (const key in crud.query) {
    if (key.startsWith('tag.') || key.startsWith('top.') || key.startsWith('date.')) {
      delete crud.query[key];
    }
  }
  crud.url = getSmallApi;
}

/**
 * 重置搜索资产参数调用
 */
export function resetAssetQuery(crud, fieldsToReset = []) {
  const defaultQuery = { ...crud.defaultQuery };
  const query = crud.query;
  Object.keys(query).forEach(key => {
    if (fieldsToReset.includes(key)) {
      query[key] = defaultQuery[key]; // 清空匹配到的字段
    }
  });
}

/**
 * 格式化标签请求参数
 */
export function formatRequestJson(selectedCategories) {
  const json = {
    'type.fieldName': 'fv1',
    'type.values': '数基建规划',
    dateFormat: 'yyyy-MM-d',
    indexName: 'fv2'
  };

  let hasDateRange = false;
  let hasNonDateRange = false;

  selectedCategories.forEach(item => {
    if (item.dateRange && item.dateRange.length) {
      if (!json['date.fieldName']) json['date.fieldName'] = 'fv11';
      if (!json['date.values']) json['date.values'] = [];
      if (!json['top.fieldName']) json['top.fieldName'] = 'fv10';
      if (!json['top.values']) json['top.values'] = [];
      json['date.values'].push(...item.dateRange);
      json['top.values'].push(item.topName);
      hasDateRange = true;
    } else {
      if (!json['tag.fieldName']) json['tag.fieldName'] = 'fv11';
      if (!json['tag.values']) json['tag.values'] = [];
      if (!json['top.fieldName']) json['top.fieldName'] = 'fv10';
      if (!json['top.values']) json['top.values'] = [];
      json['tag.values'].push(item.name);
      json['top.values'].push(item.topName);
      hasNonDateRange = true;
    }
  });

  if (!hasDateRange) {
    delete json['date.fieldName'];
    delete json['date.values'];
  }

  if (!hasNonDateRange) {
    delete json['tag.fieldName'];
    delete json['tag.values'];
  }

  if (!hasDateRange && !hasNonDateRange) {
    delete json['top.fieldName'];
    delete json['top.values'];
  }

  return json;
}
