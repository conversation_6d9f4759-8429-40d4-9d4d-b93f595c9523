import dayjs from 'dayjs';
export function getWeeksOfYear(year) {
  let index = 1; // 初始化周数计数器
  // 获取当前年份的第一个周一
  let firstMondayOfYear = dayjs(year)
    .startOf('year')
    .isoWeekday(1)
    .startOf('day');

  // 获取当前年份的最后一天
  const lastDayOfYear = dayjs(year)
    .endOf('year')
    .endOf('day');

  // 获取当前日期
  const today = dayjs().startOf('day');

  // 存储每周的起止日期
  const weeks = [];

  // 从年初的第一个周一开始循环，每次增加一周
  while (firstMondayOfYear.isBefore(lastDayOfYear)) {
    // 当周的开始日期（周一）
    const startOfWeek = firstMondayOfYear.clone();

    // 当周的结束日期（周日）
    const endOfWeek = startOfWeek.add(6, 'day');

    // 如果这周的结束日期在今天之后，但今天在这周内，则包含本周
    if (endOfWeek.isAfter(today) && startOfWeek.isAfter(today)) {
      break;
    }

    // 将这一周的起止日期添加到数组中
    const weekObj = {
      text: `${startOfWeek.format('YYYY-MM-DD')}~${endOfWeek.format('YYYY-MM-DD')}`,
      year: endOfWeek.year(),
      md: `${startOfWeek.format('MM/DD')}~${endOfWeek.format('MM/DD')}`,
      s: startOfWeek.format('YYYY-MM-DD'),
      e: endOfWeek.format('YYYY-MM-DD'),
      week: `第${index}周 (${startOfWeek.format('YYYY-MM-DD')}~${endOfWeek.format('YYYY-MM-DD')})`,
      index: index
    };
    weeks.push(weekObj);
    index++; // 周数计数器递增

    // 移动到下一周的开始
    firstMondayOfYear = endOfWeek.add(1, 'day');
  }
  return weeks.reverse();
}
