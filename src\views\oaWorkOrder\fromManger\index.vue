<template>
  <div class="app-container">
    <el-card class="box-card">
      <el-form :inline="true">
        <el-form-item label="表单名称">
          <el-input
            v-model="seleckName"
            clearable
            placeholder="请输入表单名称"
            size="small"
            style="width: 240px"
            @keyup.native.enter="handleQuery()"
          />
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-search" size="small" type="primary" @click="handleQuery">搜索</el-button>
        </el-form-item>
      </el-form>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            v-permission="['admin','oaTpl:add']"
            icon="el-icon-plus"
            size="mini"
            type="primary"
            @click="handleCreate"
          >新增
          </el-button>
        </el-col>
      </el-row>

      <el-table v-loading="loading" :data="tplList" style="margin-top: 20px" @selection-change="handleSelectionChange">
        <!-- <el-table-column type="selection" width="55" align="center" /> -->
        <!-- <el-table-column label="ID" prop="id" width="120" /> -->
        <el-table-column :show-overflow-tooltip="true" label="名称" prop="name" />
        <el-table-column :show-overflow-tooltip="true" label="创建者" prop="createBy" />
        <el-table-column align="center" label="创建时间" prop="createTime" />
        <el-table-column align="center" label="更新时间" prop="updateTime" />
        <el-table-column
          v-if="checkPer(['admin','oaTpl:edit','oaTpl:del'])"
          align="center"
          class-name="small-padding fixed-width"
          label="操作"
        >
          <template slot-scope="scope">
            <el-button
              v-permission="['admin','oaTpl:edit']"
              size="mini"
              type="success"
              @click="handleEdit(scope.row)"
            >编辑
            </el-button>
            <el-popconfirm
              :hide-icon="true"
              cancel-button-text="取消"
              confirm-button-text="确认"
              icon-color="red"
              title="确认要删除该模板？"
              @confirm="handleDelete(scope.row)"
            >
              <el-button slot="reference" v-permission="['admin','oaTpl:del']" size="mini" type="danger">删除</el-button>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <Paging :page="pageInfo" @handleCurrentChange="getList()" />
      <el-dialog
        :fullscreen="true"
        :title="dialogFormVisibleName===1?'新建表单':'编辑表单'"
        :visible.sync="open"
        style="margin-top: 0"
      >
        <div class="tpl-create-content">
          <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px">
            <el-form-item label="名称" prop="name">
              <el-input v-model="ruleForm.name" />
            </el-form-item>
            <el-form-item label="备注">
              <el-input v-model="ruleForm.remarks" type="textarea" />
            </el-form-item>
            <el-form-item label="表单" prop="form_structure">
              <div style="border-radius: 4px; border: 1px solid #ccc; overflow:hidden">
                <fm-making-form
                  ref="makingform"
                  :advance-fields="['editor', 'imgupload', 'file', 'subform', 'cascader','treeSelect']"
                  clearable
                  generate-code
                  generate-json
                  preview
                  style="height: 600px;"
                  upload
                >
                  <template slot="action" />
                </fm-making-form>
              </div>
            </el-form-item>
          </el-form>
          <div style="text-align: center">
            <el-button type="primary" @click="dialogFormVisibleName===1?submitForm('ruleForm'):editForm('ruleForm')">
              提交
            </el-button>
            <el-button @click="open = false">取 消</el-button>
          </div>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import Vue from 'vue'

import oaTpl from '@/api/oaWorkOrder/oaTpl'

// 表单设计
import {
  GenerateForm,
  MakingForm
} from '@/components/VueFormMaking'
import '@/components/VueFormMaking/styles/FormMaking.css'

import ace from 'ace-builds'
import 'ace-builds/webpack-resolver'

Vue.use(ace)
Vue.use(GenerateForm)
Vue.use(MakingForm)

export default {
  name: 'FromManger',
  data() {
    return {
      seleckName: undefined,
      dialogFormVisibleName: 1,
      pageInfo: {
        size: 10,
        page: 1,
        total: 0
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 是否显示弹出层
      open: false,
      // 表格数据
      tplList: [],
      ruleForm: {
        id: undefined,
        name: '',
        remarks: '',
        form_structure: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入表单名称', trigger: 'blur' }
        ],
        form_structure: [
          { required: true, message: '请设计表单', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询表单列表 */
    getList() {
      this.loading = true;
      const data = {
        page: this.pageInfo.page - 1,
        size: this.pageInfo.size,
        enabled: true,
        sort: 'id,desc',
        name: this.seleckName
      };
      oaTpl.get(data).then(response => {
        this.tplList = response.content;
        this.pageInfo.total = response.totalElements
        this.loading = false
      })
    },
    handleCreate() {
      this.ruleForm = {
        id: undefined,
        name: '',
        remarks: '',
        form_structure: '',
        enabled: true
      }
      this.dialogFormVisibleName = 1
      this.open = true
      this.$nextTick(() => {
        this.$refs.makingform.handleClear()
      })
    },
    handleEdit(row) {
      this.dialogFormVisibleName = 2;
      this.ruleForm = {
        id: row.id,
        name: row.name,
        remarks: row.remarks,
        form_structure: row.formStructure,
        enabled: row.enabled
      }
      this.open = true;
      this.$nextTick(() => {
        this.$refs.makingform.setJSON(JSON.parse(this.ruleForm.form_structure))
      })
    },
    submitForm(formName) {
      this.handleSave(this.$refs.makingform.getJSON())
      this.$refs[formName].validate((valid) => {
        if (valid) {
          oaTpl.add(this.ruleForm).then(() => {
            this.getList()
            this.open = false
          })
        }
      })
    },
    editForm(formName) {
      this.handleSave(this.$refs.makingform.getJSON())
      this.$refs[formName].validate((valid) => {
        if (valid) {
          oaTpl.edit(this.ruleForm).then(() => {
            this.getList()
            this.open = false
          })
        }
      })
    },
    handleQuery() {
      this.pageInfo.size = 10;
      this.pageInfo.page = 1;
      this.getList()
    },
    handleDelete(row) {
      oaTpl.del([row.id]).then(() => {
        this.getList()
        this.$notify({
          title: '删除成功',
          type: 'success',
          duration: 2500
        })
      })
    },
    handleSelectionChange() {
    },
    handleSave(values) {
      if (values.list.length > 0) {
        this.ruleForm.form_structure = values
      } else {
        this.ruleForm.form_structure = ''
      }
    }
  }
}
</script>
