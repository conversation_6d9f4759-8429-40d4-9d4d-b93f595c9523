<template>
  <div id="drawer-container" ref="drawerContainer">
    <el-drawer
      ref="drawer"
      :append-to-body="false"
      :direction="drawerOptions.direction"
      :modal="false"
      :size="drawerOptions.size"
      :title="drawerOptions.title"
      :visible.sync="labelVisible"
      :wrapper-closable="true"
    >
      <!--表格渲染-->
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="crud.data"
        style="width: 100%;"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column label="标签" prop="fv10" />
        <el-table-column label="版本" prop="fv30" />
        <el-table-column label="创建人" prop="create_by" />
        <el-table-column label="总数" prop="total" />
      </el-table>
      <!--分页组件-->
      <pagination />
    </el-drawer>
  </div>
</template>

<script>
import CRUD, { presenter, header, crud } from '@crud/crud'
import pagination from '@crud/Pagination'

export default {
  name: 'UploadTagHistory',
  components: { pagination },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: false,
      title: '历史数据',
      url: 'api/omAssetTag/statVersion',
      crudMethod: {},
      sort: [],
      query: {
        enabled: 1,
        sort: 'fv30,desc'
      }
    })
  },
  mixins: [presenter(), header(), crud()],
  props: {
    drawerOptions: {
      type: Object,
      default: () => ({
        title: '标签导入历史',
        direction: 'ltr',
        size: '35%'
      })
    },
    selectQuery: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      labelVisible: false
    };
  },
  created() {
  },
  methods: {
    initData() {
      this.labelVisible = true;
      this.crud.toQuery()
    },
    [CRUD.HOOK.beforeRefresh]() {
      this.crud.query = {
        ...this.query,
        ...this.selectQuery
      }
    }
  }
};
</script>

<style scoped>
#drawer-container {
	position: absolute;
	left: 0;
	top: 0;
}

::v-deep .el-drawer__header {
	margin-bottom: 10px !important;
}

::v-deep .el-drawer__body {
	padding: 0 20px !important;
}

::v-deep .el-drawer__header {
	font-size: 17px !important;
	font-weight: bold !important;
	color: #000 !important;
}
</style>
