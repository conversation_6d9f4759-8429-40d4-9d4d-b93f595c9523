const PROJECT_LABEL = [
  {
    id: 1,
    label: '项目交付内容属性',
    disabled: true,
    children: [
      {
        id: 2,
        pid: 1,
        label: '软件开发'
      },
      {
        id: 3,
        pid: 1,
        label: '软件采购'
      },
      {
        id: 4,
        pid: 1,
        label: '硬件开发'
      },
      {
        id: 5,
        pid: 1,
        label: '硬件采购'
      },
      {
        id: 6,
        pid: 1,
        label: '系统集成'
      },
      {
        id: 7,
        pid: 1,
        label: '运行维护'
      },
      {
        id: 8,
        pid: 1,
        label: '项目管理'
      },
      {
        id: 9,
        pid: 1,
        label: '购买服务'
      }
    ]
  },
  {
    id: 10,
    label: '是否为延续性项目',
    disabled: true,
    children: [
      {
        id: 11,
        pid: 10,
        label: '延续性项目'
      },
      {
        id: 12,
        pid: 10,
        label: '非延续性项目'
      }
    ]
  },
  {
    id: 14,
    label: '项目预算规模属性',
    disabled: true,
    children: [
      {
        id: 15,
        pid: 14,
        label: '规模≤50万'
      },
      {
        id: 16,
        pid: 14,
        label: '50万<规模≤100万'
      },
      {
        id: 17,
        pid: 14,
        label: '100万<规模≤200万'
      },
      {
        id: 18,
        pid: 14,
        label: '200万<规模≤300万'
      },
      {
        id: 19,
        pid: 14,
        label: '300万<规模≤400万'
      },
      {
        id: 20,
        pid: 14,
        label: '400万<规模≤500万'
      },
      {
        id: 21,
        pid: 14,
        label: '500万<规模≤1000万'
      },
      {
        id: 22,
        pid: 14,
        label: '1000万<规模≤1200万'
      },
      {
        id: 23,
        pid: 14,
        label: '1200万<规模≤1500万'
      },
      {
        id: 24,
        pid: 14,
        label: '1500万<规模≤2000万'
      },
      {
        id: 25,
        pid: 14,
        label: '2000万<规模≤5000万'
      },
      {
        id: 26,
        pid: 14,
        label: '规模>5000万'
      }
    ]
  }
]

const state = {
  projectInfo: JSON.parse(sessionStorage.getItem('projectInfo')) || {},
  projectLabels: PROJECT_LABEL
}

const mutations = {
  SET_PROJECT_INFO: (state, projectInfo) => {
    state.projectInfo = projectInfo
  }
}

const actions = {
  setCurrentProject({ commit }, projectInfo) {
    commit('SET_PROJECT_INFO', projectInfo)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
