<template>
  <div class="supplementary-top">
    <ul class="legend-box">
      <li v-for="item in legend" :key="item.id">
        <span :style="{ backgroundColor: item.color }" class="legend-point" />
        <span class="legend-title">{{ item.title }}</span>
      </li>
    </ul>
    <!--<div>右侧菜单</div>-->
  </div>
</template>

<script>
import {
  legend
} from '../utils/fileds'

export default {
  name: 'Legend',
  components: {},

  props: {},
  data() {
    return {
      legend
    }
  },

  computed: {},
  watch: {},
  created() {
  },
  methods: {}
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.supplementary-top {
	width: 1920px;
	padding: 0px 58px;
	transform: scale(0.75);
	transform-origin: top left; /* 确保缩放基点为左上角 */
	position: relative;

	.legend-box {
		position: absolute;
		left: 50%;
		transform: translateX(-50%);

		width: 626px;
		height: 62px;
		border-radius: 8px;
		border: 2px dashed #CCCCCC;
		display: flex;
		align-items: center;
		justify-content: space-around;

		li {
			display: flex;
			align-items: center;
			justify-content: center;

			.legend-point {
				width: 20px;
				height: 20px;
				border-radius: 4px;
				margin-right: 6px;
			}

			.legend-title {
				font-weight: 500;
				font-size: 20px;
				color: #555555;
			}
		}
	}

}
</style>
