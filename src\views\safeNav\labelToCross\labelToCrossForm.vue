<template>
  <div class="app-container">
    <el-card class="box-card" style="margin-top: 10px">
      <div slot="header" class="clearfix">
        <span>自动驾驶3.0项目</span>
      </div>
      <el-form ref="elFormRef" :model="elForm" :rules="elRules" label-width="130px" size="small">
        <!--额外的表单-->
      </el-form>
      <div v-if="showFormData" class="text item">
        <fm-generate-form
          :ref="'generateForm'"
          :data="formStruct"
          :preview="viewOrEdit"
          :remote="remoteFunc"
          :value="formData"
        />
      </div>
      <hr style="background-color: #d9d9d9; border:0; height:1px;">
      <div class="text item" style="text-align: center;margin-top:18px">
        <el-button v-if="!viewOrEdit" :disabled="submitDisabled" type="primary" @click="submitAction">提交</el-button>
        <el-button @click="concelForm">{{ viewOrEdit == false ? '取 消' : '返回' }}</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import Vue from 'vue'
import { GenerateForm } from '@/components/VueFormMaking'
import 'form-making/dist/FormMaking.css'

Vue.component(GenerateForm.name, GenerateForm)
import extendBindTpl from '@/api/system/extendBindTpl';
import { getToken } from '@/utils/auth';
import crudDictDetail from '@/api/system/dictDetail';
import crudTable from '@/api/parts/assets'

export default {
  name: 'ElectricForm',
  components: {},
  data() {
    return {
      elForm: {
        enabled: 1
      },
      elRules: {},
      formStruct: {},
      formData: {},
      remoteFunc: {
        funcGetToken(resolve) {
          resolve(getToken())
        },
        getDictDetail(resolve, dictName) {
          crudDictDetail.get(dictName).then(res => {
            const options = res.content
            resolve(options)
          })
        },
        getPartition(resolve) {
          // 施工分区
          this.getDictDetail(resolve, 'partition_type');
        },
        getXyTown(resolve) {
          // 区域
          this.getDictDetail(resolve, 'xyTown_type');
        },
        getAutoDiveStaus(resolve) {
          // 盘点情况
          this.getDictDetail(resolve, 'auto_drive_status');
        }
      },
      submitDisabled: false,
      viewOrEdit: false,
      showFormData: false,
      pointList: [], // 点位列表
      positionList: [] // 位置列表
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      const { id, type } = this.$route.query
      this.viewOrEdit = type
      if (id) {
        // 编辑或者查看
        this.getContent(id)
      } else {
        // 添加
        this.getProcessNodeList()
      }
    },
    getContent(id) {
      crudTable.get({ id, enabled: 1 }).then(res => {
        if (res && res.content && res.content.length) {
          const jsonData = res.content[0];
          this.processStructureValue = jsonData;
          this.formStruct = JSON.parse(jsonData.formStruct);
          this.formData = JSON.parse(jsonData.formData);
          this.jsonData = jsonData;
          this.showFormData = true;
          this.elForm = jsonData
        }
      })
    },
    getProcessNodeList() {
      const data = { id: this.$config.auto_pilot3_key.bindId, enabled: 1 }
      extendBindTpl.get(data).then(res => {
        if (res && res.content && res.content.length) {
          this.processStructureValue = res.content[0];
          const FormStruct = JSON.parse(this.processStructureValue.extendTpl.formStruct);
          this.formStruct = FormStruct;
        }
        this.showFormData = true;
      });
    },
    submitAction() {
      this.$refs['elFormRef'].validate((valid) => {
        if (!valid) {
          return false;
        } else {
          this.checkModule().then(res => {
            if (!res.flag) {
              return false;
            } else {
              const subData = { ...this.elForm, ...res.subData };
              let request = crudTable.add;
              let title = '添加'
              if (subData.id) {
                request = crudTable.edit;
                title = '编辑'
              }
              console.log(subData, '<===>', 'subData')
              request(subData).then(response => {
                this.$notify({
                  title: `${title}成功`,
                  type: 'success',
                  duration: 2500
                })
                this.concelForm();
              }).catch((e) => {
                console.log(e);
              })
            }
          })
        }
      })
    },
    async checkModule() {
      const subData = {
        bindId: this.$config.auto_pilot3_key.bindId,
        categoryId: this.$config.auto_pilot3_key.categoryId,
        formData: null,
        formStruct: null,
        formBindToVar: true,
        relation: this.processStructureValue.relation
      };
      if (this.showFormData) {
        const p = await this.$refs['generateForm'].getData().then(values => {
          subData.formData = JSON.stringify(values);
          subData.formStruct = JSON.stringify(this.formStruct);
          return {
            flag: true,
            subData
          };
        }).catch(() => {
          return {
            flag: false
          };
        })
        return p;
      } else {
        return Promise.resolve({
          subData,
          flag: true
        });
      }
    },
    concelForm() {
      const query = {
        bindId: this.$config.auto_pilot3_key.bindId,
        categoryId: this.$config.auto_pilot3_key.categoryId
      }
      this.$router.push({ name: 'AutoPilot3', query });
    }

  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
