<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input
          v-model="query.name"
          class="filter-item"
          clearable
          placeholder="请输入项目名称搜索"
          size="small"
          style="width: 200px;"
          @keyup.enter.native="crud.toQuery"
        />
        <date-range-picker v-model="query.createTime" class="date-item" />
        <rrOperation />
      </div>
      <crudOperation :permission="permission">
        <update-button
          slot="right"
          :permission="permission"
          :bind-id="bindId"
          :enabled="[1]"
        />
      </crudOperation>
    </div>
    <!--表格渲染-->
    <el-table
      ref="table"
      v-loading="crud.loading"
      :data="tableData"
      row-key="id"
      @select="crud.selectChange"
      @select-all="crud.selectAllChange"
      @selection-change="crud.selectionChangeHandler"
    >
      <el-table-column
        v-for="item in tableHeader"
        :key="item.prop"
        :show-overflow-tooltip="true"
        :prop="item.prop"
        :label="item.label"
        align="center"
      >
        <template slot-scope="scope"><span>{{ scope.row[item.prop] }}</span></template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination />
  </div>
</template>

<script>
import oaPmStageExec from '@/api/oaWorkOrder/oaPmStageExec'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import updateButton from '@/components/UpdateButton/index'
import crudOperation from '@crud/CRUD.operation'
import DateRangePicker from '@/components/DateRangePicker'
import pagination from '@crud/Pagination';
// crud交由presenter持有
const defaultForm = {
  id: null
}
export default {
  name: 'OaPmSs',
  components: { crudOperation, rrOperation, DateRangePicker, pagination, updateButton },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: true,
      title: '立项阶段',
      url: 'api/oaPmStageExec',
      sort: ['createTime,desc'],
      query: { enabled: 1 },
      crudMethod: { ...oaPmStageExec },
      optShow: {
        add: false,
        edit: false,
        del: false,
        download: false,
        reset: true,
        rightGroup: false
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      menus: [],
      projectConfig: {},
      tableData: [],
      tableHeader: [],
      permission: {
        add: ['admin', 'oaPmStageExec:add'],
        edit: ['admin', 'oaPmStageExec:edit'],
        del: ['admin', 'oaPmStageExec:del'],
        updateT: ['admin', 'oaPmStageExec:updateFormStruct'],
        updateR: ['admin', 'oaPmStageExec:updateRelation']
      },
      rules: {
      },
      bindId: ''
    }
  },
  watch: {
    'crud.data': {
      handler(val) {
        if (val && val.length) {
          const tableData = val.map(item => ({
            id: item.id,
            title: item.title || '',
            ...this.getJson([item.extend.data])
          }));

          const tableHeader = val[0]?.extend?.tableHeader || [];

          this.tableData = tableData;
          this.tableHeader = tableHeader;
        } else {
          this.tableData = [];
          this.tableHeader = [];
        }
      },
      deep: true
    }
  },
  methods: {
    [CRUD.HOOK.beforeRefresh]() {
      const { id } = this.$route.query
      this.bindId = id;
      this.crud.query.bindId = id;
    },
    getJson(item) {
      const json = {}
      item.forEach(i => {
        for (const name in i) {
          json[name] = i[name];
        }
      })
      return json;
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
::v-deep .el-input-number .el-input__inner {
	text-align: left;
}

::v-deep .vue-treeselect__control, ::v-deep .vue-treeselect__placeholder, ::v-deep .vue-treeselect__single-value {
	height: 30px;
	line-height: 30px;
}
</style>
