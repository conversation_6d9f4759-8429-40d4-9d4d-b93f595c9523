import { isOnline } from '@/utils/index'

class WebSocketClient {
  constructor(url, options = {}) {
    this.url = url;
    this.ws = null;
    const maxReconnectTimes = isOnline() ? 1000 : 5;
    this.heartbeatInterval = options.heartbeatInterval || 60000; // 心跳间隔时间，单位毫秒
    this.reconnectInterval = options.reconnectInterval || 5000; // 重连间隔时间，单位毫秒
    this.maxReconnectTimes = options.maxReconnectTimes || maxReconnectTimes; // 最大重连次数
    this.reconnectTimes = 0; // 当前重连次数
    this.onmessage = options.onmessage || function() {
    }; // 消息处理函数
    this.connect();
  }

  connect() {
    this.ws = new WebSocket(this.url);
    this.ws.onopen = () => {
      console.log('WebSocket 连接成功');
      this.reconnectTimes = 0; // 重置重连次数
      this.startHeartbeat();
    };

    this.ws.onmessage = (event) => {
      // 处理接收到的消息
      this.onmessage(event.data);
    };

    this.ws.onclose = () => {
      console.log('WebSocket 连接关闭');
      this.stopHeartbeat();
      this.reconnect();
    };

    this.ws.onerror = () => {
      this.ws.close();
    };
  }

  send(data) {
    if (this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(data);
    }
  }

  startHeartbeat() {
    this.heartbeatTimer = setInterval(() => {
      if (this.ws.readyState === WebSocket.OPEN) {
        this.ws.send('ping'); // 发送心跳消息
      }
    }, this.heartbeatInterval);
  }

  stopHeartbeat() {
    clearInterval(this.heartbeatTimer);
  }

  reconnect() {
    if (this.reconnectTimes < this.maxReconnectTimes) {
      this.reconnectTimes++;
      console.log(`尝试第${this.reconnectTimes}次重连...`);
      setTimeout(() => {
        this.connect();
      }, this.reconnectInterval);
    } else {
      console.log('已达到最大重连次数，停止重连。');
    }
  }
}

export default WebSocketClient;
