<template>
  <el-dialog
    v-dialog-drag
    :before-close="concelForm"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :title="title"
    :visible.sync="visible"
    append-to-body
    width="800px"
  >
    <el-descriptions
      v-if="queryInfo && queryInfo.length"
      :column="2"
      content-class-name="self-contentClassName"
      label-class-name="self-labelClassName"
      style="margin-bottom: 20px"
      title="统计参数"
    >
      <el-descriptions-item v-for="item in queryInfo" :key="item.key" :label="item.key">
        {{ item.value }}
      </el-descriptions-item>
    </el-descriptions>
    <el-descriptions
      :column="2"
      content-class-name="self-contentClassName"
      label-class-name="self-labelClassName"
      title="统计结果"
    >
      <el-descriptions-item label="项目个数">{{ statisticsData.BB }}个</el-descriptions-item>
      <el-descriptions-item label="项目预算金额的总和">{{ statisticsData.CC }}万元</el-descriptions-item>
      <el-descriptions-item label="项目状态(中标/合同)数量">{{ statisticsData.DD }}个</el-descriptions-item>
      <el-descriptions-item label="项目状态(中标/合同)金额合计">{{ statisticsData.EE }}万元</el-descriptions-item>
    </el-descriptions>
    <div slot="footer" class="dialog-footer">
      <el-button type="text" @click="concelForm">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { field } from '@/views/spider/utils/field';
import { parseTime } from '@/utils/index'

const queryKey = {
  fv1: '项目名称',
  fv2: '项目编号',
  fv5: '采购单位',
  fv12: '中标单位',
  fv13: '标签',
  fv3: '公告类型',
  fv11: '公告日期'
}
export default {
  name: 'Statistics',

  props: {},
  data() {
    return {
      title: '统计信息',
      visible: false,
      // 统计结果
      statisticsData: {
        BB: '', // 项目个数
        CC: '', // 项目预算金额的总和
        DD: '', // 状态(中标/合同)数量
        EE: '' // 状态(中标/合同)金额合计
      },
      queryKey,
      queryInfo: []
    }
  },
  methods: {
    initData(data, query) {
      this.visible = true;
      this.statisticsData = data;
      this.handelQueryInfo(query)
    },
    concelForm() {
      this.visible = false
      this.queryInfo = []
    },
    handelQueryInfo(query) {
      const getValue = (key, value) => {
        if (key === 'fv3') {
          return field[value].type;
        } else if (key === 'fv11') {
          return `${parseTime(value[0], '{y}-{m}-{d}')}~${parseTime(value[1], '{y}-{m}-{d}')}`
        } else {
          return value;
        }
      }
      for (var key in query) {
        if (queryKey.hasOwnProperty(key) && query[key]) {
          const obj = {
            key: queryKey[key],
            value: getValue(key, query[key])
          }
          this.queryInfo.push(obj)
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-descriptions {
	.self-labelClassName {
		font-size: 14px !important;
	}

	.self-contentClassName {
		font-size: 14px !important;
	}
}

</style>
