<template>
  <div class="document-info">
    <!--文件数据-->
    <div class="head-container">
      <e-header :permission="permission" />
      <crudOperation :permission="permission">

        <update-button
          v-if="bindId"
          slot="right"
          :bind-id="bindId"
          :enabled="[1]"
          :permission="permission"
        />
      </crudOperation>
    </div>
    <div class="body-box">
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="tableData"
        :load="getMenus"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        lazy
        row-key="id"
        style="width: 100%;"
        @select="crud.selectChange"
        @select-all="crud.selectAllChange"
        @selection-change="crud.selectionChangeHandler"
      >
        <!-- <el-table-column :fixed="true" type="selection" width="55" /> -->
        <el-table-column
          v-for="item in tableHeader"
          :key="item.prop"
          :align="item.align || 'center'"
          :fixed="item.fixed || false"
          :label="item.label"
          :prop="item.prop"
          :show-overflow-tooltip="true"
          :width="item.width"
        >
          <template slot-scope="scope">
            <template v-if="item.label == '预览图'">
              <el-image
                :key="item.id"
                :preview-src-list="[scope.row.ft1[0].url]"
                :src="scope.row.ft1[0].url"
                class="el-avatar"
                fit="contain"
                lazy
              >
                <div slot="error">
                  <i class="el-icon-document" />
                </div>
              </el-image>
            </template>
            <span
              v-else-if="item.prop === 'taskTitle'"
              style="cursor: pointer;color: #2476F8"
              @click="goDetail(scope.row)"
            >
              {{ scope.row[item.prop] }}
            </span>
            <span v-else>{{ scope.row[item.prop] }}</span>
          </template>
        </el-table-column>
        <!--   编辑与删除   -->
        <!-- <el-table-column
	align="center"
	fixed="right"
	label="操作"
	width="240"
>
	<template slot-scope="scope">
		<udOperation
			:data="scope.row"
			:permission="permission"
			msg="确定删除吗,如果存在下级节点则一并删除，此操作不能撤销！"
		/>
		<el-button type="primary" @click="createTask(scope.row)">创建子任务</el-button>
	</template>
</el-table-column> -->
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import eHeader from '@/views/oaWorkOrder/oaProject/components/task/TaskHeader.vue';
import oaPmTree from '@/api/oaWorkOrder/oaPmTree'
import CRUD, { crud, form, presenter } from '@crud/crud';
import crudOperation from '@crud/CRUD.operation.vue';
import pagination from '@crud/Pagination.vue';
import updateButton from '@/components/UpdateButton/index.vue'
// import udOperation from '@crud/UD.operation';
import { getConfigInfo } from '@/utils/configInfo';

const defaultForm = {
  id: null

}
const configDefinitions = [
  { key: 'task_keys', prop: 'taskConfig' }
]
export default {
  name: 'OaProject',
  components: { eHeader, crudOperation, pagination, updateButton },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: false,
      title: '任务',
      url: 'api/oaPmTree/small',
      sort: ['createTime,desc'],
      query: { enabled: 1, fv1: '任务' },
      crudMethod: { ...oaPmTree },
      optShow: {
        add: false,
        edit: false,
        del: false,
        download: false,
        reset: false,
        rightGroup: false
      }
    })
  },
  mixins: [presenter(), form(defaultForm), crud()],
  data() {
    return {
      permission: {
        del: ['admin', 'oaDocument:del'],
        add: ['admin', 'oaDocument:add'],
        updateT: ['admin', 'oaPm:updateFormStruct'],
        updateR: ['admin', 'oaPm:updateRelation']
      },
      tableData: [],
      tableHeader: [],
      bindId: '',
      taskConfig: {},
      taskDialogVisible: false
    }
  },
  computed: {},
  watch: {
    'crud.data': {
      handler(val, oldVal) {
        if (val && val.length && val.length > 0) {
          const newVal = val;
          const tableHeader = [
            { prop: 'taskTitle', label: '任务名称' },
            // { prop: 'fv8', label: '优先级' },
            { prop: 'fv9', label: '处理人' },
            { prop: 'fv7', label: '里程碑' },
            { prop: 'createTime', label: '创建时间' },
            { prop: 'fv11', label: '预计结束时间' },
            { prop: 'fv12', label: '当前状态' }

          ]
          this.tableHeader = tableHeader;
          let tableData = [];
          tableData = newVal.map(item => {
            const json = item.extend.data || {};
            const data = {
              ...item,
              ...json
            }
            data.fv9 = JSON.parse(data.fv9).join(',');
            return data;
          });
          this.tableData = tableData;
        } else {
          this.tableHeader = [];
          this.tableData = [];
        }
      },
      deep: true
    }
  },
  // async created() {
  //   await getConfigInfo.call(this, configDefinitions)
  //   this.initData()
  // },
  methods: {
    async initData() {
      await getConfigInfo.call(this, configDefinitions)
      const { bindId } = this.taskConfig.extend.data;
      this.bindId = bindId;
      this.crud.query.bindId = bindId;
    },
    // createTask(data) {
    //   console.log(data, '<===>', 'data')
    //   const info = {
    //     data,
    //     type: 3,
    //     projectId: this.$route.query.projectId,
    //     currentProjectName: this.$route.query.name
    //   }
    //   this.$emit('toEdit', info)
    // },
    // [CRUD.HOOK.beforeToEdit](crud, form) {
    //   const info = {
    //     data: form,
    //     type: 2,
    //     projectId: this.$route.query.projectId,
    //     currentProjectName: this.$route.query.name
    //   }
    //   this.$emit('toEdit', info)
    // },
    getMenus(tree, treeNode, resolve) {
      const params = { pid: tree.id, enabled: 1, fv1: '任务', size: 999 }
      setTimeout(() => {
        oaPmTree.getPmTreeSmall(params).then(res => {
          let tableData = res.content;
          tableData = tableData.map(item => {
            const json = item.extend.data || {};
            const data = {
              ...item,
              ...json
            }
            data.fv9 = JSON.parse(data.fv9).join(',');
            return data;
          });
          resolve(tableData)
        })
      }, 100)
    },
    goDetail(data) {
      this.$router.push({
        name: 'DoTask',
        query: {
          id: data.id,
          type: 2
        }
      })
    },
    async upDataTableData(data) {
      if (!this.taskConfig) {
        await this.initData()
      }

      if (data) {
        this.crud.query.fv7 = data.title;
        this.crud.toQuery();
      } else {
        this.crud.query.fv7 = ''
        this.crud.data = []
      }
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>

</style>
