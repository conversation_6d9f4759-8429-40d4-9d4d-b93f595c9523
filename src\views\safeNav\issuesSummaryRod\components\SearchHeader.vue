<template>
  <div v-if="crud.props.searchToggle" class="">
    <el-input
      v-model="query.title"
      class="filter-item"
      clearable
      placeholder="请输入路口名称搜索"
      size="small"
      style="width: 180px;"
      @keyup.enter.native="crud.toQuery"
    />
    <el-input
      v-model="query.fv2"
      class="filter-item"
      clearable
      placeholder="请输入路口编号搜索"
      size="small"
      style="width: 180px;"
      @keyup.enter.native="crud.toQuery"
    />
    <el-input
      v-model="query.fv3"
      class="filter-item"
      clearable
      placeholder="请输入杆体编号搜索"
      size="small"
      style="width: 180px;"
      @keyup.enter.native="crud.toQuery"
    />
    <self-select
      :options="dict.issues_classification"
      :select-value.sync="query.fv4"
      :tags="2"
      class="filter-item"
      clearable
      placeholder="请选择分类"
      size="small"
      style="width: 180px;"
      @selectChange="(val)=>selectChange(val, 'fv4')"
    />
    <rrOperation />
  </div>
</template>

<script>
import { header } from '@crud/crud';
import rrOperation from '@crud/RR.operation';

export default {
  components: { rrOperation },
  mixins: [header()],
  props: {
    permission: {
      type: Object,
      required: true
    },
    dict: {
      type: Object,
      required: true
    }
  },
  data() {
    return {}
  },

  computed: {},
  watch: {},
  created() {
  },
  methods: {
    selectChange(val, type) {
      this.crud.query[type] = val;
      this.crud.toQuery();
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
