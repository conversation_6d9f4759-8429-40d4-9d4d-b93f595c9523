<template>
  <el-card style="margin-bottom: 20px;">
    <div class="project-header">
      <div class="project-name">
        <span class="project-title">当前项目:{{ projectInfo.name }}</span>
        <el-button
          v-permission="permission.importXlsWithRule"
          class="filter-item"
          icon="el-icon-upload2"
          size="mini"
          type="success"
          @click="uploadtask()"
        >
          导入目录/任务
        </el-button>

        <el-button
          v-permission="permission.updateTaskAndDirectory"
          class="filter-item"
          icon="el-icon-refresh"
          size="mini"
          @click="updateImportInfo()"
        >更新导入信息
        </el-button>

        <el-button class="filter-item" icon="el-icon-location" size="mini" type="primary" @click="showMapDialog()">地图展示
        </el-button>
      </div>

      <ul class="planning-list">
        <li
          v-for="item in planningList"
          :key="item.id"
          v-permission="item.permission"
          :class="item.id == planningId ? 'active' : ''"
          @click="onClickLabel(item)"
        >
          <span>{{ item.name }}</span>
          <p class="bottom-line" />
        </li>
      </ul>
    </div>

    <upload-task ref="uploadTask" />
    <map-dialog ref="mapDialog" />
  </el-card>

</template>

<script>
import UploadTask from '@/views/oaWorkOrder/oaProject/components/justCom/uploadTask.vue';
import MapDialog from '@/views/oaWorkOrder/oaPm/components/mapDialog.vue';
import { updateTaskAndDirectory } from '@/api/oaWorkOrder/oaPmTree'
import {
  getCatalogTree
} from '@/api/oaWorkOrder/oaPmProgress'

export default {
  name: 'ProjectHeader',
  components: {
    UploadTask,
    MapDialog
  },
  props: {
    planningId: {
      type: Number,
      default: 3
    },
    planningList: {
      type: Array,
      default: () => []
    },
    permission: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    return {
      projectInfo: {
        id: '',
        name: ''
      },
      // 模拟的项目点位数据
      locationPoints: []
    }
  },

  computed: {},
  watch: {},
  created() {
    const { name, projectId } = this.$route.query
    this.projectInfo.name = name
    this.projectInfo.id = projectId
  },
  methods: {
    onClickLabel(data) {
      this.$emit('planning-change', data)
    },
    handleCommand(command) {
      this.$message({
        type: 'info',
        message: `你点击了${command.name}`
      })
    },
    uploadtask() {
      this.$refs.uploadTask.init()
    },
    updateImportInfo() {
      const { name, projectId } = this.$route.query
      const query = {
        fv18: name,
        indexNotNull: true,
        enabled: 1,
        pmId: projectId
      }
      console.log(query, '<===>', 'query')
      updateTaskAndDirectory(query).then(res => {
        if (this.planningId === 1 || this.planningId === 3) {
          this.$emit('updateImportInfo')
        }
      })
    },
    async showMapDialog() {
      try {
        // 获取项目ID
        const projectId = this.projectInfo.id || this.$route.query.projectId

        if (!projectId) {
          this.$message.warning('未找到项目ID')
          return
        }

        // 调用API获取目录树
        const response = await getCatalogTree(projectId)

        if (response.treeList) {
          // 递归遍历树结构，筛选出type=='point'的节点
          const pointNodes = this.extractPointNodes(response.treeList)

          // 转换为地图组件需要的格式
          this.locationPoints = pointNodes.map(point => ({
            name: point.name || point.title || '未命名点位',
            status: point.status || 1, // 默认为1，表示未开始
            longitude: point.longitude || point.lng || point.lon,
            latitude: point.latitude || point.lat,
            id: point.id,
            ...point // 保留原始数据
          })).filter(point => point.longitude && point.latitude) // 过滤掉没有坐标的点位

          // 传递点位数据到地图组件
          this.$refs.mapDialog.show(this.locationPoints)
        } else {
          this.$message.error('获取项目节点数据失败')
        }
      } catch (error) {
        console.error('获取项目节点失败:', error)
        this.$message.error('获取项目节点失败: ' + (error.message || '未知错误'))
      }
    },

    // 递归提取type=='point'的节点
    extractPointNodes(nodes) {
      const pointNodes = []

      const traverse = (nodeList) => {
        if (!Array.isArray(nodeList)) return

        nodeList.forEach(node => {
          // 如果节点类型是'point'，添加到结果数组
          if (node.type === 'point') {
            pointNodes.push(node)
          }

          // 如果有子节点，递归遍历
          if (node.children && Array.isArray(node.children)) {
            traverse(node.children)
          }
        })
      }

      traverse(nodes)
      return pointNodes
    }

  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.project-header {
  display: flex;

  .project-name {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    color: #000;
    min-width: 200px;
    margin-right: 50px;

    .project-title {
      margin-right: 20px;
    }

    i {
      font-size: 16px;
      cursor: pointer;
      color: #409EFF;
      margin-left: 20px;
    }
  }

  .planning-list {
    display: flex;
    align-items: center;

    li {
      height: 40px;
      line-height: 40px;
      font-size: 14px;
      color: #364359;
      margin-right: 20px;
      cursor: pointer;
      position: relative;

      p {
        display: none;
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 2px;
        background: #409EFF;
      }

      &:hover,
      &.active {
        color: #409EFF;

        p {
          display: block;
        }
      }

    }
  }
}
</style>
