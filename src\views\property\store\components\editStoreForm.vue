<template>
  <div class="task-dialog">
    <el-drawer
      :before-close="cancelForm"
      :close-on-click-modal="false"
      :title="ADDTYPE[type]"
      :visible.sync="visible"
      append-to-body
      class="task-dialog"
      direction="rtl"
      width="800px"
    >

      <div v-if="showFormData" class="text item task-content">
        <!-- 基础信息 -->
        <base-info :base-info="baseInfo" :asset-info="assetInfo" />
        <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="130px" />
        <fm-generate-form
          :ref="'generateForm'"
          :data="formStruct"
          :preview="viewOrEdit"
          :remote="remoteFunc"
          :value="formData"
        />

        <!-- 入库信息表单 -->
        <el-form ref="storeForm" :model="storeForm" :rules="storeRules" label-width="130px">

          <el-form-item label="入库类型" prop="orderType">
            <el-input v-model="orderTypeText" :disabled="true" style="width: 100%" />
          </el-form-item>
          <el-form-item label="入库时间" prop="inDate">
            <el-date-picker
              v-model="storeForm.inDate"
              type="datetime"
              placeholder="选择入库时间"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
              :disabled="viewOrEdit"
            />
          </el-form-item>

          <el-form-item label="存放地址" prop="address">
            <el-input v-model="storeForm.address" placeholder="请输入存放地址" :disabled="viewOrEdit" />
          </el-form-item>

          <el-form-item label="SN码" prop="snNo">
            <el-input v-model="storeForm.snNo" placeholder="请输入SN码" :disabled="viewOrEdit" />
          </el-form-item>

          <!-- 拆回/维修返回时显示的字段 -->
          <template v-if="storeForm.orderType === '2' || storeForm.orderType === '3'">
            <el-form-item label="归还人" prop="revertMan">
              <el-input v-model="storeForm.revertMan" placeholder="请输入归还人" :disabled="viewOrEdit" />
            </el-form-item>

            <el-form-item label="归还原因" prop="revertReason">
              <el-input v-model="storeForm.revertReason" type="textarea" placeholder="请输入归还原因" :disabled="viewOrEdit" />
            </el-form-item>
          </template>

          <!-- 后期接收时显示的字段 -->
          <template v-if="storeForm.orderType === '4'">
            <el-form-item label="交接单位" prop="handoverUnit">
              <el-input v-model="storeForm.handoverUnit" placeholder="请输入交接单位" :disabled="viewOrEdit" />
            </el-form-item>
          </template>
        </el-form>
      </div>
      <div class="dialog-footer">
        <el-button type="text" @click="cancelForm">取消</el-button>
        <el-button :disabled="submitDisabled" type="primary" @click="submitAction">
          {{ ADDTYPE[type] }}
        </el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth';
import { mapGetters } from 'vuex';
import amStockIn from '@/api/property/amStockIn'
import BaseInfo from './baseInfo.vue'
// import { convertCurrency } from '@/utils'

const ADDTYPE = {
  1: '入库',
  2: '编辑入库',
  3: '查看入库'
}
export default {
  components: {
    BaseInfo
  },
  data() {
    return {
      visible: false,
      maxStoreNum: 58,
      baseInfo: {},
      assetInfo: {},
      ruleForm: {
        enabled: 1,
        amount: null
      },
      rules: {
        amount: []
      },
      formStruct: {},
      formData: {},
      jsonData: {},
      submitDisabled: false,
      viewOrEdit: false,
      showFormData: false,
      remoteFunc: {
        funcGetToken(resolve) {
          resolve(getToken())
        }
      },
      type: 1,
      ADDTYPE,
      // 入库信息表单数据
      storeForm: {
        inDate: '',
        address: '',
        orderType: '',
        revertMan: '',
        snNo: '',
        revertReason: '',
        handoverUnit: ''
      },
      // 入库信息表单验证规则
      storeRules: {
        inDate: [
          { required: true, message: '请选择入库时间', trigger: 'change' }
        ],
        address: [
          { required: true, message: '请输入存放地址', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ]),
    // 入库类型显示文本
    orderTypeText() {
      const typeMap = {
        '1': '新设备',
        '2': '拆回',
        '3': '维修返回',
        '4': '后期接收'
      };
      return typeMap[this.storeForm.orderType] || '';
    }
  },
  methods: {
    init(info) {
      this.visible = true;
      const { type, id } = info;
      this.type = type;
      if (this.type === 2) {
        this.getContent(id)
      }
    },
    getContent(id) {
      amStockIn.get({ id, enabled: 1 }).then(res => {
        if (res && res.content) {
          const jsonData = res.content[0];
          this.baseInfo = jsonData.basicData;
          this.assetInfo = jsonData; // 设置assetInfo以便base-info组件能够显示amount
          this.jsonData = jsonData;
          this.processStructureValue = jsonData;

          // 安全解析JSON的辅助函数
          const safeJsonParse = (jsonString, defaultValue = {}) => {
            try {
              if (!jsonString || jsonString === 'null' || jsonString === 'undefined') {
                return defaultValue;
              }
              return JSON.parse(jsonString);
            } catch (error) {
              console.warn('JSON解析失败:', jsonString, error);
              return defaultValue;
            }
          };

          this.formStruct = safeJsonParse(jsonData.formStruct, {});
          this.formData = safeJsonParse(jsonData.formData, {});
          this.ruleForm = jsonData;

          // 加载入库信息表单数据
          this.loadStoreFormData(jsonData);

          this.showFormData = true;
        }
      })
    },
    submitAction() {
      // 先验证基础表单
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          // 再验证入库表单
          this.$refs['storeForm'].validate((storeValid) => {
            if (storeValid) {
              // 验证SN码和数量的关系
              if (this.storeForm.snNo && this.storeForm.snNo.trim() && this.ruleForm.amount > 1) {
                this.$message.error('填写SN码后，出库数量必须为1。即只能出库一个设备。');
                this.submitDisabled = false;
                return false;
              }

              this.checkModule().then(res => {
                if (!res.flag) {
                  return false;
                } else {
                  // 合并入库信息到提交数据中
                  const subData = {
                    ...this.ruleForm,
                    ...res.subData,
                    ...this.storeForm
                  };
                  let request = amStockIn.add;
                  let title = '新增';
                  if (subData.id) {
                    request = amStockIn.edit;
                    title = '编辑'
                  }
                  request(subData).then(response => {
                    this.$notify({
                      title: `${title}成功`,
                      type: 'success',
                      duration: 2500
                    })
                    this.cancelForm();
                  }).catch((e) => {
                    console.log(e);
                  })
                }
              })
            } else {
              this.submitDisabled = false
              return false
            }
          })
        } else {
          this.submitDisabled = false
          return false
        }
      });
    },

    async checkModule() {
      const subData = {
        bindId: this.bindId,
        formData: null,
        formStruct: null,
        formBindToVar: true,
        relation: this.processStructureValue.relation
      };
      if (this.showFormData) {
        return await this.$refs['generateForm'].getData().then(values => {
          subData.formData = JSON.stringify(values);
          subData.formStruct = JSON.stringify(this.formStruct);
          return {
            flag: true,
            subData
          };
        }).catch(() => {
          return {
            flag: false
          };
        })
      } else {
        return Promise.resolve({
          subData,
          flag: true
        });
      }
    },
    // 加载入库信息表单数据
    loadStoreFormData(jsonData) {
      // 从已有数据中加载入库信息
      this.storeForm = {
        inDate: jsonData.inDate || '',
        address: jsonData.address || '',
        orderType: jsonData.orderType || '',
        revertMan: jsonData.revertMan || '',
        snNo: jsonData.snNo || '',
        revertReason: jsonData.revertReason || '',
        handoverUnit: jsonData.handoverUnit || ''
      };

      // 更新验证规则
      this.updateStoreRules();
    },

    // 更新入库表单验证规则
    updateStoreRules() {
      const baseRules = {
        inDate: [
          { required: true, message: '请选择入库时间', trigger: 'change' }
        ],
        address: [
          { required: true, message: '请输入存放地址', trigger: 'blur' }
        ]
      };

      this.storeRules = baseRules;
    },

    // 重置入库表单
    resetStoreForm() {
      this.storeForm = {
        inDate: '',
        address: '',
        orderType: '',
        revertMan: '',
        snNo: '',
        revertReason: '',
        handoverUnit: ''
      };
      this.updateStoreRules();
    },

    cancelForm() {
      this.visible = false;
      // 重置入库表单
      if (this.$refs['storeForm']) {
        this.$refs['storeForm'].resetFields();
      }
      this.resetStoreForm();
      this.$emit('successAction')
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.el-picker-panel__footer .el-button--text.el-picker-panel__link-btn {
  display: none;
}
</style>

<style lang="scss" rel="stylesheet/scss">
.no-atTheMoment {
  .el-picker-panel__footer .el-button--text.el-picker-panel__link-btn {
    display: none;
  }
}

.task-dialog .dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-bottom: 20px;
}
</style>
