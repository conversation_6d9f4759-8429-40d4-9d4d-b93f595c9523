<template>
  <div class="multi-select-tag">
    <div class="multi-select-tag-left">
      <span v-for="(item,index) in categories" :key="index" class="multi-select-tag-box">
        <h3 class="tag-top-name">{{ item.fv1 }}</h3>
        <tag-group
          :ref="item.fv1"
          :tag-item="item"
          @updateSelectedTags="updateSelectedTags"
        />
      </span>
    </div>

    <div class="multi-select-tag-right">
      <!--重置标签-->
      <el-button
        class="multi-select-tag-button"
        round
        size="mini"
        style="display: block;margin-bottom: 10px;"
        type="warning"
        @click="resetTag()"
      >重置
      </el-button>
      <span v-for="(item,index) in finalShowSelectedTags" :key="index" class="multi-select-tag-box">
        <h3 class="tag-top-name">{{ index }}</h3>
        <div v-for="(subItem,subIndex) in item" :key="subIndex" class="tag-detail-box">
          <h4>{{ subIndex }}</h4>
          <div class="all-tag">
            <el-tag
              v-for="tag in subItem"
              :key="tag.id"
              closable
              style="margin-right:8px;margin-bottom:6px;"
              @close="removeTag(index,tag)"
            >
              {{ tag.fv11 }}
            </el-tag>
          </div>
        </div>
      </span>
    </div>

  </div>
</template>

<script>
import TagGroup from './TagGroup.vue'
import { groupBy } from '@/utils/index'

export default {
  name: 'InfoTagSelect',
  components: { TagGroup },

  props: {
    categories: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      needTag: [],
      selectedTags: {},
      finalShowSelectedTags: {},
      isResetRag: false
    }
  },

  computed: {},
  watch: {
    needTag: {
      handler(val) {
        this.$emit('update:selectedCategories', val, this.selectedTags);
      },
      deep: false
    }
  },
  methods: {
    // 移除tag
    removeTag(index, tag) {
      tag.isChecked = false;
      const dom = this.$refs[index][0]
      dom.updateTags(tag)
    },
    // 重置所有tag
    async resetTag() {
      this.needTag = [];
      this.isResetRag = true;
      Object.keys(this.selectedTags).forEach(key => {
        // 将每个键对应的值设置为空数组
        this.selectedTags[key] = [];
      });
      this.categories.map(item => {
        const dom = this.$refs[item.fv1][0]
        dom.currentSelectTag = []
      })
      this.formatSelectTag()
      this.$emit('resetTag');
    },
    // 接受子组件事件
    updateSelectedTags(tag, groupName) {
      this.$set(this.selectedTags, groupName, tag)
      this.updateInfoTag()
    },
    // 更新最外层父组件事件
    updateInfoTag() {
      const tag = Object.values(this.selectedTags).flat();
      this.formatSelectTag()
      this.needTag = tag;
    },
    formatSelectTag() {
      const data = JSON.parse(JSON.stringify(this.selectedTags));
      for (const key in data) {
        const newObj = groupBy(data[key], 'fv10')
        data[key] = newObj
      }
      this.finalShowSelectedTags = data
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.multi-select-tag {
	display: flex;
	width: 100%;
	padding-bottom: 50px;

	.multi-select-tag-left {
		width: 50%;
		display: flex;
		flex-direction: column;
		overflow-y: auto;
		height: calc(100vh - 80px);
		padding-right: 20px;

		.multi-select-tag-box {
			display: inline-block;
			padding-right: 10px;
			border-right: 1px solid #e8e8e8;
			box-sizing: border-box;

		}
	}

	.multi-select-tag-right {
		width: 50%;
		padding: 50px 20px 0 10px;
		box-sizing: border-box;
		position: relative;
		overflow-y: auto;
		height: calc(100vh - 80px);

		.tag-detail-box {
			margin-left: 10px;
		}

		.all-tag {
			margin-top: 5px;
			margin-left: 10px;
		}

		.multi-select-tag-button {
			position: absolute;
			right: 0;
			top: 0px;
		}
	}

	.tag-top-name {
		font-size: 1rem;
		font-weight: 500;
		font-weight: bold;
	}

}
</style>
