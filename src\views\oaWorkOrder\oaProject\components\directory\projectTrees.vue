<template>
  <div class="project-tree">
    <div class="head-container">
      <el-input
        v-model="projectKey"
        class="filter-item"
        clearable
        placeholder="输入名称搜索"
        prefix-icon="el-icon-search"
        size="small"
        @input="loadProjectNode"
      />
    </div>
    <div class="oper-button">
      <crudOperation :permission="permission">
        <el-button
          slot="right"
          class="filter-item"
          icon="el-icon-refresh-left"
          size="mini"
          type="warning"
          @click="onClickLabel()"
        >重置
        </el-button>
        <el-popconfirm
          slot="right"
          style="margin-left: 10px;"
          title="确定要删除这些目录吗？"
          @confirm="delMany()"
        >
          <el-button
            slot="reference"
            v-permission="permission.delMany"
            size="mini"
            type="danger"
          >
            批量删除
          </el-button>
        </el-popconfirm>
        <el-dropdown slot="right" v-permission="permission.addCatalogWithTemplate" @command="createCatalogueDir">
          <el-button class="filter-item" size="mini" style="margin-left: 15px;" type="primary">
            生成目录<i class="el-icon-arrow-down el-icon--right" />
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="item in catalogueList" :key="item.id" :command="item.id">
              {{ item.title }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </crudOperation>
    </div>
    <div class="tree-box">
      <div v-permission="permission.delMany" class="all-check">
        <el-checkbox v-model="checkAllProejct" :indeterminate="isIndeterminate" @change="handleCheckAllChange">全选
        </el-checkbox>
      </div>

      <el-tree
        ref="tree"
        :data="projectList"
        :expand-on-click-node="false"
        :load="loadProjectNode"
        :props="defaultProps"
        :show-checkbox="currentPlanning==3 && checkPer(permission.delMany)"
        highlight-current
        lazy
        node-key="id"
        @node-click.self="onClickLabel"
        @check-change="checkChange"
      >
        <template v-slot="{ node, data }">
          <span class="custom-tree-node">

            <el-popover
              placement="right"
              popper-class="poject-tree-el-popover"
              trigger="hover"
            >
              <el-button
                v-if="formatJunction2(data)"
                size="mini"
                type="text"
                @click="goJunction2(node,data)"
              >
                路口
              </el-button>
              <el-button
                v-permission="permission.add"
                size="mini"
                type="text"
                @click="addChildrenNode(node,data)"
              >
                新增目录
              </el-button>
              <el-button
                v-if="data.fv1 =='目录'"
                v-permission="permission.add"
                size="mini"
                type="text"
                @click="addTask(node,data)"
              >
                新增任务
              </el-button>
              <el-button
                v-if="data.fv1 =='目录'"
                v-permission="permission.upload"
                size="mini"
                type="text"
                @click="uploadProject(data,node)"
              >
                上传
              </el-button>
              <el-button
                v-if="data.fv1 =='目录'"
                v-permission="permission.edit"
                size="mini"
                type="text"
                @click="editChildrenNode(node,data)"
              >编辑
              </el-button>
              <!--<el-button-->
              <!--  v-permission="permission.add"-->
              <!--  size="mini"-->
              <!--  type="text"-->
              <!--  @click="editStatus(data)"-->
              <!--&gt;-->
              <!--  修改状态-->
              <!--</el-button>-->
              <el-popconfirm
                style="margin-left: 10px;"
                title="确定要删除这个目录吗？"
                @confirm="doDelete(node,data)"
              >
                <el-button
                  v-if="data.fv1 =='目录'"
                  slot="reference"
                  v-permission="permission.del"
                  size="mini"
                  type="text"
                >
                  删除
                </el-button>
              </el-popconfirm>
              <p
                slot="reference"
                class="node-label"
              >
                {{ data.fv16 }}  {{ data.name }}
                <!--<span v-if="data.fv12" :style="{ color: statusColor(data) }" class="node-label">-->
                <!--  ({{ data.fv12 }})-->
                <!--</span>-->
              </p>

            </el-popover>
          </span>
        </template>
      </el-tree>
    </div>

    <!-- 目录弹框 -->
    <add-directory
      v-if="addDirectoryVisible"
      ref="addDirectoryRef"
      @success="successOperate"
    />
  </div>
</template>

<script>
import CRUD, { form, presenter } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation.vue'
import oaPmTree from '@/api/oaWorkOrder/oaPmTree'
import { mapGetters } from 'vuex';
import AddDirectory from '@/views/oaWorkOrder/oaProject/components/directory/AddDirectory.vue';
import oaPmCatalog from '@/api/oaWorkOrder/oaPmCatalog';

const defaultForm = { id: null }
export default {
  name: 'ProjectTree',
  components: { crudOperation, AddDirectory },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: false,
      title: '项目',
      url: 'api/oaPmTree',
      sort: ['createTime,desc'],
      query: { enabled: 1, size: 999 },
      crudMethod: { ...oaPmTree },
      optShow: {
        add: true,
        edit: false,
        del: false,
        download: false,
        reset: false,
        rightGroup: false
      }
    })
  },
  mixins: [presenter(), form(defaultForm)],
  dicts: ['project_status'],
  props: {
    currentPlanning: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      checkAllProejct: false, // 是否全选
      isIndeterminate: false, // 是否半选状态
      addDirectoryVisible: false,
      inputValue: '',
      hasSelectUser: false,
      hasSelectFile: false,
      projectKey: '',
      defaultProps: {
        isLeaf: 'leaf',
        children: 'children', // 子节点字段名
        label: 'label' // 节点文本字段名
      },
      permission: {
        add: ['admin', 'oaPmTree:add'],
        edit: ['admin', 'oaPmTree:edit'],
        del: ['admin', 'oaPmTree:del'],
        delMany: ['admin', 'oaPmTree:delMany'],
        addMember: ['admin', 'oaPmMember:edit'],
        createDir: ['admin', 'oaPmCatalogTemplate:list'],
        updateT: ['admin', 'oaPmTree:updateFormStruct'],
        updateR: ['admin', 'oaPmTree:updateRelation'],
        upload: ['admin', 'oaDocument:add'],
        dispatch: ['admin', 'oaWorkOrderInfo:add'],
        addCatalogWithTemplate: ['admin', 'oaPmTree:addCatalogWithTemplate']
      },
      bindId: '',
      projectList: [],
      dirTypeList: [],
      projectId: '',
      editInfo: {},
      id: 1,
      type: 1,
      currentProjectName: '',
      taskConfig: {},
      catalogueList: []
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ]),
    statusColor() {
      // const colors = ['red', '#1890ff'];
      const colors = {
        '已审核': '#1890ff',
        '逾期': 'red'
      }
      return data => colors[data.fv12] || 'red';// 1 是已完成状态，0 是未完成状态
    }
  },
  created() {
    // const { dir_keys } = this.$configData;
    // this.dirTypeList = JSON.parse(dir_keys);
    this.initData()
  },
  methods: {
    createCatalogueDir(command) {
      const loading = this.$loading({
        lock: true,
        text: '生成目录中...',
        spinner: 'el-icon-loading'
      });
      const { task_keys: config } = this.$config;
      const data = {
        templateId: command,
        bindId: config.bindId,
        itemName: this.currentProjectName
      }
      oaPmTree.addCatalogWithTemplate(data).then(res => {
        this.$notify({
          title: '创建成功',
          type: 'success',
          duration: 2500
        })
        loading.close();
        this.loadProjectNode();
      }).finally(() => {
        loading.close();
      })
    },

    // 新建任务
    addTask(node, data) {
      // const config = this.taskConfig.extend.data
      const { task_keys: config } = this.$config;
      data.currentProjectName = this.currentProjectName
      data.bindId = config.bindId;
      const info = {
        data,
        directoryId: data.id,
        currentProjectName: this.currentProjectName,
        projectId: this.projectId,
        type: 1
      }
      this.$emit('addTask', info);
    },

    initData() {
      const { id, projectId, name } = this.$route.query
      this.bindId = id;
      this.currentProjectName = name
      this.projectId = projectId
      this.crud.query.bindId = id;
      // 判断是否有创建目录的权限
      const flag1 = this.checkPer([...this.permission.addCatalogWithTemplate])
      if (flag1) {
        this.getCatalogueList()
      }
    },
    // 获取模板
    getCatalogueList() {
      const query = {
        enabled: 1,
        status: true,
        size: 999,
        sort: 'createTime,desc'
      }
      oaPmCatalog.getSmall(query).then(res => {
        this.catalogueList = res.content || [];
      })
    },
    // 添加
    [CRUD.HOOK.beforeToAdd](crud, form) {
      const { projectId, name } = this.$route.query
      const data = {
        name: name,
        id: projectId,
        fv18: name
      }
      console.log(data, '<===>', 'data')
      this.addOrEditNode('', data, 1);
    },
    // 添加子节点
    addChildrenNode(node, data) {
      console.log(data, '<===>', 'data')
      this.addOrEditNode(node, data, 2);
    },
    // 修改节点
    editChildrenNode(node, data) {
      this.addOrEditNode(node, data, 3);
    },
    // 懒加载数据
    async loadProjectNode(node, resolve) {
      const { projectId, name, code } = this.$route.query;
      let fv18 = ''
      if (node && node.data) {
        const { fv1: nodeFv1, fv18: nodeFv18, name: nodeName } = node?.data;
        if (nodeFv1 === '目录') {
          fv18 = nodeFv18
        } else {
          fv18 = nodeName
        }
      } else {
        fv18 = name;
      }

      // const { bindId } = this.$config.task_keys;
      const sort = 'sort,asc';
      const params = { fv18, sort, enabled: 1, size: 9999, fv1: ['目录', '子项目'], pidNotNull: true };

      if (typeof node !== 'object') {
        if (node) {
          params['name'] = node;
        } else {
          params['pid'] = projectId;
        }
      } else {
        if (node.level !== 0) {
          params['pid'] = node.data.id;
        } else if (projectId) {
          params['pid'] = projectId;
        }
      }
      if (code) {
        params['name'] = code;
        this.projectKey = code
        delete params['pid'];
        const currentRoute = this.$route;
        // 获取当前路由参数
        const currentQuery = { ...currentRoute.query };
        delete currentQuery.code;
        this.$router.replace({ path: currentRoute.path, query: currentQuery });
      }
      try {
        const response = await oaPmTree.getPmTreeSmall(params);
        const data = response.content;
        if (resolve) {
          if (node.level === 0) {
            this.handleFirstClick()
          }
          resolve(data);
        } else {
          this.projectList = data;
          this.handleFirstClick()
        }
      } catch (error) {
        // 处理错误
        console.error('Failed to load project nodes:', error);
      }
    },
    handleFirstClick() {
      this.$emit('onClickLabel');
    },
    doDelete(node, data) {
      oaPmTree.del([data.id]).then(() => {
        this.$notify({
          title: '数据已删除',
          type: 'success',
          duration: 2500
        })
        node.parent.loaded = false;
        node.parent.expand();
      })
    },
    // 去上传
    uploadProject(data, node) {
      this.$emit('uploadFile', data, node);
    },
    // 点击label
    onClickLabel(data) {
      if (!data) {
        this.loadProjectNode();
      } else {
        this.$emit('onClickLabel', data);
      }
    },

    // 添加成功之后的操作
    successOperate(params) {
      this.addDirectoryVisible = false
      const { node } = params;
      if (params.type == 1) {
        this.loadProjectNode()
      } else if (params.type == 2) {
        // node子节点添加
        node.loaded = false;
        node.expand();
      } else if (params.type == 3) {
        node.parent.loaded = false;
        node.parent.expand();
      }
    },
    // 增加或者编辑子级目录
    addOrEditNode(node, data, type) {
      const query = {
        type: type,
        // bindId: this.$route.query.id,
        pid: data.id,
        name: data.name,
        node: node,
        id: data.id,
        fv18: ''
      };
      if (type == '1') {
        query.fv18 = data.fv18;
      } else {
        if (data.fv1 === '子项目') {
          query.fv18 = data.name
        } else {
          query.fv18 = data.fv18
        }
      }
      this.addNode(query);
    },
    // 点击上传之后的操作
    async addNode(query) {
      this.addDirectoryVisible = true
      await this.$nextTick()
      this.$refs.addDirectoryRef.init(query);
    },
    formatJunction2(data) {
      if (typeof data === 'object' && data !== null) {
        if (data.extend?.data?.['9'] === '路口') {
          const content = data.extend?.data?.['1'];
          if (content) {
            // 使用正则表达式匹配格式（例如：SY-269，或者更多大写字母）
            const match = content.match(/[A-Za-z0-9]+-\d+/);
            // 如果匹配成功且得到的格式正确，则返回true
            return match !== null;
          }
        }
      }
      return false;
    },
    extractCode(data) {
      const content = data.extend?.data?.['1'];
      const match = content?.match(/[A-Za-z0-9]+-\d+/);
      return match ? match[0] : null;
    },
    goJunction2(node, data) {
      const code = this.extractCode(data);
      const { bindId, categoryId } = this.$config['Junction_list2'];
      if (code) {
        this.$router.push({
          name: 'JunctionList2',
          query: { formData: code, id: bindId, category: categoryId }
        });
      } else {
        console.log('No valid code found in data', data);
      }
    },
    handleCheckAllChange(val) {
      this.isIndeterminate = false;
      if (this.checkAllProejct) {
        this.isIndeterminate = false
        // 全选
        this.$refs.tree.setCheckedKeys(this.getAllNodes());
      } else {
        // 取消选中
        this.$refs.tree.setCheckedKeys([]);
      }
    },
    // 获取所有选项
    getAllNodes() {
      const rootNodes = this.$refs.tree.store.root;
      const allNodesKeys = [];
      const traverse = (nodes) => {
        console.log(nodes, '<===>', 'nodes')
        nodes.forEach(node => {
          allNodesKeys.push(node.data.id);
          if (node.childNodes && node.childNodes.length > 0) {
            traverse(node.childNodes);
          }
        });
      };
      traverse(rootNodes.childNodes);
      return allNodesKeys;
    },
    delMany() {
      console.log(this.$refs.tree.getCheckedKeys(), '<===>', 'this.$refs.tree.getCheckedKeys()')
      const Ids = this.$refs.tree.getCheckedKeys()
      oaPmTree.del(Ids).then(() => {
        this.$notify({
          title: '数据已删除',
          type: 'success',
          duration: 2500
        })
        this.loadProjectNode()
      })
    },
    checkChange() {
      const currentChecked = this.$refs.tree.getCheckedKeys()
      const allkeys = this.getAllNodes()
      if (currentChecked.length > 0 && currentChecked.length == allkeys.length) {
        this.checkAllProejct = true
      }
      this.isIndeterminate = currentChecked.length > 0 && currentChecked.length < allkeys.length;
    }
  }
}
</script>

<style lang="scss" scoped>
.project-tree {
	.tree-box {
		//padding: 20px 0;
		.all-check {
			margin: 10px 0 10px 10px;
		}

		.current-project {
			max-width: 80%;
			margin-bottom: 10px;
		}

		.custom-tree-node {
			flex: 1;
			display: flex;
			align-items: center;
			// justify-content: space-between;
			font-size: 14px;
			padding-right: 8px;

			.node-label {
				margin-right: 10px;
			}

			.oper-span {
				display: flex;
				align-items: center;
			}

			.node-input {
				::v-deep .el-input__inner {
					height: 24px;
					line-height: 24px;
				}
			}
		}

		::v-deep .el-tree--highlight-current {
			.el-tree-node.is-current
			> .el-tree-node__content {
				background: #ececec;

				.node-label {
					color: #409eff;
				}
			}
		}

		::v-deep .el-tree-node:hover > .el-tree-node__content {

			background: #ececec;

			.node-label {
				color: #409eff;
			}

		}

	}

}

</style>

<style>
.poject-tree-el-popover {
	padding: 5px !important
}
</style>
