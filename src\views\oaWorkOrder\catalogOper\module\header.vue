<template>
  <div
    v-if="crud.props.searchToggle"
  >
    <el-select
      v-model="query.status"
      class="filter-item"
      clearable
      placeholder="请选择是否执行"
      size="small"
      style="width: 180px"
    >
      <el-option label="已执行" value="0" />
      <el-option label="未执行" value="1" />
    </el-select>
    <rrOperation />
  </div>
</template>

<script>
import { header } from '@crud/crud';
import rrOperation from '@crud/RR.operation';

export default {
  components: { rrOperation },
  mixins: [header()],
  props: {
    permission: {
      type: Object,
      required: true
    }
  },
  data() {
    return {}
  },
  methods: {}
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>

</style>
