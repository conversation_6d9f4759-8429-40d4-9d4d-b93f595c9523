/* eslint-disable */
const MultiMarker = function(cThis, marker) {
  var iconUrl = location.origin + '/mapIcon/';
  return {
    map: cThis.map,
    styles: {
      // 摄像头图标
      /** start */
      // 未开始
      noT: new TMap.MarkerStyle({
        width: 25, // 点标记样式宽度（像素）
        height: 25, // 点标记样式高度（像素）
        src: iconUrl + 'noT.png', // 图片路径
        // 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
        anchor: { x: 8, y: 5 }
      }),
      noB: new TMap.MarkerStyle({
        width: 25, // 点标记样式宽度（像素）
        height: 25, // 点标记样式高度（像素）
        src: iconUrl + 'noB.png', // 图片路径
        // 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
        anchor: { x: 8, y: 5 }
      }),
      noR: new TMap.MarkerStyle({
        width: 25, // 点标记样式宽度（像素）
        height: 25, // 点标记样式高度（像素）
        src: iconUrl + 'noR.png', // 图片路径
        // 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
        anchor: { x: 8, y: 5 }
      }),
      noL: new TMap.MarkerStyle({
        width: 25, // 点标记样式宽度（像素）
        height: 25, // 点标记样式高度（像素）
        src: iconUrl + 'noL.png', // 图片路径
        // 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
        anchor: { x: 8, y: 5 }
      }),
      // 施工中
      doT: new TMap.MarkerStyle({
        width: 25, // 点标记样式宽度（像素）
        height: 25, // 点标记样式高度（像素）
        src: iconUrl + 'doT.png', // 图片路径
        // 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
        anchor: { x: 8, y: 5 }
      }),
      doB: new TMap.MarkerStyle({
        width: 25, // 点标记样式宽度（像素）
        height: 25, // 点标记样式高度（像素）
        src: iconUrl + 'doB.png', // 图片路径
        // 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
        anchor: { x: 8, y: 5 }
      }),
      doR: new TMap.MarkerStyle({
        width: 25, // 点标记样式宽度（像素）
        height: 25, // 点标记样式高度（像素）
        src: iconUrl + 'doR.png', // 图片路径
        // 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
        anchor: { x: 8, y: 5 }
      }),
      doL: new TMap.MarkerStyle({
        width: 25, // 点标记样式宽度（像素）
        height: 25, // 点标记样式高度（像素）
        src: iconUrl + 'doL.png', // 图片路径
        // 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
        anchor: { x: 8, y: 5 }
      }),
      okT: new TMap.MarkerStyle({
        width: 25, // 点标记样式宽度（像素）
        height: 25, // 点标记样式高度（像素）
        src: iconUrl + 'okT.png', // 图片路径
        // 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
        anchor: { x: 8, y: 5 }
      }),
      okB: new TMap.MarkerStyle({
        width: 25, // 点标记样式宽度（像素）
        height: 25, // 点标记样式高度（像素）
        src: iconUrl + 'okB.png', // 图片路径
        // 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
        anchor: { x: 8, y: 5 }
      }),
      okR: new TMap.MarkerStyle({
        width: 25, // 点标记样式宽度（像素）
        height: 25, // 点标记样式高度（像素）
        src: iconUrl + 'okR.png', // 图片路径
        // 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
        anchor: { x: 8, y: 5 }
      }),
      okL: new TMap.MarkerStyle({
        width: 25, // 点标记样式宽度（像素）
        height: 25, // 点标记样式高度（像素）
        src: iconUrl + 'okL.png', // 图片路径
        // 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
        anchor: { x: 8, y: 5 }
      }),
    },
    geometries: marker || []
  }
}
export default MultiMarker;
