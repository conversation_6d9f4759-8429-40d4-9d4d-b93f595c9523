<template>
  <el-card class="box-card">
    <div slot="header" class=" box-card-cus">
      <span>项目基本信息</span>
      <div v-permission="permission.editBaseInfo">
        <el-button style=" padding: 3px 0" type="text" @click="updateBaseInfo()">更正基础信息</el-button>
      </div>
    </div>
    <el-descriptions :column="Column" size="medium">
      <el-descriptions-item v-for="filedItem in baseProjectFiled.fields" :key="filedItem.id" :label="filedItem.label">
        {{ filedShowText(baseProjectInfo[filedItem.value]) }}
      </el-descriptions-item>
    </el-descriptions>

    <!--基础信息弹框-->
    <base-form v-if="baseFormVisible" ref="baseFormRef" @success="successEditBase()" />
  </el-card>
</template>

<script>
import { mapGetters } from 'vuex';
import { baseProjectFiled, permission } from '@/views/spider/utils/field';
import { getBaseProject } from '@/views/spider/utils/spider';
import BaseForm from '@/views/spider/spiderArticle/components/baseForm.vue';

export default {
  name: 'BaseInfo',
  components: { BaseForm },

  data() {
    return {
      baseFormVisible: false,
      baseProjectFiled,
      baseProjectInfo: {},
      Column: 2,
      permission
    }
  },
  computed: {
    filedShowText() {
      return function(value) {
        if (value === null || value === undefined || value === 'null') {
          return ''
        }
        return value
      }
    },
    ...mapGetters([
      'currentProject'
    ])
  },
  watch: {},
  created() {
    this.getCurrentProject()
  },
  methods: {
    async getCurrentProject() {
      const queryResult = await getBaseProject({ fv1: this.currentProject.projectName })
      this.baseProjectInfo = queryResult.content[0] || {}
    },
    async updateBaseInfo() {
      this.baseFormVisible = true
      await this.$nextTick();
      this.$refs.baseFormRef.initData(this.baseProjectInfo)
    },
    successEditBase() {
      this.baseFormVisible = false;
      this.getCurrentProject()
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>

.box-card {
	margin-bottom: 20px;

	.box-card-cus {
		padding: 0 16px;
		display: flex;
		justify-content: space-between;
	}
}
</style>
