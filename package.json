{"name": "eladmin-web", "version": "2.6.0", "description": "EL-ADMIN 前端源码", "author": "<PERSON>", "license": "Apache-2.0", "scripts": {"dev": "vue-cli-service serve --open", "dev:pile": "vue-cli-service serve --open --mode pile", "dev:online": "vue-cli-service serve --open --mode online", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "report": "vue-cli-service build --report", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "svgo": "svgo -f src/assets/icons/svg --config=src/assets/icons/svgo.yml", "new": "plop"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "repository": {"type": "git", "url": "https://github.com/elunez/eladmin-web.git"}, "bugs": {"url": "https://github.com/elunez/eladmin/issues"}, "dependencies": {"@antv/g6": "3.1.10", "@antv/g6-editor": "^1.2.0", "@antv/util": "1.3.1", "@babel/plugin-proposal-optional-chaining": "^7.16.7", "@riophae/vue-treeselect": "^0.4.0", "ace-builds": "^1.4.12", "af-table-column": "^1.0.3", "ali-oss": "^6.20.0", "axios": "^0.21.1", "clipboard": "2.0.4", "codemirror": "^5.49.2", "connect": "3.6.6", "core-js": "^2.6.12", "dayjs": "^1.11.5", "e-icon-picker": "1.0.7", "echarts": "^4.2.1", "echarts-gl": "^1.1.1", "echarts-wordcloud": "^1.1.3", "element-ui": "^2.13.2", "file-saver": "1.3.8", "form-making": "1.2.10", "fuse.js": "3.4.4", "js-beautify": "^1.10.2", "js-cookie": "2.2.0", "js-yaml": "^4.1.0", "jsencrypt": "^3.0.0-rc.1", "jszip": "^3.10.1", "mavon-editor": "^2.9.0", "nanoid": "^5.0.7", "normalize.css": "7.0.0", "nprogress": "0.2.0", "numericjs": "^1.2.6", "path-to-regexp": "2.4.0", "qs": "^6.9.1", "screenfull": "4.2.0", "sortablejs": "1.8.4", "vue": "2.6.12", "vue-codemirror": "^4.0.6", "vue-count-to": "1.0.13", "vue-cropper": "0.4.9", "vue-echarts": "^5.0.0-beta.0", "vue-highlightjs": "^1.3.3", "vue-image-crop-upload": "^2.5.0", "vue-json-viewer": "^2.2.22", "vue-router": "3.0.2", "vue-splitpane": "1.0.4", "vue-upload-component": "^2.8.20", "vue2-editor": "^2.10.3", "vuedraggable": "2.20.0", "vuex": "3.1.0", "vxe-table": "^3.8.10", "wangeditor": "^3.1.1", "xlsx": "^0.14.1"}, "devDependencies": {"@babel/core": "^7.23.2", "@babel/parser": "^7.7.4", "@babel/register": "7.0.0", "@vue/babel-plugin-transform-vue-jsx": "^1.2.1", "@vue/cli-plugin-babel": "3.5.3", "@vue/cli-plugin-eslint": "^3.9.1", "@vue/cli-plugin-unit-jest": "3.5.3", "@vue/cli-service": "3.5.3", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.5.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.0", "babel-plugin-transform-remove-console": "^6.9.4", "chalk": "2.4.2", "chokidar": "2.1.5", "connect": "3.6.6", "eslint": "5.15.3", "eslint-plugin-vue": "5.2.2", "html-webpack-plugin": "3.2.0", "http-proxy-middleware": "^0.19.1", "husky": "1.3.1", "lint-staged": "8.1.5", "plop": "2.3.0", "runjs": "^4.3.2", "sass": "^1.26.10", "sass-loader": "^7.1.0", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "0.7.2", "serve-static": "^1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "vue-template-compiler": "2.6.12", "vue-wxlogin": "^1.0.4"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}