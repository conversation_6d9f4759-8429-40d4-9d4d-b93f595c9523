<template>
  <div class="app-container" style="padding: 8px;">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input
          v-model="query.blurry"
          class="filter-item"
          clearable
          placeholder="输入内容模糊搜索"
          size="small"
          style="width: 200px;"
          @keyup.enter.native="crud.toQuery"
        />
        <date-range-picker v-model="query.createTime" class="date-item" />
        <rrOperation />
      </div>
      <crudOperation :permission="permission">
        <!-- 新增 -->
        <el-button
          slot="left"
          v-permission="['admin','extendFileStorage:add']"
          class="filter-item"
          icon="el-icon-upload"
          size="mini"
          style="margin-right:8px;"
          type="primary"
          @click="addFile(true)"
        >文件上传
        </el-button>
        <el-button
          slot="left"
          v-permission="['admin','extendFileStorage:add']"
          class="filter-item"
          icon="el-icon-picture-outline"
          size="mini"
          type="primary"
          @click="addFile(false)"
        >图片上传
        </el-button>
      </crudOperation>
    </div>
    <!--表单组件-->
    <el-dialog
      :before-close="crud.cancelCU"
      :close-on-click-modal="false"
      :title="crud.status.add ? '上传' : '编辑'"
      :visible.sync="crud.status.cu > 0"
      append-to-body
      width="500px"
    >
      <el-form v-if="showFile" ref="form" :model="form" label-width="80px" size="small">
        <el-form-item label="文件名">
          <el-input v-model="form.name" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="上传平台">
          <el-select v-model="form.platform" style="width: 370px;">
            <el-option
              v-for="(item,index) in platformList"
              :key="index"
              :label="index"
              :value="item.extend.data.platform"
            />
          </el-select>
        </el-form-item>
        <!--   上传文件   -->
        <el-form-item v-if="crud.status.add" label="上传">
          <el-upload
            ref="upload"
            :action="form.platform?(cloudfileUploadApi + '?name=' + form.name + '&platform=' + form.platform):(cloudfileUploadApi + '?name=' + form.name)"
            :auto-upload="false"
            :before-upload="beforeUpload"
            :headers="headers"
            :limit="1"
            :on-error="handleError"
            :on-success="handleSuccess"
          >
            <div class="eladmin-upload"><i class="el-icon-upload" /> 添加文件</div>
            <div slot="tip" class="el-upload__tip">可上传任意格式文件，且不超过10M</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <el-form v-else ref="form" :model="form" label-width="80px" size="small">
        <el-form-item label="图片名">
          <el-input v-model="form.name" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="上传平台">
          <el-select v-model="form.platform" style="width: 370px;">
            <el-option
              v-for="(item,index) in platformList"
              :key="index"
              :label="index"
              :value="item.extend.data.platform"
            />
          </el-select>
        </el-form-item>
        <!--   上传图片   -->
        <el-form-item v-if="crud.status.add" label="上传">
          <el-upload
            ref="upload"
            :action="form.platform?(cloudImgUploadApi + '?name=' + form.name + '&platform=' + form.platform +`&thSize=${thSize}`):(cloudImgUploadApi + '?name=' + form.name +`&thSize=${thSize}`)"
            :auto-upload="false"
            :before-upload="beforeImgUpload"
            :headers="headers"
            :limit="1"
            :on-error="handleError"
            :on-success="handleSuccess"
            list-type="picture"
          >
            <div class="eladmin-upload"><i class="el-icon-upload" /> 添加图片</div>
            <div slot="tip" class="el-upload__tip">可上传jpg或png格式图片，且不超过10M</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="crud.cancelCU">取消</el-button>
        <el-button v-if="crud.status.add" :loading="loading" type="primary" @click="upload">确认</el-button>
        <el-button v-else :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </div>
    </el-dialog>
    <!--表格渲染-->
    <el-table
      ref="table"
      v-loading="crud.loading"
      :data="crud.data"
      style="width: 100%;"
      @selection-change="crud.selectionChangeHandler"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="文件名" prop="filename">
        <template slot-scope="scope">
          <el-popover
            :content="scope.row.platform.indexOf('local')!=-1?(baseApi + '/file/' + scope.row.url):scope.row.url"
            placement="top-start"
            title="路径"
            trigger="hover"
            width="200"
          >
            <a
              slot="reference"
              :href="scope.row.url"
              class="el-link--primary"
              style="word-break:keep-all;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;color: #1890ff;font-size: 13px;"
              target="_blank"
            >
              {{ scope.row.filename }}
            </a>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="预览图" prop="path">
        <template slot-scope="{row}">
          <template v-if="row.platform.indexOf('local')!=-1">
            <el-image
              :preview-src-list="[baseApi + '/file/' + row.url]"
              :src="row.thUrl?(baseApi + '/file/' + row.thUrl):(baseApi + '/file/' + row.url)"
              class="el-avatar"
              fit="contain"
              lazy
            >
              <div slot="error">
                <i class="el-icon-document" />
              </div>
            </el-image>
          </template>
          <template v-else>
            <el-image
              :preview-src-list="[row.url]"
              :src="row.thUrl || row.url"
              class="el-avatar"
              fit="contain"
              lazy
            >
              <div slot="error">
                <i class="el-icon-document" />
              </div>
            </el-image>
          </template>
        </template>
      </el-table-column>
      <el-table-column label="文件类型" prop="ext" />
      <el-table-column :show-overflow-tooltip="true" label="大小" prop="size">
        <template slot-scope="scope">
          <div>{{ bytesToMBRounded(scope.row.size) }}M</div>
        </template>
      </el-table-column>
      <el-table-column label="操作人" prop="updateBy" />
      <el-table-column label="操作日期" prop="updateTime" />
    </el-table>
    <!--分页组件-->
    <pagination />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getToken } from '@/utils/auth'
import { bytesToMBRounded } from '@/utils'
import crudFile from '@/api/tools/cloudStorage'
import { findOptions } from '@/api/system/globalConfig'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import DateRangePicker from '@/components/DateRangePicker'
import { getConfig } from '@/utils/getConfigData'

const defaultForm = { id: null, name: '', platform: '' }
export default {
  components: { pagination, crudOperation, rrOperation, DateRangePicker },
  cruds() {
    return CRUD({ title: '文件', url: 'api/extendFileStorage', crudMethod: { ...crudFile }})
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      delAllLoading: false,
      loading: false,
      headers: { 'Authorization': getToken() },
      permission: {
        edit: ['admin', 'extendFileStorage:edit'],
        del: ['admin', 'extendFileStorage:del']
      },
      showFile: true,
      platformList: {},
      thSize: '' // 缩略图大小
    }
  },
  computed: {
    ...mapGetters([
      'baseApi',
      'cloudfileUploadApi',
      'cloudImgUploadApi'
    ])
  },
  created() {
    this.crud.optShow.add = false;
    this.getPlatform();
    this.getThSize();
  },
  methods: {
    bytesToMBRounded,
    async getThSize() {
      const data = { key: 'upload_image_thsize' }
      await getConfig(data).then(res => {
        this.thSize = res.extend.data.option
      });
    },
    // 上传文件
    upload() {
      this.$refs.upload.submit()
    },
    beforeUpload(file) {
      let isLt2M = true
      isLt2M = file.size / 1024 / 1024 < 10
      if (!isLt2M) {
        this.loading = false
        this.$message.error('上传文件大小不能超过 10MB!')
      }
      this.form.name = file.name
      return isLt2M
    },
    beforeImgUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
      let isLt2M = true
      isLt2M = file.size / 1024 / 1024 < 10;
      if (!isJPG) {
        this.loading = false
        this.$message.error('必须上传jpg或png图片格式!');
      }
      if (!isLt2M) {
        this.loading = false
        this.$message.error('上传文件大小不能超过 10MB!')
      }
      this.form.name = file.name;
      return isLt2M && isJPG;
    },
    handleSuccess(response, file, fileList) {
      this.crud.notify('上传成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      this.$refs.upload.clearFiles()
      this.crud.status.add = CRUD.STATUS.NORMAL
      this.crud.resetForm()
      this.crud.toQuery()
    },
    // 监听上传失败
    handleError(e, file, fileList) {
      const msg = JSON.parse(e.message)
      this.$notify({
        title: msg.message,
        type: 'error',
        duration: 2500
      })
      this.loading = false
    },
    addFile(isFile) {
      if (isFile) {
        this.showFile = true;
      } else {
        this.showFile = false;
      }
      this.crud.toAdd();
    },
    getPlatform() {
      findOptions({ type: 'STORAGE', enabled: 1 }).then(res => {
        if (res && res.STORAGE) {
          this.platformList = res.STORAGE;
        }
      })
    }
  }
}
</script>

<style scoped>
::v-deep .el-image__error, .el-image__placeholder {
	background: none;
}

::v-deep .el-image-viewer__wrapper {
	top: 55px;
}
</style>
