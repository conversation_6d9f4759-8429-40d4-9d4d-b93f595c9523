<template>
  <el-dialog
    title="批量入库订单"
    :visible.sync="dialogVisible"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="batch-import-content">
      <!-- 提示文字 -->
      <div class="tip-text">
        选择要导入的Excel文件
      </div>

      <!-- 上传组件 -->
      <el-upload
        ref="upload"
        class="upload-demo"
        drag
        :action="uploadUrl"
        :headers="uploadHeaders"
        :on-success="handleSuccess"
        :on-error="handleError"
        :before-upload="beforeUpload"
        :on-change="handleFileChange"
        :on-remove="handleFileRemove"
        :file-list="fileList"
        :auto-upload="false"
        :limit="1"
        :multiple="false"
        accept=".xlsx,.xls"
        :show-file-list="false"
        :style="{ width: '184px', height: '180px' }"
      >
        <div v-if="fileList.length === 0">
          <i class="el-icon-upload" />
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        </div>
        <div v-else class="uploaded-file">
          <i class="el-icon-document" />
          <span>{{ fileList[0].name }}</span>
          <el-button type="text" class="remove-btn" @click="removeFile">
            <i class="el-icon-close" />
          </el-button>
        </div>
      </el-upload>

      <!-- 模板下载链接 -->
      <div class="template-download">
        <el-link type="primary" @click="downloadTemplate">导入模板下载</el-link>
      </div>
    </div>

    <!-- 底部按钮 -->
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" :loading="uploading" @click="handleConfirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getToken } from '@/utils/auth'
import { mapGetters } from 'vuex'
import { importStockIn } from '@/api/property/amOrder'
export default {
  name: 'BatchImportDialog',
  data() {
    return {
      dialogVisible: false,
      fileList: [],
      uploading: false,
      uploadUrl: '', // 上传接口地址，暂时为空
      uploadHeaders: {
        'Authorization': getToken()
      }
    }
  },
  computed: {
    ...mapGetters([
      'omOaAPi'
    ])
  },
  methods: {
    // 显示对话框
    show() {
      this.dialogVisible = true
      this.fileList = []
      this.uploading = false
      // TODO: 设置实际的上传接口地址
      this.uploadUrl = `${this.omOaAPi}/amOrder/batchImport`
    },

    // 关闭对话框
    handleClose() {
      this.dialogVisible = false
      this.fileList = []
      this.uploading = false
    },

    // 文件变化时的回调
    handleFileChange(file, fileList) {
      console.log('文件变化:', file, fileList)
      this.fileList = fileList
    },

    // 移除文件时的回调
    handleFileRemove(file, fileList) {
      console.log('移除文件:', file, fileList)
      this.fileList = fileList
    },

    // 手动移除文件
    removeFile() {
      this.fileList = []
      this.$refs.upload.clearFiles()
    },

    // 上传前验证
    beforeUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.type === 'application/vnd.ms-excel'
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isExcel) {
        this.$message.error('只能上传Excel文件!')
        return false
      }
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!')
        return false
      }
      return true
    },

    // 确定按钮点击
    handleConfirm() {
      if (this.fileList.length === 0) {
        this.$message.warning('请先选择要上传的文件')
        return
      }

      this.uploading = true

      // 上传文件
      const formData = new FormData()
      formData.append('file', this.fileList[0].raw)
      importStockIn(formData).then(() => {
        this.$message.success('批量入库成功')
        this.handleClose()
        // 触发父组件刷新数据
        this.$emit('success')
      }).catch((e) => {
        console.log(e)
      }).finally(() => {
        this.uploading = false
      })
    },

    // 上传成功回调
    handleSuccess(_response, _file, _fileList) {
      this.uploading = false
    },

    // 上传失败回调
    handleError(err, _file, _fileList) {
      this.uploading = false
      console.error('文件上传失败:', err)
      this.$message.error('上传失败，请重试!')
    },

    // 下载模板
    downloadTemplate() {
      const templateUrl = `https://routine-fatoan.oss-cn-beijing.aliyuncs.com/asset/2025/07/30/入库-模版_1753855617_.xlsx`
      window.open(templateUrl)
    }
  }
}
</script>

<style lang="scss" scoped>
.batch-import-content {
  .tip-text {
    margin-bottom: 20px;
    font-size: 14px;
    color: #606266;
  }

  .upload-demo {
    width: 184px;
    margin: 0 auto 20px;

    .uploaded-file {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      border: 2px dashed #d9d9d9;
      border-radius: 6px;
      background-color: #fafafa;

      i.el-icon-document {
        font-size: 24px;
        color: #409EFF;
        margin-right: 8px;
      }

      span {
        flex: 1;
        color: #606266;
        font-size: 14px;
      }

      .remove-btn {
        color: #f56c6c;
        padding: 0;
        margin-left: 8px;

        &:hover {
          color: #f78989;
        }
      }
    }
  }

  .template-download {
    text-align: center;
    margin-top: 10px;
  }

}

.dialog-footer {
  text-align: right;
}

::v-deep .el-upload-dragger {
  width: 100%;
}
</style>
