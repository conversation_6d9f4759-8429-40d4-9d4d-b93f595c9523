<template>
  <div>
    <div v-show="isShowOptions.formShow" class="search-box">
      <el-input
        v-model="query.title"
        :placeholder="searchTitlePlaceholder"
        class="filter-item"
        clearable
        size="small"
        style="width: 220px;"
        @keyup.enter.native="searchMapByEnter"
      />
      <span style="padding-left:10px;padding-right:10px;">
        <el-button
          class="filter-item"
          icon="el-icon-search"
          size="small"
          type="success"
          @click="searchMapByEnter"
        >搜索</el-button>
      </span>
      <el-select
        v-model="query.category"
        class="filter-item"
        placeholder="请选择部件"
        size="small"
        style="width: 200px"
        @change="changeCategory"
      >
        <el-option
          v-for="item in assetsList"
          :key="item.id"
          v-permission="['admin',item.fv3]"
          :label="item.label"
          :value="item.id"
        />
      </el-select>
    </div>
    <el-button
      v-if="justAutoPilot()"
      :disabled="!showTgaDisabled"
      :loading="!showTgaDisabled"
      class="view-tag"
      icon="el-icon-view"
      size="mini"
      type="primary"
      @click="openDrawer"
    >标签
    </el-button>
    <!--抽屉——分类标签-->
    <LabelClassify
      ref="labelClassify"
      @update:showTgaDisabled="upDateTagDisabled"
      @update:selectedTag="handleSelectedLabel(...arguments)"
    />
    <foRm
      v-show="isShowOptions.isForm"
      :cid="id"
      :cname="name"
      :latitude="lat + ''"
      :longitude="lng + ''"
      @cancel="cancel"
      @success="success"
    />
  </div>
</template>
<script>
/* eslint-disable */
import { getOmAssetSmall, getDetailWithX, getOmAssetAutoPilot, findAssetWithTagAndTop } from '@/api/parts/assets';
import crudCategory from '@/api/system/category';
// import { getConfig } from '@/utils/getConfigData';
import MultiMarker from '../SupportingFile/MultiMarker';
import foRm from './editForm.vue';
import LabelClassify from './LabelClassify.vue';
import { isHasPermission } from '@/utils/permission'
import {
	defaultContent,
	xlAssetContent,
	styleIconFun,
	getCode,
	getPosition,
	searchConfig,
	setConfig, autoPilotsContent,
} from '../SupportingFile/commonFun'
import { isOnline } from "@/utils/index"

const currentConfig = setConfig()
/** 给信息框的点击事件添加方法 */
var cthis = null;
var cdata = null;
var mapClick = null;

function formatAssets(categoryId, obj) {
	for (const key in obj) {
		if (obj[key].categoryId == categoryId) {
			return obj[key];
		}
	}
	return null; // 如果未找到则返回null
}

window.infoClick = function (e) {
	const pointData = formatAssets(cdata.geometry.properties.type, currentConfig);
	const properties = cdata.geometry.properties;
	const position = cdata.geometry.position;

	function navigateTo(name, query) {
		cthis.$router.push({name, query});
	}

	if (e === 'xunjian') {
		navigateTo(pointData.inspectsName, {
			id: pointData.inspectsBindId,
			category: properties.type,
			omAssetID: properties.backstageId,
			omAssetTitle: properties.title
		});
	} else if (e === 'details') {
		navigateTo(pointData.name, {
			id: pointData.bindId,
			rowId: properties.backstageId,
			type: 'see'
		});
	} else if (e === 'edit') {
		const mapClick = document.getElementById('mapContainer');
		cthis.isShowOptions.isForm = true;
		cthis.isShowOptions.formShow = false;
		mapClick.style.cursor = 'crosshair';
		cthis.markerLayer.setGeometries([]);
		cthis.id = properties.backstageId + '';
		cthis.name = properties.title + '';
		cthis.editMarker[0] = {
			id: 1000000, //点标记唯一标识
			styleId: 'iconidet', //指定样式id
			position: new TMap.LatLng(position.lat, position.lng), //点标记坐标位置
			properties: {
				title: properties.title,
				type: properties.type,
				backstageId: properties.backstageId
			}
		};
		cthis.lat = position.lat;
		cthis.lng = position.lng;
		cthis.markerLayer.setGeometries(cthis.editMarker);
		cthis.map.on('click', cthis.onMapClick);
	} else if (e === 'xlAsset') {
		navigateTo(pointData.name, {
			bindId: pointData.bindId,
			categoryId: pointData.categoryId,
			fv4: properties.fv4,
		});
	} else if (e === 'xlAssetInspect') {
		navigateTo(pointData.inspectsName, {
			omAssetID: properties.id,
			omAssetTitle: properties.title,
			fv4: properties.fv4,
			type: 1
		});
	} else if (e === 'autoPilots') {
		navigateTo(pointData.name, {
			fv4: properties.fv4,
		});
	}
	cthis.infoWindow.close();
};
export default {
	name: 'MarkerList',
	components: {
		foRm,
		LabelClassify
	},
	props: {
		map: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			isLoadAll: true,
			markerLayer: null,
			markerLabel: null,
			infoWindow: null,
			query: {
				title: '',
				category: ""
			},
			markList: [],
			labelList: [],
			assetsList: [],
			page: 0,
			//修改位置
			lat: '',
			lng: '',
			id: '',
			name: '',
			editMarker: [],
			isShowOptions: {
				isForm: false,
				formShow: true
			},
			searchTitlePlaceholder: '输入路口名称',
			searchTitle: 'title',
			isFist: true,
			isSearch: false,
			currentConfig,
			showTgaDisabled: false
		};
	},
	watch: {
		map(val, old) {
			this.initData();
		},
		'query.category'(newValue) {
			this.query.title = '';
			let searchConfigData = searchConfig(newValue);
			this.searchTitlePlaceholder = searchConfigData.placeholder;
			this.searchTitle = searchConfigData.searchTitle;
		}
	},
	created() {
		// 为了防止本地联调时多次调用，特意加上判断，如果需要加载全部数据(点位)，请注释下面这行代码
		this.isLoadAll = isOnline()
	},
	mounted() {
		cthis = this;
	},
	beforeDestroy() {
		this.markList = [];
		this.markerLayer = null;
		this.markerLabel = null;
		this.infoWindow = null;
		this.lat = '';
		this.lng = '';
		this.id = '';
		this.name = '';
		cthis = null;
		cdata = null;
		mapClick = null;
	},
	methods: {
		upDateTagDisabled() {
			setTimeout(() => {
				this.showTgaDisabled = true;
			}, 1000);
		},
		/** 地图初始化操作 */
		initData() {
			// 初始化marker
			this.setMaker();
			// 信息弹窗
			this.infoWin();
			// 地图放大缩小
			this.mapZoomChange();
			// 初始化label
			this.infoLabel()
		},
		// 通过输入框或者回车查询
		searchMapByEnter(e) {
			this.isSearch = true;
			this.searchMap()
		},
		/** 通过条件搜索 */
		async searchMap() {
			this.markList = [];
			this.labelList = [];
			this.page = 0;
			this.infoWindow.close();
			await this.setMaker();

			// await this.getSmallMap();
			// this.markerLayer.setGeometries([]);
			// this.markerLayer.setGeometries(this.markList);
		},
		/** 如果是第一次进入也页面需要获取部件 */
		async getParts() {
			this.assetsList = [];
			const {categoryId, bindId} = currentConfig.Asset_class;

			try {
				const resCategory = await crudCategory.getCategory({pid: categoryId, bindId, enabled: 1});
				const data = resCategory.content;

				// 使用数组过滤方法获取符合权限条件的项
				this.assetsList = data.filter(item => isHasPermission(['admin', item.fv3]));
				this.isFist = false;
				// 如果有符合条件的项，将第一个项的ID赋值给 query.category
				if (this.assetsList.length > 0) {
					this.query.category = this.assetsList[0].id;
					await this.$nextTick()
					this.justLoadLabel()
				}
			} catch (error) {
				console.error('Error fetching categories:', error);
			}
		},
		/** 设置点位 */
		async setMaker() {
			if (this.isFist) {
				await this.getParts()
			}
			await this.getSmallMap();
			if (this.markerLayer) {

				if (this.isSearch) {
					this.markerLayer.setMap(null);
					this.markerLayer = new TMap.MultiMarker(MultiMarker(this, this.markList));
				} else {
					this.markerLayer.setGeometries([]);
					this.markerLayer.setGeometries(this.markList);
				}
				this.markerLabel.setGeometries([]);
			} else {
				this.markerLayer = new TMap.MultiMarker(MultiMarker(this, this.markList));
			}

			if (this.markerLabel) {
				this.markerLabel.updateGeometries([])
				this.markerLabel.updateGeometries(this.labelList);
			}
			// 监听marker点击事件
			this.markerLayer.on('click', this.clickMaker);

		},
		/** 点击maker点位打开信息窗 */
		clickMaker(evt) {
			let info = evt.geometry
			cdata = evt;
			let content = ''
			console.log(this.justAutoPilot(), '<===>', 'this.justAutoPilot()')
			if (this.query.category == currentConfig.xl_asset_key.categoryId) {
				content = xlAssetContent(info)
			} else if (this.justAutoPilot()) {
				content = autoPilotsContent(info)
			} else {
				if (!evt.geometry || !evt.geometry.properties || !evt.geometry.properties.type || !evt.geometry.properties.title || !evt.geometry.position) return;
				content = defaultContent(info, this.assetsList);
			}
			this.infoWindow.open(); // 打开信息窗
			this.infoWindow.setPosition(evt.geometry.position); // 设置信息窗位置
			this.infoWindow.setContent(content); // 设置信息窗内容
		},
		/** 创建信息窗口 */
		infoWin() {
			this.infoWindow = new TMap.InfoWindow({
				map: this.map,
				position: new TMap.LatLng(0, 0),
				offset: {x: -12, y: -32} // 设置信息窗相对position偏移像素，为了使其显示在Marker的上方
			});
			this.infoWindow.close(); // 初始关闭信息窗关闭
		},
		/** 创建label */
		infoLabel() {
			this.markerLabel = new TMap.MultiLabel({
				id: 'label-layer',
				map: this.map,
				styles: {
					label: new TMap.LabelStyle({
						color: '#050504', // 颜色属性
						size: 8, // 文字大小属性
						offset: {x: 0, y: 4}, // 文字偏移属性单位为像素
						angle: 0, // 文字旋转属性
						alignment: 'center', // 文字水平对齐属性
						verticalAlignment: 'middle', // 文字垂直对齐属性
					}),
				},
				geometries: [],
			});
			// this.infoLabel.close(); // 初始关闭信息窗关闭
		},
		/**格式化请求参数和接口 */
		async requestData() {
			let json = {
				categoryId: this.query.category,
				enabled: 1,
				size: 500,
				page: this.page++,
			};
			if (this.query.title) {
				json[this.searchTitle] = this.query.title;
			}
			let requestApi = getOmAssetSmall;
			let isXlAsset = false;
			if (this.query.category == currentConfig.xl_asset_key.categoryId) {
				requestApi = getDetailWithX;
				isXlAsset = true;
			} else if (this.query.category == currentConfig.auto_pilot3_key.categoryId) {
				requestApi = getOmAssetAutoPilot;
				json['type.fieldName'] = 'fv1';
				json['type.values'] = '路口';
			} else if (this.query.category == currentConfig.auto_pilot4_key.categoryId) {
				requestApi = getOmAssetAutoPilot;
				json['type.fieldName'] = 'fv1';
				json['type.values'] = '路口';
			}
			try {
				const res = await requestApi(json, {isCancel: true, cancelKey: 'omAssetSmall'});
				return {data: res.content, isXlAsset: isXlAsset, totalElements: res.totalElements};
			} catch (e) {
				console.log(e);
				return {data: [], isXlAsset: isXlAsset, totalElements: 0};
			}
		},
		/**获取点位 */
		async getSmallMap(resolve) {
			const {data, isXlAsset, totalElements} = await this.requestData();
			this.processData(data, isXlAsset, totalElements, this.getSmallMap);
		},
		/**处理点位 */
		processData(arr, isXlAsset, totalElements, request) {
			if (arr && arr.length) {
				const {markList, labelList} = this.createMarkersAndLabels(arr, isXlAsset);
				this.updateMapLayers(markList, labelList);
				if (this.isLoadAll && this.markList.length < totalElements) {
					request && request();
				}
			} else {
				this.updateMapLayers(this.markList, this.labelList);
				this.isSearch = false;
			}
			this.isSearch = false;
		},
		/**处理点位参数 */
		createMarkersAndLabels(arr, isXlAsset) {
			const markList = [];
			const labelList = [];
			const zoom = this.map.getZoom().toFixed(0) < 13 ? '1' : '';
			const scaleStart = this.isSearch ? 3 : 1;

			for (const item of arr) {
				const code = getCode(item);
				const position = isXlAsset ? getPosition(item, 1) : getPosition(item);

				const baseMarker = {
					id: item.id,
					styleId: this.styleIcon(item.status || '', this.query.category + '', zoom, (item.inspect && item.inspect.fv2) ? item.inspect.fv2 : ''),
					position: position,
					markerAnimation: {
						enter: {
							scaleStart: scaleStart,
							scaleEnd: 1,
							duration: 2000,
						},
					},
					properties: {
						id: item.id,
						title: item.title,
						type: this.query.category,
						backstageId: item.id,
						fv4: item.fv4 || '',
						code: code,
						fv19: item.fv19 || '',
					}
				};
				markList.push(baseMarker);

				if (this.justAutoPilot()) {
					const baseLabel = {
						id: item.id,
						styleId: 'label',
						position: position,
						content: item.fv4,
						properties: {
							title: item.title,
							type: this.query.category,
							backstageId: item.id,
							code: code || '',
						}
					};
					labelList.push(baseLabel);
				}
			}

			return {markList, labelList};
		},
		/**更新点位数据 */
		updateMapLayers(markList, labelList) {
			const zoomSize = this.map.getZoom().toFixed(0);

			this.markList = this.markList.concat(markList);
			this.labelList = this.labelList.concat(labelList);

			if (this.markerLayer) {
				// this.markerLayer.setGeometries(this.markList);
				if (this.isSearch) {
					this.markerLayer.setMap(null);
					this.markerLayer = new TMap.MultiMarker(MultiMarker(this, this.markList));
				} else {
					this.markerLayer.setGeometries([]);
					this.markerLayer.setGeometries(this.markList);
				}
				this.markerLabel.setGeometries([]);
			}
			if (this.markerLabel) {
				this.markerLabel.setGeometries(this.labelList);
				this.markerLabel.setVisible(zoomSize > 12);
			}
		},
		changeCategory() {
			this.query.title = '';
			this.searchMap();
			// 需要执行刷新标签
			this.justLoadLabel()
		},
		/** marker点位的icon */
		styleIcon(state, type, zoom, level) {
			let zoomStr = zoom.toString();  // 将 zoom 转换为字符串，以便拼接
			return styleIconFun(state, type, zoomStr, level);
		},
		/** 地图缩放级别显示的内容 */
		mapZoomChange() {
			this.map.on('zoom', () => {
				if (this.isShowOptions.formShow) {
					if (this.map.getZoom() >= 14) {
						this.editMakerIcon('del');
					} else if (this.map.getZoom() <= 13) {
						this.editMakerIcon('add');
					}
				}
				// 	自定驾驶
				if (this.justAutoPilot()) {
					const zoomToSize = {
						10: 0,
						11: 8,
						12: 8,
						13: 8,
						14: 8,
						15: 10,
						16: 10,
						17: 10,
						18: 14,
						19: 14,
						20: 14
					};
					const size = this.map.getZoom().toFixed(0);
					if (size <= 12) {
						this.markerLabel.setVisible(false)
					} else {
						this.markerLabel.setVisible(true)
					}

					const newSize = zoomToSize[size] || 8; // 获取对应的文字大小，默认大小为8
					let y = 4;
					if (size > 13) {
						y = 10
					}
					this.markerLabel.setStyles({
						label: new TMap.LabelStyle({
							color: '#050504', // 保持颜色属性不变
							size: newSize, // 根据缩放级别调整文字大小
							offset: {x: 0, y: y}, // 保持文字偏移属性不变
							angle: 0, // 保持文字旋转属性不变
							alignment: 'center', // 保持文字水平对齐属性不变
							verticalAlignment: 'middle' // 保持文字垂直对齐属性不变
						})
					});
				}
			});
		},
		/** 地图缩放级别坐标的样式图片 */
		editMakerIcon(idType) {
			if (this.markList.length !== 0) {
				this.markList.map(item => {
					if (idType === 'add') {
						if (item.styleId.indexOf('1') === -1) {
							item.styleId += '1';
						}
					} else if (idType === 'del') {
						if (item.styleId.indexOf('1') !== -1) {
							item.styleId = item.styleId.replace('1', '');
						}
					}
				});

				this.markerLayer.setGeometries([]);
				this.markerLayer.setGeometries(this.markList);
			}
		},
		/** 给地图添加点击事件 */
		onMapClick(evt) {
			this.editMarker[1] = {
				id: 1000000, //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
				styleId: 'iconidet', //指定样式id
				position: new TMap.LatLng(evt.latLng.lat, evt.latLng.lng)//点标记坐标位置
			};
			this.markerLayer.setGeometries([]);
			this.markerLayer.setGeometries(this.editMarker);
			this.lat = evt.latLng.getLat().toFixed(6) + '';
			this.lng = evt.latLng.getLng().toFixed(6) + '';
			this.markerLayer.on('click', this.obtain);
		},
		obtain(evt) {
			this.lat = evt.latLng.getLat().toFixed(6) + '';
			this.lng = evt.latLng.getLng().toFixed(6) + '';
			this.infoWindow.close();
		},
		success() {
			/**移除点击事件 */
			this.removeClick(true);
		},
		/** 取消修改 */
		cancel() {
			/**移除点击事件 */
			this.removeClick();
		},
		/**移除点击事件后的处理方法 */
		removeClick(isSuccess) {
			this.map.off('click', this.onMapClick);
			this.isShowOptions.isForm = false;
			this.isShowOptions.formShow = true;
			mapClick.style.cursor = 'default';
			if (this.markerLayer) {
				this.markerLayer.setGeometries([]);
			}
			if (isSuccess) {
				this.searchMap();
			} else {
				this.setMaker();
			}
		},
		openDrawer() {
			this.$refs.labelClassify.labelVisible = true;
		},
		handleSelectedLabel(selectedCategories, query) {
			const hasSelectedCategories = selectedCategories && selectedCategories.length > 0;
			this.page = 0; // 重置分页
			this.markList = []; // 清空现有点位
			this.labelList = [];
			if (hasSelectedCategories) {
				this.getAssetListByLabel(query); // 获取点位
			} else {
				this.getSmallMap();
			}
		},
		async getAssetListByLabel(query) {
			this.isSearch = true;
			let key;
			if (this.query.category == this.$config.auto_pilot3_key.categoryId) {
				key = this.$config.auto_pilot3_key;
			} else if (this.query.category == this.$config.auto_pilot4_key.categoryId) {
				key = this.$config.auto_pilot4_key;
			}
			const {bindId, categoryId} = key;
			// const {bindId, categoryId} = this.$config.auto_pilot3_key;
			const json = {
				page: this.page++,
				size: 200,
				bindId,
				categoryId,
				enabled: [1],
				...query
			};
			const res = await findAssetWithTagAndTop(json, {isCancel: true, cancelKey: 'omAssetSmall'});
			const arr = res.content;
			const totalElements = res.totalElements;
			this.processData(arr, false, totalElements, () => this.getAssetListByLabel(query));
		},
		// 判断是不是自动驾驶3.0或者自动驾驶4.0
		justAutoPilot() {
			return (
					this.query.category == currentConfig.auto_pilot3_key.categoryId ||
					this.query.category == currentConfig.auto_pilot4_key.categoryId
			);
		},
		justLoadLabel() {
			if (this.justAutoPilot()) {
				let name = ""
				if (this.query.category == currentConfig.auto_pilot3_key.categoryId) {
					name = "InfrastructurePlanP"
				} else if (this.query.category == currentConfig.auto_pilot4_key.categoryId) {
					name = "InfrastructurePlanP4"
				}
				this.$refs.labelClassify.getAllTags(name)
			}

		}

	}
}
</script>
<style lang="scss" scoped>
.search-box {
	position: absolute;
	width: 100%;
	left: 20px;
	top: 20px;
	z-index: 2000;
	padding: 10px 0 0 10px;
}

.icon-open {
	position: absolute;
	left: 30px;
	top: 62px;
	font-size: 24px;
	z-index: 2000;
	cursor: pointer;
}

.view-tag {
	position: absolute;
	left: 30px;
	top: 72px;
	cursor: pointer;
	z-index: 2000;
}

</style>
