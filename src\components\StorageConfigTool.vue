<template>
  <div class="storage-config-tool">
    <el-card header="存储平台配置工具">
      <el-row :gutter="20">
        <el-col :span="12">
          <h3>当前存储平台</h3>
          <el-table :data="platformList" border size="small">
            <el-table-column prop="key" label="配置名称" width="150" />
            <el-table-column prop="platform" label="平台标识符" />
            <el-table-column label="类型">
              <template slot-scope="scope">
                <el-tag :type="scope.row.isLocal ? 'success' : 'info'">
                  {{ scope.row.isLocal ? '本地存储' : '云存储' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-col>

        <el-col :span="12">
          <h3>表单存储字段</h3>
          <el-table :data="formFields" border size="small">
            <el-table-column prop="name" label="字段名称" />
            <el-table-column prop="type" label="类型" width="100" />
            <el-table-column prop="platform" label="当前平台" />
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="primary"
                  :disabled="!localPlatform"
                  @click="changePlatform(scope.row)"
                >
                  改为本地
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>

      <el-row style="margin-top: 20px;">
        <el-col :span="24">
          <el-button type="primary" @click="loadConfig">刷新配置</el-button>
          <el-button type="success" :disabled="!hasChanges" @click="saveConfig">保存修改</el-button>
          <el-button type="info" @click="exportConfig">导出配置</el-button>
          <el-button type="warning" @click="resetConfig">重置</el-button>
        </el-col>
      </el-row>

      <el-row v-if="localPlatform" style="margin-top: 20px;">
        <el-col :span="24">
          <el-alert
            :title="`建议使用本地存储平台: ${localPlatform.platform}`"
            type="success"
            :closable="false"
          />
        </el-col>
      </el-row>

      <el-row v-if="!localPlatform" style="margin-top: 20px;">
        <el-col :span="24">
          <el-alert
            title="警告: 未找到本地存储平台配置，请先在系统中配置本地存储平台"
            type="warning"
            :closable="false"
          />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import storageHelper from '@/utils/storageConfigHelper'
import { updateFormStruct } from '@/api/system/globalConfig'

export default {
  name: 'StorageConfigTool',
  props: {
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      platformList: [],
      formFields: [],
      localPlatform: null,
      originalFormStruct: '',
      currentFormStruct: '',
      hasChanges: false
    }
  },
  async mounted() {
    await this.loadConfig()
  },
  methods: {
    async loadConfig() {
      try {
        // 加载存储平台配置
        const platforms = await storageHelper.getStoragePlatforms()
        this.platformList = Object.entries(platforms).map(([key, config]) => {
          const platform = config.extend?.data?.platform || ''
          return {
            key,
            platform,
            config,
            isLocal: platform.toLowerCase().includes('local')
          }
        })

        // 查找本地存储平台
        this.localPlatform = await storageHelper.findLocalStoragePlatform()

        // 解析表单字段
        if (this.formData.formStruct) {
          this.originalFormStruct = this.formData.formStruct
          this.currentFormStruct = this.formData.formStruct
          this.formFields = storageHelper.getFormStorageInfo(this.formData.formStruct)
        }

        this.hasChanges = false
        this.$message.success('配置加载完成')
      } catch (error) {
        this.$message.error('加载配置失败: ' + error.message)
      }
    },

    changePlatform(field) {
      if (!this.localPlatform) {
        this.$message.warning('未找到本地存储平台')
        return
      }

      // 更新表单配置
      this.currentFormStruct = storageHelper.updateFormStoragePlatform(
        this.currentFormStruct,
        field.name,
        this.localPlatform.platform
      )

      // 更新显示的字段信息
      const fieldIndex = this.formFields.findIndex(f => f.name === field.name)
      if (fieldIndex !== -1) {
        this.$set(this.formFields[fieldIndex], 'platform', this.localPlatform.platform)
      }

      this.hasChanges = true
      this.$message.success(`已将 "${field.name}" 修改为本地存储`)
    },

    async saveConfig() {
      try {
        if (!this.formData.id) {
          this.$message.error('缺少表单ID')
          return
        }

        const updateData = {
          id: this.formData.id,
          formStruct: this.currentFormStruct
        }

        await updateFormStruct(updateData)
        this.originalFormStruct = this.currentFormStruct
        this.hasChanges = false
        this.$message.success('配置保存成功')
      } catch (error) {
        this.$message.error('保存失败: ' + error.message)
      }
    },

    exportConfig() {
      const config = {
        platforms: this.platformList,
        formFields: this.formFields,
        localPlatform: this.localPlatform,
        formStruct: this.currentFormStruct
      }

      const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'storage-config.json'
      a.click()
      URL.revokeObjectURL(url)

      this.$message.success('配置已导出')
    },

    resetConfig() {
      this.currentFormStruct = this.originalFormStruct
      this.formFields = storageHelper.getFormStorageInfo(this.originalFormStruct)
      this.hasChanges = false
      this.$message.success('配置已重置')
    }
  }
}
</script>

<style scoped>
.storage-config-tool {
  padding: 20px;
}

.el-table {
  margin-bottom: 20px;
}

h3 {
  margin-bottom: 15px;
  color: #303133;
}
</style>
