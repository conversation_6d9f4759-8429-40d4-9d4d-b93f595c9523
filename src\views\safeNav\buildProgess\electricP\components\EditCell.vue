<template>
  <!--表格行内编辑-->
  <div class="edit-cell-box">
    <el-input
      v-if="editingConfig.currentEditHeader.type==='input'"
      v-model="currentScope.row[header.prop]"
      v-auto-focus
      class="edit-cell"
      size="mini"
      @blur="handleInputBlur"
      @change="() => handleCellChange(currentScope.row, header.prop)"
      @input="(event) => debounced<PERSON>andleMoney(currentScope.row, header.prop)"
    />
    <el-select
      v-else-if="editingConfig.currentEditHeader.type === 'select'"
      v-model="currentScope.row[header.prop]"
      v-auto-focus
      class="filter-item edit-cell"
      placeholder="请选择"
      size="small"
      style="width: 180px"
      @blur="handleInputBlur"
      @change="handleCellChange(currentScope.row, header.prop)"
    >
      <el-option
        v-for="item in editingConfig.dictList"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <el-date-picker
      v-else-if="editingConfig.currentEditHeader.type === 'date'"
      v-model="currentScope.row[header.prop]"
      v-auto-focus
      class="filter-item edit-cell"
      placeholder="选择日期"
      type="date"
      value-format="yyyy-MM-dd"
      @blur="handleInputBlur"
      @change="handleCellChange(currentScope.row, header.prop)"
    />
    <el-cascader
      v-else-if="editingConfig.currentEditHeader.type === 'cascader'"
      v-model="currentScope.row[header.mapKey]"
      v-auto-focus
      :options="categoryList"
      :props="{ label: 'name',value: 'id',children: 'children',expandTrigger: 'hover' }"
      class="filter-item edit-cell"
      @blur="handleInputBlur"
      @change="handleCascader(currentScope.row, header.prop)"
    />

    <el-select
      v-else-if="editingConfig.currentEditHeader.type === 'selectSearch'"
      v-model="currentScope.row[header.prop]"
      v-auto-focus
      :loading="selectLoading"
      :remote-method="remoteSelectUsers"
      class="filter-item edit-cell"
      debounce="500"
      filterable
      placeholder="请输入项目负责人"
      remote
      reserve-keyword
      size="small"
      style="width: 180px"
      @blur="handleInputBlur"
      @change="handleCascader(currentScope.row, header.prop)"
    >
      <el-option
        v-for="item in userList"
        :key="item.id"
        :label="item.username"
        :value="item.username"
      />
    </el-select>
  </div>
</template>

<script>
import numeric from 'numeric';
import { simpleDebounce } from '@/utils/index'
import oaPmTree from '@/api/oaWorkOrder/oaPmTree';
import { getByName } from '@/api/system/user';

export default {
  name: 'EditCell',
  components: {},
  props: {
    editingConfig: {
      type: Object,
      default: () => {
        return {
          cell: null, // 当前编辑的单元格
          currentEditData: {},
          currentEditHeader: {},
          dictList: []
        }
      }
    },
    categoryList: {
      type: Array,
      default: () => {
        return []
      }
    },
    header: {
      type: Object,
      default: () => {
        return {}
      }
    },
    permission: {
      type: Object,
      default: () => {
        return {}
      }
    },
    currentScope: {
      type: Object,
      default: () => {
        return {}
      }
    },
    currentCrud: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      userList: [],
      selectLoading: false,
      debouncedHandleMoney: null,
      flattenCategoryList: []
    }
  },

  computed: {},
  watch: {},
  created() {
    this.initData()
  },
  methods: {
    initData() {
      // 初始化防抖函数
      this.debouncedHandleMoney = simpleDebounce((row, prop) => {
        this.handleMoney(row, prop);
      }, 300,); // 300 毫秒的防抖时间

      // 扁平化数组
      this.flattenCategoryList = this.flattenArray(this.categoryList, 'children');
    },
    // 输入框  先走改变值（走接口） 再走失去焦点(消失） 回车只走改变值
    // 下拉框 先走失去焦点 然后走改变值
    // 级联也是先失去焦点 然后走改变值
    // 日期 先走改变值（走接口） 再走失去焦点(消失） 清空只走改变值
    // 统一处理这些函数
    handleInputBlur() {
      setTimeout(() => {
        this.editingConfig.cell = null
        this.editingConfig.currentEditHeader = {}
      }, 300)
    },
    handleCellChange(row, prop) {
      // 有些需要单独走一次失去焦点
      this.handleInputBlur()
      this.handleParameter(row, prop)
    },
    handleParameter(row, prop) {
      // 需要走接口
      const data = {
        enabled: '1',
        formBindToVar: 0,
        id: row.id,
        ft4: row.ft4, // 部门
        fv2: row.fv2, // 项目类型
        createBy: row.createBy, // 项目负责人
        fv3: row.fv3, // 项目阶段
        fv4: row.fv4, // 项目状态
        fv14: row.fv14, // 毛利率
        fv7: row.fv7, // 收入
        fv6: JSON.stringify(row.fv6), // 级联分类
        fv8: row.fv8, // 已收入金额
        fv9: row.fv9, // 未收入金额
        fv11: row.fv11, // 预算支出
        fv12: row.fv12, // 已支出费用
        fv21: row.fv21, // 未支出费用
        fv13: row.fv13, // 预算结余
        fv15: row.fv15, // 合同开始日期
        fv16: row.fv16, // 合同结束日期
        fv17: row.fv17, // 终验日期
        fv20: row.fv20 // 质保日期
      }
      oaPmTree.edit(data).then(res => {

      }).catch(() => {
        this.currentCrud.refresh()
      })
    },
    // 处理级联
    handleCascader(row, prop) {
      // 对分类级联做特殊处理
      const [idOne, idTwo] = row['fv6'];
      const categoryNames = this.flattenCategoryList.reduce((acc, item) => {
        if (item.id === idOne) acc[0] = item.name;
        if (idTwo && item.id === idTwo) acc[1] = item.name;
        return acc;
      }, []);
      // 如果存在一级分类
      row['cateNamesOne'] = categoryNames[0] || '';
      // 如果存在二级分类
      row['cateNamesTwo'] = categoryNames[1] || '';
      this.handleCellChange(row, prop)
    },

    // 金额计算相关
    // 总金额不可以编辑 fv22(总金额) = fv7(立项阶段收入金额)
    // 未收入金额(元)不可以编辑 fv9(未收入金额(元)) = fv7(立项阶段收入金额) - fv8(立项阶段收入)
    // 预算结余 fv13(预算结余) =fv11(立项结算预算支出) - fv12(已支出费用)
    // 毛利率不可以编辑 fv14(毛利率) = (fv7(立项阶段收入金额)-fv11(立项阶段预算支出))/fv7(立项阶段收入) * 100 四舍五入保留俩位小数
    handleMoney(row, prop) {
      const { regular } = this.editingConfig.currentEditHeader
      if (!regular) {
        return
      } else {
        const reg = new RegExp(regular);
        if (!reg.test(row[prop])) {
          // 输入的值不符合正则表达式
          row[prop] = row[prop].replace(/[^-?\d.]/g, ''); // 移除所有不符合正则的字符
        }
        // 金额需要计算
        const operations = {
          fv7: () => {
            // 总金额 总金额 = 立项阶段收入
            row['fv22'] = this.inputNumFormat(row['fv7'])
            // 未收入金额 = 立项阶段收入 - 已收入金额
            row['fv9'] = this.numSubtract(row['fv7'], row['fv8'])
            // 毛利率
            row['fv14'] = this.grossMarginCalculation(row['fv7'], row['fv11'])
          },
          fv8: () => {
            row['fv9'] = this.numSubtract(row['fv7'], row['fv8'])
          },
          fv11: () => {
            // 预算结余
            row['fv13'] = this.numSubtract(row['fv11'], row['fv12'])
            // 毛利率
            row['fv14'] = this.grossMarginCalculation(row['fv7'], row['fv11'])
          },
          fv12: () => {
            // 预算结余
            row['fv13'] = this.numSubtract(row['fv11'], row['fv12'])
          }
        }
        const operation = operations[prop];
        if (operation) {
          operation();
        } else {
          console.error('Invalid model');
        }
      }
    },
    // 计算函数
    numSubtract(a, b) {
      const num1 = parseFloat(a) || 0.00;
      const num2 = parseFloat(b) || 0.00;
      const num = numeric.sub(num1, num2)
      return this.numformat(num);
    },
    grossMarginCalculation(a, b) {
      const num1 = parseFloat(a) || 0.00;
      const num2 = parseFloat(b) || 0.00;
      if (num1 === 0) {
        return '0.00%'; // 避免除以0的情况
      }
      const c = numeric.sub(num1, num2); // 相减
      const result = numeric.mul(numeric.div(c, num1), 100);
      // return `${ result.toFixed(2) }%`;
      return this.numformat(result) + '%';
    },
    numformat(num) {
      const str = num.toFixed(2).toString()
      // return str.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      return str
    },
    inputNumFormat(num) {
      const num1 = parseFloat(num) || 0.00
      return this.numformat(num1)
    },
    flattenArray(arr, prop) {
      const result = [];

      const flatten = (array) => {
        array.forEach(item => {
          result.push(item);
          if (item[prop] && Array.isArray(item[prop])) {
            flatten(item[prop]);
          }
        });
      };

      flatten(arr);
      return result;
    },
    remoteSelectUsers(query) {
      if (query !== '') {
        this.selectLoading = true;
        const data = {
          enabled: 1,
          userName: query,
          size: 99
        }
        getByName(data).then(res => {
          if (res) {
            this.userList = res || [];
            this.selectLoading = false;
          } else {
            this.userList = [];
          }
        })
      } else {
        this.userList = [];
      }
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.edit-cell {
	position: absolute;
	z-index: 999;
	width: 180px;
	text-align: left;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
}
</style>
