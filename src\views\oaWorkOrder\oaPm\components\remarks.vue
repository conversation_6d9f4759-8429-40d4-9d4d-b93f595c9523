<template>
  <el-dialog
    v-dialog-drag="{minHeight:'800px'}"
    :before-close="concelForm"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :modal="false"
    :modal-append-to-body="false"
    :title="fullTitle"
    :visible.sync="visible"
    append-to-body
    width="800px"
  >
    <!--旧的评论-->
    <RemarkItem
      :all-remarks="oldRemarks"
      :indent-num="0"
      :old-remarks="oldRemarks"
      :permission="permission"
      :project-id="projectId"
      :user="user"
    />
    <el-form
      ref="elForm"
      v-permission="permission.addRemarks"
      :model="elForm"
      :rules="elRule"
      class="remarks"
      label-width="88px"
      size="small"
    >
      <!--新评论的内容-->
      <el-form-item
        label="整改要求"
        prop="detail"
        style="margin-top: 20px"
      >
        <el-input
          v-model="elForm.detail"
          :autosize="{ minRows: 5, maxRows: 20}"
          type="textarea"
        />
      </el-form-item>
      <el-form-item label="" style="margin-top: 20px">
        <el-radio-group v-model="ft2">
          <el-radio label="0">整改要求</el-radio>
          <el-radio label="1">整改合格</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item style="margin-top: 20px">
        <el-button class="submit-button" type="primary" @click="submitAction">
          提交
        </el-button>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer" v>
      <el-button type="text" @click="concelForm">取消</el-button>
      <!--<el-button-->
      <!--  v-permission="permission.addRemarks"-->
      <!--  :disabled="submitDisabled"-->
      <!--  type="primary"-->
      <!--  @click="submitAction"-->
      <!--&gt;-->
      <!--  确定-->
      <!--</el-button>-->
    </div>
  </el-dialog>
</template>

<script>
import oaPmTree from '@/api/oaWorkOrder/oaPmTree'
import { mapGetters } from 'vuex';
import { createID } from '@/utils/index'
import RemarkItem from '@/views/oaWorkOrder/oaPm/components/remarkItem.vue';

export default {
  name: 'Remarks',
  components: {
    RemarkItem
  },
  props: {
    permission: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      title: '整改要求',
      submitDisabled: false,
      iconLabel: '<i class="el-icon-error" />',
      visible: false,
      oldRemarks: [], // 旧的整改
      elForm: {
        pid: 0,
        id: createID(),
        reply: [],
        username: '',
        detail: '',
        currentTime: '',
        placeholder: '请输入回复内容',
        isDelete: 0 // 0是默认值 1是删除了
      },
      elRule: {
        detail: []
      },
      projectName: '',
      projectId: '',
      ft2: 0 // 没有整改 1 是整改 2 是有回复内容
    }
  },

  computed: {
    ...mapGetters([
      'user'
    ]),
    fullTitle() {
      let projectName = ''
      if (this.projectName) {
        projectName = `【${this.projectName}】`
      }
      return `${projectName}${this.title}`;
    }
  },
  methods: {
    // 初始化
    initData(data) {
      this.visible = true;
      this.oldRemarks = data.ft1 ? JSON.parse(data.ft1) : [];
      this.ft2 = data.ft2 ? data.ft2 : '0';
      this.projectName = data.name
      this.projectId = data.id;
    },
    submitAction() {
      this.$refs['elForm'].validate((valid) => {
        if (valid) {
          this.submitDisabled = true;
          this.elForm.username = this.user.username;
          this.elForm.currentTime = this.$dayJS().format('YYYY-MM-DD HH:mm:ss');
          const ft1 = this.elForm.detail === '' ? [...this.oldRemarks] : [...this.oldRemarks, this.elForm];
          const data = {
            enabled: 1,
            ft1: JSON.stringify(ft1),
            id: this.projectId,
            ft2: this.ft2
          }
          oaPmTree.edit(data).then(res => {
            this.submitDisabled = false;
            this.$message({
              message: '添加成功',
              type: 'success'
            })
            // this.$emit('toRefresh')
            this.concelForm();
          })
        } else {
          this.submitDisabled = false
          return false
        }
      })
    },
    concelForm() {
      this.$refs['elForm'].resetFields()
      this.visible = false
      this.oldRemarks = []
      this.$emit('concelForm')
      this.$emit('toRefresh')
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.remarks {

	.remarks-item {
		position: relative;
		display: flex;
		align-items: center;

		.remarks-del {
			width: 70px;
			poasition: relative;
			left: 0;
			top: 0;
			font-size: 22px;
			color: #ff0000;
			text-align: right;
		}

		.remarks-detail {
			width: calc(100% - 88px);
			//white-space: pre-wrap;
			white-space: pre-line;
			background-color: #e8f4ff;
			margin-left: 10px;
			border-radius: 5px;
			padding: 0 10px 20px;
		}

		.reply-button {
			margin-left: 10px;
			height: 30px;

		}
	}

	.submit-button {
		min-width: 80px;
		float: right;
		text-align: center;
		margin-left: 10px;
	}
}
</style>
