// 项目列表所有权限
export const permission = {
  add: ['admin', 'AmCategory:add'],
  edit: ['admin', 'AmCategory:edit'],
  del: ['admin', 'AmCategory:del'],
  importXlsWithRule: ['admin', 'AmCategory:importXlsWithRule'],
  updateT: ['admin', 'AmCategory:updateFormStruct'],
  updateR: ['admin', 'AmCategory:updateRelation']
}

// 项目列表表头
export const tableHeader = [
  // { label: '名称', prop: 'name', align: 'center', width: 200, treenode: true },
  { label: '类型', prop: 'category', align: 'center', width: 200 },
  { label: 'id', prop: 'id', align: 'center', width: 100 },
  { label: '排序', prop: 'sort', align: 'center', width: 100 },
  { label: '创建日期', prop: 'createTime', width: 150, align: 'center' }
]

// 定义按钮组
export const updateButtonsLists = [
  {
    id: '1',
    label: '导入',
    permission: permission.importXlsWithRule,
    fun: 'importProject',
    size: 'mini',
    className: [],
    icon: 'plus',
    type: 'primary'
  }
  // {
  //   id: '6',
  //   label: '导出',
  //   permission: permission.exportProject,
  //   fun: 'exportProject',
  //   size: 'mini',
  //   className: [],
  //   icon: 'download',
  //   type: 'primary',
  //   query: {
  //     fileType: 'xls'
  //   }
  // },
  // {
  //   id: '7',
  //   label: '预览',
  //   permission: permission.previewProject,
  //   fun: 'exportProject',
  //   size: 'mini',
  //   className: [],
  //   icon: 'view',
  //   type: 'primary',
  //   query: {
  //     fileType: 'html'
  //   }
  // }
]
