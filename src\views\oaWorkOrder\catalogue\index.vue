<template>
  <div class="app-container">
    <div class="head-container">
      <e-header :permission="permission" />
      <crudOperation :permission="permission" />
    </div>
    <!-- 表格 -->
    <div class="body-box">
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="tableData"
        style="width: 100%;"
        @select="crud.selectChange"
        @select-all="crud.selectAllChange"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column
          v-for="(item) in tableHeader"
          :key="item.prop"
          :align="item.align"
          :fixed="item.fixed || false"
          :label="item.label"
          :min-width="item.minWidth"
          :prop="item.prop"
          :width="item.width"
        >
          <template slot-scope="scope">
            <template v-if="item.prop == 'filePre'">
              <file-thumb
                :file-ext="scope.row.fileExt"
                :preview-list="[scope.row.url]"
                :url="scope.row.thUrl"
                @preView="preView"
              />
            </template>

            <template v-else-if="item.prop == 'status'">
              <el-switch
                v-model="scope.row.status"
                active-color="#409EFF"
                inactive-color="#F56C6C"
                @change="changeStatus(scope.row, scope.row.status)"
              />
            </template>
            <span v-else>{{ scope.row[item.prop] }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkPer(setOperateShow)"
          fixed="right"
          label="操作"
          width="240"
        >
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
              msg="确定删除吗,此操作不能撤销！"
            />
          </template>
        </el-table-column>

      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import eHeader from './module/header';
import CRUD, { form, presenter } from '@crud/crud';
import crudOperation from '@crud/CRUD.operation';
import pagination from '@crud/Pagination';
import oaPmCatalog from '@/api/oaWorkOrder/oaPmCatalog';
import { formatterTableHeader, permission, formatterTableData } from '@/views/oaWorkOrder/catalogue/utils/catalogue';

import udOperation from '@crud/UD.operation.vue';

const defaultForm = { id: null }
export default {
  name: 'Catalogue',
  components: { udOperation, eHeader, crudOperation, pagination },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: true,
      title: '目录模板',
      url: 'api/oaPmCatalogTemplate/small',
      sort: [],
      query: { enabled: 1, sort: 'createTime,desc' },
      crudMethod: { ...oaPmCatalog },
      optShow: {
        add: true,
        edit: false,
        del: true,
        download: false,
        reset: true,
        rightGroup: false
      },
      page: {
        // 页码
        page: 0,
        // 每页数据条数
        size: 20,
        // 总数据条数
        total: 0
      }
    })
  },

  mixins: [presenter(), form(defaultForm)],
  data() {
    return {
      permission,
      tableData: [],
      tableHeader: [],
      bindId: '34',
      currentIndex: '',
      currentColumnIndex: ''
    };
  },
  computed: {
    setOperateShow() {
      return this.$setArrPermission(['edit', 'del'], this.permission)
    }
  },
  watch: {
    'crud.data': {
      handler(val) {
        this.tableData = formatterTableData(val)
        this.tableHeader = formatterTableHeader(val)
      },
      deep: true
    }
  },
  created() {
  },
  methods: {
    [CRUD.HOOK.beforeToAdd]() {
      this.$router.push({ name: 'AddCatalogue' })
    },
    [CRUD.HOOK.beforeToEdit](crud, form) {
      this.$router.push({ name: 'EditCatalogue', query: { id: form.id }})
    },
    changeStatus(data, status) {
      this.$confirm(`此操作将${status ? '激活' : '禁用'} ${data.title}`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        oaPmCatalog.edit(data).then(res => {
          this.$message({
            message: '操作成功',
            type: 'success'
          })
        })
      }).catch(() => {
        data.status = !data.status
      })
    }
  }
};
</script>

<style lang="scss" rel="stylesheet/scss" scoped>

</style>
