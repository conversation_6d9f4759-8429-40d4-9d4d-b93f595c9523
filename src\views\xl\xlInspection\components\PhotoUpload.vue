<template>
  <el-form-item label="" :required="required" :prop="pname">
    <el-upload
      v-if="multiple || fileList.length === 0"
      ref="upload"
      list-type="picture-card"
      :action="uploadUrl"
      :file-list="fileList"
      :on-preview="handlePreview"
      :on-remove="handleRemove"
      :on-success="handleSuccess"
      :before-upload="beforeUpload"
      :limit="multiple ? null : 1"
      :headers="headers"
      :disabled="!disabled"
      :multiple="multiple"
    >
      <i v-if="disabled" class="el-icon-plus" />
    </el-upload>
    <div v-if="!multiple && fileList.length > 0" class="uploaded-image">
      <img :src="fileList[0].url || fileList[0].response.url" class="uploaded-img">
      <i v-if="disabled" class="el-icon-close" @click="handleRemove(fileList[0])" />
    </div>
    <p class="upload-title">
      <span v-if="required" class="require-star">*</span>{{ title }}
    </p>
  </el-form-item>
</template>

<script>
import { mapGetters } from 'vuex'
import { getToken } from '@/utils/auth'

export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    required: {
      type: Boolean,
      default: false
    },
    value: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    pname: {
      type: String,
      default: ''
    },
    multiple: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      headers: { 'Authorization': getToken() },
      fileList: this.value // 初始化为父组件传入的值
    }
  },
  computed: {
    uploadUrl() {
      return `${this.cloudImgUploadApi}?name=${this.title}&platform=ALIYUN-oss-1&thSize=200x200`;
    },
    ...mapGetters([
      'cloudfileUploadApi',
      'cloudImgUploadApi'
    ])
  },
  watch: {
    value(newValue) {
      this.fileList = newValue;
    }
  },
  methods: {
    handlePreview(file) {
      console.log(file);
    },
    handleRemove(file) {
      this.fileList = this.fileList.filter(item => item.uid !== file.uid);
      this.$emit('update:fileList', this.fileList);
      this.$emit('change', this.fileList); // 触发父组件的change事件以触发验证
    },
    handleSuccess(response, file) {
      const newFile = {
        name: file.name,
        url: response.url || URL.createObjectURL(file.raw),
        ...response
      };
      if (this.multiple) {
        this.fileList = [...this.fileList, newFile];
      } else {
        this.fileList = [newFile];
      }
      this.$emit('update:fileList', this.fileList);
      this.$emit('change', this.fileList); // 触发父组件的change事件以触发验证
    },
    beforeUpload(file) {
      const isJPGPNG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPGPNG) {
        this.$message.error('上传图片只能是 JPG 或 PNG 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!');
      }
      return isJPGPNG && isLt2M;
    },
    submitUpload() {
      this.$refs.upload.submit(); // 触发上传
    }
  }
};
</script>

<style scoped lang="scss" rel="stylesheet/scss">
.el-form-item__label {
  text-align: right;
}
.upload-title {
  .require-star {
    color: red;
    font-size: 18px;
    vertical-align: middle;
  }
}
.uploaded-image {
  position: relative;
  width: 148px;
  height: 148px;
  .uploaded-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .el-icon-close {
    position: absolute;
    top: 5px;
    right: 5px;
    cursor: pointer;
    color: red;
    background-color: white;
    border-radius: 50%;
  }
}
</style>
