import { isArray } from '@/utils/is'
import crudCategory from '@/api/system/category';
import { sortDynamicHeader } from '@/utils/setTableByLabel';
import assetsCrudTable from '@/api/parts/assets'
import assetAffiliatedCrudTable from '@/api/safeNav/omAssetAffiliated'
import {
  permissionAsset,
  tableHeaderCrossing,
  tableHeaderConfig
} from './field'
import { getConfig } from '@/utils/getConfigData.js'
import route from '@/router/routers'
import Vue from 'vue';

let labelConfigData = null;
let isConfigLoading = false;
let lastRouteName = null; // 新增的变量，保存上一次请求时的路由名称

export async function getConfigData() {
  const currentRouteName = route.currentRoute.name;
  console.log(currentRouteName, '<===>', 'currentRouteName')
  // 如果当前路由与上一次请求时的路由不同，重置缓存数据
  if (currentRouteName !== lastRouteName) {
    labelConfigData = null;
    lastRouteName = currentRouteName; // 更新为当前路由名称
  }

  if (labelConfigData) {
    // console.log('Returning cached config data');
    return labelConfigData;
  }

  if (isConfigLoading) {
    // 如果正在加载，等待加载完成
    return new Promise((resolve, reject) => {
      const waitForLoading = () => {
        if (!isConfigLoading) {
          resolve(labelConfigData);
        } else {
          setTimeout(waitForLoading, 100);
        }
      };
      waitForLoading();
    });
  }

  // console.log('Fetching new config data');
  isConfigLoading = true;
  try {
    const res = await getConfig({ key: 'build_progess' });
    const arr = res.extend.data.configInfo;
    const configData = arr.find(item => item.key === currentRouteName);
    const configDataNew = { ...configData, ...JSON.parse(configData.otherInfo) };
    labelConfigData = configDataNew;
    return labelConfigData;
  } catch (err) {
    console.error(err);
    throw err; // 重新抛出错误，以便在调用方进行处理
  } finally {
    isConfigLoading = false;
  }
}

export function dictConfig(name, key) {
  const tableHeader = key ? tableHeaderConfig[key].tableHeader : tableHeaderCrossing;
  const permission = key ? tableHeaderConfig[key].permission : permissionAsset;

  const dictApi = {
    assets: {
      url: 'api/omAsset/autoPilot',
      tagUrl: 'api/omAsset/findAssetWithTagAndTop',
      exportUrl: 'api/omAsset/import/xls/rule',
      crud: assetsCrudTable,
      // configKey: '',
      tableHeader,
      permission
    },
    assetsAffiliated: {
      url: 'api/omAssetAffiliated/autoPilot',
      tagUrl: 'api/omAssetAffiliated/findAssetWithTagAndTop',
      exportUrl: 'api/omAssetAffiliated/import/xls/rule',
      crud: assetAffiliatedCrudTable,
      // configKey: '',
      tableHeader,
      permission
    }
  }
  return dictApi[name] || dictApi['assets'];
}

/**
 * 格式化表格数据
 * @param {Array} val - 表格数据数组
 */
export function formatterTableData(val) {
  if (val && val.length) {
    let tableData = [];
    tableData = val.map(item => {
      const json = item.extend.data || {};
      const tagMap = item?.tagMap;
      let fv7 = ''
      try {
        // 尝试解析 item.fv7，如果解析失败会抛出异常
        fv7 = JSON.parse(item.fv7);
      } catch (error) {
        // 如果 JSON.parse 失败，意味着 fv7 可能已经是字符串了，直接赋值
        fv7 = item.fv7;
      }

      // 判断 fv7 是否为数组
      if (isArray(fv7)) {
        // 如果是数组，将其转换成逗号分隔的字符串
        fv7 = fv7.join(',');
      }

      return {
        ...json,
        ...item,
        fv7,
        ...tagMap
      };
    });
    return tableData;
  } else {
    return [];
  }
}

/**
 * 格式化表头
 * @param {Array} val - 表格数据数组
 */
export function formatterTableHeader(val, dynamicHeader, query, name, key) {
  const otherHeader = [];
  let topValuesUnique = []
  if (query.tag) {
    topValuesUnique = Object.keys(query.tag);
  }
  if (!dynamicHeader) {
    dynamicHeader = []
  }
  const dynamicHeaderArr = sortDynamicHeader(dynamicHeader, topValuesUnique, 'prop')
  const tableHeader = dictConfig(name, key).tableHeader;
  return [...tableHeader, ...dynamicHeaderArr, ...otherHeader];
}

/**
 * 点击是否显示操作列
 * @param vueInstance
 * @returns {Promise<void>}
 */
export async function toggleSideBarFun(vueInstance) {
  vueInstance.operateShow = !vueInstance.operateShow;
}

export function mergeCell({ row, column, rowIndex, columnIndex, tableData }) {
  if (columnIndex === 1 || columnIndex === 2 || columnIndex === 3) {
    const arr = getSpanArray(tableData, 'fv4');
    return getSpan(arr, rowIndex);
  } else if (columnIndex === 4 || columnIndex === 5) {
    const arr = getSpanArray(tableData, 'fv4', 'fv5');
    return getSpan(arr, rowIndex);
  }
  return { rowspan: 1, colspan: 1 };
}

function getSpanArray(list, key1, key2) {
  const spanArray = [];
  for (let i = 0; i < list.length; i++) {
    if (i === 0) {
      spanArray.push({ row: 1, col: 1 });
    } else {
      const isSame = key2
        ? list[i][key1] === list[i - 1][key1] && list[i][key2] === list[i - 1][key2]
        : list[i][key1] === list[i - 1][key1];
      if (isSame) {
        spanArray.push({ row: 0, col: 0 });
        const index = spanArray.findIndex((_, idx) => list[idx][key1] === list[i][key1] && (!key2 || list[idx][key2] === list[i][key2]));
        spanArray[index].row++;
      } else {
        spanArray.push({ row: 1, col: 1 });
      }
    }
  }
  return spanArray;
}

function getSpan(arr, rowIndex) {
  return {
    rowspan: arr[rowIndex].row,
    colspan: arr[rowIndex].col
  };
}

/**
 * 获取级联分类数据
 */
export async function getCategoryList(categoryId, bindId) {
  const fetchChildren = async(categoryId) => {
    const json = {
      page: 0,
      size: 9999999999,
      sort: 'id, desc',
      pidIsNull: true,
      enabled: 1,
      bindId: bindId,
      pid: categoryId

    }
    const res = await crudCategory.getCategory(json);
    const data = res.content;

    if (!data || data.length === 0) {
      return [];
    }

    const children = await Promise.all(data.map(async(item) => {
      item.value = item.id;
      if (item.hasChildren) {
        item.children = await fetchChildren(item.id);
      }
      return item;
    }));

    return children;
  };

  try {
    return await fetchChildren(categoryId);
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

/**
 * 检查调用哪个接口然后调用
 */
export function checkAndTagToQuery(crud, selectedCategories, fieldsToReset, configName) {
  const hasSelectedCategories = selectedCategories && selectedCategories.length > 0;
  const getTagApi = dictConfig(configName.request, configName.key).tagUrl;
  if (hasSelectedCategories) {
    crud.url = getTagApi;
    crud.requestConfig = { isCancel: true, cancelKey: 'omAssetAffiliated', method: 'post' }
    delete crud.query['type.fieldName'];
    delete crud.query['type.values'];
    resetAssetQuery(crud, fieldsToReset);
    crud.toQuery();
  } else {
    // crud.requestConfig = { isCancel: true, cancelKey: 'omAssetAffiliated', method: 'get' }
    // crud.query['type.fieldName'] = 'fv1'
    // crud.query['type.values'] = configName.topName
    clearTagAssetToQuery(crud, configName);
    crud.toQuery();
  }
}

/**
 * 清空标签的一大堆参数 使用资产接口调用
 */
export function clearTagAssetToQuery(crud, configName) {
  const keysToClear = [
    'type', 'date', 'tag',
    'dateFormat', 'indexName'
  ];

  keysToClear.forEach(key => {
    if (Array.isArray(crud.query[key])) {
      crud.query[key] = [];
    } else {
      delete crud.query[key];
    }
  });

  // 清空其它的键值对
  for (const key in crud.query) {
    if (key.startsWith('type.') || key.startsWith('date.') || key.startsWith('tag.')) {
      delete crud.query[key];
    }
  }
  crud.requestConfig = { isCancel: true, cancelKey: 'omAssetAffiliated', method: 'get' }
  crud.query['type.fieldName'] = 'fv1'
  crud.query['type.values'] = configName.topName;
  const getSmallApi = dictConfig(configName.request, configName.key).url;
  crud.url = getSmallApi;
}

/**
 * 重置搜索资产参数调用
 */
export function resetAssetQuery(crud, fieldsToReset = []) {
  const defaultQuery = { ...crud.defaultQuery };
  const query = crud.query;
  Object.keys(query).forEach(key => {
    if (fieldsToReset.includes(key)) {
      query[key] = defaultQuery[key]; // 清空匹配到的字段
    }
  });
}

/**
 * 格式化标签请求参数
 */
export function formatRequestJson(selectedCategories, selectObject) {
  const json = {
    dateFormat: 'yyyy-MM-d',
    indexName: 'fv2',
    type: { values: [], fieldName: 'fv1' },
    tag: groupByKey(selectedCategories, 'fv10', 'fv11')
  };
  json.type.values = Object.keys(selectObject)

  return json;
}

export function handleSummaries(param) {
  const { firstValue, columns, total } = param;
  const sums = []
  if (total) {
    columns.forEach((column, index) => {
      if (index === 0) {
        sums[index] = firstValue;
        return;
      }
      sums[index] = total[column.property]
    })
  }
  return sums
}

function groupByKey(arr, initKey, valKey) {
  const obj = arr.reduce((acc, item) => {
    const key = item[initKey]; // 使用fv10作为键
    if (!acc[key]) {
      acc[key] = []; // 如果这个键不存在，则初始化一个空数组
    }
    acc[key].push(item[valKey]); // 将fv11的值添加到对应的数组中
    return acc;
  }, {});
  return obj
}

// 初始化label数据
export function initLabelData(data) {
  return data.map(item => {
    return {
      ...item,
      detail: item.detail.map(detailItem => {
        Vue.set(detailItem, 'isOpen', true);
        Vue.set(detailItem, 'isCheckedAll', false);
        return {
          ...detailItem,
          currentTopName: item.fv1,
          tag: detailItem.tag.map(tagItem => {
            Vue.set(tagItem, 'typeName', item.fv1);
            Vue.set(tagItem, 'fv10', detailItem.fv10);
            Vue.set(tagItem, 'isChecked', false);
            return {
              ...tagItem
            }
          })
        };
      })
    };
  });
}
