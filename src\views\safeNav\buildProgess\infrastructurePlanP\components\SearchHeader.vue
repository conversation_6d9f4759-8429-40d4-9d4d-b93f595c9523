<template>
  <div v-if="crud.props.searchToggle" class="">
    <el-input
      v-model="crud.query.fv4OrTitle"
      class="filter-item"
      clearable
      placeholder="请输入路口名称或编号"
      size="small"
      style="width: 180px;"
      @keyup.enter.native="assetToQuery"
    />
    <!-- <self-select
      :options="dict.xyTown_type"
      :select-value.sync="crud.query.fv7"
      :tags="2"
      class="filter-item"
      clearable
      placeholder="请选择区域"
      size="small"
      style="width: 180px;"
      @selectChange="(val)=>selectChange(val, 'fv7')"
    />
    <self-select
      :options="dict.partition_type"
      :select-value.sync="crud.query.fv6"
      :tags="2"
      class="filter-item"
      clearable
      placeholder="请选择施工区域"
      size="small"
      style="width: 180px;"
      @selectChange="(val)=>selectChange(val, 'fv6')"
    /> -->
    <el-button class="filter-item" size="mini" type="success" icon="el-icon-search" @click="assetToQuery">搜索</el-button>
    <el-button class="filter-item" size="mini" type="warning" icon="el-icon-refresh-left" @click="resetToQuery">重置</el-button>
    <el-button class="filter-item" size="mini" type="primary" icon="el-icon-view" @click="openLabelDrawer">打开标签</el-button>
    <LabelDrawer ref="labelDrawer" :drawer-options="drawerOptions" :crud="crud" />
  </div>
</template>

<script>
import { header } from '@crud/crud';
import LabelDrawer from './LabelDrawer.vue';
import {
  clearTagAssetToQuery,
  resetAssetQuery
} from '../utils/commonFun';
export default {
  components: { LabelDrawer },
  mixins: [header()],
  props: {
    permission: {
      type: Object,
      required: true
    },
    dict: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      drawerOptions: {
        title: '标签选择',
        direction: 'rtl',
        size: '45%'
      }
    }
  },

  computed: {},
  watch: {},
  created() {
  },
  methods: {
    selectChange(val, type) {
      this.crud.query[type] = val;
      this.assetToQuery();
    },
    openLabelDrawer() {
      this.$refs.labelDrawer.labelVisible = true;
    },
    /** 清空标签换资产接口去请求 */
    assetToQuery() {
      clearTagAssetToQuery(this.crud);
      this.crud.toQuery();
    },
    /** 清空选项，清空标签换资产接口去请求 */
    resetToQuery() {
      resetAssetQuery(this.crud, ['fv7', 'fv6', 'fv4OrTitle']);
      clearTagAssetToQuery(this.crud);
      this.crud.toQuery();
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
