<template>
  <div class="">
    <div class="head-container">
      <e-header :announcement-type="dict.announcement_type" :permission="permission" />
      <crudOperation :permission="permission" />
    </div>
    <div class="body-box">
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="tableData"
        style="width: 100%;"
        @select="crud.selectChange"
        @select-all="crud.selectAllChange"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column :fixed="true" type="selection" width="55" />
        <el-table-column
          v-for="item in tableHeader"
          :key="item.prop"
          :align="item.align || 'center'"
          :fixed="item.fixed || false"
          :header-align="item.headerAlign || 'center'"
          :label="item.label"
          :prop="item.prop"
          :show-overflow-tooltip="true"

          :sortable="item.sortable || ''"
          :width="item.width"
        >
          <template #default="{row}">
            <template v-if="item.prop === 'fv3'">
              {{ dict.label['announcement_type'][row[item.prop]] }}
            </template>
            <span v-else>{{ row[item.prop] }}</span>
          </template>
        </el-table-column>
        <!--   编辑与删除   -->
        <el-table-column
          v-if="checkPer(['admin','spiderArticle:edit','spiderArticle:del'])"
          align="left"
          fixed="right"
          label="操作"
          width="80"
        >
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="toRelevance(scope.row)">关联</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import eHeader from '@/views/spider/spiderArticle/module/header.vue';
import CRUD, { header, presenter } from '@crud/crud';
import spiderArticleApi from '@/api/spider/spiderArticle';
import pagination from '@crud/Pagination'
import { formatterTableData, formatterRelevTableHeader } from '@/views/spider/utils/spider';
import crudOperation from '@crud/CRUD.operation.vue';

const PERMISSION = {
  add: ['admin', 'spiderArticle:add'],
  updateT: ['admin', 'spiderArticle:updateFormStruct'],
  updateR: ['admin', 'spiderArticle:updateRelation']
}
export default {
  name: '',
  components: { crudOperation, eHeader, pagination },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: true,
      title: '文章信息',
      url: 'spider/api/viewArticleBase',
      sort: ['createTime,asc'],
      query: { enabled: 1, categoryId: null },
      crudMethod: { ...spiderArticleApi },
      optShow: {
        add: false,
        edit: false,
        del: false,
        download: false,
        reset: true,
        rightGroup: false
      }
    })
  },
  mixins: [presenter(), header()],
  dicts: ['announcement_type'],
  data() {
    return {
      permission: PERMISSION,
      tableData: [],
      tableHeader: []
    }
  },

  computed: {},
  watch: {
    'crud.data': {
      handler(val, oldVal) {
        this.tableHeader = formatterRelevTableHeader(val)
        this.tableData = formatterTableData(val)
      },
      deep: true
    }
  },
  created() {
  },
  methods: {

    toRelevance(row) {
      this.$emit('toRelevance', row)
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
