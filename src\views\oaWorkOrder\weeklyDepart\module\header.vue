<template>
  <div
    v-if="crud.props.searchToggle"
  >
    <el-select
      v-model="query.pmId"
      :collapse-tags="true"
      :loading="selectLoading"
      :remote-method="remoteSelectProject"
      class="filter-item"
      clearable
      debounce="500"
      filterable
      multiple
      placeholder="请输入名称"
      remote
      reserve-keyword
      size="small"
      style="width: 180px"
    >
      <el-option
        v-for="item in projectList"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      />
    </el-select>
    <!--<el-input v-model="query.fv1" style="width: 200px;margin-right:3px;" placeholder="请输入请项目名称" clearable />-->
    <el-select
      v-model="query.fv6"
      class="filter-item"
      clearable
      placeholder="请选择周报分类"
      size="small"
      style="width: 180px"
    >
      <el-option
        v-for="item in dict.weekly_type"
        :key="item.id"
        :label="item.label"
        :value="item.label"
      />
    </el-select>
    <self-select
      :filterable="true"
      :options="userList"
      :select-value.sync="query.updateBy"
      :tags="1"
      clearable
      placeholder="请选择填报人"
      size="small"
      style="width: 200px;vertical-align: middle;margin-top:-9px;"
      custom-label="username"
      custom-value="username"
      @selectChange="(val)=>selectChange(val, 'updateBy')"
    />
    <self-select
      :filterable="true"
      :options="userList"
      :select-value.sync="query.pmLeader"
      :tags="1"
      clearable
      placeholder="请选择项目负责人"
      size="small"
      style="width: 200px;vertical-align: middle;margin-top:-9px;"
      custom-label="username"
      custom-value="username"
      @selectChange="(val)=>selectChange(val, 'pmLeader')"
    />
    <el-select
      v-model="query.fv7"
      class="filter-item"
      clearable
      placeholder="请选择内容分类"
      size="small"
      style="width: 180px"
    >
      <el-option
        v-for="item in dict.weekly_work_classify"
        :key="item.id"
        :label="item.label"
        :value="item.label"
      />
    </el-select>
    <el-select
      v-model="weekValue"
      class="filter-item"
      clearable
      placeholder="请选择周"
      size="small"
      style="width: 250px"
    >
      <el-option
        v-for="item in weekList"
        :key="item.index"
        :label="item.week"
        :value="item.text"
      />
    </el-select>
    <!-- <date-range-picker
v-model="createTime"
:picker-options="datePickerOptions"
class="date-item"
end-placeholder="结束时间"
start-placeholder="开始时间"
style="width: 300px"
value-format="yyyy-MM-dd"
/> -->
    <rrOperation />
  </div>
</template>

<script>
import { header } from '@crud/crud';
import rrOperation from '@crud/RR.operation';
// import DateRangePicker from '@/components/DateRangePicker';
import { getPmTreeSmall } from '@/api/oaWorkOrder/oaPmTree'
// import { downloadUrl } from '@/utils/index'
import { mapGetters } from 'vuex'
import {
  getWeeksOfYear
} from '../utils/commonFun'
import CRUD from '@crud/crud';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn'; // 按需引入中文语言包
import isoWeek from 'dayjs/plugin/isoWeek';
import { getUser } from '@/api/system/user'

dayjs.extend(isoWeek);
dayjs.locale('zh-cn'); // 设置为中文

export default {
  components: { rrOperation },
  dicts: ['weekly_work_classify', 'weekly_type', 'project_info_stage'],
  mixins: [header()],
  props: {
    permission: {
      type: Object,
      required: true
    }
  },
  computed: {
    ...mapGetters([
      'preViewUrl',
      'omOaAPi'
    ])
  },
  watch: {
    // 监听 createTime
    // 'createTime': function(newVal) {
    //   if (Array.isArray(newVal) && newVal.length >= 2) {
    //     this.weekValue = newVal.join('~');
    //   } else {
    //     this.weekValue = '';
    //   }
    // },
    'weekValue': function(newVal) {
      if (typeof newVal !== 'string') {
        this.resetValues();
        return;
      }

      const arr = newVal.split('~');
      if (arr.length < 2) {
        this.resetValues();
        return;
      }

      this.weekIndex = this.weekList.find(item => item.text === newVal)?.week || '';
      [this.query.fv3, this.query.fv4] = arr;
    }
  },
  data() {
    return {
      docList: [],
      projectList: [],
      selectLoading: false,
      createTime: [],
      weekList: [],
      weekValue: '',
      weekIndex: '',
      userList: [],
      datePickerOptions: {
        shortcuts: [
          {
            text: '本周',
            onClick(picker) {
              const now = dayjs();
              // 确定今天是周几，dayjs的周从0（周日）到6（周六）
              const dayOfWeek = now.day();

              let lastSaturday;
              if (dayOfWeek === 0) { // 如果今天是周日
                // 上周六就是昨天
                lastSaturday = now.subtract(1, 'day');
              } else if (dayOfWeek === 6) { // 如果今天是周六
                // 上周六就是今天
                lastSaturday = now;
              } else {
                // 否则，找到这周的周六（上周六），需要回退到上周六
                lastSaturday = now.subtract(dayOfWeek + 1, 'day');
              }

              // 获取本周的周五，从上周六开始加上6天
              const thisFriday = lastSaturday.add(6, 'day');

              // 设置picker的值
              picker.$emit('pick', [lastSaturday.toDate(), thisFriday.toDate()]);
            }
          }],
        disabledDate(time) {

        }
      }
    }
  },
  created() {
    // this.createTime = this.getRelevantWeekRange();
    this.weekList = getWeeksOfYear();
    this.weekValue = this.weekList[0]?.text;
    this.getSelectUsers();
  },
  methods: {
    resetValues() {
      this.weekIndex = '';
      this.query.fv3 = '';
      this.query.fv4 = '';
    },
    selectChange(val, type) {
      this.crud.query[type] = val;
      this.crud.toQuery();
    },
    [CRUD.HOOK.beforeResetQuery]() {
      this.createTime = [];
      this.weekValue = '';
    },
    async remoteSelectProject(query) {
      const { bindId } = this.$config.projects_keys;
      const parameters = {
        enabled: 1,
        name: query,
        size: 99,
        bindId,
        fv1: '子项目',
        categoryId: this.$config['depart_weekly_categorize']?.categoryId
      }
      const result = await this.publicRemote(query, 'projectType', parameters)
      const content = result.content || [];

      this.projectList = content || [];
    },

    async publicRemote(query, type, parameters) {
      const QueryTypes = {
        'projectType': { loadingProp: 'selectLoading', remoteMethod: getPmTreeSmall }
      }
      const typeMap = QueryTypes[type];
      if (query !== '') {
        this[typeMap.loadingProp] = true;
        try {
          const res = await typeMap.remoteMethod(parameters);
          return res || [];
        } catch (error) {
          return [];
        } finally {
          this[typeMap.loadingProp] = false;
        }
      } else {
        return [];
      }
    },
    getSelectUsers(query) {
      getUser({ page: 0, size: 999999999, sort: 'id,desc', enabled: true }).then(response => {
        const userArr = response?.content;
        if (userArr && userArr.length) {
          this.userList = userArr;
        }
      })
    }
    // getDefaultWeekRange() {
    //   const now = dayjs();
    //   // 确定今天是周几，dayjs的周从0（周日）到6（周六）
    //   const dayOfWeek = now.day();

    //   let lastSaturday;
    //   if (dayOfWeek === 0) { // 如果今天是周日
    //     // 上周六就是昨天
    //     lastSaturday = now.subtract(1, 'day');
    //   } else if (dayOfWeek === 6) { // 如果今天是周六
    //     // 上周六就是今天
    //     lastSaturday = now;
    //   } else {
    //     // 否则，找到这周的周六（上周六），需要回退到上周六
    //     lastSaturday = now.subtract(dayOfWeek + 1, 'day');
    //   }

    //   // 获取本周的周五，从上周六开始加上6天
    //   const thisFriday = lastSaturday.add(6, 'day');

    //   // 返回格式化的日期字符串
    //   return [lastSaturday.format('YYYY-MM-DD'), thisFriday.format('YYYY-MM-DD')];
    // },
    // getPreWeekDate() {
    //   // 获取当前日期
    //   const now = dayjs();

    //   // 计算当前日期是周几，dayjs的周从0（周日）到6（周六）
    //   const dayOfWeek = now.day();

    //   let daysToLastSaturday;
    //   if (dayOfWeek === 6) { // 如果今天是周六
    //     daysToLastSaturday = 7; // 直接回溯7天到上周的周六
    //   } else {
    //     // 对于周日到周五，需要找到这周的周六，然后再回溯7天到上周的周六
    //     daysToLastSaturday = dayOfWeek + 1 + 7;
    //   }

    //   // 获取上周的周六
    //   const lastSaturday = now.subtract(daysToLastSaturday, 'day');

    //   // 获取上周的周五，从上周的周六开始加上6天
    //   const lastFriday = lastSaturday.add(6, 'day');

    //   return [lastSaturday.format('YYYY-MM-DD'), lastFriday.format('YYYY-MM-DD')];
    // },
    // getRelevantWeekRange() {
    //   const now = dayjs();
    //   const dayOfWeek = now.day();
    //   const isAfterMidnight = now.isAfter(now.startOf('day')); // 检查是否超过了00:00:00

    //   // 判断当前时间是否是周二零点之前（包括周一、周日和周六）
    //   // dayjs的周从0（周日）到6（周六）
    //   // 修改逻辑以确保周六也被视为周二之前的时间
    //   if (dayOfWeek === 6 || dayOfWeek < 2 || (dayOfWeek === 2 && !isAfterMidnight)) {
    //     // 如果是周六或者是周二零点以前，获取上周的时间范围
    //     return this.getPreWeekDate();
    //   } else {
    //     // 如果是周二零点之后，获取本周的时间范围
    //     return this.getDefaultWeekRange();
    //   }
    // }
  }
}
</script>
