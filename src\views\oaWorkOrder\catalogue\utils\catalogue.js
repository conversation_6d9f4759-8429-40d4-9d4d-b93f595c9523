import oaPmCatalog from '@/api/oaWorkOrder/oaPmCatalog';

export const permission = {
  del: ['admin', 'oaPmCatalogTemplate:del'],
  add: ['admin', 'oaPmCatalogTemplate:add'],
  edit: ['admin', 'oaPmCatalogTemplate:edit'],
  updateT: ['admin', 'oaPmCatalogTemplate:updateFormStruct'],
  updateR: ['admin', 'oaPmCatalogTemplate:updateRelation']
}

export const statusList = [
  { id: 1, label: '启用', value: true },
  { id: 0, label: '禁用', value: false }
]

export function formatterTableData(val) {
  let tableData = [];
  if (!val && !val.length) {
    return tableData
  }
  const mapItem = (item) => {
    return {
      ...item,
      status: item.status == 'true'
    };
  };
  tableData = val.map(mapItem);
  return tableData
}

export function formatterTableHeader(val) {
  const tableHeader = [
    { prop: 'title', label: '模板名称', align: 'center' },
    { prop: 'status', label: '状态', align: 'center' },
    { prop: 'description', label: '项目描述', align: 'center' },
    { prop: 'createBy', label: '创建人', align: 'center' },
    { prop: 'createTime', label: '创建时间', align: 'center' }
  ]
  const otherHeader = [];
  return [...tableHeader, ...otherHeader];
}

export async function getOaPmCatalogData(query) {
  const queryResult = await oaPmCatalog.getSmall(query)
  console.log(queryResult, '<===>', 'queryResult')
  return formatterTableData(queryResult.content)
}
