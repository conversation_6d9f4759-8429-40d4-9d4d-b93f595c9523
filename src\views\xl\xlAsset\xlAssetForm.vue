<template>
  <div class="app-container">
    <el-card class="box-card" style="margin-top: 10px">
      <div slot="header" class="clearfix">
        <span>雪亮资产信息</span>
      </div>
      <el-form ref="elFormRef" :model="elForm" :rules="elRules" label-width="130px" size="small">
        <!--额外的表单-->
      </el-form>
      <div v-if="showFormData" class="text item">
        <fm-generate-form
          :ref="'generateForm'"
          :data="formStruct"
          :preview="viewOrEdit"
          :remote="remoteFunc"
          :value="formData"
        >
          <template v-slot:1="{ model,allInfo }">
            <el-select
              v-model="formData[1]"
              :remote-method="(query) => publicRemoteMethod(query,'1')"
              filterable
              placeholder="请输入融合后点位"
              remote
              style="width: 50%;"
              @change="publicChange('1',)"
            >
              <el-option
                v-for="item in pointList"
                :key="item.fv4"
                :label="item.fv4"
                :value="item.fv4"
              />
            </el-select>
          </template>
          <template v-slot:2="{ model }">
            <el-select
              v-model="formData[2]"
              :remote-method="(query) => publicRemoteMethod(query,2)"
              filterable
              placeholder="请输入立杆位置"
              remote
              style="width: 50%;"
              @change="publicChange('2')"
            >
              <el-option
                v-for="item in positionList"
                :key="item.title"
                :label="item.title"
                :value="item.title"
              />
            </el-select>
          </template>

        </fm-generate-form>
      </div>
      <hr style="background-color: #d9d9d9; border:0; height:1px;">
      <div class="text item" style="text-align: center;margin-top:18px">
        <el-button v-if="!viewOrEdit" :disabled="submitDisabled" type="primary" @click="submitAction">提交</el-button>
        <el-button @click="concelForm">{{ viewOrEdit == false ? '取 消' : '返回' }}</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import Vue from 'vue'
import { GenerateForm } from '@/components/VueFormMaking'
import 'form-making/dist/FormMaking.css'

Vue.component(GenerateForm.name, GenerateForm)
import extendBindTpl from '@/api/system/extendBindTpl';
import { getToken } from '@/utils/auth';
import crudDictDetail from '@/api/system/dictDetail';
import crudTable from '@/api/parts/assets'
import {
  allKey
} from '@/views/xl/xlAsset/utils/field'

export default {
  name: 'XlAssetForm',
  components: {},

  props: {},
  data() {
    const that = this
    return {
      allKey,
      elForm: {
        enabled: 1
      },
      elRules: {},
      formStruct: {},
      formData: {
        '1': '',
        '2': ''
      },
      remoteFunc: {
        funcGetToken(resolve) {
          resolve(getToken())
        },
        getDictDetail(resolve, dictName) {
          crudDictDetail.get(dictName).then(res => {
            const options = res.content
            resolve(options)
          })
        },

        // 获取点位列表
        getAssetList(resolve, option, type) {
          const { bindId, categoryId } = that.$config.xl_asset_key
          const data = {
            page: 0,
            size: 30,
            enabled: 1,
            [type]: option?.query,
            bindId,
            categoryId
          }
          crudTable.getDetailWithX(data).then(res => {
            const data = res.content
            if (data.length > 0) {
              resolve(data)
            } else {
              resolve([{ [type]: option?.query, [type]: option?.query, self: true }])
            }
          })
        },

        getCameraType(resolve) {
          // 摄像机类型
          this.getDictDetail(resolve, 'xl_camera_type');
        },
        getEquipmentVendors(resolve) {
          // 设备厂商
          this.getDictDetail(resolve, 'xl_equipment_vendors');
        },
        getPoliceStation(resolve) {
          // 派出所
          this.getDictDetail(resolve, 'xl_police_station');
        },
        getPropertyUnit(resolve) {
          // 产权单位
          this.getDictDetail(resolve, 'xl_property_unit');
        },
        getLink(resolve) {
          // 融合后链路
          this.getDictDetail(resolve, 'xl_link');
        },
        getInventorySituation(resolve) {
          // 盘点情况
          this.getDictDetail(resolve, 'xl_inventory_situation');
        },
        // 通过点位获取列表
        getAssetByPoint(resolve, option) {
          console.log(option, '<===>', 'option')
          if (!option.query) {
            resolve([])
          } else {
            this.getAssetList(resolve, option, 'fv4')
          }
        },
        // 通过位置获取列表
        getAssetByPosition(resolve, option) {
          if (!option.query) {
            resolve([])
          } else {
            this.getAssetList(resolve, option, 'title')
          }
        }

      },
      submitDisabled: false,
      viewOrEdit: false,
      showFormData: false,
      pointList: [], // 点位列表
      positionList: [] // 位置列表
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      const { id, type } = this.$route.query
      this.viewOrEdit = type
      if (id) {
        // 编辑或者查看
        this.getContent(id)
      } else {
        // 添加
        this.getProcessNodeList()
      }
    },
    getContent(id) {
      crudTable.get({ id, enabled: 1 }).then(res => {
        if (res && res.content && res.content.length) {
          const jsonData = res.content[0];
          this.processStructureValue = jsonData;
          this.formStruct = JSON.parse(jsonData.formStruct);
          this.formData = JSON.parse(jsonData.formData);
          this.jsonData = jsonData;
          this.showFormData = true;
          this.elForm = jsonData
        }
      })
    },
    getProcessNodeList() {
      const data = { id: this.$config.xl_asset_key.bindId, enabled: 1 }
      extendBindTpl.get(data).then(res => {
        if (res && res.content && res.content.length) {
          this.processStructureValue = res.content[0];
          const FormStruct = JSON.parse(this.processStructureValue.extendTpl.formStruct);
          this.formStruct = FormStruct;
        }
        this.showFormData = true;
      });
    },
    submitAction() {
      this.$refs['elFormRef'].validate((valid) => {
        if (!valid) {
          return false;
        } else {
          this.checkModule().then(res => {
            if (!res.flag) {
              return false;
            } else {
              const subData = { ...this.elForm, ...res.subData };
              let request = crudTable.add;
              let title = '添加'
              if (subData.id) {
                request = crudTable.edit;
                title = '编辑'
              }
              console.log(subData, '<===>', 'subData')
              request(subData).then(response => {
                this.$notify({
                  title: `${title}成功`,
                  type: 'success',
                  duration: 2500
                })
                this.concelForm();
              }).catch((e) => {
                console.log(e);
              })
            }
          })
        }
      })
    },
    async checkModule() {
      const subData = {
        bindId: this.$config.xl_asset_key.bindId,
        categoryId: this.$config.xl_asset_key.categoryId,
        formData: null,
        formStruct: null,
        formBindToVar: true,
        relation: this.processStructureValue.relation
      };
      if (this.showFormData) {
        const p = await this.$refs['generateForm'].getData().then(values => {
          subData.formData = JSON.stringify(values);
          subData.formStruct = JSON.stringify(this.formStruct);
          return {
            flag: true,
            subData
          };
        }).catch(() => {
          return {
            flag: false
          };
        })
        return p;
      } else {
        return Promise.resolve({
          subData,
          flag: true
        });
      }
    },
    concelForm() {
      const query = {
        bindId: this.$config.xl_asset_key.bindId,
        categoryId: this.$config.xl_asset_key.categoryId
      }
      this.$router.push({ name: 'XlAsset', query });
    },
    publicRemoteMethod(query, key,) {
      const { bindId, categoryId } = this.$config.xl_asset_key
      const currentKey = this.allKey[key]
      const data = {
        page: 0,
        size: 30,
        enabled: 1,
        [currentKey.comType]: query,
        bindId,
        categoryId
      }
      crudTable.getDetailWithX(data).then(res => {
        const data = res.content
        if (data.length > 0) {
          this[currentKey.list] = data;
        } else {
          this[currentKey.list] = [{ [currentKey.comType]: query, self: true }]
        }
      })
    },

    publicChange(key) {
      const currentKey = this.allKey[key]
      const flag = this[currentKey.list].some(item => item.self);
      let obj = {}
      if (flag) {
        // 有一个符合条件 则是true
        return
      } else {
        obj = this[currentKey.list].find(item => item[currentKey.comType] === this.formData[key])
        this.formData[currentKey.target] = obj[currentKey.targetType]
      }
    }

  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
