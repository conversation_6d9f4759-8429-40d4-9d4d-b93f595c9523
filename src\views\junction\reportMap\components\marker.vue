<template>
  <div class="search-box">
    <el-input v-model="query.title" clearable size="small" placeholder="输入路口名称" style="width: 200px;" class="filter-item" />
    <el-select
      v-model="query.fv1"
      size="small"
      placeholder="请选择阶段"
      class="filter-item"
      style="width: 200px"
    >
      <el-option
        v-for="item in dict.report_stage"
        :key="item.id"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <el-select
      v-model="query.fv2"
      size="small"
      placeholder="请选择进度"
      class="filter-item"
      style="width: 200px"
    >
      <el-option
        v-for="item in dict.report_process"
        :key="item.id"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <date-range-picker v-model="query.createTime" class="date-item" size="small" style="width: 250px;" />
    <span style="padding-left:10px;">
      <el-button class="filter-item" size="small" type="success" icon="el-icon-search" @click="searchMap">搜索</el-button>
      <el-button class="filter-item" size="small" type="warning" icon="el-icon-refresh-left" @click="resetMap">重置</el-button>
    </span>
    <!-- <div style="margin-top: 10px;">
      <el-checkbox-group v-model="query.fv2" :max="1" size="small" @change="handleCheckedChange">
        <el-checkbox-button v-for="item in dict.report_process" :key="item.value" :label="item.value">{{ item.value }}</el-checkbox-button>
      </el-checkbox-group>
    </div> -->
  </div>
</template>
<script>
/* eslint-disable */
// import { getSmall } from '@/api/parts/fault'
import { getSmall }  from '@/api/parts/fault'
import DateRangePicker from '@/components/DateRangePicker'
import MultiMarker from '../SupportingFile/MultiMarker';
import { getConfig } from '@/utils/getConfigData.js'
export default {
  name: 'MarkerList',
  components: { DateRangePicker },
  dicts: ['report_process', 'report_stage'],
  props: {
    map: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      markerLayer: null,
      infoWindow: null,
      query: {
        title: '',
        createTime: [],
        fv2: '未完成',
        fv1:'fv11'
      },
      markList: [],
      bindId:"",
      isDisabled:true
    };
  },
  watch: {
    map(val, old) {
      // 初始化marker
      this.setMaker();
      //信息弹窗
      this.infoWin();
    },
  },
  created() {
    // this.getConfigData()
    this.bindId = this.$route.query.id;
  },
  beforeDestroy() {
    this.markList = [];
    this.markerLayer = null;
    this.infoWindow = null;
  },
  methods: {
    getConfigData() {
      getConfig({
        key: 'report_map'
      }).then(res => {
        this.bindId = res.extend.data.bindId;
      });
    },
    async setMaker() {
      await this.getSmallMap();
      // 点标记 单个
      // this.markerLayer = new TMap.MultiMarker({
      //   id: 'marker-layer', // 图层id
      //   map: this.map,
      //   styles: { // 点标注的相关样式
      //     'marker': new TMap.MarkerStyle({
      //       'width': 5,
      //       'height': 5,
      //       'anchor': { x: 16, y: 32 },
      //       'src': location.origin + '/mapIcon/red1.png'
      //     })
      //   },
      //   geometries: []
      // });
      // this.markerLayer.setGeometries(this.markList);
      if (this.markerLayer) {
        this.markerLayer.setGeometries([]);
        this.markerLayer.setGeometries(this.markList);
      } else {
        this.markerLayer = new TMap.MultiMarker(MultiMarker(this, this.markList));
      }
      // 监听marker点击事件
      this.markerLayer.on('click', this.clickHandler);
    },
    /** 点击点位打开信息窗 */
    clickHandler(evt) {
      var content = `<div style="padding:3px;">${evt.geometry.properties.title}</div>`;
      this.infoWindow.open(); // 打开信息窗
      this.infoWindow.setPosition(evt.geometry.position); // 设置信息窗位置
      this.infoWindow.setContent(content); // 设置信息窗内容
    },
    /** 创建信息窗口 */
    infoWin() {
      this.infoWindow = new TMap.InfoWindow({
        map: this.map,
        position: new TMap.LatLng(0, 0),
        offset: { x: -12, y: -32 } // 设置信息窗相对position偏移像素，为了使其显示在Marker的上方
      });
      this.infoWindow.close(); // 初始关闭信息窗关闭
    },
    async getSmallMap(resolve) {
      this.markList = [];
      let params = {
        status: '已匹配',
        enabled: 1,
        title: this.query.title,
        createTime: this.query.createTime,
        bindId:this.bindId,
        page:0,
        size:999999
      }
      if(this.query.fv1 && this.query.fv1 !== '') {
        params[this.query.fv1] = this.query.fv2
      }
      await getSmall(params).then(res => {
        const arr = res.content;
        arr && (arr.map(item => {
          let fv2 = '';
          let fv3 = '';
          let intersection = item.asset
          if (intersection.fv2 && intersection.fv2 !== 'null' && intersection.fv2 !== 'undefined') {
            fv2 = intersection.fv2;
          }
          if (intersection.fv3 && intersection.fv3 !== 'null' && intersection.fv3 !== 'undefined') {
            fv3 = intersection.fv3;
          }
          if(item[this.query.fv1] !== '/') {
            let obj = {
              'id': item.id,
              // 'styleId': 'marker',
              'styleId': this.styleIcon(item[this.query.fv1],item.fv9),
              'position': new TMap.LatLng(fv3, fv2),
              'properties': {
                'title': item.title
              }
            }
            this.markList.push(obj)
          }
        }));
      }).catch(e => { console.log(e) })
    },
    async searchMap() {
      await this.setMaker();
    },
    resetMap() {
      this.query = {
        title: '',
        createTime: [],
        fv2: '未完成',
        fv1:'fv11'
      };
      this.searchMap();
    },
    // handleCheckedChange() {
    //   this.searchMap();
    // },
    styleIcon(process,direction) {
      const iconMap = {
        "未完成": {
          "东": "noR",
          "南": "noB",
          "西": "noL",
          "北": "noT",
        },
        "施工中": {
          "东": "doR",
          "南": "doB",
          "西": "doL",
          "北": "doT",
        },
        "完成": {
          "东": "okR",
          "南": "okB",
          "西": "okL",
          "北": "okT",
        },
      };
      let str = '';
      str = iconMap[process][direction]
      // console.log('str==>',str);
      return str
    }
  }
};
</script>
<style lang="scss" scoped>
.search-box{
  position: absolute;
  width:100%;
  left:20px;
  top:20px;
  z-index: 2000;
  padding: 10px 0 0 10px;
}

</style>
