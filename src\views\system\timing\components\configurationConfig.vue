<template>
  <div class="app-container">
    <el-form ref="form" :model="formData" label-width="250px">
      <add-input :arr-value="formData.resolvers" label-info="自定义解析器列表" tip-info="自定义解析器列表（文件或逗号分隔）" />
      <el-form-item label="最大爬取深度">
        <el-input-number v-model="formData.max_depth" style="width: 200px" :min="0" label="最大爬取深度" />
      </el-form-item>
      <el-form-item label="启用 JavaScript 文件中的端点解析/爬取">
        <el-radio-group v-model="formData.scrape_js_responses">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="启用 JavaScript 文件中的 jsluice 解析（内存密集型）">
        <el-radio-group v-model="formData.scrape_js_luice_responses">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="爬取目标的最大持续时间(s)">
        <el-input-number v-model="formData.crawl_duration" style="width: 200px" :min="0" />
      </el-form-item>
      <el-form-item label="启用已知文件的爬取（all、robotstxt、sitemapxml）">
        <el-input v-model="formData.known_files" placeholder="启用已知文件的爬取（all、robotstxt、sitemapxml）" />
      </el-form-item>
      <el-form-item label="读取的最大响应大小">
        <el-input-number v-model="formData.body_read_size" style="width: 200px" :min="0" />
      </el-form-item>
      <el-form-item label="请求等待时间（s）">
        <el-input-number v-model="formData.timeout" style="width: 200px" :min="0" />
      </el-form-item>
      <el-form-item label="启用自动填充表单（实验性功能）">
        <el-radio-group v-model="formData.automatic_form_fill">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="在jsonl输出中提取表单、输入、文本区域和选择元素">
        <el-radio-group v-model="formData.form_extraction">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="请求重试次数">
        <el-input-number v-model="formData.retries" style="width: 200px" :min="0" />
      </el-form-item>
      <el-form-item label="要使用的HTTP/Socks5代理">
        <el-input v-model="formData.proxy" placeholder="要使用的HTTP/Socks5代理" />
      </el-form-item>
      <add-input :arr-value="formData.custom_headers" label-info="在所有HTTP请求中包含的自定义头部/cookie（文件）" tip-info="在所有HTTP请求中包含的自定义头部/cookie（文件）" />

      <el-form-item label="自定义表单配置文件的路径">
        <el-input v-model="formData.form_config" placeholder="自定义表单配置文件的路径" />
      </el-form-item>
      <el-form-item label="自定义字段配置文件的路径">
        <el-input v-model="formData.field_config" placeholder="自定义字段配置文件的路径" />
      </el-form-item>
      <el-form-item label="访问策略（depth-first、breadth-first）">
        <el-input v-model="formData.strategy" placeholder="访问策略（depth-first、breadth-first）" />
      </el-form-item>
      <!-- <el-form-item label="自定义字段配置文件的范围">
        <el-input v-model="formData.field_scope" />  有疑问
      </el-form-item> -->
      <el-form-item label="忽略相同路径但不同查询参数值的爬取">
        <el-radio-group v-model="formData.ignore_query_params">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="启用实验性的Client Hello（ja3）TLS随机化">
        <el-radio-group v-model="formData.tls_impersonate">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>

    </el-form>
  </div>
</template>

<script>
import addInput from '@/views/system/timing/common/addInput'
import { delArrInvalid } from '@/views/system/timing/utils/formate'
export default {
  name: 'ConfigurationConfig',
  components: { addInput },
  data() {
    return {
      formData: {
        resolvers: [], // []string
        max_depth: null, // int
        scrape_js_responses: null, // bool
        scrape_js_luice_responses: null, // bool
        crawl_duration: null, // time.Duration 默认是s秒
        known_files: null, // string
        body_read_size: null, // int
        timeout: null, // int
        automatic_form_fill: null, // bool
        form_extraction: null, // bool
        retries: null, // int 3
        proxy: null, // string
        custom_headers: [], // []string
        form_config: null, // string
        field_config: null, // string
        strategy: null, // string
        // field_scope: null, // string
        ignore_query_params: null, // bool
        tls_impersonate: null // bool
      }
    };
  },

  mounted() {

  },

  methods: {
    getFormData() {
      return {
        configuration: delArrInvalid(this.formData)
      };
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
