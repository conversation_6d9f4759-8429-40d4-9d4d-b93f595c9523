<template>
  <div id="drawer-container" ref="drawerContainer">
    <!-- <div v-if="!labelVisible" class="icon-open" @click.prevent="openDrawer">
<i class="el-icon-s-unfold" />
</div> -->
    <el-drawer
      ref="drawer"
      :append-to-body="false"
      :modal="false"
      :visible.sync="labelVisible"
      :wrapper-closable="true"
      direction="ltr"
      size="40%"
      title="自动驾驶标签选择"
      @close="enableBodyScroll"
      @open="disableBodyScroll"
    >
      <info-tag-select
        ref="infoTagSelect"
        :categories="categoryData"
        @update:selectedCategories="updateSelectedCategories(...arguments)"
      />
    </el-drawer>
  </div>
</template>

<script>
import {
  formatRequestJson,
  initLabelData
} from '../SupportingFile/commonFun';
import InfoTagSelect from '@/views/components/InfoTagSelect/InfoTagSelect.vue';
import { findFilterItem } from '@/api/safeNav/omAssetTag';
import { getBulidenConfig } from '@/utils/getConfigData'

export default {
  name: 'LabelClassify',
  components: { InfoTagSelect },
  props: {
    // categories: {
    //   type: Array,
    //   required: true
    // }
  },
  data() {
    return {
      // 自动驾驶3.0标签
      selectedCategories: [],
      categoryData: [],
      labelVisible: false,
      labelConfig: {}
    };
  },
  created() {
    // this.getAllTags();
  },
  methods: {
    openDrawer() {
      this.labelVisible = true;
    },
    async getAllTags(name) {
      this.labelConfig = await getBulidenConfig(name)
      const query = {
        'type.values': this.labelConfig.topName,
        'top.fieldName': 'fv10',
        'type.fieldName': 'fv1'
      }
      findFilterItem(query).then(res => {
        const data = res || []
        this.categoryData = initLabelData(data)
        this.$emit('update:showTgaDisabled')
      })
    },
    updateSelectedCategories(selectArr, selectObject, isFromClear) {
      if (isFromClear) return;// 如果是主动关闭抽屉，不去重新请求
      this.selectedCategories = selectArr;
      const requestJson = formatRequestJson(selectArr, selectObject);
      this.$emit('update:selectedTag', this.selectedCategories, requestJson);
    },
    disableBodyScroll() {
      document.body.style.overflow = 'hidden';
    },
    enableBodyScroll() {
      document.body.style.overflow = '';
    },
    closeDrawer() {
      // this.query.top = [];
      // this.query.tag = [];
      // this.$refs.multiCategorySelector.resetAllCategories();
    }
  }
};
</script>

<style scoped>
#drawer-container {
	position: absolute;
	left: 0;
	top: 0;
}

/* ::v-deep .el-drawer__wrapper{
  position: absolute !important;;
  left:20px;
  top:20px;
} */
::v-deep .el-drawer__header {
	margin-bottom: 10px !important;
}

::v-deep .el-drawer__body {
	padding: 0 20px !important;
}

::v-deep .el-drawer__header {
	font-size: 17px !important;
	font-weight: bold !important;
	color: #000 !important;
}
</style>
