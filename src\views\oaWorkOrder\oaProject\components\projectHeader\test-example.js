// 测试示例：模拟 getCatalogTree API 返回的数据结构
const mockCatalogTreeResponse = [
  {
    id: 1,
    name: '项目根目录',
    type: 'catalog',
    children: [
      {
        id: 2,
        name: '施工区域A',
        type: 'catalog',
        children: [
          {
            id: 3,
            name: '监测点A1',
            type: 'point',
            longitude: '116.816054',
            latitude: '40.154257',
            description: '施工区域A的第一个监测点'
          },
          {
            id: 4,
            name: '监测点A2',
            type: 'point',
            longitude: '116.825623',
            latitude: '40.110006',
            description: '施工区域A的第二个监测点'
          }
        ]
      },
      {
        id: 5,
        name: '施工区域B',
        type: 'catalog',
        children: [
          {
            id: 6,
            name: '监测点B1',
            type: 'point',
            longitude: '116.830000',
            latitude: '40.120000',
            description: '施工区域B的监测点'
          },
          {
            id: 7,
            name: '里程碑1',
            type: 'milestone',
            description: '这是一个里程碑，不是点位'
          }
        ]
      }
    ]
  }
]

// 测试 extractPointNodes 函数的逻辑
function extractPointNodes(nodes) {
  const pointNodes = []

  const traverse = (nodeList) => {
    if (!Array.isArray(nodeList)) return

    nodeList.forEach(node => {
      // 如果节点类型是'point'，添加到结果数组
      if (node.type === 'point') {
        pointNodes.push(node)
      }

      // 如果有子节点，递归遍历
      if (node.children && Array.isArray(node.children)) {
        traverse(node.children)
      }
    })
  }

  traverse(nodes)
  return pointNodes
}

// 测试函数
console.log('测试数据:', mockCatalogTreeResponse)
const pointNodes = extractPointNodes(mockCatalogTreeResponse)
console.log('提取的点位节点:', pointNodes)

// 转换为地图组件需要的格式
const locationPoints = pointNodes.map(point => ({
  name: point.name || point.title || '未命名点位',
  longitude: point.longitude || point.lng || point.lon,
  latitude: point.latitude || point.lat,
  id: point.id,
  ...point // 保留原始数据
})).filter(point => point.longitude && point.latitude)

console.log('转换后的地图点位数据:', locationPoints)

export { mockCatalogTreeResponse, extractPointNodes }
