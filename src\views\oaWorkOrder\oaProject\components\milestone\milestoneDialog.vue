<template>
  <el-dialog
    :before-close="concelForm"
    :close-on-click-modal="false"
    :title="title"
    :visible.sync="visible"
    append-to-body
    width="900px"
  >
    <div v-if="showFormData" v-loading="loading" class="text item">
      <fm-generate-form
        :ref="'generateForm'"
        :data="formStruct"
        :preview="viewOrEdit"
        :remote="remoteFunc"
        :value="formData"
      />
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="130px">
      <!-- <el-form-item label="通讯录:" prop="selecContact">
        </el-form-item> -->
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="text" @click="concelForm">取消</el-button>
      <el-button :disabled="submitDisabled" type="primary" @click="submitAction">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getToken } from '@/utils/auth';
import Vue from 'vue'
import { GenerateForm } from '@/components/VueFormMaking'
import 'form-making/dist/FormMaking.css'

Vue.component(GenerateForm.name, GenerateForm)
import extendBindTpl from '@/api/system/extendBindTpl';
import { get, add, edit } from '@/api/oaWorkOrder/oaPmMilestone'

export default {
  components: {},
  data() {
    return {
      visible: false,
      ruleForm: {
        enable: 1
      },
      rules: {
        // fv7: [
        //   { required: true, message: '请选择里程碑', trigger: 'change' }
        // ]
      },
      title: '添加里程碑',
      formStruct: {},
      formData: {},
      jsonData: {},
      submitDisabled: false,
      viewOrEdit: false,
      showFormData: false,
      milestoneConfig: {},
      remoteFunc: {
        funcGetToken(resolve) {
          resolve(getToken())
        }
      },
      loading: false
    }
  },
  created() {
  },
  methods: {
    async init(info) {
      this.bindId = info.bindId;
      this.visible = true;

      if (info && info.rowId) {
        this.getContent(info);
      } else {
        this.getProcessNodeList(this.bindId);
      }
      // this.viewOrEdit = info.status == 'see';
    },
    getProcessNodeList(id) {
      this.loading = true;
      const data = { id, enabled: 1 }
      extendBindTpl.get(data).then(res => {
        if (res && res.content && res.content.length) {
          this.processStructureValue = res.content[0];
          const FormStruct = JSON.parse(this.processStructureValue.extendTpl.formStruct);
          this.formStruct = FormStruct;
        }
        this.showFormData = true;
      }).finally(() => {
        this.loading = false;
      });
    },
    getContent(info) {
      this.loading = true;
      get({ id: info.rowId }).then(res => {
        if (res && res.content && res.content.length) {
          const jsonData = res.content[0];
          this.jsonData = jsonData;
          this.formStruct = JSON.parse(jsonData.formStruct);
          this.formData = JSON.parse(jsonData.formData);
          this.showFormData = true;
        }
      }).finally(() => {
        this.loading = false;
      });
    },
    submitAction() {
      let isSubmit = false;
      this.$refs['ruleForm'].validate((valid) => {
        if (!valid) {
          isSubmit = true;
          return false;
        }
      })
      this.submitDisabled = true;
      const subData = {
        pmId: this.$route.query.projectId,
        bindId: this.bindId,
        enabled: 1,
        formData: '',
        formStruct: JSON.stringify(this.formStruct),
        formBindToVar: true,
        relation: this.processStructureValue.relation
      };
      this.$refs['generateForm'].getData().then(values => {
        subData.formData = JSON.stringify(values);
      }).catch(() => {
        isSubmit = true;
      })
      setTimeout(() => {
        if (isSubmit) {
          this.submitDisabled = false
          this.$notify({
            title: '请根据提示填写表单信息',
            type: 'info',
            duration: 2500
          });
        } else {
          let request = add;
          if (this.jsonData && this.jsonData.id) {
            subData.id = this.jsonData.id;
            request = edit;
          }
          request(subData).then(response => {
            this.$message({
              title: '操作成功',
              type: 'success',
              duration: 2500
            });
            this.submitDisabled = false
            this.concelForm();
          }).catch(() => {
            this.submitDisabled = false
          })
        }
      })
    },
    concelForm() {
      this.visible = false;
      this.processStructureValue = {};
      this.formStruct = {};
      this.formData = {};
      this.showFormData = false;
      this.$emit('succeSubmit');
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>

</style>
