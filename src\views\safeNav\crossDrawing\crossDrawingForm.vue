<template>
  <div class="app-container">
    <el-card class="box-card" style="margin-top: 10px">
      <div slot="header" class="clearfix">
        <span>路口图纸</span>
      </div>
      <el-form ref="elFormRef" :model="elForm" :rules="elRules" label-width="130px" size="small">
        <!--额外的表单-->
      </el-form>
      <div v-if="showFormData" class="text item">
        <fm-generate-form
          :ref="'generateForm'"
          :data="formStruct"
          :preview="viewOrEdit"
          :remote="remoteFunc"
          :value="formData"
        >
          <template v-slot:1="{ model }">
            <el-select
              v-model="formData[1]"
              :remote-method="(query) => publicRemoteMethod(query,'1')"
              filterable
              placeholder="请输入路口编号"
              remote
              style="width: 50%;"
              @change="publicChange('1',)"
            >
              <el-option
                v-for="item in autoPilotList"
                :key="item.id"
                :label="item.fv4"
                :value="item.fv4"
              />
            </el-select>
          </template>
          <template v-slot:2="{ model }">
            <el-select
              v-model="formData[2]"
              :remote-method="(query) => publicRemoteMethod(query,2)"
              filterable
              placeholder="请输入路口名称"
              remote
              style="width: 50%;"
              @change="publicChange('2')"
            >
              <el-option
                v-for="item in autoPilotList"
                :key="item.id"
                :label="item.title"
                :value="item.title"
              />
            </el-select>
          </template>
          <template v-slot:6="{ model }">
            <el-input
              v-model="formData[6]"
              disabled
              placeholder="路口分区"
              style="width: 100%;"
            />
          </template>
        </fm-generate-form>
      </div>
      <hr style="background-color: #d9d9d9; border:0; height:1px;">
      <div class="text item" style="text-align: center;margin-top:18px">
        <el-button v-if="!viewOrEdit" :disabled="submitDisabled" type="primary" @click="submitAction">提交</el-button>
        <el-button @click="concelForm">{{ viewOrEdit == false ? '取 消' : '返回' }}</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import Vue from 'vue'
import { GenerateForm } from '@/components/VueFormMaking'
import 'form-making/dist/FormMaking.css'
import { getOmAssetAutoPilot } from '@/api/parts/assets'

Vue.component(GenerateForm.name, GenerateForm)
import extendBindTpl from '@/api/system/extendBindTpl';
import { getToken } from '@/utils/auth';
import crudDictDetail from '@/api/system/dictDetail';
import crudTable from '@/api/safeNav/omAssetAffiliated'
import { allKey } from './utils/field'

export default {
  name: 'RectifyLedgerForm',
  components: {},
  data() {
    return {
      allKey,
      elForm: {
        enabled: 1
      },
      elRules: {},
      formStruct: {},
      formData: {
        4: ''
      },
      remoteFunc: {
        funcGetToken(resolve) {
          resolve(getToken())
        },
        getDictDetail(resolve, dictName) {
          crudDictDetail.get(dictName).then(res => {
            const options = res.content
            resolve(options)
          })
        },
        getPartition(resolve) {
          // 施工分区
          this.getDictDetail(resolve, 'partition_type');
        },
        getPartitionPerson(resolve) {
          // 施工区域负责人
          this.getDictDetail(resolve, 'partition_person_type');
        },
        getRectificateStatus(resolve) {
          // 整改状态
          this.getDictDetail(resolve, 'rectificate_status');
        },
        getInspectionUnitList(resolve) {
          // 检查单位
          this.getDictDetail(resolve, 'inspection_unit');
        }
      },
      submitDisabled: false,
      viewOrEdit: false,
      showFormData: false,
      autoPilotList: []
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      const { id, type } = this.$route.query
      this.viewOrEdit = type
      if (id) {
        // 编辑或者查看
        this.getContent(id)
      } else {
        // 添加
        this.getProcessNodeList()
        this.formData['4'] = `${this.$dayJS().format('YYYY/MM/DD')}`
      }
    },
    getContent(id) {
      crudTable.get({ id, enabled: 1 }).then(res => {
        if (res && res.content && res.content.length) {
          const jsonData = res.content[0];
          this.processStructureValue = jsonData;
          this.formStruct = JSON.parse(jsonData.formStruct);
          this.formData = JSON.parse(jsonData.formData);
          this.jsonData = jsonData;
          this.showFormData = true;
          this.elForm = jsonData
        }
      })
    },
    getProcessNodeList() {
      const data = { id: this.$config.corss_draw_key.bindId, enabled: 1 }
      extendBindTpl.get(data).then(res => {
        if (res && res.content && res.content.length) {
          this.processStructureValue = res.content[0];
          const FormStruct = JSON.parse(this.processStructureValue.extendTpl.formStruct);
          this.formStruct = FormStruct;
        }
        this.showFormData = true;
      });
    },
    submitAction() {
      this.$refs['elFormRef'].validate((valid) => {
        if (!valid) {
          return false;
        } else {
          this.checkModule().then(res => {
            if (!res.flag) {
              return false;
            } else {
              const subData = { ...this.elForm, ...res.subData };
              let request = crudTable.add;
              let title = '添加'
              if (subData.id) {
                request = crudTable.edit;
                title = '编辑'
              }
              console.log(subData, '<===>', 'subData')
              request(subData).then(response => {
                this.$notify({
                  title: `${title}成功`,
                  type: 'success',
                  duration: 2500
                })
                this.concelForm();
              }).catch((e) => {
                console.log(e);
              })
            }
          })
        }
      })
    },
    async checkModule() {
      const subData = {
        bindId: this.$config.corss_draw_key.bindId,
        categoryId: this.$config.corss_draw_key.categoryId,
        formData: null,
        formStruct: null,
        formBindToVar: true,
        relation: this.processStructureValue.relation
      };
      if (this.showFormData) {
        const p = await this.$refs['generateForm'].getData().then(values => {
          subData.formData = JSON.stringify(values);
          subData.formStruct = JSON.stringify(this.formStruct);
          return {
            flag: true,
            subData
          };
        }).catch(() => {
          return {
            flag: false
          };
        })
        return p;
      } else {
        return Promise.resolve({
          subData,
          flag: true
        });
      }
    },
    concelForm() {
      const query = {
        bindId: this.$config.corss_draw_key.bindId,
        categoryId: this.$config.corss_draw_key.categoryId
      }
      this.$router.push({ name: 'CrossDrawing', query });
    },
    publicRemoteMethod(query, key,) {
      const { bindId, categoryId } = this.$config.corss_draw_key
      const data = {
        page: 0,
        size: 30,
        enabled: 1,
        fv4OrTitle: query,
        bindId,
        categoryId
      }
      getOmAssetAutoPilot(data).then(res => {
        const data = res.content
        if (data.length > 0) {
          this.autoPilotList = data;
        } else {
          this.autoPilotList = []
        }
      })
    },

    publicChange(key) {
      const currentKey = this.allKey[key]
      const obj = this.autoPilotList.find(item => item[currentKey.comType] === this.formData[key])
      this.formData[currentKey.target] = obj[currentKey.targetType]
      this.formData['6'] = obj['fv6']
    }

  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
