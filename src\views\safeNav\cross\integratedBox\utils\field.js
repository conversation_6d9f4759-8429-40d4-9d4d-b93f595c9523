// 项目列表所有权限
export const permission = {
  add: ['admin', 'omAssetAffiliated:add'],
  edit: ['admin', 'omAssetAffiliated:edit'],
  del: ['admin', 'omAssetAffiliated:del'],
  upload: ['admin', 'omAssetAffiliated:importXlsWithRule'],
  updateT: ['admin', 'omAssetAffiliated:updateFormStruct'],
  updateR: ['admin', 'omAssetAffiliated:updateRelation']
  // updateG: ['admin', 'omAssetAffiliated:toRedisGeoIndex']
}

// 项目列表表头
export const tableHeader = [
  // { label: '序号', prop: '0', fixed: 'left', align: 'left', width: 80 },
  { label: '路口编号', prop: 'fv4', align: 'left', width: 100 },
  { label: '路口名称', prop: 'title', width: 180, align: 'left' },
  { label: '施工分区', prop: 'fv6', align: 'left', width: 80 },
  { label: '区域', prop: 'fv7', width: 120, align: 'left' },
  { label: '经度', prop: 'fv2', width: 120, align: 'left' },
  { label: '维度', prop: 'fv3', width: 120, align: 'left' },
  { label: '状态', prop: 'status', width: 120, align: 'left' },
  { label: '创建日期', prop: 'createTime', width: 150 }
]

// 定义按钮组
export const updateButtonsLists = [
  {
    id: '1',
    label: '导入',
    permission: permission.upload,
    fun: 'importProject',
    size: 'mini',
    className: [],
    icon: 'plus',
    type: 'primary'
  },
  {
    id: '2',
    label: '预览',
    permission: permission.upload,
    fun: 'exportProject',
    size: 'mini',
    className: [],
    icon: 'view',
    type: 'success',
    query: {
      fileType: 'html'
    }
  },
  {
    id: '3',
    label: '导出',
    permission: permission.upload,
    fun: 'exportProject',
    size: 'mini',
    className: [],
    icon: 'download',
    type: 'primary',
    query: {
      fileType: 'xls'
    }
  }
]

// 联合key
export const allKey = {
  '1': {
    target: '2',
    comType: 'fv4',
    targetType: 'title',
    list: 'pointList'
  },
  '2': {
    target: '1',
    comType: 'title',
    targetType: 'fv4',
    list: 'positionList'
  }
}
