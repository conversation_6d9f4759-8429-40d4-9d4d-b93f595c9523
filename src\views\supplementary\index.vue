<template>
  <div class="app-container" style="overflow-x: auto;">
    <legend-com />
    <select-machine v-if="currentSelects" :current-selects="currentSelects" :machine-list="machineList" @selectMachine="selectMachine" />
    <div class="topology-con" style="min-height: calc(100vh - 84px);">
      <machine-picture v-if="currentSelects" ref="machinePicture" :current-selects="currentSelects" />
    </div>
  </div>
</template>

<script>
import {
  legend,
  allMachine
} from './utils/fileds'
import machinePicture from './components/machinePicture.vue'
import LegendCom from './components/legendCom.vue'
import SelectMachine from './components/selectMachine.vue';
import { getOmAssetAutoPilot } from '@/api/parts/assets';

export default {
  name: 'Supplementary',
  components: { SelectMachine, machinePicture, LegendCom },

  props: {},
  data() {
    return {
      legend,
      currentSelects: {},
      allMachine,
      showPicture: false,
      machineList: []
    }
  },

  computed: {
  },
  watch: {
  },
  async created() {
    this.currentSelects = allMachine[0];
    await this.$nextTick()
    await this.getAutoPilotList();
    this.currentSelects = this.machineList[0];
    this.$refs.machinePicture.initData(this.currentSelects)
  },
  methods: {
    selectMachine(item) {
      this.currentSelects = item
      this.$refs.machinePicture.initData(this.currentSelects)
    },
    async getAutoPilotList(query) {
      const { bindId } = this.$config.auto_pilot3_key;
      const json = {
        page: 0,
        size: 99999,
        bindId,
        enabled: [1],
        'type.fieldName': 'fv1',
        'type.values': '路口施工进度',
        'tagName': ['京智网通网', '车网通网']
      };

      try {
        const res = await getOmAssetAutoPilot(json);
        const contList = res.content;
        this.machineList = allMachine.map(machineItem =>
          this.processMachineItem(machineItem, contList)
        );
      } catch (error) {
        console.error('Failed to fetch data:', error);
      }
    },
    processMachineItem(machineItem, contList) {
      const machRes = machineItem.dataList.reduce((count, item1) => {
        const match = contList.find(item2 => item2.fv4 === item1.fv4);
        if (match) {
          const key = Object.keys(match.tagMap).find(k => k.includes(machineItem.tagName));
          if (key && match.tagMap[key] === '在线') {
            return count + 1;
          }
        }
        return count;
      }, 0);

      return {
        ...machineItem,
        count: machRes,
        selectText: `${machineItem.dataList.length}/${machRes}`
      };
    }

  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>

</style>
