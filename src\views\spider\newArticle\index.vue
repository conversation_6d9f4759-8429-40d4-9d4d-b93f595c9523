<template>
  <div class="app-container">
    <!-- 表格 -->
    <div class="body-box">
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="tableData"
        :header-cell-class-name="handleHeadAddClass"
        style="width: 100%;"
        @select="crud.selectChange"
        @select-all="crud.selectAllChange"
        @sort-change="sortChangeHandler"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column :fixed="true" type="selection" width="55" />
        <el-table-column
          v-for="item in tableHeader"
          :key="item.prop"
          :align="item.align || 'center'"
          :fixed="item.fixed || false"
          :header-align="item.headerAlign || 'center'"
          :label="item.label"
          :prop="item.prop"
          :show-overflow-tooltip="true"

          :sortable="item.sortable || ''"
          :width="item.width"
        >
          <template #default="{row}">
            <template v-if="item.prop === 'fv3'">
              {{ dict.label['announcement_type'][row[item.prop]] }}
            </template>
            <span v-else>{{ row[item.prop] }}</span>
          </template>
        </el-table-column>
        <!--   编辑与删除   -->
        <el-table-column
          align="center"
          fixed="right"
          label="操作"
          width="240"
        >
          <template slot-scope="scope">
            <el-button v-permission="permission.affirm" size="mini" type="success" @click="sureInfo(scope.row)">信息确认
            </el-button>
            <el-button v-permission="permission.labelEdit" size="mini" type="primary" @click="toDetail(scope.row,2)">
              标签
            </el-button>
            <el-button v-permission="permission.attention" size="mini" type="warning" @click="addTtention(scope.row)">
              {{ attention[scope.row.status] }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import spiderArticleApi from '@/api/spider/spiderArticle'
import {
  formatterTableData,
  formatterTableHeader,
  follow,
  toDetail,
  sureInfo,
  publicChangeHandler
} from '@/views/spider/utils/spider';
import { permission } from '@/views/spider/utils/field';
import CRUD, { presenter, form } from '@crud/crud'
import pagination from '@crud/Pagination'
import { header } from '@crud/crud'
import { mapGetters } from 'vuex';

const defaultForm = { id: null }

const attention = {
  unAttention: '关注',
  attention: '取关'
}
export default {
  name: 'NewArticle',
  components: { pagination },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: true,
      title: '文章信息',
      url: 'spider/api/viewArticleBase',
      sort: [],
      query: { enabled: 1, categoryId: null, fv15: 0 },
      crudMethod: { ...spiderArticleApi },
      optShow: {
        add: false,
        edit: false,
        del: false,
        download: true,
        reset: true,
        rightGroup: false
      }
    })
  },
  mixins: [presenter(), form(defaultForm), header()],
  dicts: ['announcement_type'],
  data() {
    return {
      permission,
      attention,
      tableData: [],
      tableHeader: [],
      bindId: '',
      isFirstEnter: true,
      sortField: {}
    };
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  watch: {
    'crud.data': {
      handler(val, oldVal) {
        this.tableHeader = formatterTableHeader(val)
        this.tableData = formatterTableData(val)
      },
      deep: true
    }
  },
  created() {
    this.isFirstEnter = false
  },
  activated() {
    if (this.isFirstEnter) {
      return
    }
    this.crud.refresh();
  },
  methods: {
    [CRUD.HOOK.beforeResetQuery]() {
      this.crud.sort = []
      this.$refs.table.$el.querySelectorAll('.is-sortable').forEach((item) => {
        // 移除table表头中的排序样式descending和ascending
        item.classList.remove('descending');
        item.classList.remove('ascending');
      });
      Object.getOwnPropertyNames(this.sortField).forEach(key => {
        delete this.sortField[key];
      });
    },
    sortChangeHandler(data) {
      this.crud.sort = publicChangeHandler(data, this.sortField)
      this.crud.refresh();
    },
    handleHeadAddClass({ column }) {
      if (this.sortField[column.property]) {
        column.order = this.sortField[column.property];
      }
    },
    addTtention(row) {
      try {
        follow(row, {}, this.fllowCallback)
      } catch (error) {
        console.error('Error toggling attention:', error);
      }
    },
    fllowCallback(message) {
      this.$notify({
        title: message,
        type: 'success',
        duration: 2500
      });
      this.crud.refresh();
    },
    toDetail,
    sureInfo
  }
};
</script>

<style lang="scss" scoped>

</style>
