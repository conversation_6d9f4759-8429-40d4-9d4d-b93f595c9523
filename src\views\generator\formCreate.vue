<template>
  <div>
    <el-row style="padding: 20px 20px;text-align: right;">
      <el-button type="success" @click="getJson">生成json</el-button>
      <el-button type="primary" @click="getOption">生成option</el-button>
    </el-row>
    <div class="designer">
      <fc-designer ref="designer" height="800px" />
    </div>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="30%"
    >
      <div ref="editor">{{ content }}</div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'FormCreate',
  data() {
    return {
      dialogVisible: false,
      content: ''
    }
  },
  methods: {
    getJson() {
      const json = this.$refs.designer.getJson();
      this.content = json;
      this.dialogVisible = true;
    },
    getOption() {
      const json = this.$refs.designer.getOption();
      this.content = json;
      this.dialogVisible = true;
    }
  }
};
</script>

<style scoped>

</style>
