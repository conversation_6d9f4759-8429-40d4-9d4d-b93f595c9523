<template>
  <div class="task-dialog">
    <el-dialog
      :before-close="cancelForm"
      :close-on-click-modal="false"
      title="出库信息"
      :visible.sync="visible"
      append-to-body
      width="800px"
    >

      <div v-if="showFormData" class="text item task-content">
        <el-form ref="ruleForm" :model="ruleForm" :rules="viewOrEdit ? {} : rules" label-width="130px">
          <el-form-item label="库房名称">
            <span>{{ displayBasicData && displayBasicData.depot && displayBasicData.depot.title }}</span>
          </el-form-item>
          <el-form-item label="项目名称">
            <span>{{ displayBasicData && displayBasicData.pm && displayBasicData.pm.name }}</span>
          </el-form-item>
          <el-form-item label="设备">
            <span>{{ displayBasicData && displayBasicData.device && displayBasicData.device.name }}</span>
          </el-form-item>
          <el-form-item label="品牌">
            <span>{{ displayBasicData && displayBasicData.brand && displayBasicData.brand.name }}</span>
          </el-form-item>
          <el-form-item label="型号">
            <span>{{ displayBasicData && displayBasicData.model && displayBasicData.model.name }}</span>
          </el-form-item>
          <el-form-item label="出库数量" prop="stockOutAmount">
            <el-input-number
              v-if="!viewOrEdit"
              v-model="ruleForm.stockOutAmount"
              :min="0"
              :precision="0"
              :step="1"
              controls-position="right"
              placeholder="请输入出库数量"
              style="width: 220px"
            />
            <span v-else>{{ ruleForm.stockOutAmount || "-" }}</span>
          </el-form-item>
        </el-form>
        <fm-generate-form
          :ref="'generateForm'"
          :data="formStruct"
          :preview="viewOrEdit"
          :remote="remoteFunc"
          :value="formData"
        />

        <!-- 新增的出库信息表单字段 -->
        <el-form ref="outBoundForm" :model="outBoundForm" label-width="130px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备SN码" prop="snNo">
                <el-input
                  v-if="!viewOrEdit"
                  v-model="outBoundForm.snNo"
                  placeholder="请输入设备SN码"
                  @change="handleSnNoChange"
                />
                <span v-else>{{ outBoundForm.snNo || '-' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="MAC地址" prop="mac">
                <el-input v-if="!viewOrEdit" v-model="outBoundForm.mac" placeholder="请输入MAC地址" />
                <span v-else>{{ outBoundForm.mac || '-' }}</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="IP地址" prop="ip">
                <el-input v-if="!viewOrEdit" v-model="outBoundForm.ip" placeholder="请输入IP地址" />
                <span v-else>{{ outBoundForm.ip || '-' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="领取人" prop="receiver">
                <el-input v-if="!viewOrEdit" v-model="outBoundForm.receiver" placeholder="请输入领取人" />
                <span v-else>{{ outBoundForm.receiver || '-' }}</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="领取原因" prop="receiveReason">
            <el-input
              v-if="!viewOrEdit"
              v-model="outBoundForm.receiveReason"
              type="textarea"
              :rows="3"
              placeholder="请输入领取原因"
            />
            <span v-else>{{ outBoundForm.receiveReason || '-' }}</span>
          </el-form-item>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="箱体号">
                <el-input v-if="!viewOrEdit" v-model="outBoundForm.boxNo" placeholder="请输入箱体号" />
                <span v-else>{{ outBoundForm.boxNo || '-' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="更换点位">
                <el-input v-if="!viewOrEdit" v-model="outBoundForm.changeLocation" placeholder="请输入更换点位" />
                <span v-else>{{ outBoundForm.changeLocation || '-' }}</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="原设备品牌">
                <el-input v-if="!viewOrEdit" v-model="outBoundForm.originalBrand" placeholder="请输入原设备品牌" />
                <span v-else>{{ outBoundForm.originalBrand || '-' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="原设备型号">
                <el-input v-if="!viewOrEdit" v-model="outBoundForm.originalModel" placeholder="请输入原设备型号" />
                <span v-else>{{ outBoundForm.originalModel || '-' }}</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="原建设单位">
                <el-input v-if="!viewOrEdit" v-model="outBoundForm.constructionUnit" placeholder="请输入原建设单位" />
                <span v-else>{{ outBoundForm.constructionUnit || '-' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="更换区域">
                <el-input v-if="!viewOrEdit" v-model="outBoundForm.changeArea" placeholder="请输入更换区域" />
                <span v-else>{{ outBoundForm.changeArea || '-' }}</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="退回日期">
                <el-date-picker
                  v-if="!viewOrEdit"
                  v-model="outBoundForm.returnDate"
                  type="date"
                  placeholder="请选择退回日期"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  style="width: 100%"
                />
                <span v-else>{{ outBoundForm.returnDate || '-' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="归还人">
                <el-input v-if="!viewOrEdit" v-model="outBoundForm.returnMan" placeholder="请输入归还人" />
                <span v-else>{{ outBoundForm.returnMan || '-' }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="cancelForm">取消</el-button>
        <el-button v-if="!viewOrEdit" :disabled="submitDisabled" type="primary" @click="submitAction">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth';
import { mapGetters } from 'vuex';
import amStockOut from '@/api/property/amStockOut'
export default {
  components: {},
  data() {
    return {
      loading: false,
      visible: false,
      ruleForm: {
        stockOutAmount: 0
      },
      displayBasicData: {},
      rules: {
        stockOutAmount: [
          { required: true, message: '请输入出库数量', trigger: 'blur' }
        ]
      },
      // 新增的出库信息表单数据
      outBoundForm: {
        // 必填项
        snNo: '',
        mac: '',
        ip: '',
        receiver: '',
        receiveReason: '',
        // 非必填项
        boxNo: '',
        changeLocation: '',
        originalBrand: '',
        originalModel: '',
        constructionUnit: '',
        changeArea: '',
        returnDate: '',
        returnMan: ''
      },
      // 新增表单的验证规则
      outBoundRules: {
        mac: [
          { required: true, message: '请输入MAC地址', trigger: 'blur' }
        ],
        ip: [
          { required: true, message: '请输入IP地址', trigger: 'blur' }
        ],
        receiver: [
          { required: true, message: '请输入领取人', trigger: 'blur' }
        ],
        receiveReason: [
          { required: true, message: '请输入领取原因', trigger: 'blur' }
        ]
      },
      submitDisabled: false,
      viewOrEdit: false,
      showFormData: false,
      processStructureValue: {},
      formStruct: {},
      formData: {},
      remoteFunc: {
        funcGetToken(resolve) {
          resolve(getToken())
        }
      }
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  methods: {
    init(info) {
      this.visible = true;
      const { id, type } = info;
      if (type == '2') {
        this.viewOrEdit = true;
      } else {
        this.viewOrEdit = false;
      }
      this.getBasicData(id)
    },
    async getBasicData(id) {
      // 重置表单数据状态
      this.showFormData = false;
      this.formData = {};
      this.formStruct = {};

      await amStockOut.get({ id, enabled: 1 }).then(res => {
        if (res && res.content) {
          const jsonData = res.content[0];
          this.processStructureValue = jsonData;
          this.formStruct = JSON.parse(jsonData.formStruct);
          this.formData = JSON.parse(jsonData.formData);

          this.ruleForm = jsonData;
          this.displayBasicData = jsonData.basicData;

          this.outBoundForm = {
            ...this.outBoundForm,
            snNo: jsonData.snNo,
            mac: jsonData.mac,
            ip: jsonData.ip,
            receiver: jsonData.receiver,
            receiveReason: jsonData.receiveReason,
            boxNo: jsonData.boxNo,
            changeLocation: jsonData.changeLocation,
            originalBrand: jsonData.originalBrand,
            originalModel: jsonData.originalModel,
            constructionUnit: jsonData.constructionUnit,
            changeArea: jsonData.changeArea,
            returnDate: jsonData.returnDate,
            returnMan: jsonData.returnMan
          };

          // 使用 nextTick 确保 DOM 更新后再显示表单
          this.$nextTick(() => {
            this.showFormData = true;
          });
        }
      })
    },
    submitAction() {
      // 提交前验证SN码和出库数量的关系
      if (this.outBoundForm.snNo && this.ruleForm.stockOutAmount > 1) {
        this.$message.error('填写SN码时，出库数量必须为1');
        return;
      }

      // 验证原有表单
      this.$refs['ruleForm'].validate((valid) => {
        if (!valid) {
          return false;
        } else {
          // 验证新增的出库信息表单
          this.$refs['outBoundForm'].validate((outBoundValid) => {
            if (!outBoundValid) {
              return false;
            } else {
              this.checkModule().then(res => {
                if (!res.flag) {
                  return false;
                } else {
                  // 合并所有表单数据
                  const subData = {
                    ...this.ruleForm,
                    ...this.outBoundForm,
                    ...res.subData
                  };
                  const request = amStockOut.edit;
                  this.submitDisabled = true
                  request(subData).then(() => {
                    this.$notify({
                      title: `修改出库成功`,
                      type: 'success',
                      duration: 2500
                    })
                    this.cancelForm();
                    this.$emit('successAction')
                  }).catch((e) => {
                    console.log(e);
                  }).finally(() => {
                    this.submitDisabled = false
                  })
                }
              })
            }
          })
        }
      })
    },
    async checkModule() {
      const subData = {
        bindId: this.$config.outBound_key.bindId,
        formData: null,
        formStruct: null,
        formBindToVar: true,
        relation: this.processStructureValue.relation
      };
      if (this.showFormData) {
        const p = await this.$refs['generateForm'].getData().then(values => {
          subData.formData = JSON.stringify(values);
          subData.formStruct = JSON.stringify(this.formStruct);
          return {
            flag: true,
            subData
          };
        }).catch(() => {
          return {
            flag: false
          };
        })
        return p;
      } else {
        return Promise.resolve({
          subData,
          flag: true
        });
      }
    },
    // 处理SN码变化事件
    handleSnNoChange(value) {
      if (value && value.trim()) {
        // 如果填写了SN码，出库数量必须为1
        if (this.ruleForm.stockOutAmount > 1) {
          this.ruleForm.stockOutAmount = 1;
          this.$message.warning('填写SN码后，出库数量已自动调整为1');
        }
      }
    },
    cancelForm() {
      this.visible = false;

      // 重置所有表单数据
      this.showFormData = false;
      this.formData = {};
      this.formStruct = {};
      this.displayBasicData = {};
      this.processStructureValue = {};
      this.ruleForm = {
        stockOutAmount: 0
      };

      // 重置新增表单数据
      this.outBoundForm = {
        snNo: '',
        mac: '',
        ip: '',
        receiver: '',
        receiveReason: '',
        boxNo: '',
        changeLocation: '',
        originalBrand: '',
        originalModel: '',
        constructionUnit: '',
        changeArea: '',
        returnDate: '',
        returnMan: ''
      };

      // 清除表单验证状态
      if (this.$refs['outBoundForm']) {
        this.$refs['outBoundForm'].clearValidate();
      }
      if (this.$refs['ruleForm']) {
        this.$refs['ruleForm'].clearValidate();
      }
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.el-picker-panel__footer .el-button--text.el-picker-panel__link-btn {
  display: none;
}
</style>

<style lang="scss" rel="stylesheet/scss">
.no-atTheMoment {
  .el-picker-panel__footer .el-button--text.el-picker-panel__link-btn {
    display: none;
  }
}
</style>
