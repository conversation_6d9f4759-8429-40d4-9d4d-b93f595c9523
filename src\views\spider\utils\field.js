export const field = {
  '0': {
    type: '采购意向',
    fields: [
      { label: '采购项目名称', value: 'fv1' },
      { label: '预算单位名称', value: 'fv5' },
      { label: '预算金额 (万元)', value: 'fv6' },
      { label: '预计采购时间', value: 'fv7' }
    ]
  },
  '1': {
    type: '招标公告',
    fields: [
      { label: '项目名称', value: 'fv1' },
      { label: '项目编号', value: 'fv2' },
      { label: '日期', value: 'fv11' },
      { label: '预算金额', value: 'fv8' },
      { label: '最高限价', value: 'fv9' }
    ]
  },
  '2': {
    type: '更正公告',
    fields: [
      { label: '项目名称', value: 'fv1' },
      { label: '项目编号', value: 'fv2' },
      { label: '日期', value: 'fv11' }
    ]
  },
  '3': {
    type: '二次招标',
    fields: [
      { label: '项目名称', value: 'fv1' },
      { label: '项目编号', value: 'fv2' },
      { label: '预算金额', value: 'fv8' },
      { label: '最高限价', value: 'fv9' },
      { label: '日期', value: 'fv11' }
    ]
  },
  '4': {
    type: '中标公告',
    fields: [
      { label: '项目名称', value: 'fv1' },
      { label: '项目编号', value: 'fv2' },
      { label: '中标单位名称', value: 'fv12' },
      { label: '中标单位金额', value: 'fv14' },
      { label: '日期', value: 'fv11' }
    ]
  },
  '5': {
    type: '废标公告',
    fields: [
      { label: '项目名称', value: 'fv1' },
      { label: '项目编号', value: 'fv2' },
      { label: '日期', value: 'fv11' }
    ]
  },
  '6': {
    type: '合同公告',
    fields: [
      { label: '项目名称', value: 'fv1' },
      { label: '项目编号', value: 'fv2' },
      { label: '日期', value: 'fv11' },
      { label: '合同编号', value: 'fv10' },
      { label: '合同名称', value: 'fv13' }
    ]
  }
}
export const projectFiled = {
  fields: [
    { label: '项目名称', value: 'projectName' },
    { label: '项目编号', value: 'projectNo' },
    { label: '采购单位', value: 'purchasingUnit' },
    { label: '采购进度', value: 'type' }
  ]
}

export const permission = {
  add: ['admin', 'spiderArticle:add'],
  affirm: ['admin', 'spiderReadAttention:affirm'],
  labelEdit: ['admin', 'spiderLabel:edit'],
  attention: ['admin', 'spiderReadAttention:cancel'],
  relevance: ['admin', 'spiderArticle:relevance'],
  editBaseInfo: ['admin', 'spiderArticleBase:edit'],
  updateT: ['admin', 'spiderArticle:updateFormStruct'],
  updateR: ['admin', 'spiderArticle:updateRelation'],
  count: ['admin', 'spiderArticle:count']
}

export const baseProjectFiled = {
  fields: [
    { label: '项目名称', value: 'fv1' },
    { label: '采购单位', value: 'fv3' },
    { label: '项目编号', value: 'fv2' },
    { label: '采购进度', value: 'fv4' }
  ]
}
