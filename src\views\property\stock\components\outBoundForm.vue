<template>
  <div class="task-dialog">
    <el-dialog
      :before-close="cancelForm"
      :close-on-click-modal="false"
      title="出库"
      :visible.sync="visible"
      append-to-body
      width="800px"
    >

      <div v-if="showFormData" class="text item task-content">
        <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="130px">
          <el-form-item label="库房名称">
            <span>{{ displayBasicData && displayBasicData.depot && displayBasicData.depot.title }}</span>
          </el-form-item>
          <el-form-item label="项目名称">
            <span>{{ displayBasicData && displayBasicData.pm && displayBasicData.pm.name }}</span>
          </el-form-item>
          <el-form-item label="设备">
            <span>{{ displayBasicData && displayBasicData.device && displayBasicData.device.name }}</span>
          </el-form-item>
          <el-form-item label="品牌">
            <span>{{ displayBasicData && displayBasicData.brand && displayBasicData.brand.name }}</span>
          </el-form-item>
          <el-form-item label="型号">
            <span>{{ displayBasicData && displayBasicData.model && displayBasicData.model.name }}</span>
          </el-form-item>
          <!--表单项-->
          <el-form-item label="出库数量" prop="stockOutAmount">
            <el-input-number
              v-model="ruleForm.stockOutAmount"
              :min="0"
              :precision="0"
              :step="1"
              controls-position="right"
              placeholder="请输入出库数量"
              style="width: 220px"
            />
          </el-form-item>
        </el-form>
        <fm-generate-form
          :ref="'generateForm'"
          :data="formStruct"
          :preview="viewOrEdit"
          :remote="remoteFunc"
          :value="formData"
        />

        <!-- 新增的出库信息表单 -->
        <el-form ref="outBoundForm" :model="outBoundForm" label-width="130px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备SN码" prop="snNo">
                <el-input v-model="outBoundForm.snNo" placeholder="请输入设备SN码" @change="handleSnNoChange" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="MAC地址" prop="mac">
                <el-input v-model="outBoundForm.mac" placeholder="请输入MAC地址" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="IP地址" prop="ip">
                <el-input v-model="outBoundForm.ip" placeholder="请输入IP地址" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="领取人" prop="receiver">
                <el-input v-model="outBoundForm.receiver" placeholder="请输入领取人" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="领取原因" prop="receiveReason">
            <el-input v-model="outBoundForm.receiveReason" type="textarea" :rows="3" placeholder="请输入领取原因" />
          </el-form-item>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="箱体号">
                <el-input v-model="outBoundForm.boxNo" placeholder="请输入箱体号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="更换点位">
                <el-input v-model="outBoundForm.changeLocation" placeholder="请输入更换点位" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="原设备品牌">
                <el-input v-model="outBoundForm.originalBrand" placeholder="请输入原设备品牌" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="原设备型号">
                <el-input v-model="outBoundForm.originalModel" placeholder="请输入原设备型号" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="原建设单位">
                <el-input v-model="outBoundForm.constructionUnit" placeholder="请输入原建设单位" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="更换区域">
                <el-input v-model="outBoundForm.changeArea" placeholder="请输入更换区域" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="退回日期">
                <el-date-picker
                  v-model="outBoundForm.returnDate"
                  type="date"
                  placeholder="请选择退回日期"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="归还人">
                <el-input v-model="outBoundForm.returnMan" placeholder="请输入归还人" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="cancelForm">取消</el-button>
        <el-button :disabled="submitDisabled" type="primary" @click="submitAction">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth';
import extendBindTpl from '@/api/system/extendBindTpl';
import { mapGetters } from 'vuex';
import amStock from '@/api/property/amStock'
export default {
  components: {},
  data() {
    // 自定义校验函数
    const validateStockOutAmount = (rule, value, callback) => {
      if (!value && value !== 0) {
        callback(new Error('请输入出库数量'))
        return
      }
      const stockOutAmount = Number(value)
      if (!Number.isInteger(stockOutAmount) || stockOutAmount < 0) {
        callback(new Error('出库数量必须为正整数'))
        return
      }
      const validStockAmount = Number.isInteger(this.stockAmount) && this.stockAmount >= 0
      if (!validStockAmount) {
        callback(new Error('库存数量无效，请确认库存后再操作'))
        return
      }
      if (stockOutAmount > this.stockAmount) {
        callback(new Error(`出库数量不能大于库存数量${this.stockAmount}`))
        return
      }
      callback()
    }
    return {
      visible: false,
      ruleForm: {
        basicData: {},
        enabled: 1,
        stockOutAmount: 0,
        id: null
      },
      displayBasicData: {},
      rules: {
        stockOutAmount: [
          { required: true, message: '请输入出库数量', trigger: 'blur' },
          { validator: validateStockOutAmount, trigger: ['blur', 'change'] }
        ]
      },
      // 新增的出库信息表单数据
      outBoundForm: {
        // 必填项
        snNo: '',
        mac: '',
        ip: '',
        receiver: '',
        receiveReason: '',
        // 非必填项
        boxNo: '',
        changeLocation: '',
        originalBrand: '',
        originalModel: '',
        constructionUnit: '',
        changeArea: '',
        returnDate: '',
        returnMan: ''
      },
      // 出库信息表单验证规则
      outBoundRules: {
        mac: [
          { required: true, message: '请输入MAC地址', trigger: 'blur' }
        ],
        ip: [
          { required: true, message: '请输入IP地址', trigger: 'blur' }
        ],
        receiver: [
          { required: true, message: '请输入领取人', trigger: 'blur' }
        ],
        receiveReason: [
          { required: true, message: '请输入领取原因', trigger: 'blur' }
        ]
      },
      formStruct: {},
      formData: {},
      jsonData: {},
      submitDisabled: false,
      viewOrEdit: false,
      showFormData: false,
      remoteFunc: {
        funcGetToken(resolve) {
          resolve(getToken())
        }
      },
      stockAmount: null
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  methods: {
    init(info) {
      this.visible = true;
      const { basicNo, stockAmount, id } = info;
      this.stockAmount = stockAmount || 0;
      this.bindId = this.$config.outBound_key.bindId
      this.ruleForm.basicData = { basicNo: basicNo }
      this.ruleForm.id = id
      this.getBasicData(id)
      this.getProcessNodeList(this.bindId)
    },
    async getBasicData(id) {
      await amStock.get({ id, enabled: 1 }).then(res => {
        if (res && res.content) {
          const jsonData = res.content[0];
          this.displayBasicData = jsonData.basicData
        }
      })
    },
    getProcessNodeList(id) {
      this.loading = true;
      const data = { id, enabled: 1 }
      extendBindTpl.get(data).then(res => {
        if (res && res.content && res.content.length) {
          this.processStructureValue = res.content[0];
          this.formStruct = JSON.parse(this.processStructureValue.extendTpl.formStruct);
        }
        this.showFormData = true;
      }).finally(() => {
        this.loading = false;
      });
    },
    // getContent(id) {
    //   amStockOut.get({ id, enabled: 1 }).then(res => {
    //     if (res && res.content) {
    //       const jsonData = res.content[0];
    //       this.jsonData = jsonData;
    //       this.processStructureValue = jsonData;
    //       this.formStruct = JSON.parse(jsonData.formStruct);
    //       this.formData = JSON.parse(jsonData.formData);
    //       this.formData.unitPrice = convertCurrency(this.formData.unitPrice, false)
    //       this.ruleForm = jsonData;
    //       this.showFormData = true;
    //     }
    //   })
    // },
    submitAction() {
      // 提交前验证SN码和出库数量的关系
      if (this.outBoundForm.snNo && this.ruleForm.stockOutAmount > 1) {
        this.$message.error('填写SN码时，出库数量必须为1');
        return;
      }

      // 验证原有表单
      this.$refs['ruleForm'].validate((valid) => {
        if (!valid) {
          return false;
        } else {
          // 验证新增的出库信息表单
          this.$refs['outBoundForm'].validate((outBoundValid) => {
            if (!outBoundValid) {
              return false;
            } else {
              this.checkModule().then(res => {
                if (!res.flag) {
                  return false;
                } else {
                  // 合并所有表单数据
                  const subData = {
                    ...this.ruleForm,
                    ...this.outBoundForm,
                    ...res.subData
                  };
                  const request = amStock.stockOut;
                  this.submitDisabled = true
                  request(subData).then(() => {
                    this.$notify({
                      title: `出库成功`,
                      type: 'success',
                      duration: 2500
                    })
                    this.cancelForm();
                    this.$emit('successAction')
                  }).catch((e) => {
                    console.log(e);
                  }).finally(() => {
                    this.submitDisabled = false
                  })
                }
              })
            }
          })
        }
      });
    },

    async checkModule() {
      const subData = {
        bindId: this.bindId,
        formData: null,
        formStruct: null,
        formBindToVar: true,
        relation: this.processStructureValue.relation
      };
      if (this.showFormData) {
        return await this.$refs['generateForm'].getData().then(values => {
          console.log(values, '<===>', 'values')
          subData.formData = JSON.stringify(values);
          subData.formStruct = JSON.stringify(this.formStruct);
          return {
            flag: true,
            subData
          };
        }).catch(() => {
          return {
            flag: false
          };
        })
      } else {
        return Promise.resolve({
          subData,
          flag: true
        });
      }
    },
    // 处理SN码变化事件
    handleSnNoChange(value) {
      if (value && value.trim()) {
        // 如果填写了SN码，出库数量必须为1
        if (this.ruleForm.stockOutAmount > 1) {
          this.ruleForm.stockOutAmount = 1;
          this.$message.warning('填写SN码后，出库数量已自动调整为1');
        }
      }
    },
    cancelForm() {
      this.visible = false;
      // 重置出库信息表单
      this.outBoundForm = {
        snNo: '',
        mac: '',
        ip: '',
        receiver: '',
        receiveReason: '',
        boxNo: '',
        changeLocation: '',
        originalBrand: '',
        originalModel: '',
        constructionUnit: '',
        changeArea: '',
        returnDate: '',
        returnMan: ''
      };
      // 清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.outBoundForm) {
          this.$refs.outBoundForm.clearValidate();
        }
      });
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.el-picker-panel__footer .el-button--text.el-picker-panel__link-btn {
  display: none;
}
</style>

<style lang="scss" rel="stylesheet/scss">
.no-atTheMoment {
  .el-picker-panel__footer .el-button--text.el-picker-panel__link-btn {
    display: none;
  }
}
</style>
