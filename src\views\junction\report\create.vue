<template>
  <div class="app-container">
    <el-card v-loading="loading" class="box-card" style="margin-top: 10px">
      <div slot="header" class="clearfix">
        <span>表单信息</span>
      </div>
      <el-form ref="ruleForm" :model="ruleForm" label-width="120px">
        <!-- <el-form-item label="选择:" prop="name">
          <el-input v-model="ruleForm.name" clearable size="small" placeholder="输入关键字搜索" />
        </el-form-item> -->
      </el-form>
      <div v-if="showFormData" class="text item">
        <fm-generate-form
          :ref="'generateForm'"
          :data="formStruct"
          :remote="remoteFunc"
          :value="formData"
          :preview="viewOrEdit"
        />
      </div>
      <el-form ref="ruleForm" :model="ruleForm" label-width="120px">
        <el-form-item label="匹配路口:" prop="description">
          <!-- <el-select v-if="!viewOrEdit" v-model="ruleForm.description" style="width: 400px" filterable class="edit-input" clearable size="mini" placeholder="请选择别名">
            <el-option
              v-for="item in assetList"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            />
          </el-select> -->
          <el-autocomplete
            v-if="!viewOrEdit"
            v-model="ruleForm.description"
            :fetch-suggestions="querySearchAsync"
            placeholder="请输入部件名称"
            style="width:600px"
            :debounce="800"
            @select="handleSelect"
          />
          <p v-else>{{ jsonData.status=="已匹配" ? jsonData.asset.title :'' }}</p>
        </el-form-item>
      </el-form>
      <hr style="background-color: #d9d9d9; border:0; height:1px;">
      <div class="text item" style="text-align: center;margin-top:18px">
        <el-button v-if="!viewOrEdit" type="primary" :disabled="submitDisabled" @click="submitAction">提交</el-button>
        <el-button @click="concelForm">{{ viewOrEdit == false ? '取 消' :'返回' }}</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>

import Vue from 'vue'
import { GenerateForm } from '@/components/VueFormMaking'
import 'form-making/dist/FormMaking.css'
Vue.component(GenerateForm.name, GenerateForm)
import { getToken } from '@/utils/auth'
import { getOmAssetSmall } from '@/api/parts/assets'
import { get, add, edit } from '@/api/parts/fault'
import extendBindTpl from '@/api/system/extendBindTpl'
import crudDictDetail from '@/api/system/dictDetail'
import { getConfig } from '@/utils/getConfigData.js'
export default {
  name: 'ReportCreate',
  data() {
    return {
      loading: false,
      viewOrEdit: false, // 默认是编辑
      submitDisabled: false,
      processStructureValue: {},
      ruleForm: {
        description: ''
      },
      jsonData: {},
      formStruct: {},
      formData: {},
      showFormData: false,
      assetList: [],
      page: 0,
      remoteFunc: {
        funcGetToken(resolve) {
          resolve(getToken())
        },
        getDictDetail(resolve, dictName) {
          crudDictDetail.get(dictName).then(response => {
            const options = response.content
            resolve(options)
          })
        },
        // getStage(resolve) {
        //   // 阶段
        //   this.getDictDetail(resolve, 'report_stage');
        // },
        getProcess(resolve) {
          // 进度
          this.getDictDetail(resolve, 'report_process');
        },
        getDirection(resolve) {
          // 方向
          this.getDictDetail(resolve, 'report_direction');
        }
      },
      assetQuery: {
        categoryId: '',
        bindId: '',
        enabled: 1,
        title: ''
      },
      asset: {}
    }
  },
  created() {
    this.getInitData();
  },
  methods: {
    // 获取所有路口
    querySearchAsync(queryString, cb) {
      if (queryString) {
        this.assetQuery.title = queryString;
        getOmAssetSmall(this.assetQuery).then(resOmAsset => {
          let options = resOmAsset.content || [];
          if (options && options.length) {
            options = options.map(item => {
              item.value = item.title;
              return item;
            })
          } else {
            this.ruleForm.description = '';
            this.asset = {};
          }
          cb(options)
        });
      }
    },
    // throttle(this.handleData),
    handleData(queryString, cb) {
      if (queryString) {
        this.assetQuery.title = queryString;
        getOmAssetSmall(this.assetQuery).then(resOmAsset => {
          let options = resOmAsset.content || [];
          if (options && options.length) {
            options = options.map(item => {
              item.value = item.title;
              return item;
            })
          } else {
            this.ruleForm.description = '';
            this.asset = {};
          }
          cb(options)
        });
      }
    },
    handleSelect(item) {
      this.asset = item;
    },
    getConfigData() {
      const data = {
        key: 'Junction_list'
      }
      getConfig(data).then(res => {
        this.assetQuery.bindId = res.extend.data.bindId;
        this.assetQuery.categoryId = res.extend.data.categoryId;
      })
    },
    getInitData() {
      if (this.$route.query && this.$route.query.rowId) {
        this.getContent();
        this.viewOrEdit = this.$route.query.type == 'see';
      } else {
        // 创建的时候
        this.getProcessNodeList();
      }
      if (this.$route.query.type !== 'see') {
        this.getConfigData();
      }
    },
    getContent() {
      this.loading = true;
      get({ id: this.$route.query.rowId }).then(res => {
        if (res && res.content && res.content.length) {
          const jsonData = res.content[0];
          this.processStructureValue = jsonData;
          this.formStruct = JSON.parse(jsonData.formStruct);
          this.formData = JSON.parse(jsonData.formData);
          this.jsonData = jsonData;
          this.ruleForm.description = this.jsonData?.asset?.title;
          this.asset = this.jsonData?.asset
          this.showFormData = true;
        }
      }).finally(() => {
        this.loading = false;
      });
    },
    getProcessNodeList() {
      this.loading = true;
      const data = { id: this.$route.query.id, enabled: 1 }
      extendBindTpl.get(data).then(res => {
        if (res && res.content && res.content.length) {
          this.processStructureValue = res.content[0];
          const FormStruct = JSON.parse(this.processStructureValue.extendTpl.formStruct);
          this.formStruct = FormStruct;
        }
        this.showFormData = true;
      }).finally(() => {
        this.loading = false;
      });
    },
    submitAction() {
      // console.log(this.ruleForm.description);
      // return
      let isSubmit = false;
      this.$refs['ruleForm'].validate((valid) => {
        if (!valid) {
          isSubmit = true;
          return false;
        }
      })
      this.submitDisabled = true;
      const subData = {
        bindId: this.$route.query.id,
        enabled: 1,
        formData: '',
        formStruct: JSON.stringify(this.formStruct),
        formBindToVar: true,
        relation: this.processStructureValue.relation,
        status: this.ruleForm.description ? '已匹配' : '未匹配'
        // asset: {
        //   id: this.ruleForm.description
        // }
      };
      if (subData.status == '已匹配') {
        subData.asset = this.asset || {}
      }
      this.$refs['generateForm'].getData().then(values => {
        subData.formData = JSON.stringify(values);
      }).catch(() => {
        isSubmit = true;
      })
      setTimeout(() => {
        if (isSubmit) {
          this.submitDisabled = false
          this.$notify({
            title: '请根据提示填写表单信息',
            type: 'info',
            duration: 2500
          });
        } else {
          let request = add;
          if (this.jsonData && this.jsonData.id) {
            subData.id = this.jsonData.id;
            // subData.status = this.jsonData.status;
            request = edit;
          }
          request(subData).then(response => {
            this.concelForm();
          }).catch(() => {
            this.submitDisabled = false
          })
        }
      })
    },
    concelForm() {
      this.$router.go(-1);
    }
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
  ::v-deep .vue-treeselect__control,::v-deep .vue-treeselect__placeholder,::v-deep .vue-treeselect__single-value {
    height: 30px;
    line-height: 30px;
  }
</style>
