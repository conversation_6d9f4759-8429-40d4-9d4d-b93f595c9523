<template>
  <div class="app-container">
    <el-form ref="form" :model="formData" label-width="250px">
      <el-form-item label="运行诊断检查">
        <el-radio-group v-model="formData.health_check">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="要写入发送请求错误日志的文件">
        <el-input v-model="formData.error_log_file" placeholder="要写入发送请求错误日志的文件" />
      </el-form-item>

    </el-form>
  </div>
</template>

<script>
import { delArrInvalid } from '@/views/system/timing/utils/formate'
export default {
  name: 'DebugConfig',
  data() {
    return {
      formData: {
        health_check: null, // []string
        error_log_file: null // int
      }
    };
  },

  mounted() {

  },

  methods: {
    getFormData() {
      return {
        debug: delArrInvalid(this.formData)
      };
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
