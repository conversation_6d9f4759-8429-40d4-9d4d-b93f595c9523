<template>
  <div class="app-container">
    <project-header
      :permission="permission"
      :planning-id="currentPlanning"
      :planning-list="planningList"
      @updateImportInfo="updateImportInfo"
      @planning-change="planningChange"
    />
    <el-card>
      <!--任务或者文档-->
      <just-com
        v-if="currentPlanning == 1 || currentPlanning == 3"
        ref="justComRef"
        :current-planning="currentPlanning"
      />
      <!--进度-->
      <project-progress v-else-if="currentPlanning == 2" ref="projectProgressRef" />
      <!--周报-->
      <weekly v-else-if="currentPlanning == 4" ref="weeklyRef" />
      <!--进度管控-->
      <progress-control v-else-if="currentPlanning == 5" ref="progressControlRef" />
    </el-card>
  </div>
</template>

<script>
import ProjectHeader from '@/views/oaWorkOrder/oaProject/components/projectHeader/ProjectHeader.vue'
import JustCom from '@/views/oaWorkOrder/oaProject/components/justCom/JustCom.vue';
import ProjectProgress from '@/views/oaWorkOrder/oaProject/components/progress/ProjectProgress.vue';
import Weekly from '@/views/oaWorkOrder/oaProject/components/weeklyList/index.vue';
import ProgressControl from '@/views/oaWorkOrder/oaProject/components/progressControl/index.vue';
// 页面中所有的权限
const permission = {
  importXlsWithRule: ['admin', 'oaPmTree:importXlsWithRule'],
  updateTaskAndDirectory: ['admin', 'oaPmTree:updateTaskAndDirectory']
}
export default {
  name: 'OaProject',
  components: { ProjectHeader, ProjectProgress, JustCom, Weekly, ProgressControl },
  data() {
    return {
      permission,
      currentPlanning: 3,
      planningList: [
        { id: 3, name: '文档', permission: ['admin', 'oaDocument:list'] },
        { id: 1, name: '任务', permission: ['admin', 'oaPmTree:list'] },
        { id: 2, name: '进度', permission: ['admin', 'oaPmTree:list'] },
        { id: 4, name: '周报', permission: ['admin', 'oaPmWeeklyReport:list'] },
        { id: 5, name: '进度管控', permission: ['admin', 'oaPmTree:list'] }
      ]
    }
  },
  mounted() {
    // 如果状态为归档，则过滤掉周报
    const status = this.$route.query.status;
    if (status === '归档') {
      this.planningList = this.planningList.filter(item => item.id !== 4);
    }

    if (this.$route.query.from) {
      this.currentPlanning = parseInt(this.$route.query.from)
    }
  },
  methods: {
    planningChange(val) {
      const { id } = val
      this.currentPlanning = id
    },
    updateImportInfo() {
      this.$refs.justComRef.loadTrees()
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
::v-deep .vue-treeselect__control,
::v-deep .vue-treeselect__placeholder,
::v-deep .vue-treeselect__single-value {
  height: 30px;
  line-height: 30px;
}
</style>
