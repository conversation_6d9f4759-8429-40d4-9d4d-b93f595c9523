<template>
  <div class="app-container">
    <el-card v-loading="loading" class="box-card" style="margin-top: 10px">
      <div slot="header" class="clearfix">
        <span class="float:left">表单信息</span>
        <div style="float:right">
          <el-button v-permission="permission.edit" type="success" @click="editDetail">修改</el-button>
        </div>
      </div>
      <el-form ref="ruleForm" :model="ruleForm" label-width="120px">
        <!-- <el-form-item label="选择:" prop="name">
          <el-input v-model="ruleForm.name" clearable size="small" placeholder="输入关键字搜索" />
        </el-form-item> -->
      </el-form>
      <div v-if="showFormData" class="text item">
        <fm-generate-form
          :ref="'generateForm'"
          :data="formStruct"
          :remote="remoteFunc"
          :value="formData"
          :preview="viewOrEdit"
        />
      </div>
      <hr style="background-color: #d9d9d9; border:0; height:1px;">
      <div class="text item" style="text-align: center;margin-top:18px">
        <el-button v-if="!viewOrEdit" type="primary" :disabled="submitDisabled" @click="submitAction">提交</el-button>
        <el-button @click="concelForm">{{ viewOrEdit == false ? '取 消' :'返回' }}</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>

import Vue from 'vue'
import { GenerateForm } from '@/components/VueFormMaking'
import 'form-making/dist/FormMaking.css'
Vue.component(GenerateForm.name, GenerateForm)
import { getToken } from '@/utils/auth'
import { get, add, edit } from '@/api/parts/omItem'
import extendBindTpl from '@/api/system/extendBindTpl'
import { getUser } from '@/api/system/user'
export default {
  name: 'ProjectListCreate',
  data() {
    return {
      loading: false,
      viewOrEdit: false, // 默认是编辑
      submitDisabled: false,
      processStructureValue: {},
      ruleForm: {
      },
      jsonData: {},
      formStruct: {},
      formData: {},
      showFormData: false,
      permission: {
        edit: ['admin', 'omItem:edit']
      },
      remoteFunc: {
        funcGetToken(resolve) {
          resolve(getToken())
        },
        // 获取用户列表
        userList(resolve) {
          getUser().then(response => {
            const options = response.content
            resolve(options)
          })
        },
        // 对级联选择器数据进行处理
        handleData(option) {
          option.map(item => {
            item.value = item.name;
            if (item.hasChildren) {
              this.handleData(option.children);
            }
          });
        }
      }
    }
  },
  created() {
    this.getInitData();
  },
  methods: {
    getInitData() {
      if (this.$route.query && this.$route.query.rowId) {
        this.getContent();
        this.viewOrEdit = this.$route.query.type == 'see';
      } else {
        // 创建的时候
        this.getProcessNodeList();
      }
    },
    getContent() {
      this.loading = true;
      get({ id: this.$route.query.rowId }).then(res => {
        if (res && res.content && res.content.length) {
          const jsonData = res.content[0];
          this.processStructureValue = jsonData;
          this.formStruct = JSON.parse(jsonData.formStruct);
          this.formData = JSON.parse(jsonData.formData);
          this.jsonData = jsonData;
          this.showFormData = true;
        }
      }).finally(() => {
        this.loading = false;
      });
    },
    getProcessNodeList() {
      this.loading = true;
      const data = { id: this.$route.query.id, enabled: 1 }
      extendBindTpl.get(data).then(res => {
        if (res && res.content && res.content.length) {
          this.processStructureValue = res.content[0];
          const FormStruct = JSON.parse(this.processStructureValue.extendTpl.formStruct);
          this.formStruct = FormStruct;
        }
        this.showFormData = true;
      }).finally(() => {
        this.loading = false;
      });
    },
    submitAction() {
      let isSubmit = false;
      this.$refs['ruleForm'].validate((valid) => {
        if (!valid) {
          isSubmit = true;
          return false;
        }
      })
      this.submitDisabled = true;
      const subData = {
        bindId: this.$route.query.id,
        categoryId: this.$route.query.category,
        enabled: 1,
        formData: '',
        formStruct: JSON.stringify(this.formStruct),
        formBindToVar: true,
        relation: this.processStructureValue.relation
      };
      this.$refs['generateForm'].getData().then(values => {
        subData.formData = JSON.stringify(values);
      }).catch(() => {
        isSubmit = true;
      })
      setTimeout(() => {
        if (isSubmit) {
          this.submitDisabled = false
          this.$notify({
            title: '请根据提示填写表单信息',
            type: 'info',
            duration: 2500
          });
        } else {
          let request = add;
          if (this.jsonData && this.jsonData.id) {
            subData.id = this.jsonData.id;
            request = edit;
          }
          request(subData).then(response => {
            this.concelForm();
          }).catch(() => {
            this.submitDisabled = false
          })
        }
      })
    },
    concelForm() {
      this.$router.go(-1);
    },
    editDetail() {
      this.viewOrEdit = false;
    }
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
  ::v-deep .vue-treeselect__control,::v-deep .vue-treeselect__placeholder,::v-deep .vue-treeselect__single-value {
    height: 30px;
    line-height: 30px;
  }
  .peishi{
    font-size:16px;
    color:#ffba00;
    font-weight: 600;
    text-decoration: underline;
    margin-left:47px;
    cursor: pointer;
  }
</style>
