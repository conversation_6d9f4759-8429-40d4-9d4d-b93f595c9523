<template>
  <div class="weekly-list">
    <el-row :gutter="20">
      <!--项目数据-->
      <el-col :lg="6" :md="6" :sm="6" :xl="6" :xs="6">
        <el-date-picker
          v-model="year"
          :picker-options="yearPickerOptions"
          placeholder="选择年"
          style="width:90%;margin:0 auto;display: block;"
          type="year"
          value-format="yyyy"
          @change="changeYear"
        />
        <ul class="date-list">
          <!-- <li
	key="-1"
	:class="selectedDateIndex === '-1' ? 'week-active' : ''"
	@click="changeYearWeek(null,'-1')"
>全部</li> -->
          <li
            v-for="(item,index) in dateList"
            :key="index+1"
            :class="selectedDateIndex === index ? 'week-active' : ''"
            @click="changeYearWeek(item,index)"
          >
            <span class="week-index">第{{ item.index }}周</span>
            {{ item.text }}
          </li>
        </ul>
      </el-col>
      <el-col :lg="18" :md="18" :sm="18" :xl="18" :xs="18">
        <div class="week-right-box">
          <el-button
            slot="left"
            v-permission="permission.add"
            class="filter-item write-weekly"
            icon="el-icon-plus"
            size="mini"
            type="primary"
            @click="crud.toAdd"
          >
            写周报
          </el-button>
          <el-tabs v-model="weekActiveName" @tab-click="weekHandleClick">
            <el-tab-pane label="看周报" name="weekdetail">
              <weeklyDetail ref="weeklyDetail" />
            </el-tab-pane>
            <el-tab-pane label="筛选" name="weeklist">
              <!--周报列表-->
              <div class="head-container">
                <e-header :permission="permission" />
                <crudOperation :permission="permission">
                  <!-- <el-button
							slot="left"
							v-permission="permission.add"
							class="filter-item"
							size="mini"
							type="primary"
							icon="el-icon-plus"
							@click="crud.toAdd"
						>
							写周报
						</el-button> -->
                  <!-- <el-button
						slot="left"
						class="filter-item"
						size="mini"
						type="success"
						icon="el-icon-view"
						@click="goWeeklyDetail"
					>
						看周报
					</el-button> -->
                  <update-button
                    v-if="bindId"
                    slot="right"
                    :bind-id="bindId"
                    :enabled="[1]"
                    :permission="permission"
                  />
                </crudOperation>
              </div>
              <div class="body-box">
                <el-table
                  ref="table"
                  v-loading="crud.loading"
                  :data="tableData"
                  style="width: 100%;"
                  @select="crud.selectChange"
                  @select-all="crud.selectAllChange"
                  @selection-change="crud.selectionChangeHandler"
                >
                  <el-table-column :fixed="true" type="selection" width="55" />
                  <el-table-column
                    v-for="item in tableHeader"
                    :key="item.prop"
                    :align="item.align || 'center'"
                    :fixed="item.fixed || false"
                    :label="item.label"
                    :prop="item.prop"
                    :show-overflow-tooltip="true"
                    :width="item.width || 160"
                  >
                    <template slot-scope="scope">
                      <template v-if="item.label == '预览图'">
                        <el-image
                          :key="item.id"
                          :preview-src-list="[scope.row.ft1[0].url]"
                          :src="scope.row.ft1[0].url"
                          class="el-avatar"
                          fit="contain"
                          lazy
                        >
                          <div slot="error">
                            <i class="el-icon-document" />
                          </div>
                        </el-image>
                      </template>
                      <template v-else-if="item.label == '状态'">
                        <el-tag :type="scope.row.status == '发布' ? 'success' :'info'">{{ scope.row.status }}</el-tag>
                      </template>
                      <span v-else>{{ scope.row[item.prop] }}</span>
                    </template>
                  </el-table-column>
                  <!--   编辑与删除   -->
                  <el-table-column
                    align="center"
                    fixed="right"
                    label="操作"
                    width="100"
                  >
                    <template slot-scope="scope">
                      <udOperation
                        :data="scope.row"
                        :permission="permission"
                      />
                    </template>
                  </el-table-column>
                </el-table>
                <!--分页组件-->
                <pagination />
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import eHeader from './module/header.vue';
import oaPmWeeklyReport from '@/api/oaWorkOrder/oaPmWeeklyReport';
import CRUD, { form, presenter } from '@crud/crud';
import crudOperation from '@crud/CRUD.operation.vue';
import pagination from '@crud/Pagination.vue';
import updateButton from '@/components/UpdateButton/index.vue'
import { mapGetters } from 'vuex'
import udOperation from '@crud/UD.operation.vue';
import dayjs from 'dayjs';
import isoWeek from 'dayjs/plugin/isoWeek';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import weeklyDetail from './module/weeklyDetail.vue'
// import { downloadUrl } from '@/utils/index'

dayjs.extend(isoWeek);
dayjs.extend(isSameOrBefore);

const defaultForm = { id: null }
export default {
  name: 'WeeklyList',
  components: { udOperation, eHeader, crudOperation, pagination, updateButton, weeklyDetail },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: false,
      title: '周报',
      url: 'api/oaPmWeeklyReport/small',
      sort: ['id,desc'],
      query: { enabled: 1 },
      crudMethod: { ...oaPmWeeklyReport },
      optShow: {
        add: false,
        edit: false,
        del: true,
        download: false,
        reset: false,
        rightGroup: false
      }
    })
  },
  mixins: [presenter(), form(defaultForm)],
  data() {
    return {
      permission: {
        del: ['admin', 'oaPmWeeklyReport:del'],
        add: ['admin', 'oaPmWeeklyReport:add'],
        // edit: ['admin', 'oaPmWeeklyReport:edit'],
        updateT: ['admin', 'oaPmWeeklyReport:updateFormStruct'],
        updateR: ['admin', 'oaPmWeeklyReport:updateRelation']
      },
      tableData: [],
      tableHeader: [],
      bindId: '',
      yearPickerOptions: {
        disabledDate(time) {
          return time && time.getFullYear() > new Date().getFullYear();
        }
      },
      year: new Date().getFullYear().toString(),
      dateList: [],
      selectedDateIndex: 0,
      currentTime: {},
      weekActiveName: 'weekdetail'
    }
  },
  computed: {
    ...mapGetters([
      'preViewUrl',
      'omOaAPi'
    ])

  },
  watch: {
    'crud.data': {
      handler(val) {
        if (val && val.length) {
          const tableData = val.map(item => ({
            origin: item,
            id: item.id,
            fv6: item.fv6,
            fv7: item.fv7,
            fv8: item.fv8,
            weekTime: `${item.fv3}~${item.fv4}`,
            createTime: item.createTime,
            pmTreeCreateBy: item.oaPmTree?.createBy
            // ...this.getJson([item.extend.data])
          }));

          // const tableHeader = val[0]?.extend?.tableHeader || [];
          const otherTableHeader = [
            { prop: 'fv6', label: '分类' },
            { prop: 'fv7', label: '内容分类' },
            { prop: 'fv8', label: '工作内容' },
            { prop: 'pmTreeCreateBy', label: '项目负责人', width: 100 },
            { prop: 'createTime', label: '提交时间' },
            { prop: 'weekTime', label: '周报时间', width: 200 }
          ];

          this.tableData = tableData;
          this.tableHeader = [...otherTableHeader];
        } else {
          this.tableData = [];
          this.tableHeader = [];
        }
      },
      deep: true
    }
  },
  created() {
    this.initData();
  },
  methods: {
    weekHandleClick(str) {
      if (this.dateList && this.dateList.length > 0) {
        this.currentTime = this.dateList[0];
      }
      this.changeYearWeek(this.currentTime, 0);
    },
    changeYearWeek(item, index) {
      this.selectedDateIndex = index;
      if (item) {
        this.currentTime = item;
        this.getWeekListByDate();
      }
    },
    getWeekListByDate() {
      switch (this.weekActiveName) {
        case 'weekdetail':
          this.goWeeklyDetail(this.currentTime)
          break;
        case 'weeklist':
          this.crud.query.fv3 = this.currentTime.s;
          this.crud.query.fv4 = this.currentTime.e;
          // 左边的时间和右边的筛选不联动 选择时间时候删除筛选框的筛选
          this.crud.query.fv6 = '';
          this.crud.query.fv7 = '';
          delete this.crud.query.fv6; // 周报分类
          delete this.crud.query.fv7; // 内容分类
          this.crud.toQuery();
          break;
        default:
          break;
      }
    },
    // getWeeksOfYearOld(year) {
    //   year = parseInt(year);
    //   const weeks = [];
    //   let start = dayjs(`${year}-01-01`);
    //   let index = 1; // 初始化周数计数器
    //   const today = dayjs(); // 获取今天的日期
    //   const currentYear = today.year();

    //   // 如果1月1日不是周六，则调整到第一个周六
    //   if (start.day() !== 6) {
    //     start = start.add(6 - start.day(), 'day');
    //   }

    //   while (start.year() === year || (start.year() === year + 1 && start.week() === 1)) {
    //     const endOfWeek = start.add(6, 'day'); // 本周的结束日期（周五）

    //     // 如果处理的年份已经超过了今天所在的年份，或者处理的起始日期超过了今天，则终止循环
    //     if ((start.year() > currentYear) || (start.year() === currentYear && start > today)) {
    //       break;
    //     }

    //     // 特别处理跨年的周
    //     if (endOfWeek.year() === year + 1) {
    //       // 如果本周跨年
    //       if (start.year() === year) {
    //         // 并且本周起始于指定年，则将本周计入指定年
    //         // 不需要额外处理，因为这周自然会被加入
    //       } else {
    //         // 如果本周起始不属于指定年，意味着这是下一年的第一周，应该跳出循环
    //         break;
    //       }
    //     }

    //     const weekObj = {
    //       text: `${start.format('YYYY-MM-DD')}~${endOfWeek.format('YYYY-MM-DD')}`,
    //       year: start.year(),
    //       week: `第${start.week()}周`,
    //       md: `${start.format('MM/DD')}~${endOfWeek.format('MM/DD')}`,
    //       s: start.format('YYYY-MM-DD'),
    //       e: endOfWeek.format('YYYY-MM-DD'),
    //       value: start.week(),
    //       index: index
    //     };

    //     weeks.push(weekObj);
    //     index++; // 周数计数器递增
    //     // 移动到下一个周六，准备计算下一周
    //     start = endOfWeek.add(1, 'day');
    //   }

    //   return weeks.reverse();
    // },
    getWeeksOfYear(year) {
      let index = 1; // 初始化周数计数器
      // 获取当前年份的第一个周一
      let firstMondayOfYear = dayjs(year)
        .startOf('year')
        .isoWeekday(1)
        .startOf('day');

      // 获取当前年份的最后一天
      const lastDayOfYear = dayjs(year)
        .endOf('year')
        .endOf('day');

      // 获取当前日期
      const today = dayjs().startOf('day');

      // 存储每周的起止日期
      const weeks = [];

      // 从年初的第一个周一开始循环，每次增加一周
      while (firstMondayOfYear.isBefore(lastDayOfYear)) {
        // 当周的开始日期（周一）
        const startOfWeek = firstMondayOfYear.clone();

        // 当周的结束日期（周日）
        const endOfWeek = startOfWeek.add(6, 'day');

        // 如果这周的结束日期在今天之后，但今天在这周内，则包含本周
        if (endOfWeek.isAfter(today) && startOfWeek.isAfter(today)) {
          break;
        }

        // 将这一周的起止日期添加到数组中
        const weekObj = {
          text: `${startOfWeek.format('YYYY-MM-DD')}~${endOfWeek.format('YYYY-MM-DD')}`,
          year: endOfWeek.year(),
          md: `${startOfWeek.format('MM/DD')}~${endOfWeek.format('MM/DD')}`,
          s: startOfWeek.format('YYYY-MM-DD'),
          e: endOfWeek.format('YYYY-MM-DD'),
          week: `第${startOfWeek.week()}周`,
          index: index
        };
        weeks.push(weekObj);
        index++; // 周数计数器递增

        // 移动到下一周的开始
        firstMondayOfYear = endOfWeek.add(1, 'day');
      }
      return weeks.reverse();
    },
    changeYear(year) {
      this.getDataList(year);
      // this.selectedDateIndex = '-1';
    },
    initData() {
      this.bindId = this.$config['pm_weekly']?.bindId;
      this.crud.query.bindId = this.bindId;
      this.crud.query.fv1 = this.$route.query.name; // 项目名称
      this.crud.query.pmId = this.$route.query.projectId; // 项目id
      this.getDataList(this.year);
    },
    async getDataList(year) {
      this.dateList = await this.getWeeksOfYear(year);

      if (this.dateList && this.dateList.length > 0) {
        const currentItem = this.dateList[0]; // 或选择适当的元素
        this.changeYearWeek(currentItem, 0); // 自动选中第一项并执行搜索
      }
      // this.crud.toQuery();
    },
    getJson(item) {
      const json = {}
      item.forEach(i => {
        for (const name in i) {
          json[name] = i[name];
        }
      })
      return json;
    },
    [CRUD.HOOK.beforeToAdd](crud, form) {
      this.$emit('updateListFlag', false);
      this.$emit('sendData', { dateList: this.dateList, currentTime: this.currentTime });// 周列表
    },
    [CRUD.HOOK.beforeToEdit](crud, form) {
      if (form.origin) {
        this.$emit('updateListFlag', false);
      }
    },
    [CRUD.HOOK.beforeRefresh](crud, form) {
      // 左边的时间和右边的筛选不联动 选择筛选框的时候去掉时间
      if (this.crud.query.fv6 || this.crud.query.fv7) {
        this.crud.query.fv3 = '';
        this.crud.query.fv4 = '';
        delete this.crud.query.fv3; // 开始时间
        delete this.crud.query.fv4; // 结束时间
        this.selectedDateIndex = -1;
      }
    },
    succeSubmit() {
      this.crud.toQuery();
    },
    goWeeklyDetail(item) {
      this.$refs.weeklyDetail.init(item);
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
::v-deep .vue-treeselect__control, ::v-deep .vue-treeselect__placeholder, ::v-deep .vue-treeselect__single-value {
	height: 30px;
	line-height: 30px;
}

.date-list {
	padding: 20px 0;

	> li {
		font-size: 16px;
		line-height: 24px;
		margin-bottom: 10px;
		cursor: pointer;
		// width: 220px;
		text-align: center;

		> .week-index {
			margin-right: 5px;
			display: inline-block;
			width: 60px;
			text-align: center;
		}
	}

	> .week-active {
		background: #dfe6ec;
	}
}

.week-right-box {
	position: relative;

	.write-weekly {
		position: absolute;
		top: 5px;
		left: 160px;
		z-index: 9999;
	}
}
</style>
