<template>
  <div class="task-dialog">
    <el-drawer
      :before-close="cancelForm"
      :close-on-click-modal="false"
      :title="ADDTYPE[type]"
      :visible.sync="visible"
      append-to-body
      class="task-dialog"
      direction="rtl"
      width="800px"
    >

      <div v-if="visible" class="text item task-content">
        <!-- 基础信息 -->
        <base-info :base-info="baseInfo" :asset-info="assetInfo" />
        <el-form ref="baseInfo" :model="baseInfo" label-width="130px">
          <el-form-item label="到货清单">
            <p
              v-for="item in assetInfo.ft1"
              :key="item.id"
              style="cursor: pointer;color: #2476F8"
              @click="previewFile(item)"
            >{{ item.name }}</p>
          </el-form-item>
          <el-form-item label="验货清单">
            <p
              v-for="item in assetInfo.ft2"
              :key="item.id"
              style="cursor: pointer;color: #2476F8"
              @click="previewFile(item)"
            >{{ item.name }}</p>
          </el-form-item>
          <el-form-item label="现场照片">
            <!-- {{ baseInfo.device.name }} -->
            <el-image
              v-for="(item, index) in assetInfo.sitePhoto.url"
              :key="index"
              style="width: 100px; height: 100px; margin-right: 10px;"
              :src="item"
              :preview-src-list="assetInfo.sitePhoto.url"
            />

          </el-form-item>

          <!-- 入库信息 -->
          <template v-if="type === 3 && storeInfo">
            <el-form-item label="入库时间">
              {{ storeInfo.inDate || '-' }}
            </el-form-item>

            <el-form-item label="存放地址">
              {{ storeInfo.address || '-' }}
            </el-form-item>

            <el-form-item label="入库类型">
              {{ getOrderTypeLabel(storeInfo.orderType) }}
            </el-form-item>

            <el-form-item label="SN码">
              {{ storeInfo.snNo || '-' }}
            </el-form-item>
            <!-- 拆回或维修返回时显示的字段 -->
            <template v-if="storeInfo.orderType === '2' || storeInfo.orderType === '3'">
              <el-form-item label="归还人">
                {{ storeInfo.revertMan || '-' }}
              </el-form-item>

              <el-form-item label="归还原因">
                {{ storeInfo.revertReason || '-' }}
              </el-form-item>
            </template>

            <!-- 后期接收时显示的字段 -->
            <template v-if="storeInfo.orderType === '4'">
              <el-form-item label="交接单位">
                {{ storeInfo.handoverUnit || '-' }}
              </el-form-item>
            </template>
          </template>

          <el-form-item label="备注">
            {{ assetInfo.ft4 }}
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import BaseInfo from './baseInfo.vue'
const ADDTYPE = {
  1: '入库',
  2: '编辑订单',
  3: '查看入库'
}
export default {
  components: { BaseInfo },
  data() {
    return {
      visible: false,
      baseInfo: {},
      type: 1,
      ADDTYPE,
      storeInfo: null // 入库信息
    }
  },
  computed: {
    ...mapGetters([
      'preViewUrl'
    ])
  },
  methods: {
    init(info) {
      const { type, row } = info;
      this.type = type;
      this.baseInfo = row.basicData;
      console.log('row', row);

      // 安全解析JSON的辅助函数
      const safeJsonParse = (jsonString, defaultValue = []) => {
        try {
          if (!jsonString || jsonString === 'null' || jsonString === 'undefined') {
            return defaultValue;
          }
          return JSON.parse(jsonString);
        } catch (error) {
          console.warn('JSON解析失败:', jsonString, error);
          return defaultValue;
        }
      };

      // 使用安全解析函数处理JSON字符串
      const { ft3: ft3String } = row;
      const ft3 = safeJsonParse(ft3String, []);
      const sitePhoto = ft3.reduce((acc, item) => {
        if (item && item.url) {
          acc.url.push(item.url);
          // 如果有thUrl就使用thUrl，否则使用原图url
          acc.thUrl.push(item.thUrl || item.url);
        }
        return acc;
      }, { url: [], thUrl: [] });
      console.log(sitePhoto, 'sitePhoto');

      // 直接在对象字面量中使用属性简写
      this.assetInfo = {
        ...row,
        ft1: safeJsonParse(row.ft1, []),
        ft2: safeJsonParse(row.ft2, []),
        sitePhoto
      };

      if (type === 3) {
        // 模拟入库信息数据（实际使用时应该从API获取）
        this.storeInfo = {
          inDate: row.inDate || '',
          address: row.address || '',
          orderType: row.orderType,
          revertMan: row.revertMan || '',
          snNo: row.snNo || '',
          revertReason: row.revertReason || '',
          handoverUnit: row.handoverUnit || '',
          inAmount: row.inAmount
        };
      } else {
        this.storeInfo = null;
      }

      this.visible = true;
    },
    cancelForm() {
      this.visible = false;
      this.$emit('successAction')
    },
    previewFile(item) {
      const url = item.raw.response.url;
      window.open(`${this.preViewUrl}/onlinePreview?url=${encodeURIComponent(btoa(url))}`);
    },
    // 获取入库类型标签
    getOrderTypeLabel(orderType) {
      const orderTypeMap = {
        '1': '新设备',
        '2': '拆回',
        '3': '维修返回',
        '4': '后期接收'
      };
      return orderTypeMap[orderType] || '-';
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.el-picker-panel__footer .el-button--text.el-picker-panel__link-btn {
  display: none;
}
</style>

<style lang="scss" rel="stylesheet/scss">
.no-atTheMoment {
  .el-picker-panel__footer .el-button--text.el-picker-panel__link-btn {
    display: none;
  }
}

.task-dialog .dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style>
