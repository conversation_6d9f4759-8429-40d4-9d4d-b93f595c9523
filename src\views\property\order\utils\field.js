// 项目列表所有权限
export const permission = {
  add: ['admin', 'amOrder:add'],
  edit: ['admin', 'amOrder:edit'],
  del: ['admin', 'amOrder:del'],
  storeDetail: ['admin', 'amStockIn:list'],
  outBoundDetail: ['admin', 'amStockOut:list'],
  stockDetail: ['admin', 'amStock:list'],
  stocktakingDetail: ['admin', 'amStockCount:list'],
  stockIn: ['admin', 'amOrder:stockIn'],
  updateT: ['admin', 'amOrder:updateFormStruct'],
  updateR: ['admin', 'amOrder:updateRelation'],
  batchImport: ['admin', 'amOrder:batchImport']

}
// 项目列表表头
export const tableHeader = [
  { label: '资产编号', prop: 'basicNo', fixed: 'left', headerAlign: 'left', align: 'left', width: 200 },
  { label: '订单编号', prop: 'orderNo', headerAlign: 'left', align: 'left', width: 200 },
  { label: '项目', prop: 'pmName', align: 'left', headerAlign: 'left', width: 200 },
  { label: '库房', prop: 'depotTitle', headerAlign: 'left', align: 'left' },
  { label: '数量', prop: 'amount', align: 'left', headerAlign: 'left' },
  { label: '计量单位', prop: 'unit', align: 'left', headerAlign: 'left', width: 100 },
  { label: '单价(元)', prop: 'showPrice', align: 'left', headerAlign: 'left' },
  { label: '设备名称', prop: 'deviceName', align: 'left', headerAlign: 'left' },
  { label: '品牌', prop: 'brandName', align: 'left', headerAlign: 'left' },
  { label: '型号', prop: 'modelName', align: 'left', headerAlign: 'left' },
  { label: '入库类型', prop: 'storageTypeText', align: 'left', headerAlign: 'left', width: 100 },
  { label: '下单时间', prop: 'createTime', align: 'left', headerAlign: 'left' },
  { label: '下单人', prop: 'createBy', align: 'left', headerAlign: 'left' }
]

// 定义按钮组
export const updateButtonsLists = [
  {
    label: '批量入库',
    icon: 'upload',
    type: 'warning',
    size: 'mini',
    permission: permission.batchImport,
    fun: 'batchImport'
  }
]

export const manyOption = [
  { name: '入库详情', command: '1', permission: permission.storeDetail, fun: 'toDetail', always: true, routerName: 'Store' },
  { name: '出库详情', command: '2', permission: permission.outBoundDetail, fun: 'toDetail', always: true, routerName: 'OutBound' },
  { name: '库存详情', command: '3', permission: permission.stockDetail, fun: 'toDetail', always: false, routerName: 'Stock' },
  { name: '盘库详情', command: '4', permission: permission.stocktakingDetail, fun: 'toDetail', always: false, routerName: 'Stocktaking' }
]
