<template>
  <div class="">
    <el-form ref="form" v-loading="!isYamlReady" :model="formData" label-width="120px">
      <el-form-item label="链接">
        <el-input v-model="formData.params.url" placeholder="链接地址" />
      </el-form-item>
      <el-form-item label="text脚本">
        <Yaml v-if="isYamlReady" ref="yamlOne" style="width: 100%;" :value="valueOne" :height="height" />
      </el-form-item>
      <el-form-item label="分类ID">
        <el-input v-model="formData.params.content.params.category_id" placeholder="分类ID" />
      </el-form-item>
      <el-form-item label="绑定ID">
        <el-input v-model="formData.params.content.params.bind_id" placeholder="绑定ID" />
      </el-form-item>
      <el-form-item label="conten脚本">
        <Yaml v-if="isYamlReady" ref="yamlTwo" style="width: 100%;" :value="valueTwo" :height="height" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import Yaml from '@/components/YamlEdit/index'
import { delArrInvalid } from '@/views/system/timing/utils/formate'
export default {
  name: 'FerretConfig',
  components: { Yaml },
  data() {
    return {
      height: 300 + 'px',
      valueOne: null,
      valueTwo: null,
      formData: {
        text: '',
        params: {
          url: '',
          content: {
            text: '',
            params: {
              category_id: '',
              bind_id: ''
            }
          }
        }
      },
      isYamlReady: false
    };
  },

  mounted() {
    // const that = this
    // window.onresize = function temp() {
    //   that.height = document.documentElement.clientHeight - 210 + 'px'
    // }
    setTimeout(() => {
      this.isYamlReady = true;
    }, 2000);
  },

  methods: {
    getFormData() {
      this.formData.text = this.$refs.yamlOne.getValue()
      this.formData.params.content.text = this.$refs.yamlTwo.getValue()
      console.log('this.formData==>', this.formData);
      return delArrInvalid(this.formData)
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
