<template>
  <div
    v-if="crud.props.searchToggle"
  >
    <!--<input-select v-model="query.pmId" :options-list="projectList" placeholder="请选择项目" @handleEnter="handleEnter" />-->
    <!--query.pmId-->
    <el-select
      ref="elselect"
      v-model="query.pmId"
      :collapse-tags="true"
      :loading="selectLoading"
      :remote-method="remoteSelectProject"
      class="filter-item"
      clearable
      debounce="500"
      filterable
      multiple
      placeholder="请输入请项目名称"
      remote
      reserve-keyword
      size="small"
      style="width: 200px"
      @keyup.enter.native="handleEnter"
    >

      <el-option
        v-for="item in projectList"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      />

    </el-select>
    <el-input
      v-model="query.ft1"
      class="filter-item"
      clearable
      placeholder="请输入文件名称"
      size="small"
      style="width: 200px;"
    />
    <el-select
      v-model="query.createBy"
      :loading="selectLoading"
      :remote-method="remoteSelectUsers"
      class="filter-item"
      clearable
      debounce="500"
      filterable
      placeholder="请输入上传人"
      remote
      reserve-keyword
      size="small"
      style="width: 200px"
    >
      <el-option
        v-for="item in userList"
        :key="item.id"
        :label="item.username"
        :value="item.username"
      />
    </el-select>
    <date-range-picker v-model="query.createTime" class="date-item" />
    <rrOperation />
  </div>
</template>

<script>
import { header } from '@crud/crud';
import rrOperation from '@crud/RR.operation';
import DateRangePicker from '@/components/DateRangePicker';
import { getPmTreeSmall } from '@/api/oaWorkOrder/oaPmTree'
import { getByName } from '@/api/system/user';

export default {
  components: { rrOperation, DateRangePicker },
  mixins: [header()],
  props: {
    permission: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      docList: [],
      projectList: [],
      userList: [],
      selectLoading: false,
      selectName: ''
    }
  },
  methods: {

    /**
		 * 检索上传人
		 * @param query
		 */
    async remoteSelectUsers(query, type) {
      this.selectName = query;
      const parameters = {
        enabled: 1,
        userName: query,
        size: 99
      }
      const result = await this.publicRemote(query, 'userType', parameters)
      if (type == 'countDoc') {
        const obj = result.find(item => item.username === query)
        this.$set(this.query, 'createBy', obj.username)
        console.log(this.query, '<===>', 'this.query')
      }
      this.userList = result || [];
    },
    async remoteSelectProject(query, type) {
      console.log(query, '<===>', 'query')
      const { bindId } = this.$config.projects_keys;
      const parameters = {
        enabled: 1,
        name: query,
        size: 99,
        bindId,
        fv1: '项目'
      }
      const result = await this.publicRemote(query, 'projectType', parameters)
      const content = result.content || [];
      if (type) {
        const obj = content.find(item => item.name === query)
        this.query.pmId = [obj.id];
      }
      this.projectList = content || [];
    },

    async publicRemote(query, type, parameters) {
      const QueryTypes = {
        'projectType': { loadingProp: 'selectLoading', remoteMethod: getPmTreeSmall },
        'userType': { loadingProp: 'selectLoading', remoteMethod: getByName }
      }
      const typeMap = QueryTypes[type];
      if (query !== '') {
        this[typeMap.loadingProp] = true;
        try {
          const res = await typeMap.remoteMethod(parameters);
          return res || [];
        } catch (error) {
          return [];
        } finally {
          this[typeMap.loadingProp] = false;
        }
      } else {
        return [];
      }
    },
    handleEnter(val) {
      this.crud.query.pmId = this.projectList.map(item => item.id);
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.option-chechbox {
	height: auto;

	.option-chechbox-group {
		display: flex;
		flex-drection: column !important;
	}
}

</style>
