<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <search-header :dict="dict" :permission="permission" />
      <crudOperation :permission="permission">
        <custom-action-button
          slot="right"
          :bind-id="bindId"
          :current-crud="crud"
          :permission="permission"
          @importProject="importProject"
        />
        <vxe-toolbar
          ref="xToolbar1"
          slot="iconButton"
          class-name="info-vxe-toolbar"
          custom
          size="mini"
        />
      </crudOperation>
    </div>
    <!--表单组件-->
    <el-dialog
      :before-close="crud.cancelCU"
      :close-on-click-modal="false"
      :title="crud.status.title"
      :visible.sync="crud.status.cu > 0"
      append-to-body
      width="750px"
    >
      <el-form ref="form" :model="form" :rules="rules" inline label-width="100px" size="small">
        <el-form-item label="资产名称" prop="name">
          <el-input v-model="form.name" style="width: 550px;" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model.number="form.sort"
            :max="999"
            :min="0"
            controls-position="right"
            style="width: 550px;"
          />
        </el-form-item>
        <el-form-item label="顶级">
          <el-radio-group v-model="form.isTop" style="width: 220px">
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="状态" prop="enabled">
          <el-radio v-for="item in dict.device_status" :key="item.id" v-model="form.enabled" :label="item.value">
            {{ item.label }}
          </el-radio>
        </el-form-item>
        <el-form-item v-if="form.isTop === '0'" label="上级" prop="pid">
          <treeselect
            v-model="form.pid"
            :load-options="loadDevice"
            :options="Devices"
            placeholder="选择上级设备"
            style="width: 550px;"
          />
        </el-form-item>
      </el-form>
      <div v-if="showFormData" class="text item">
        <fm-generate-form
          :ref="'generateForm'"
          :data="formStruct"
          :remote="remoteFunc"
          :value="formData"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="deviceSubmitCU">确认</el-button>
      </div>
    </el-dialog>
    <!--表格渲染-->
    <el-table
      ref="table"
      v-loading="crud.loading"
      :data="crud.data"
      :load="getCategoryDatas"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      lazy
      row-key="id"
      @select="crud.selectChange"
      @select-all="crud.selectAllChange"
      @selection-change="crud.selectionChangeHandler"
    >
      <el-table-column :selectable="checkboxT" type="selection" width="55" />
      <el-table-column label="名称" prop="name" />
      <el-table-column label="ID" prop="id" />
      <el-table-column label="排序" prop="sort" />
      <el-table-column align="center" label="状态" prop="enabled">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.enabled"
            :active-value="1"
            :disabled="scope.row.id === 1"
            :inactive-value="0"
            active-color="#409EFF"
            inactive-color="#F56C6C"
            @change="changeEnabled(scope.row, scope.row.enabled)"
          />
        </template>
      </el-table-column>
      <el-table-column label="创建日期" prop="createTime" />
      <el-table-column
        v-if="checkPer(['admin','extendCategory:edit','extendCategory:del'])"
        align="center"
        fixed="right"
        label="操作"
        width="260"
      >
        <template slot-scope="scope">
          <udOperation
            :data="scope.row"
            :disabled-dle="scope.row.id === 1"
            :permission="permission"
            msg="确定删除吗,如果存在下级节点则一并删除，此操作不能撤销！"
          />
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination />
    <!--导入项目-->
    <upload-file ref="refUploadFile" @getlist="crud.toQuery()" />
  </div>
</template>

<script>
import Vue from 'vue'
import {
  GenerateForm
} from '@/components/VueFormMaking'
import 'form-making/dist/FormMaking.css'

Vue.component(GenerateForm.name, GenerateForm)
import crudAmCategory from '@/api/property/amCategory'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { LOAD_CHILDREN_OPTIONS } from '@riophae/vue-treeselect'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import extendBindTpl from '@/api/system/extendBindTpl'
import { getUser } from '@/api/system/user'
import { getToken } from '@/utils/auth'
import SearchHeader from './components/searchHeader.vue'
import CustomActionButton from './components/customActionButton.vue'
import pagination from '@crud/Pagination';
import UploadFile from './components/uploadFile.vue'
import { permission } from './utils/field'
// import { formatterTableData, formatterTableHeader } from './utils/commonFun'

const defaultForm = { id: null, name: null, isTop: '1', subCount: 0, pid: null, sort: 999, enabled: '1' }

export default {
  name: 'Device',
  components: { UploadFile, Treeselect, crudOperation, udOperation, SearchHeader, CustomActionButton, pagination },
  cruds() {
    return CRUD({
      title: '设备',
      url: 'api/amCategory',
      query: { pidIsNull: true, enabled: [1, 0], bindId: '' },
      crudMethod: { ...crudAmCategory }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['device_status'],
  data() {
    return {
      Devices: [],
      // tableHeaders: tableHeader,
      // tableData: [],
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请输入序号', trigger: 'blur', type: 'number' }
        ]
      },
      permission,
      enabledTypeOptions: [
        { key: 1, display_name: '正常' },
        { key: 0, display_name: '禁用' }
      ],
      processStructureValue: {},
      formStruct: {},
      formData: {},
      showFormData: false,
      bindId: '',
      remoteFunc: {
        funcGetToken(resolve) {
          resolve(getToken())
        },
        // 获取用户列表
        userList(resolve) {
          getUser().then(response => {
            const options = response.content
            resolve(options)
          })
        },
        // 对级联选择器数据进行处理
        handleData(option) {
          option.map(item => {
            item.value = item.name;
            if (item.hasChildren) {
              this.handleData(option.children);
            }
          });
        }
      }
    }
  },
  // watch: {
  //   'crud.data': {
  //     async handler(val, oldVal) {
  //       // 处理表头和筛选选项
  //       const headers = formatterTableHeader(val)
  //       this.tableHeaders = headers.map(header => {
  //         // 获取当前列的所有唯一值作为筛选选项
  //         const values = [...new Set(val.map(item => item[header.prop]))].filter(Boolean)
  //         return {
  //           ...header,
  //           filters: values.map(value => ({
  //             label: String(value),
  //             value: value
  //           }))
  //         }
  //       })
  //       this.tableData = formatterTableData(val)
  //       this.$nextTick(() => {
  //         // 手动将表格和工具栏进行关联
  //         this.$refs.table.connect(this.$refs.xToolbar1)
  //       })
  //     },
  //     deep: true
  //   }
  // },
  methods: {
    getCategoryDatas(tree, treeNode, resolve) {
      const params = { pid: tree.id, enabled: [1, 0] }
      setTimeout(() => {
        crudAmCategory.getAmCategory(params).then(res => {
          resolve(res.content)
        })
      }, 100)
    },
    [CRUD.HOOK.beforeRefresh](crud, form) {
      const { bindId } = this.$config.amCategory_key
      this.crud.query.bindId = bindId;
      this.bindId = bindId;
    },
    // 新增与编辑前做的操作
    [CRUD.HOOK.afterToCU](crud, form) {
      if (form.pid !== null) {
        form.isTop = '0'
      } else if (form.id !== null) {
        form.isTop = '1'
      }
      if (form.enabled === true || form.enabled == 1) {
        form.enabled = '1';
      }
      if (form.enabled === false || form.enabled == 0) {
        form.enabled = '0';
      }
      if (form.id != null) {
        this.getSupDevice(form.id);
        this.getRowData(form.id);
      } else {
        this.getDevice();
        this.getExtendTpl();
      }
    },
    /** 添加取消 - 之后 */
    [CRUD.HOOK.afterAddCancel](crud, form) {
      this.resetModule();
    },
    /** 编辑取消 - 之后 */
    [CRUD.HOOK.afterEditCancel](crud, form) {
      this.resetModule();
    },
    resetModule() {
      this.processStructureValue = {};
      this.formStruct = {};
      this.formData = {};
      this.showFormData = false;
    },
    getExtendTpl() {
      const { bindId } = this.$config.amCategory_key
      const data = { id: bindId, enabled: 1 }
      extendBindTpl.get(data).then(res => {
        if (res && res.content && res.content.length) {
          this.processStructureValue = res.content[0];
          const FormStruct = JSON.parse(this.processStructureValue.extendTpl.formStruct);
          this.formStruct = FormStruct;
        }
        this.showFormData = true;
      });
    },
    getRowData(rowId) {
      if (rowId) {
        crudAmCategory.getAmCategory({ id: rowId }).then(res => {
          if (res && res.content && res.content[0]) {
            const jsonData = res.content[0];
            this.processStructureValue = jsonData;
            this.formStruct = JSON.parse(jsonData.formStruct);
            this.formData = JSON.parse(jsonData.formData);
            this.showFormData = true;
          }
        });
      }
    },
    getSupDevice(id) {
      crudAmCategory.getAmCategorySuperior(id).then(res => {
        const date = res.content
        this.buildDevice(date)
        this.Devices = date
      })
    },
    buildDevice(data) {
      data.forEach(data => {
        if (data.children) {
          this.buildDevice(data.children)
        }
        if (data.hasChildren && !data.children) {
          data.children = null
        }
      })
    },
    getDevice() {
      const { bindId } = this.$config.amCategory_key
      crudAmCategory.getAmCategory({ enabled: '1', bindId: bindId }).then(res => {
        this.Devices = res.content.map(function(obj) {
          if (obj.hasChildren) {
            obj.children = null
          }
          return obj
        })
      })
    },
    // 获取弹窗内设备数据
    loadDevice({ action, parentNode, callback }) {
      if (action === LOAD_CHILDREN_OPTIONS) {
        crudAmCategory.getAmCategory({ enabled: '1', pid: parentNode.id }).then(res => {
          parentNode.children = res.content.map(function(obj) {
            if (obj.hasChildren) {
              obj.children = null
            }
            return obj
          })
          setTimeout(() => {
            callback()
          }, 100)
        })
      }
    },
    handleToggleExpand({ row, column, expanded }) {
      if (expanded && row.hasChildren && !row.children) {
        this.loadDevice({ action: LOAD_CHILDREN_OPTIONS, parentNode: row, callback: () => {} })
      }
    },
    deviceSubmitCU() {
      this.$refs['form'].validate((valid) => {
        if (!valid) {
          return false;
        } else {
          if (this.form.pid !== null && this.form.pid === this.form.id) {
            this.$message({
              message: '上级不能为空',
              type: 'warning'
            })
            return false
          }
          if (this.form.isTop === '1') {
            this.form.pid = null
          }
          this.checkModule().then(res => {
            if (!res.flag) {
              return false;
            } else {
              const subData = { ...this.form, ...res.subData };
              let request = crudAmCategory.add;
              if (subData.id) {
                request = crudAmCategory.edit;
              }
              request(subData).then(response => {
                this.crud.refresh();
                this.crud.cancelCU();
                this.resetModule();
                this.$notify({
                  title: '操作成功',
                  type: 'success',
                  duration: 2500
                })
              }).catch((e) => {
                console.log(e);
              })
            }
          })
        }
      })
    },
    async checkModule() {
      const { bindId } = this.$config.amCategory_key
      const subData = {
        bindId: bindId,
        formData: '',
        formStruct: JSON.stringify(this.formStruct),
        formBindToVar: true,
        relation: this.processStructureValue.relation
      };
      const p = await this.$refs['generateForm'].getData().then(values => {
        subData.formData = JSON.stringify(values);
        return {
          flag: true,
          subData
        };
      }).catch(() => {
        return {
          flag: false
        };
      })
      return p;
    },
    // 改变状态
    changeEnabled(data, val) {
      this.$confirm('此操作将 "' + this.dict.label.device_status[val] + '" ' + data.name + '设备, 是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        crudAmCategory.edit(data).then(res => {
          this.crud.notify(this.dict.label.device_status[val] + '成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
        }).catch(err => {
          if (val == 0) {
            data.enabled = 1;
          }
          if (val == 1) {
            data.enabled = 0;
          }
          console.log(err.response.data.message)
        })
      }).catch(() => {
        if (val == 0) {
          data.enabled = 1;
        }
        if (val == 1) {
          data.enabled = 0;
        }
      })
    },
    checkboxT(row, rowIndex) {
      return row.id !== 1
    },
    // 导入项目
    importProject() {
      const { bindId, categoryId } = this.$config.projects_keys
      this.$refs.refUploadFile.init({ bindId, categoryId });
    }

  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
::v-deep .vue-treeselect__control, ::v-deep .vue-treeselect__placeholder, ::v-deep .vue-treeselect__single-value {
  height: 30px;
  line-height: 30px;
}
</style>
<style lang="scss" rel="stylesheet/scss" scoped>
::v-deep .el-input-number .el-input__inner {
  text-align: left;
}
</style>
