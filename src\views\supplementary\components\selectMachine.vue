<template>
  <div class="select-machine">
    <div :style="'background-image:url(' + Background + ');'" class="machine-top">
      <span class="title">{{ currentSelects.title }}</span>
      <img alt="" class="arrow" src="@/assets/images/selectArrow.png">
    </div>
    <div class="all-machine">
      <ul class="all-machine-box">
        <li
          v-for="item in machineList"
          :key="item.id"
          :class="item.id===selectID ? 'active':''"
          @click="selectMachine(item)"
        >
          {{ item.title }} <span style="padding-left: 15px;">({{ item.selectText }})</span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import Background from '@/assets/images/machineSelect.png'
import { allMachine } from '../utils/fileds'

export default {
  name: 'SelectMachine',
  components: {},

  props: {
    currentSelects: {
      type: Object,
      default: () => {
      }
    },
    machineList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      Background,
      allMachine,
      selectID: 1
    }
  },

  computed: {},
  watch: {},
  created() {
    this.selectID = this.currentSelects.id
  },
  methods: {

    selectMachine(item) {
      this.selectID = item.id
      this.$emit('selectMachine', item)
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.select-machine {
	width: 1920px;
	//padding: 0 58px;
	transform: scale(0.75);
	transform-origin: top left; /* 确保缩放基点为左上角 */
	position: relative;
	z-index: 99;

	&:hover {
		.all-machine {
			display: block;
			max-height: 808px;
		}

		.arrow {
			transform: rotateX(180deg);
		}
	}

	.machine-top {
		width: 555px;
		height: 62px;
		background-size: contain;
		background-position: top left;
		background-repeat: no-repeat;
		position: absolute;
		right: 0;
		top: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;

		&:hover {
			.arrow {
				transform: rotateX(180deg);
			}
		}

		.title {
			font-weight: 500;
			font-size: 20px;
			color: #FFFFFF;
		}

		.arrow {
			width: 16px;
			height: 10px;
			margin-left: 25px;
			transition: transform 1s ease;
		}

	}

	.all-machine {
		position: absolute;
		right: 93px;
		top: 62px;
		width: 444px;
		max-height: 0;
		display: none;
		background: #FFFFFF;
		transition: all 0.5s ease-in-out;
		box-shadow: 0 0 8px 8px rgba(20, 145, 255, 0.3);

		.all-machine-box {
			display: flex;
			flex-direction: column;
			align-content: center;
			background: #FFFFFF;

			li {
				width: 100%;
				height: 50px;
				color: #3B444F;
				font-size: 20px;
				text-align: center;
				line-height: 50px;
				cursor: pointer;

				&:hover, &.active {
					background: #249FE1;
					color: #fff;
				}

			}
		}
	}
}
</style>
