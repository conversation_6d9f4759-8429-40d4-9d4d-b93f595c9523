import { tableHeader } from './field'
import { isArray } from '@/utils/is'

/**
 * 格式化表格数据
 * @param {Array} val - 表格数据数组
 */
export function formatterTableData(val) {
  if (val && val.length) {
    let tableData = [];
    tableData = val.map(item => {
      const json = item.extend.data || {};
      let fv7 = ''
      try {
        // 尝试解析 item.fv7，如果解析失败会抛出异常
        fv7 = JSON.parse(item.fv7);
      } catch (error) {
        // 如果 JSON.parse 失败，意味着 fv7 可能已经是字符串了，直接赋值
        fv7 = item.fv7;
      }

      // 判断 fv7 是否为数组
      if (isArray(fv7)) {
        // 如果是数组，将其转换成逗号分隔的字符串
        fv7 = fv7.join(',');
      }

      return {
        ...json,
        ...item,
        fv7
      };
    });
    return tableData;
  } else {
    return [];
  }
}

/**
 * 格式化表头
 * @param {Array} val - 表格数据数组
 */
export function formatterTableHeader(val) {
  const otherHeader = [];
  return [...tableHeader, ...otherHeader];
}

/**
 * 点击是否显示操作列
 * @param vueInstance
 * @returns {Promise<void>}
 */
export async function toggleSideBarFun(vueInstance) {
  vueInstance.operateShow = !vueInstance.operateShow;
  await vueInstance.$nextTick()
  vueInstance.$refs.table.doLayout()
}

export function mergeCell({ row, column, rowIndex, columnIndex, tableData }) {
  if (columnIndex === 1 || columnIndex === 2 || columnIndex === 3) {
    const arr = getSpanArray(tableData, 'fv4');
    return getSpan(arr, rowIndex);
  } else if (columnIndex === 4 || columnIndex === 5) {
    const arr = getSpanArray(tableData, 'fv4', 'fv5');
    return getSpan(arr, rowIndex);
  }
  return { rowspan: 1, colspan: 1 };
}

function getSpanArray(list, key1, key2) {
  const spanArray = [];
  for (let i = 0; i < list.length; i++) {
    if (i === 0) {
      spanArray.push({ row: 1, col: 1 });
    } else {
      const isSame = key2
        ? list[i][key1] === list[i - 1][key1] && list[i][key2] === list[i - 1][key2]
        : list[i][key1] === list[i - 1][key1];
      if (isSame) {
        spanArray.push({ row: 0, col: 0 });
        const index = spanArray.findIndex((_, idx) => list[idx][key1] === list[i][key1] && (!key2 || list[idx][key2] === list[i][key2]));
        spanArray[index].row++;
      } else {
        spanArray.push({ row: 1, col: 1 });
      }
    }
  }
  return spanArray;
}

function getSpan(arr, rowIndex) {
  return {
    rowspan: arr[rowIndex].row,
    colspan: arr[rowIndex].col
  };
}
