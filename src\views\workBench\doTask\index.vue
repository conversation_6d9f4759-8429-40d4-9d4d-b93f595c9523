<template>
  <div class="app-container do-task">
    <el-card>
      <div slot="header" class="clearfix">
        <span>任务详情</span>
        <el-button size="mini" style="float: right; " type="warning" @click="handleCancel">返回</el-button>
      </div>
      <work-task-detail :project-detail="projectDetail" :type="type" @delAttachment="delAttachment" />
    </el-card>
    <el-card v-if="type==1" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span>处理任务</span>
      </div>
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="130px">
        <!--<el-form-item v-if="isShowFv9" label="处理人:" prop="fv9">-->
        <!--  <el-select-->
        <!--    v-model="ruleForm.fv9"-->
        <!--    :loading="selectLoading"-->
        <!--    :remote-method="remoteSelectUsers"-->
        <!--    class="filter-item"-->
        <!--    clearable-->
        <!--    debounce="500"-->
        <!--    filterable-->
        <!--    placeholder="请输入处理人"-->
        <!--    remote-->
        <!--    reserve-keyword-->
        <!--    size="small"-->
        <!--    style="width: 100%"-->
        <!--  >-->
        <!--    <el-option-->
        <!--      v-for="item in userList"-->
        <!--      :key="item.id"-->
        <!--      :label="item.username"-->
        <!--      :value="item.username"-->
        <!--    />-->
        <!--  </el-select>-->
        <!--</el-form-item>-->

        <el-form-item label="状态:" prop="fv12">
          <el-select
            v-model="ruleForm.fv12"
            class="filter-item"
            clearable
            placeholder="请选择状态"
            size="small"
            style="width: 100%"
          >
            <el-option
              v-for="item in dict.task_status"
              :key="item.id"
              :label="item.label"
              :value="item.label"
            />
          </el-select>
        </el-form-item>
        <!--        <el-form-item label="备注:" prop="ft2">-->
        <!--          <el-input-->
        <!--            v-model="ruleForm.ft2"-->
        <!--            :rows="4"-->
        <!--            placeholder="请输入备注"-->
        <!--            type="textarea"-->
        <!--          />-->
        <!--        </el-form-item>-->
        <el-form-item label="附件:">
          <el-upload
            ref="upload"
            :action="`${cloudfileUploadApi}?name=${fileName}&platform=${platform}`"
            :auto-upload="true"
            :before-upload="beforeUpload"
            :file-list="filesList"
            :headers="headers"
            :on-change="handleChange"
            :on-error="handleError"
            :on-success="handleSuccess"
          >
            <div class="eladmin-upload"><i class="el-icon-upload" /> 添加文件</div>
            <div slot="tip" class="el-upload__tip">可上传任意格式文件，且不超过10M</div>
          </el-upload>
        </el-form-item>

        <el-form-item>
          <el-button :disabled="submitDisabled" type="primary" @click="handleSubmit">处理</el-button>
          <el-button type="danger" @click="handleCancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span>任务流转历史</span>
      </div>
      <work-bench-task
        ref="workTaskRef"
        :custom-crud-option="CRUDOPTION"
        :operate-column="OPERATECOLUMN"
        :permission="permission"
        :table-data="tableData"
        :table-header="TATBLEHEADER"
      />
    </el-card>
  </div>
</template>

<script>
import WorkTaskDetail from '@/views/workBench/components/WorkTaskDetail.vue';
import WorkBenchTask from '@/views/workBench/components/WorkBenchTask.vue';
// import { getTaskTodo } from '@/api/workBench/oaPmTaskTodo';
import { mapPmTree, mapTaskItem } from '@/views/workBench/utils/handelData';
import crudUser from '@/api/system/user';
import { mapGetters } from 'vuex';
import { getToken } from '@/utils/auth';
import oaPmTree from '@/api/oaWorkOrder/oaPmTree';
import { findOptions } from '@/api/system/globalConfig'

const CRUDOPTION = {
  url: 'api/oaPmTaskHistory'
}
const TATBLEHEADER = [
  {
    prop: 'name',
    label: '任务标题'
  },
  {
    prop: 'fv9',
    label: '处理人'
  },
  {
    prop: 'fv12',
    label: '当前状态'
  },
  {
    prop: 'currentStatusVal',
    label: '是否结束'
  },
  {
    prop: 'createTime',
    label: '创建时间'
  },
  {
    prop: 'fv13',
    label: '开始时间'
  },
  {
    prop: 'fv11',
    label: '预计结束时间'
  }
]

const OPERATECOLUMN = {
  isShow: false
}
export default {
  name: 'DoTask',
  components: { WorkTaskDetail, WorkBenchTask },

  data() {
    return {
      submitDisabled: false,
      projectDetail: {},
      permission: [],
      ruleForm: {
        fv9: '',
        ft2: '',
        attachment: []
      },
      isShowFv9: false,
      headers: { 'Authorization': getToken() },
      rules: {
        // fv9: [
        //   { required: true, message: '请输入处理人', trigger: 'change' }
        // ],
        fv12: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      },
      userList: [],
      filesList: [],
      selectLoading: false,
      fileName: '',
      tableData: [],
      CRUDOPTION,
      TATBLEHEADER,
      OPERATECOLUMN,
      platform: '',
      type: '1'
    }
  },
  computed: {
    ...mapGetters([
      'baseApi',
      'cloudfileUploadApi',
      'user'
    ])
  },

  dicts: ['task_status'],

  watch: {
    'ruleForm.fv12': {
      handler(val) {
        console.log(val, '<===>', 'val')
        if (val == '已拒绝') {
          if (this.tableData.length > 0) {
            this.ruleForm.fv9 = this.tableData[0].userName
          } else {
            this.ruleForm.fv9 = this.projectDetail.createBy
          }
        } else {
          this.ruleForm.fv9 = this.projectDetail.createBy
        }
      }
    }
  },
  created() {
    const { type } = this.$route.query;
    this.type = Number(type);
    this.getDetail();
    this.getPlatform()
  },
  methods: {
    // 获取详情
    getDetail() {
      const { id } = this.$route.query;
      const query = {
        page: 0,
        enabled: 1,
        id
      }
      oaPmTree.getPmTreeSmall(query).then(async res => {
        const data = res.content.map(mapPmTree);
        this.projectDetail = data[0];
        this.ruleForm.fv9 = this.projectDetail.createBy;
        await this.$nextTick()
        this.hadnleHistory()
      })
    },
    hadnleHistory() {
      const { taskId } = this.projectDetail;
      const { crud } = this.$refs.workTaskRef;
      crud.query.taskId = taskId;
      crud.refresh().then(res => {
        console.log(res, '<===>', 'res')
        // 获取到数据
        const data = res.content;
        this.tableData = data.map(mapTaskItem);
        console.log(this.tableData, '<===>', 'this.tableData')
      })
    },

    getPlatform() {
      findOptions({ type: 'STORAGE', enabled: 1 }).then(res => {
        if (res && res.STORAGE) {
          const platformList = res.STORAGE;
          this.platform = Object.keys(platformList)[0]
        }
      })
    },
    // 处理人搜索
    remoteSelectUsers(query) {
      if (query !== '') {
        this.selectLoading = true;
        const data = {
          enabled: 1,
          userName: query,
          size: 99
        }
        crudUser.getByName(data).then(res => {
          if (res) {
            this.userList = res || [];
            this.selectLoading = false;
          } else {
            this.userList = [];
          }
        })
      } else {
        this.userList = [];
      }
    },
    beforeUpload(file) {
      let isLt2M = true
      isLt2M = file.size / 1024 / 1024 < 10
      if (!isLt2M) {
        this.loading = false
        this.$message.error('上传文件大小不能超过 10MB!')
      }
      this.fileName = file.name
      return isLt2M
    },
    handleError(e, file, fileList) {
      const msg = JSON.parse(e.message)
      this.$notify({
        title: msg.message,
        type: 'error',
        duration: 2500
      })
      this.loading = false
    },
    handleSuccess(response, file, fileList) {
      this.$notify({
        title: '上传成功',
        type: 'success',
        duration: 2500
      })
    },
    handleChange(file, fileList) {
      this.filesList = fileList;
    },
    handleSubmit() {
      this.submitDisabled = true
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.ruleForm.attachment = [...this.projectDetail.attachment, ...this.filesList]
          const data = {
            ...this.projectDetail.original,
            ...this.ruleForm,
            formBindToVar: 0
          }
          oaPmTree.transfer(data).then(res => {
            this.submitDisabled = false
            this.$notify({
              title: '处理成功',
              type: 'success',
              duration: 2500
            })
            this.handleCancel()
          })
        } else {
          this.submitDisabled = false
          return false
        }
      });
    },
    handleCancel() {
      this.$router.go(-1);
    },
    delAttachment(val) {
      this.projectDetail.attachment.splice(val, 1)
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>

</style>
