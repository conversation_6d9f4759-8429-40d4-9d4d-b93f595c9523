export const legend = [
  { title: '在线', color: '#378b43' },
  { title: '离线', color: '#b6a014' },
  { title: '调试中', color: '#6c216d' },
  { title: '问题', color: '#ad4a45' },
  { title: '未开始', color: '#666' },
  { title: '其他', color: '#165e83' }
]

import {
  jingzhihenghuaLake,
  jingzhizhengshangyazhu1,
  jingzhizhengshangyazhu2,
  jingzhikonggangjifang1,
  jingzhikonggangjifang2,
  jingzhibanbidian1,
  chewanghenghuaLake1,
  chewanghenghuaLake2,
  chewanghenghuaLake3,
  chewangzhengshangyazhu1,
  chewangzhengshangyazhu2,
  chewangzhengshangyazhu3,
  chewangkonggang1,
  chewangkonggang2,
  chewangbanbidian1,
  chewangbanbidian2
} from './dataFileds'
// 机房下拉选择项
export const allMachine = [
  {
    id: 1,
    img: 'topology1',
    dataList: jing<PERSON><PERSON>ghuaLake,
    tagName: '京智网通网',
    title: '京智网 - 恒华湖 - 实时中心环 - 1',
    customStyle: 'padding: 107px 45px 30px 45px;',
    bgStyle: {
      width: '1830px',
      height: '943px'
    }
  },
  {
    id: 2,
    img: 'topology2',
    dataList: jingzhizhengshangyazhu1,
    tagName: '京智网通网',
    title: '京智网 - 正商雅筑 - 实时中心环 - 1',
    customStyle: 'padding: 111px 45px 18px 45px;',
    bgStyle: {
      width: '1830px',
      height: '951px'
    }
  },
  {
    id: 3,
    img: 'topology3',
    dataList: jingzhizhengshangyazhu2,
    tagName: '京智网通网',
    title: '京智网 - 正商雅筑 - 实时中心环 - 2',
    customStyle: 'padding: 128px 45px 30px 45px;',
    bgStyle: {
      width: '1830px',
      height: '922px'
    }
  },
  {
    id: 4,
    img: 'topology4',
    dataList: jingzhikonggangjifang1,
    tagName: '京智网通网',
    title: '京智网 - 空港机房 - 实时中心环 - 1',
    customStyle: 'padding: 128px 45px 22px 45px;',
    bgStyle: {
      width: '1830px',
      height: '930px'
    }
  },
  {
    id: 5,
    img: 'topology5',
    dataList: jingzhikonggangjifang2,
    tagName: '京智网通网',
    title: '京智网 - 空港机房 - 实时中心环 - 2',
    customStyle: 'padding: 160px 93px 61px 93px;',
    bgStyle: {
      width: '1734px',
      height: '859px'
    }
  },
  {
    id: 6,
    img: 'topology6',
    dataList: jingzhibanbidian1,
    tagName: '京智网通网',
    title: '京智网 - 半壁店 - 实时中心环 - 1',
    customStyle: 'padding: 160px 141px 61px 141px;',
    bgStyle: {
      width: '1638px',
      height: '859px'
    }
  },
  {
    id: 7,
    img: 'topology7',
    dataList: chewanghenghuaLake1,
    tagName: '车网通网',
    title: '车网 - 恒华湖 - 实时中心环 - 1',
    customStyle: 'padding: 292px 162px;',
    bgStyle: {
      width: '1596px',
      height: '496px'
    }
  },
  {
    id: 8,
    img: 'topology8',
    dataList: chewanghenghuaLake2,
    tagName: '车网通网',
    title: '车网 - 恒华湖 - 实时中心环 - 2',
    customStyle: 'padding: 354px 306px;',
    bgStyle: {
      width: '1308px',
      height: '372px'
    }
  },
  {
    id: 9,
    img: 'topology9',
    dataList: chewanghenghuaLake3,
    tagName: '车网通网',
    title: '车网 - 恒华湖 - 实时中心环 - 3',
    customStyle: 'padding: 354px 162px;',
    bgStyle: {
      width: '1596px',
      height: '372px'
    }
  },
  {
    id: 10,
    img: 'topology10',
    dataList: chewangzhengshangyazhu1,
    tagName: '车网通网',
    title: '车网 - 正商雅筑 - 实时中心环 - 1',
    customStyle: 'padding: 354px 306px;',
    bgStyle: {
      width: '1308px',
      height: '372px'
    }
  },
  {
    id: 11,
    img: 'topology11',
    dataList: chewangzhengshangyazhu2,
    tagName: '车网通网',
    title: '车网 - 正商雅筑 - 实时中心环 - 2',
    customStyle: 'padding: 300px 162px 202px 162px;',
    bgStyle: {
      width: '1596px',
      height: '578px'
    }
  },
  {
    id: 12,
    img: 'topology12',
    dataList: chewangzhengshangyazhu3,
    tagName: '车网通网',
    title: '车网 - 正商雅筑 - 实时中心环 - 3',
    customStyle: 'padding: 197px 210px 99px 210px;',
    bgStyle: {
      width: '1500px',
      height: '784px'
    }
  },
  {
    id: 13,
    img: 'topology13',
    dataList: chewangkonggang1,
    tagName: '车网通网',
    title: '车网 - 空港 - 实时中心环 - 1',
    customStyle: 'padding: 197px 66px 99px 66px;',
    bgStyle: {
      width: '1788px',
      height: '784px'
    }
  },
  {
    id: 14,
    img: 'topology14',
    dataList: chewangkonggang2,
    tagName: '车网通网',
    title: '车网 - 空港 - 实时中心环 - 2',
    customStyle: 'padding: 197px 162px 99px 162px;',
    bgStyle: {
      width: '1596px',
      height: '784px'
    }
  },
  {
    id: 15,
    img: 'topology15',
    dataList: chewangbanbidian1,
    tagName: '车网通网',
    title: '车网 - 半壁店 - 实时中心环 - 1',
    customStyle: 'padding: 403px 162px 305px 162px;',
    bgStyle: {
      width: '1596px',
      height: '372px'
    }
  },
  {
    id: 16,
    img: 'topology16',
    dataList: chewangbanbidian2,
    tagName: '车网通网',
    title: '车网 - 半壁店 - 实时中心环 - 2',
    customStyle: 'padding: 506px 568px 408px 568px;',
    bgStyle: {
      width: '784px',
      height: '166px'
    }
  }
]
