<template>
  <div v-if="crud.props.searchToggle" class="">
    <el-input
      v-model="query.fv4OrTitle"
      class="filter-item"
      clearable
      placeholder="请输入编号或路口名称"
      size="small"
      style="width: 180px;"
      @keyup.enter.native="crud.toQuery"
    />
    <rrOperation />
  </div>
</template>

<script>
import { header } from '@crud/crud';
import rrOperation from '@crud/RR.operation';

export default {
  components: { rrOperation },
  mixins: [header()],
  props: {
    permission: {
      type: Object,
      required: true
    },
    dict: {
      type: Object,
      required: true
    }
  },
  data() {
    return {}
  },

  computed: {},
  watch: {},
  created() {
  },
  methods: {}
}
</script>
