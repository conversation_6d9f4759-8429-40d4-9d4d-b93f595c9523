<!--创建工单-->
<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>公共信息</span>
      </div>
      <div class="text item">
        <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px">
          <el-form-item label="标题:" prop="title" style="margin-bottom: 13px">
            <el-input v-model="ruleForm.title" size="small" />
          </el-form-item>
          <el-form-item label="优先级:" prop="priority" style="margin-bottom: 0">
            <el-radio-group v-model="ruleForm.priority" size="small">
              <el-radio :label="1">一般</el-radio>
              <el-radio :label="2">紧急</el-radio>
              <el-radio :label="3">非常紧急</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <el-card class="box-card" style="margin-top: 10px;">
      <div slot="header" class="clearfix">
        <span>表单信息</span>
      </div>
      <div class="text item">
        <template v-for="(tplItem) in processStructureValue.tpls">
          <fm-generate-form
            :key="tplItem.id"
            :ref="'generateForm-'+tplItem.id"
            :data="tplItem.formStructure"
            :remote="remoteFunc"
          />
        </template>
      </div>
      <hr style="background-color: #d9d9d9; border:0; height:1px;">
      <div class="text item" style="text-align: center;margin-top:18px">
        <el-button
          v-for="(item, index) in buttonList.edges"
          v-show="item.source===buttonList.nodes[active].id"
          :key="index"
          :disabled="submitDisabled"
          type="primary"
          @click="submitAction(item)"
        >
          {{ item.label }}
        </el-button>
      </div>
    </el-card>
    <!--转交组件-->
    <forward-orde ref="forwardOrde" @reset="toUpcoming" />
  </div>
</template>

<script>

import Vue from 'vue';
import { GenerateForm } from '@/components/VueFormMaking';
import 'form-making/dist/FormMaking.css';
import ForwardOrde from './components/forwardOrde.vue';
import crudDictDetail from '@/api/system/dictDetail';
import { getToken } from '@/utils/auth';
import { addWorkOrderInfo, workOrderFirst } from '@/api/oaWorkOrder/workOrder';
import { getUser } from '@/api/system/user';
import { getInfo } from '@/api/login';
import { LOAD_CHILDREN_OPTIONS } from '@riophae/vue-treeselect';
import oaPmTree from '@/api/oaWorkOrder/oaPmTree';

Vue.component(GenerateForm.name, GenerateForm)
export default {
  name: 'Create',
  components: { ForwardOrde },
  data() {
    const _this = this
    return {
      buttonList: [],
      submitDisabled: false,
      active: 0,
      processStructureValue: {},
      ruleForm: {
        enabled: true,
        title: '',
        priority: 1,
        process: '',
        classify: '',
        data: [],
        history: {
          remarks: '',
          status: '',
          circulation: ''
        }
      },
      userInfo: {},
      rules: {
        title: [
          { required: true, message: '请输入工单标题', trigger: 'blur' }
        ],
        priority: [
          { required: true, message: '请选择工单优先级', trigger: 'blur' }
        ]
      },
      remoteFunc: {
        funcGetToken(resolve) {
          resolve(getToken())
        },
        // 获取用户列表
        userList(resolve) {
          getUser({ enabled: 1, size: 9999 }).then(response => {
            const options = response.content
            resolve(options)
          })
        },
        // 获取字典详情
        getDictDetail(resolve, dictName) {
          crudDictDetail.get(dictName).then(response => {
            const options = response.content
            resolve(options)
          })
        },
        // 文档类型
        docTypeList(resolve) {
          this.getDictDetail(resolve, 'doc_type');
        },
        // 获取所有的单位
        deptList(resolve) {
          // getUser({ enabled: 1, size: 9999 }).then(response => {
          //   const options = response.content
          //   resolve(options)
          // })
        },
        projectFun(option, resolve) {
          if (option.pid) {
            // 编辑
            oaPmTree.getPmTreeSuperior(option.pid).then(res => {
              const data = res.content
              this.buildProject(data)
              resolve(data)
            })
          } else {
            // 添加
            const { pmId } = _this.$route.query
            console.log(pmId, '<===>', 'id')
            oaPmTree.getPmTree({ id: pmId, enabled: 1, size: 9999, fv1: '项目' }).then(res => {
              const data = res.content.map(function(obj) {
                obj.label = `${obj.label}(${obj.fv1})`
                if (obj.hasChildren) {
                  obj.children = null
                }
                return obj
              })
              resolve(data)
            })
          }
        },
        buildDepts(depts) {
          depts.forEach(data => {
            data.label = `${data.label}(${data.fv1})`
            if (data.children) {
              this.buildDepts(data.children)
            }
            if (data.hasChildren && !data.children) {
              data.children = null
            }
          })
        },
        // 懒加载函数
        loadProject({ action, parentNode, callback }) {
          if (action === LOAD_CHILDREN_OPTIONS) {
            oaPmTree.getPmTree({ enabled: '1', pid: parentNode.id }).then(res => {
              parentNode.children = res.content.map(function(obj) {
                obj.label = `${obj.label}(${obj.fv1})`
                if (obj.hasChildren) {
                  obj.children = null
                }
                return obj
              })
              setTimeout(() => {
                callback()
              }, 100)
            })
          }
        }
      }
    }
  },
  created() {
    this.getProcessNodeList();
    this.getUserInfo();
  },
  methods: {
    // 获取用户信息
    getUserInfo() {
      getInfo().then((res) => {
        this.userInfo = res.user;
      })
    },
    // 工作流-工单管理(步骤一)
    getProcessNodeList() {
      const { id, classify } = this.$route.query
      workOrderFirst({ id, classify }).then(response => {
        for (const i in response.tpls) {
          response.tpls[i].formStructure = JSON.parse(response.tpls[i].formStructure);
        }
        this.processStructureValue = response;
        this.buttonList = JSON.parse(this.processStructureValue.process.structure);
        this.currentNode = response.process;
        console.log(this.processStructureValue);
      })
    },
    async submitAction(item) {
      let isSubmit = false;
      this.$refs['ruleForm'].validate((valid) => {
        if (!valid) {
          // 没有通过校验
          isSubmit = true;
          return false;
        }
      });
      this.submitDisabled = true
      // 对数据处理
      Promise.all(this.processData(item)).then(() => {
        if (isSubmit) {
          this.notAllowedSubmit()
        } else {
          this.addOrderFun();
        }
      }).catch(() => {
        this.notAllowedSubmit()
      });
    },
    // 处理数据
    processData(item) {
      this.ruleForm.process = { id: parseInt(this.$route.query.id) }
      this.ruleForm.pmId = this.$route.query.pmId
      this.ruleForm.classify = this.$route.query.classify;
      this.ruleForm.history.remarks = this.userInfo.username;
      this.ruleForm.history.status = item.flowProperties;
      this.ruleForm.history.circulation = item.label;
      const JsonData = [];
      const promises = [];
      for (const tpl of this.processStructureValue.tpls) {
        const json = {
          'formStructure': tpl.formStructure,
          'formData': '',
          'enabled': true,
          'tplId': tpl.id
        };
        const promise = this.$refs['generateForm-' + tpl.id][0].getData().then(res => {
          json.formData = res;
        });
        JsonData.push(json);
        promises.push(promise);
      }
      this.ruleForm.data = JsonData;
      return promises
    },
    // 创建走接口
    addOrderFun() {
      addWorkOrderInfo(this.ruleForm).then(res => {
        if (res.id) {
          this.tipIsInver(res);
        } else {
          this.toUpcoming();
        }
      }).catch(() => {
        this.submitDisabled = false;
      });
    },
    notAllowedSubmit() {
      this.submitDisabled = false;
      this.$notify({
        title: '请根据提示填写表单信息',
        type: 'info',
        duration: 2500
      });
    },
    // 提示是否要转交
    tipIsInver(data) {
      console.log('data<===>', data)
      this.$confirm('是否要指派给个人', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        this.onClickInverSion(data)
      }).catch(() => {
        this.toUpcoming();
      });
    },

    onClickInverSion(row) {
      this.$refs.forwardOrde.init(row);
    },

    //  转交成功或者不转交
    toUpcoming() {
      this.$router.push({ name: 'ProcessAll' })
    }
  }
}
</script>
