<template>
  <div v-if="crud.props.searchToggle" class="">
    <el-input
      v-model="query.title"
      class="filter-item"
      clearable
      placeholder="请输入立杆位置搜索"
      size="small"
      style="width: 180px;"
      @keyup.enter.native="crud.toQuery"
    />
    <el-input
      v-model="query.fv4"
      class="filter-item"
      clearable
      placeholder="请输入融合后点位搜索"
      size="small"
      style="width: 150px;"
      @keyup.enter.native="crud.toQuery"
    />
    <self-select
      :options="dict.xl_police_station"
      :select-value.sync="query.fv8"
      :tags="2"
      class="filter-item"
      clearable
      placeholder="请选择派出所"
      size="small"
      style="width: 160px;"
      @selectChange="(val)=>selectChange(val, 'fv8')"
    />
    <self-select
      :options="dict.xl_link"
      :select-value.sync="query.fv9"
      :tags="2"
      class="filter-item"
      clearable
      placeholder="请选择融合后链路"
      size="small"
      style="width: 160px;"
      @selectChange="(val)=>selectChange(val, 'fv9')"
    />
    <el-input
      v-model="query.fv15"
      class="filter-item"
      clearable
      placeholder="请输入巡检人员"
      size="small"
      style="width: 180px;"
      @keyup.enter.native="crud.toQuery"
    />
    <date-range-picker
      v-model="query.createTime"
      class="date-item"
      style="width:350px !important"
      type="datetimerange"
      start-placeholder="巡检开始日期"
      end-placeholder="巡检结束日期"
    />
    <rrOperation />
  </div>
</template>

<script>
import { header } from '@crud/crud';
import rrOperation from '@crud/RR.operation';
import DateRangePicker from '@/components/DateRangePicker'

export default {
  components: { rrOperation, DateRangePicker },
  mixins: [header()],
  props: {
    permission: {
      type: Object,
      required: true
    },
    dict: {
      type: Object,
      required: true
    }
  },
  data() {
    return {}
  },

  computed: {},
  watch: {},
  created() {
  },
  methods: {
    selectChange(val, type) {
      this.crud.query[type] = val;
      this.crud.toQuery();
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
