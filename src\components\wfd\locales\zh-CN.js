export default {
  'label': '标题',
  'hideIcon': '隐藏图标',
  'userTask': '审批节点',
  'userTask.assignType': '指派类型',
  'userTask.assignType.placeholder': '选择一个类型',
  'userTask.assignType.person': '人员',
  'userTask.assignType.person.title': '审批人',
  'userTask.assignType.person.placeholder': '选择审批人',
  'userTask.assignType.role': '角色',
  'userTask.assignType.role.title': '审批角色',
  'userTask.assignType.role.placeholder': '选择审批角色',
  'userTask.assignType.persongroup': '人员组',
  'userTask.assignType.persongroup.title': '审批组',
  'userTask.assignType.persongroup.placeholder': '选择审批组',
  'userTask.assignType.custom': '自定义类',
  'userTask.assignType.custom.title': '类名',
  'userTask.assignType.department': '部门',
  'userTask.assignType.department.title': '审批部门',
  'userTask.assignType.department.placeholder': '选择审批部门',
  'userTask.assignType.variable': '变量',
  'userTask.assignType.variable.title': '审批变量',
  'userTask.assignType.variable.placeholder': '选择审批变量',
  'userTask.dueDate': '到期时间',
  'userTask.dueDate.placeholder': '请选择日期',
  'userTask.counterSign': '会签',
  'userTask.activeOrder': '主动接单',
  'userTask.fullHandle': '全员处理',
  'userTask.endorsement': '加签',
  'conventional': '常规节点',
  'handleNode': '处理节点',
  'handleNode.assignType': '指派类型',
  'handleNode.assignType.placeholder': '选择一个类型',
  'handleNode.assignType.person': '人员',
  'handleNode.assignType.person.title': '审批人',
  'handleNode.assignType.person.placeholder': '选择审批人',
  'handleNode.assignType.persongroup': '人员组',
  'handleNode.assignType.persongroup.title': '审批组',
  'handleNode.assignType.persongroup.placeholder': '选择审批组',
  'handleNode.assignType.custom': '自定义类',
  'handleNode.assignType.custom.title': '类名',
  'handleNode.assignType.department': '部门',
  'handleNode.assignType.department.title': '审批部门',
  'handleNode.assignType.department.placeholder': '选择审批部门',
  'handleNode.assignType.variable': '变量',
  'handleNode.assignType.variable.title': '审批变量',
  'handleNode.assignType.variable.placeholder': '选择审批变量',
  'handleNode.dueDate': '到期时间',
  'handleNode.dueDate.placeholder': '请选择日期',
  'handleNode.counterSign': '会签',
  'handleNode.endorsement': '加签',
  'handleNode.activeOrder': '主动接单',
  'scriptTask': '任务节点',
  'scriptTask.script': '脚本',
  'javaTask': '自定义类节点',
  'javaTask.javaClass': '类名',
  'mailTask': '邮件节点',
  'mailTask.to': '收件人',
  'mailTask.subject': '标题',
  'mailTask.content': '内容',
  'receiveTask': '接收节点',
  'receiveTask.waitState': '等待属性',
  'receiveTask.stateValue': '等待值',
  'timerEvent': '定时节点',
  'timerEvent.cycle': '循环时间',
  'timerEvent.cycle.placeholder': '请选择时间',
  'timerEvent.duration': '持续时间',
  'messageEvent': '消息节点',
  'messageEvent.message': '消息名',
  'signalEvent': '信号节点',
  'signalEvent.signal': '信号名',
  'sequenceFlow': '连接线',
  'sequenceFlow.expression': '条件表达式',
  'sequenceFlow.seq': '序号',
  'sequenceFlow.reverse': '反向',
  'startEvent': '开始节点',
  'endEvent': '结束节点',
  'start': '开始事件',
  'end': '结束事件',
  'gateway': '网关',
  'exclusiveGateway': '排他网关',
  'parallelGateway': '并行网关',
  'inclusiveGateway': '包容网关',
  'task': '活动',
  'catch': '捕获事件',
  'tooltip.undo': '撤销',
  'tooltip.redo': '重复',
  'tooltip.copy': '复制',
  'tooltip.paste': '粘贴',
  'tooltip.delete': '删除',
  'tooltip.zoomIn': '缩小',
  'tooltip.zoomOut': '放大',
  'tooltip.zoomReset': '实际大小',
  'tooltip.autoFit': '适应屏幕',
  'tooltip.toFront': '移到上一层',
  'tooltip.toBack': '移到下一层',
  'tooltip.edit': '编辑',
  'process': '流程',
  'process.id': '流程标识',
  'process.name': '流程名称',
  'process.dataObjs': '数据对象',
  'process.signalDefs': '信号定义',
  'process.messageDefs': '消息定义',
  'process.dataObjs.id': 'Id',
  'process.dataObjs.name': '名称',
  'process.dataObjs.type': '类型',
  'process.dataObjs.defaultValue': '默认值',
  'process.signalDef.scope': '作用域'
}
