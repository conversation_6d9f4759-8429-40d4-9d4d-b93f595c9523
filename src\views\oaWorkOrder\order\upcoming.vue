<!--我的代办-->
<template>
  <div class="app-container">
    <el-card class="box-card">
      <!--搜索-->
      <el-form ref="listQuery" :model="listQuery" :inline="true">
        <WorkOrderSearch :genre="'upcoming'" @handleSearch="handleSearch" @reset="reset" />
      </el-form>
      <el-table v-loading="loading" :data="ticketList" @selection-change="handleSelectionChange">
        <!-- <el-table-column type="selection" width="55" align="center" /> -->
        <!-- <el-table-column label="ID" prop="id" width="120" /> -->
        <el-table-column label="表单标题" prop="title" min-width="120" :show-overflow-tooltip="true" />
        <el-table-column label="流程" prop="process.name" :show-overflow-tooltip="true" />
        <el-table-column label="当前状态" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>
              {{ scope.row.state[0].label }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="申请人" prop="createBy" :show-overflow-tooltip="true" />
        <el-table-column label="当前处理人" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>
              <template v-for="item in scope.row.current">
                {{ item }}
              </template>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="优先级" :show-overflow-tooltip="true" width="120" align="left">
          <template slot-scope="scope">
            <span v-if="scope.row.priority===2">
              <el-tag type="warning">紧急</el-tag>
            </span>
            <span v-else-if="scope.row.priority===3">
              <el-tag type="danger">非常紧急</el-tag>
            </span>
            <span v-else>
              <el-tag type="success">一般</el-tag>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="是否结束" :show-overflow-tooltip="true" width="80" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.isEnd===0" size="mini" type="success">否</el-tag>
            <el-tag v-else size="mini" type="danger">是</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="create_time" width="180">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="left" class-name="small-padding fixed-width" width="180">
          <template slot-scope="scope">
            <el-button
              v-permission="['admin','oaWorkOrderInfo:handle']"
              size="mini"
              type="success"
              @click="handleView(scope.row)"
            >处理</el-button>
            <el-button
              v-if="scope.row.isEnd===0"
              v-permission="['admin','oaWorkOrderInfo:forward']"
              size="mini"
              type="primary"
              @click="handleInversion(scope.row)"
            >转交</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--转交组件-->
      <forward-orde ref="forwardOrde" @reset="getList" />
      <!--分页组件-->
      <Paging :page="pageInfo" @handleCurrentChange="getList()" />
    </el-card>
  </div>
</template>

<script>
import { getoaWorkOrderTodo } from '@/api/oaWorkOrder/workOrderTodo';
import ForwardOrde from './components/forwardOrde.vue';
import WorkOrderSearch from './components/search.vue'
import { mapGetters } from 'vuex'
export default {
  components: { ForwardOrde, WorkOrderSearch },
  data() {
    return {
      queryParams: {},
      total: 0,
      loading: false,
      ticketList: [],
      pageInfo: {
        size: 10,
        page: 1,
        total: 0
      },
      listQuery: {}
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  created() {
    this.getList();
  },
  methods: {
    reset() {
      this.listQuery = {};
      this.getList();
    },
    async getList() {
      this.loading = true
      const data = {
        page: this.pageInfo.page - 1,
        size: this.pageInfo.size,
        sort: 'id,desc',
        // title:
        enabled: true,
        ...this.listQuery
      }
      getoaWorkOrderTodo(Object.assign(data, { userId: this.user.id })).then(response => {
        this.ticketList = response.content;
        this.pageInfo.total = response.totalElements;
        this.loading = false
      })
    },
    handleSearch(val) {
      for (var k in val) {
        this.listQuery[k] = val[k]
      }
      this.getList()
    },
    handleView(row) {
      this.$router.push({ name: 'ProcessListHandle', query: { workOrderId: row.id, name: 'upcoming' }})
    },
    handleInversion(row) {
      this.$refs.forwardOrde.init(row)
    },
    handleSelectionChange() {}
  }
}
</script>

<style scoped>

</style>
