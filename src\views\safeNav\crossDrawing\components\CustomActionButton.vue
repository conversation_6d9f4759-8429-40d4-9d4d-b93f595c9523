<template>
  <span class="oper-buttons">
    <update-button
      v-if="bindId"
      :bind-id="bindId"
      :enabled="[1]"
      :other-params="otherParams"
      :permission="permission"
    />
    <el-button
      v-for="(item) in updateButtonsLists"
      :key="item.label"
      v-permission="item.permission"
      :icon="`el-icon-${item.icon}`"
      :size="item.size || 'mini'"
      :type="item.type || 'primary'"
      class="filter-item"
      @click="handelClick(item)"
    >
      {{ item.label }}
    </el-button>
  </span>
</template>

<script>
import updateButton from '@/components/UpdateButton/index.vue';
import { getToken } from '@/utils/auth';
import { downloadUrl } from '@/utils';
import { mapGetters } from 'vuex';
import { updateButtonsLists } from '../utils/field'

export default {
  name: '',
  components: { updateButton },
  props: {
    bindId: {
      type: String,
      default: ''
    },
    permission: {
      type: Object,
      default: () => {
        return {}
      }
    },
    currentCrud: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    return {
      otherParams: {},
      updateButtonsLists
    }
  },

  computed: {
    ...mapGetters([
      'omAssetAffiliated'
    ])
  },
  watch: {},
  created() {
  },
  methods: {
    handelClick(item) {
      this[item.fun](item.query);
    },
    importProject() {
      this.$emit('importProject')
    },
    uploadExcelSuccess() {
      this.$emit('uploadExcelSuccess')
    },
    exportProject({ fileType }) {
      const { query } = this.currentCrud
      const { bindId, categoryId } = this.$config.rectify_ledger_key
      const fixedParams = {
        page: 0,
        size: 99999,
        fileName: '整改台账'
      };

      const variableParams = {
        'affiliated.enabled': 1,
        'affiliated.fv4OrTitle': query.fv4OrTitle,
        'affiliated.fv1': query.fv1,
        'affiliated.status': query.status,
        'affiliated.fv2With69': query.fv2With69,
        'affiliated.categoryId': categoryId,
        'affiliated.bindId': bindId
      };
      const filteredVariableParams = this.filterParams(variableParams);

      const params = { ...fixedParams, ...filteredVariableParams };

      this.downloadFile(fileType, params);
    },
    downloadFile(fileType, params) {
      const queryString = Object.keys(params)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&');
      const url = `${this.omAssetAffiliated}/rectifyLedger/${fileType}?${queryString}`;
      let message = '处理中，'
      if (fileType === 'html') {
        message = '预览中，';
      } else if (fileType === 'xls') {
        message = '下载中，';
      }
      this.$message({
        message: message + `请稍后查看...`,
        type: 'success'
      })
      fetch(url, {
        headers: {
          'Authorization': `${getToken()}`
        }
      })
        .then(response => {
          if (!response.ok) {
            throw new Error('Network response was not ok');
          }
          return response.blob();
        })
        .then(blob => {
          const newUrl = window.URL.createObjectURL(blob);

          if (fileType === 'html') {
            window.open(newUrl, '_blank');
          } else if (fileType === 'xls') {
            downloadUrl(newUrl, '整改台账');
          }
        })
        .catch(error => {
          console.error('There was a problem with your fetch operation:', error);
        });
    },
    // 专门用于过滤参数
    filterParams(variableParams) {
      return Object.entries(variableParams).reduce((acc, [key, value]) => {
        if (this.isNotEmpty(value)) {
          acc[key] = typeof value === 'string' ? value.trim() : value;
        }
        return acc;
      }, {});
    },
    // 检查给定值是否为空
    isNotEmpty(value) {
      const type = typeof value;
      switch (type) {
        case 'string':
          return value.trim() !== '';
        case 'object':
          return value !== null && (
            Array.isArray(value) ? value.length > 0 : Object.keys(value).length > 0
          );
        default:
          return value !== null && value !== undefined;
      }
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
