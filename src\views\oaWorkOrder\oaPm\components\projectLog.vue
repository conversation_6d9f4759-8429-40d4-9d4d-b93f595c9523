<template>
  <el-dialog
    v-dialog-drag="{minHeight:'600px'}"
    :before-close="concelForm"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :modal="false"
    :modal-append-to-body="false"
    :title="fullTitle"
    :visible.sync="visible"
    append-to-body
    width="800px"
  >
    <el-form ref="elForm" :model="elForm" :rules="elRule" class="remarks" size="small" label-position="top">
      <template v-for="(item,index) in oldData">
        <template v-if="item.isDelete === 0">
          <el-form-item :key="item.currentTime" :label="`${item.username}： ${item.currentTime}`">
            <div :key="index" class="remarks-item">
              <div class="remarks-detail" :class="permission.delProjectLog?'long-detail':''">{{ item.detail }}</div>
              <div v-permission="permission.delProjectLog" class="remarks-del">
                <el-button class="remarks-del" size="mini" type="danger" @click="delOldLog(item,index)">删除</el-button>
              </div>
            </div>
          </el-form-item>
        </template>
      </template>
      <el-form-item v-permission="permission.addProjectLog" label="项目日志" prop="detail" style="margin-top: 5px">
        <el-input v-model="elForm.detail" :autosize="{ minRows: 6, maxRows: 20}" type="textarea" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="text" @click="concelForm">取消</el-button>
      <el-button
        v-permission="permission.addProjectLog"
        :disabled="submitDisabled"
        type="primary"
        @click="submitAction"
      >
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import oaPmTree from '@/api/oaWorkOrder/oaPmTree'
import { mapGetters } from 'vuex';
import { createID } from '@/utils/index'

export default {
  name: 'ProjectLog',
  props: {
    permission: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      title: '项目日志',
      submitDisabled: false,
      visible: false,
      oldData: [], // 旧的
      elForm: {
        id: createID(),
        username: '',
        detail: '',
        currentTime: '',
        isDelete: 0 // 0是默认值 1是删除了
      },
      elRule: {
        detail: []
      },
      projectId: null,
      projectName: ''
    }
  },

  computed: {
    ...mapGetters([
      'user'
    ]),
    fullTitle() {
      let projectName = ''
      if (this.projectName) {
        projectName = `【${this.projectName}】`
      }
      return `${projectName}${this.title}`;
    }
  },
  watch: {},
  created() {
  },
  methods: {
    // 初始化
    initData(data) {
      this.visible = true;
      this.oldData = data.ft3 ? JSON.parse(data.ft3) : [];
      this.projectId = data.id;
      this.projectName = data.name;
    },
    delOldLog(item) {
      item.isDelete = 1;
    },
    submitAction() {
      this.$refs['elForm'].validate((valid) => {
        if (valid) {
          this.submitDisabled = true;
          this.elForm.username = this.user.username;
          this.elForm.currentTime = this.$dayJS().format('YYYY-MM-DD HH:mm:ss');
          const ft3 = this.elForm.detail === '' ? [...this.oldData] : [...this.oldData, this.elForm];

          const data = {
            enabled: 1,
            ft3: JSON.stringify(ft3),
            id: this.projectId
          }
          oaPmTree.edit(data).then(res => {
            this.submitDisabled = false;
            this.$message({
              message: '添加成功',
              type: 'success'
            })
            this.$emit('toRefresh')
            this.concelForm();
          })
        } else {
          this.submitDisabled = false
          return false
        }
      })
    },
    concelForm() {
      this.$refs['elForm'].resetFields()
      this.visible = false
      this.oldData = []
      this.$emit('concelForm')
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.remarks {

	.remarks-item {
		position: relative;
		display: flex;
    align-items: center;

		.remarks-del {
      margin-left:6px;
      vertical-align: top;
		}

		.remarks-detail {
			width: calc(100% - 62px);
			//white-space: pre-wrap;
			white-space: pre-line;
			background-color: #e8f4ff;
			border-radius: 5px;
			padding: 6px 10px;
      line-height: 22px;
		}
    .remarks-detail.long-detail{
      width: 100%;
    }
	}

}
</style>
