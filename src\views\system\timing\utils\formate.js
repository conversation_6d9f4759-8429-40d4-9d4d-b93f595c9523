// export function delArrInvalid(info) {
//   const filteredData = Object.entries(info).reduce((result, [key, value]) => {
//     result[key] = Array.isArray(value) && value.length > 0 ? value.filter(item => item) : value;
//     return result;
//   }, {});
//   return filteredData
// }

export function delArrInvalid(info) {
  const filteredFormData = Object.entries(info).reduce((result, [key, value]) => {
    if (Array.isArray(value) && value.length === 0) {
      return result;
    }
    if (value === 'null' || value === null || value === '' || value === undefined) {
      return result;
    }
    result[key] = value;
    return result;
  }, {});
  return filteredFormData
}

