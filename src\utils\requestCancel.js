import axios from 'axios';

const sources = new Map();

export function addSource(key) {
  if (sources.has(key)) {
    sources.get(key).cancel('Operation canceled due to new request.');
  }
  const source = axios.CancelToken.source();
  sources.set(key, source);
  return source.token;
}

export function removeSource(key) {
  sources.delete(key);
}

export function cancelAll() {
  sources.forEach((source) => source.cancel('Operation canceled due to new request.'));
  sources.clear();
}
