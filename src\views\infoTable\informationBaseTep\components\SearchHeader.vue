<template>
  <div v-if="crud.props.searchToggle" class="">
    <el-input
      v-model="crud.query.fv4OrTitle"
      class="filter-item"
      clearable
      placeholder="请输入路口名称或编号"
      size="small"
      style="width: 180px;"
      @keyup.enter.native="assetToQuery"
    />
    <el-button class="filter-item" icon="el-icon-search" size="mini" type="success" @click="assetToQuery">搜索</el-button>
    <el-button class="filter-item" icon="el-icon-refresh-left" size="mini" type="warning" @click="resetToQuery">重置
    </el-button>
    <el-button
      :disabled="!showTgaDisabled"
      :loading="!showTgaDisabled"
      class="filter-item"
      icon="el-icon-view"
      size="mini"
      type="primary"
      @click="openLabelDrawer"
    >打开标签
    </el-button>
    <LabelDrawer
      v-if="showTagView"
      ref="labelDrawer"
      :crud="crud"
      :drawer-options="drawerOptions"
      @update:showTgaDisabled="upDateTagDisabled"
    />
  </div>
</template>

<script>
import CRUD, { header } from '@crud/crud'
import LabelDrawer from './LabelDrawer.vue';
import {
  clearTagAssetToQuery,
  resetAssetQuery,
  getConfigData
} from '../utils/commonFun';

export default {
  components: { LabelDrawer },
  mixins: [header()],
  props: {
    permission: {
      type: Object,
      required: true
    },
    dict: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      drawerOptions: {
        title: '标签选择',
        direction: 'rtl',
        size: '45%'
      },
      labelConfig: {},
      showTagView: false,
      showTgaDisabled: false
    }
  },
  async created() {
    this.labelConfig = await getConfigData();
  },
  methods: {
    [CRUD.HOOK.afterRefresh]() {
      this.showTagView = true;
    },
    upDateTagDisabled() {
      setTimeout(() => {
        this.showTgaDisabled = true;
      }, 1000);
    },
    selectChange(val, type) {
      this.crud.query[type] = val;
      this.assetToQuery();
    },
    openLabelDrawer() {
      this.$refs.labelDrawer.labelVisible = true;
    },
    /** 清空标签换资产接口去请求 */
    assetToQuery() {
      clearTagAssetToQuery(this.crud, this.labelConfig);
      this.crud.toQuery();
    },
    /** 清空选项，清空标签换资产接口去请求 */
    resetToQuery() {
      resetAssetQuery(this.crud, ['fv7', 'fv6', 'fv4OrTitle'], this.labelConfig);
      clearTagAssetToQuery(this.crud, this.labelConfig);
      this.crud.toQuery();
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
