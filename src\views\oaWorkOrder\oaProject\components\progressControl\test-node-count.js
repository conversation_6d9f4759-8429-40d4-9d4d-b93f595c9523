// 测试节点数量统计功能
const testData = [
  {
    id: 1,
    name: '项目根目录',
    type: 'catalog',
    children: [
      {
        id: 2,
        name: '施工区域A',
        type: 'catalog',
        children: [
          {
            id: 3,
            name: '监测点A1',
            type: 'point',
            longitude: '116.816054',
            latitude: '40.154257',
            description: '施工区域A的第一个监测点'
          },
          {
            id: 4,
            name: '监测点A2',
            type: 'point',
            longitude: '116.825623',
            latitude: '40.110006',
            description: '施工区域A的第二个监测点'
          }
        ]
      },
      {
        id: 5,
        name: '施工区域B',
        type: 'catalog',
        children: [
          {
            id: 6,
            name: '监测点B1',
            type: 'point',
            longitude: '116.830000',
            latitude: '40.120000',
            description: '施工区域B的监测点'
          },
          {
            id: 7,
            name: '里程碑1',
            type: 'milestone',
            description: '这是一个里程碑，不是点位'
          }
        ]
      },
      {
        id: 8,
        name: '空目录',
        type: 'catalog',
        children: []
      },
      {
        id: 9,
        name: '混合目录',
        type: 'catalog',
        children: [
          {
            id: 10,
            name: '直接节点1',
            type: 'point',
            longitude: '116.840000',
            latitude: '40.130000'
          },
          {
            id: 11,
            name: '子目录',
            type: 'catalog',
            children: [
              {
                id: 12,
                name: '深层节点',
                type: 'point',
                longitude: '116.850000',
                latitude: '40.140000'
              }
            ]
          }
        ]
      }
    ]
  }
]

// 统计目录直接子级中的节点数量（不包括更深层级）
function countNodesInCatalog(catalogNode) {
  if (!catalogNode.children || !Array.isArray(catalogNode.children)) {
    return 0
  }

  // 只统计直接子级中的节点数量
  let count = 0
  catalogNode.children.forEach(child => {
    // 统计节点类型的数量（point、milestone等非catalog类型）
    if (child.type && child.type !== 'catalog') {
      count++
    }
  })

  return count
}

// 处理树形数据，为目录添加节点数量显示
function processTreeData(treeData) {
  const processNode = (node) => {
    // 创建节点副本，避免修改原始数据
    const processedNode = { ...node }

    if (processedNode.children && Array.isArray(processedNode.children)) {
      // 递归处理子节点
      processedNode.children = processedNode.children.map(child => processNode(child))

      // 如果当前节点是目录类型，统计其下的节点数量
      if (processedNode.type === 'catalog') {
        const nodeCount = countNodesInCatalog(processedNode)
        if (nodeCount > 0) {
          // 在目录名称后添加节点数量显示
          processedNode.name = `${processedNode.name} (${nodeCount})`
        }
      }
    }

    return processedNode
  }

  return treeData.map(node => processNode(node))
}

// 测试函数
console.log('原始测试数据:')
console.log(JSON.stringify(testData, null, 2))

console.log('\n处理后的数据:')
const processedData = processTreeData(testData)
console.log(JSON.stringify(processedData, null, 2))

// 验证结果
console.log('\n验证结果:')
console.log('项目根目录不应该显示数量（直接子级都是目录）:', processedData[0].name)
console.log('施工区域A应该显示 (2)（直接子级有2个节点）:', processedData[0].children[0].name)
console.log('施工区域B应该显示 (2)（直接子级有2个节点）:', processedData[0].children[1].name)
console.log('空目录不应该显示数量:', processedData[0].children[2].name)
console.log('混合目录应该显示 (1)（直接子级只有1个节点，深层节点不计算）:', processedData[0].children[3].name)
console.log('子目录应该显示 (1)（直接子级有1个节点）:', processedData[0].children[3].children[1].name)

export { testData, processTreeData, countNodesInCatalog }
