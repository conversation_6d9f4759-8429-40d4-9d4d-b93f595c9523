<template>
  <div>
    <el-dialog
      title="转交工单"
      :visible.sync="dialogVisible"
      width="30%"
      @close="closeVisible"
    >
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="60px" class="demo-ruleForm">
        <!-- <el-form-item label="节点" prop="node_id">
          <el-select v-model="ruleForm.node_id" placeholder="选择节点" size="small" style="width: 100%">
            <el-option v-for="(item, index) in nodeList" :key="index" :label="item.label" :value="item.id" />
          </el-select>
        </el-form-item> -->
        <el-form-item label="审批" prop="isCounterSign">
          <el-radio-group v-model="ruleForm.isCounterSign">
            <el-radio :label="false">或签</el-radio>
            <el-radio :label="true">会签</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="用户" prop="userId">
          <el-select v-model="ruleForm.userId" multiple filterable placeholder="选择用户" size="small" style="width: 100%">
            <el-option v-for="(item, index) in users" :key="index" :label="item.username" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="ruleForm.remarks" type="textarea" size="small" />
        </el-form-item>
        <el-form-item style="text-align: right">
          <el-button type="primary" @click="sureInversion('ruleForm')">转交</el-button>
          <el-button @click="dialogVisible = false">关闭</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { forwardWorkOrder } from '@/api/oaWorkOrder/workOrder';
import { getUser } from '@/api/system/user';
export default {
  name: 'ForwardOrde',
  data() {
    return {
      ruleForm: {
        id: '',
        // node_id: '',
        isCounterSign: false,
        userId: [],
        remarks: ''
      },
      // countSign
      rules: {
        // node_id: [
        //   { required: true, message: '请选择节点', trigger: 'change' }
        // ],
        userId: [
          { required: true, message: '请选择用户', trigger: 'change' }
        ]
      },
      users: [],
      nodeList: [],
      dialogVisible: false
    }
  },
  methods: {
    init(row) {
      console.log('row<===>', row)
      this.dialogVisible = true
      this.ruleForm.id = row.id
      this.nodeList = row.state
      // if (this.nodeList.length === 1) {
      //   this.ruleForm.node_id = this.nodeList[0].id
      // }
      getUser({ size: 999 }).then(res => {
        this.users = res.content
      })
    },
    // 确认----转交工单
    sureInversion(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          forwardWorkOrder(this.ruleForm).then(res => {
            this.$emit('reset');
            this.closeVisible()
          })
        }
      })
    },
    closeVisible() {
      this.$emit('reset');
      this.dialogVisible = false
      this.$refs['ruleForm'].resetFields();
      this.ruleForm.id = '';
    }
  }
};
</script>
