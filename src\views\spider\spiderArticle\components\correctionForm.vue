<template>
  <el-dialog
    v-dialog-drag
    :before-close="concelForm"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :title="title"
    :visible.sync="visible"
    append-to-body
    width="800px"
  >

    <div v-if="showFormData" v-loading="loading" class="text item">
      <el-form ref="elForm" :model="elForm" :rules="elRule" label-width="130px">
        <el-form-item v-for="item in currentField" :key="item.id" :label="`${item.label}:`" :prop="item.value">
          <el-input v-model="elForm[item.value]" :disabled="item.value == 'fv1'" style="width: 100%;" />
        </el-form-item>
      </el-form>
      <fm-generate-form
        :ref="'generateForm'"
        :data="formStruct"
        :preview="viewOrEdit"
        :remote="remoteFunc"
        :value="formData"
      />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="text" @click="concelForm">取消</el-button>
      <el-button
        :disabled="submitDisabled"
        type="primary"
        @click="submitAction"
      >
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import Vue from 'vue'
import { GenerateForm } from '@/components/VueFormMaking'
import 'form-making/dist/FormMaking.css'
import spiderArticleApi from '@/api/spider/spiderArticle'

Vue.component(GenerateForm.name, GenerateForm)
import { getToken } from '@/utils/auth';
import extendTpl from '@/api/system/extendTpl.js'

export default {
  name: 'CorrectionForm',
  components: {},

  props: {},
  data() {
    return {
      title: '更正/补充信息',
      visible: false,
      submitDisabled: false,
      elForm: {},
      elRule: {},
      loading: false,
      formStruct: {},
      processStructureValue: {},
      formData: {},
      remoteFunc: {
        funcGetToken(resolve) {
          resolve(getToken())
        }
      },
      viewOrEdit: false,
      currentField: [],
      showFormData: false
    }
  },
  methods: {
    initData(item, field) {
      this.elForm = JSON.parse(JSON.stringify(item));
      if (item.ft2) {
        this.formData = {
          custom: this.elForm?.ft2 || []
        }
      }
      this.currentField = field[item.fv3].fields;
      this.getProcessNodeList();
      this.visible = true;
    },
    concelForm() {
      this.visible = false;
      this.$emit('success');
    },
    submitAction() {
      this.$refs['elForm'].validate((valid) => {
        if (!valid) {
          return false;
        } else {
          this.checkModule().then(res => {
            if (!res.flag) {
              return false;
            } else {
              const subData = res.subData;
              const data = { ...this.elForm, ft2: subData }
              spiderArticleApi.edit(data).then(response => {
                this.concelForm();
                this.$notify({
                  title: '操作成功',
                  type: 'success',
                  duration: 2500
                })
              }).catch((e) => {
                console.log(e);
              })
            }
          })
        }
      })
    },

    async checkModule() {
      let subData = ''
      const p = await this.$refs['generateForm'].getData().then(values => {
        subData = JSON.stringify(values);
        return {
          flag: true,
          subData
        };
      }).catch(() => {
        return {
          flag: false
        };
      })
      return p;
    },
    // 获取模板
    getProcessNodeList() {
      this.loading = true;
      const { otherInfo } = this.$config['custom_key']
      const data = { id: otherInfo, enabled: 1 }
      extendTpl.get(data).then(res => {
        if (res && res.content && res.content.length) {
          this.processStructureValue = res.content[0];
          const FormStruct = JSON.parse(this.processStructureValue.formStruct);
          this.formStruct = FormStruct;
        }
        this.showFormData = true;
      }).finally(() => {
        this.loading = false;
      });
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
