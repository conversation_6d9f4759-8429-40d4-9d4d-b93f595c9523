/* eslint-disable */
const MultiMarker = function (cThis, marker) {
	var iconUrl = location.origin + '/mapIcon/';
	return {
		map: cThis.map,
		styles: {
			// 摄像头图标
			/** start */
			cameraB: new TMap.MarkerStyle({
				width: 10, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'lan.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 8, y: 5}
			}),
			cameraR: new TMap.MarkerStyle({
				width: 15, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'cameraR.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 8, y: 5}
			}),
			cameraH: new TMap.MarkerStyle({
				width: 15, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'cameraH.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 8, y: 5}
			}),
			cameraB1: new TMap.MarkerStyle({
				width: 10, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'lan.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 8, y: 5}
			}),
			cameraR1: new TMap.MarkerStyle({
				width: 15, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'cameraR.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 8, y: 5}
			}),
			cameraH1: new TMap.MarkerStyle({
				width: 15, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'cameraH.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 8, y: 5}
			}),

			cameraRiga: new TMap.MarkerStyle({
				width: 15, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'cameraR1.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 8, y: 5}
			}),
			cameraGao: new TMap.MarkerStyle({
				width: 15, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'cameraR2.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 8, y: 5}
			}),
			cameraRhona: new TMap.MarkerStyle({
				width: 15, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'cameraR3.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 8, y: 5}
			}),
			cameraRiga1: new TMap.MarkerStyle({
				width: 15, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'cameraR1.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 8, y: 5}
			}),
			cameraGao1: new TMap.MarkerStyle({
				width: 15, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'cameraR2.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 8, y: 5}
			}),
			cameraRhona1: new TMap.MarkerStyle({
				width: 15, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'cameraR3.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 8, y: 5}
			}),
			/** end */
			/** 黄闪灯 start */
			booksB: new TMap.MarkerStyle({
				width: 20, // 点标记样式宽度（像素）
				height: 20, // 点标记样式高度（像素）
				src: iconUrl + 'booksB.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 10, y: 10}
			}),
			booksR: new TMap.MarkerStyle({
				width: 20, // 点标记样式宽度（像素）
				height: 20, // 点标记样式高度（像素）
				src: iconUrl + 'booksR.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 10, y: 10}
			}),
			booksH: new TMap.MarkerStyle({
				width: 20, // 点标记样式宽度（像素）
				height: 20, // 点标记样式高度（像素）
				src: iconUrl + 'booksH.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 10, y: 10}
			}),
			booksB1: new TMap.MarkerStyle({
				width: 20, // 点标记样式宽度（像素）
				height: 20, // 点标记样式高度（像素）
				src: iconUrl + 'booksB.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 10, y: 10}
			}),
			booksR1: new TMap.MarkerStyle({
				width: 20, // 点标记样式宽度（像素）
				height: 20, // 点标记样式高度（像素）
				src: iconUrl + 'booksR.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 10, y: 10}
			}),
			booksH1: new TMap.MarkerStyle({
				width: 20, // 点标记样式宽度（像素）
				height: 20, // 点标记样式高度（像素）
				src: iconUrl + 'booksH.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 10, y: 10}
			}),

			booksJIgao: new TMap.MarkerStyle({
				width: 20, // 点标记样式宽度（像素）
				height: 20, // 点标记样式高度（像素）
				src: iconUrl + 'booksJIgao.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 10, y: 10}
			}),
			booksGao: new TMap.MarkerStyle({
				width: 20, // 点标记样式宽度（像素）
				height: 20, // 点标记样式高度（像素）
				src: iconUrl + 'booksGao.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 10, y: 10}
			}),
			booksZhong: new TMap.MarkerStyle({
				width: 20, // 点标记样式宽度（像素）
				height: 20, // 点标记样式高度（像素）
				src: iconUrl + 'booksZhong.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 10, y: 10}
			}),
			booksJIgao1: new TMap.MarkerStyle({
				width: 20, // 点标记样式宽度（像素）
				height: 20, // 点标记样式高度（像素）
				src: iconUrl + 'booksJIgao.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 10, y: 10}
			}),
			booksGao1: new TMap.MarkerStyle({
				width: 20, // 点标记样式宽度（像素）
				height: 20, // 点标记样式高度（像素）
				src: iconUrl + 'booksGao.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 10, y: 10}
			}),
			booksZhong1: new TMap.MarkerStyle({
				width: 20, // 点标记样式宽度（像素）
				height: 20, // 点标记样式高度（像素）
				src: iconUrl + 'booksZhong.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 10, y: 10}
			}),

			/** */

			/** end */
			// 创建一个styleId为"myStyle"的样式（styles的子属性名即为styleId）
			iconGreen: new TMap.MarkerStyle({
				width: 10, // 点标记样式宽度（像素）
				height: 30, // 点标记样式高度（像素）
				src: iconUrl + 'green.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 30}
			}),
			iconRed: new TMap.MarkerStyle({
				width: 10, // 点标记样式宽度（像素）
				height: 30, // 点标记样式高度（像素）
				src: iconUrl + 'red.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 30}
			}),
			iconYellow: new TMap.MarkerStyle({
				width: 10, // 点标记样式宽度（像素）
				height: 30, // 点标记样式高度（像素）
				src: iconUrl + 'yellow.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 30}
			}),

			iconGreen1: new TMap.MarkerStyle({
				width: 10, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'green1.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 10}
			}),
			iconRed1: new TMap.MarkerStyle({
				width: 10, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'red1.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 10}
			}),
			iconYellow1: new TMap.MarkerStyle({
				width: 10, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'yellow1.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 10}
			}),

			/** 红绿灯故障 start */
			errorjigao: new TMap.MarkerStyle({
				width: 10, // 点标记样式宽度（像素）
				height: 30, // 点标记样式高度（像素）
				src: iconUrl + 'errorjigao.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 30}
			}),
			errorgao: new TMap.MarkerStyle({
				width: 10, // 点标记样式宽度（像素）
				height: 30, // 点标记样式高度（像素）
				src: iconUrl + 'errorgao.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 30}
			}),
			errorzhong: new TMap.MarkerStyle({
				width: 10, // 点标记样式宽度（像素）
				height: 30, // 点标记样式高度（像素）
				src: iconUrl + 'errorzhong.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 30}
			}),

			errorjigao1: new TMap.MarkerStyle({
				width: 10, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'errorjigao1.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 10}
			}),
			errorgao1: new TMap.MarkerStyle({
				width: 10, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'errorgao1.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 10}
			}),
			errorzhong1: new TMap.MarkerStyle({
				width: 10, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'errorzhong1.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 10}
			}),
			/** end */
			eventGreen: new TMap.MarkerStyle({
				width: 20, // 点标记样式宽度（像素）
				height: 20, // 点标记样式高度（像素）
				src: iconUrl + 'eventGreen.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 10, y: 20}
			}),
			eventRes: new TMap.MarkerStyle({
				width: 20, // 点标记样式宽度（像素）
				height: 20, // 点标记样式高度（像素）
				src: iconUrl + 'eventRes.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 10, y: 20}
			}),
			eventYellow: new TMap.MarkerStyle({
				width: 20, // 点标记样式宽度（像素）
				height: 20, // 点标记样式高度（像素）
				src: iconUrl + 'eventYellow.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 10, y: 20}
			}),
			eventGreen1: new TMap.MarkerStyle({
				width: 20, // 点标记样式宽度（像素）
				height: 20, // 点标记样式高度（像素）
				src: iconUrl + 'eventGreen.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 10, y: 20}
			}),
			eventRes1: new TMap.MarkerStyle({
				width: 20, // 点标记样式宽度（像素）
				height: 20, // 点标记样式高度（像素）
				src: iconUrl + 'eventRes.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 10, y: 20}
			}),
			eventYellow1: new TMap.MarkerStyle({
				width: 20, // 点标记样式宽度（像素）
				height: 20, // 点标记样式高度（像素）
				src: iconUrl + 'eventYellow.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 10, y: 20}
			}),

			iconidet: new TMap.MarkerStyle({
				width: 30, // 点标记样式宽度（像素）
				height: 45, // 点标记样式高度（像素）
				// src: iconUrl + 'yellow.png', //图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 15, y: 45}
			}),

			//   雪亮
			xlAssetSuccess: new TMap.MarkerStyle({
				width: 30, // 点标记样式宽度（像素）
				height: 30, // 点标记样式高度（像素）
				src: iconUrl + 'xlAssetSuccess.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 10, y: 20}
			}),
			xlAssetError: new TMap.MarkerStyle({
				width: 30, // 点标记样式宽度（像素）
				height: 30, // 点标记样式高度（像素）
				src: iconUrl + 'xlAssetError.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 10, y: 20}
			}),
			xlAssetSuccess1: new TMap.MarkerStyle({
				width: 10, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'green1.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 10}
			}),
			xlAssetError1: new TMap.MarkerStyle({
				width: 10, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'red1.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 10}
			}),
			// 自动驾驶3.0项目
			autoPilot3Success: new TMap.MarkerStyle({
				width: 15, // 点标记样式宽度（像素）
				height: 15, // 点标记样式高度（像素）
				src: iconUrl + 'autoPilot3Success.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 10}
			}),
			autoPilot3Yellow: new TMap.MarkerStyle({
				width: 15, // 点标记样式宽度（像素）
				height: 15, // 点标记样式高度（像素）
				src: iconUrl + 'autoPilot3Yellow.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 10}
			}),
			autoPilot3Red: new TMap.MarkerStyle({
				width: 15, // 点标记样式宽度（像素）
				height: 15, // 点标记样式高度（像素）
				src: iconUrl + 'autoPilot3Red.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 10}
			}),
			autoPilot3Grey: new TMap.MarkerStyle({
				width: 15, // 点标记样式宽度（像素）
				height: 15, // 点标记样式高度（像素）
				src: iconUrl + 'autoPilot3Grey.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 10}
			}),
			autoPilot3Green: new TMap.MarkerStyle({
				width: 15, // 点标记样式宽度（像素）
				height: 15, // 点标记样式高度（像素）
				src: iconUrl + 'autoPilot3Green.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 10}
			}),
      autoPilot3Purple: new TMap.MarkerStyle({
				width: 15, // 点标记样式宽度（像素）
				height: 15, // 点标记样式高度（像素）
				src: iconUrl + 'autoPilot3Purple.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 10}
			}),
			autoPilot3Success1: new TMap.MarkerStyle({
				width: 10, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'autoPilot3Success.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 10}
			}),
			autoPilot3Yellow1: new TMap.MarkerStyle({
				width: 10, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'autoPilot3Yellow.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 10}
			}),
			autoPilot3Red1: new TMap.MarkerStyle({
				width: 10, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'autoPilot3Red.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 10}
			}),
			autoPilot3Grey1: new TMap.MarkerStyle({
				width: 10, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'autoPilot3Grey.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 10}
			}),
			autoPilot3Green1: new TMap.MarkerStyle({
				width: 10, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'autoPilot3Green.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 10}
			}),
      autoPilot3Purple1: new TMap.MarkerStyle({
				width: 10, // 点标记样式宽度（像素）
				height: 10, // 点标记样式高度（像素）
				src: iconUrl + 'autoPilot3Purple.png', // 图片路径
				// 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
				anchor: {x: 5, y: 10}
			})
		},
		geometries: marker || []
	}
}
export default MultiMarker;
