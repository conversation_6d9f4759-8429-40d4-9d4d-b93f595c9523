<template>
  <div class="app-container">
    <div class="head-container">
      <e-header ref="eHeaderRef" :permission="permission" />
      <crudOperation :permission="permission">
        <update-button
          v-if="bindId"
          slot="right"
          :bind-id="bindId"
          :enabled="[1]"
          :permission="permission"
        />
        <!--<el-button-->
        <!--  slot="right"-->
        <!--  v-permission="permission.add"-->
        <!--  class="filter-item"-->
        <!--  icon="el-icon-refresh-left"-->
        <!--  size="mini"-->
        <!--  type="warning"-->
        <!--  @click="uploadProject()"-->
        <!--&gt;上传-->
        <!--</el-button>-->
      </crudOperation>
    </div>
    <!-- 表格 -->
    <div class="body-box">
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="tableData"
        style="width: 100%;"
        @select="crud.selectChange"
        @select-all="crud.selectAllChange"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column :fixed="true" type="selection" width="55" />
        <af-table-column
          v-for="(item, index) in tableHeader"
          :key="item.prop"
          :align="item.align"
          :fit="item.fit"
          :fixed="item.fixed || false"
          :label="item.label"
          :prop="item.prop"
          :width="item.width"
        >
          <template slot-scope="scope">
            <template v-if="item.prop == 'filePre'">
              <file-thumb
                :file-ext="scope.row.fileExt"
                :preview-list="[scope.row.url]"
                :url="scope.row.thUrl"
                @preView="preView"
              />

            </template>
            <template v-else-if="item.prop == 'pathList'">
              <p v-for="(path,pathIndex) in scope.row[item.prop]" :key="`${index}-${pathIndex}`">
                {{ path }}
              </p>
            </template>
            <template v-else-if="item.prop == 'fv18'">
              <span style="cursor: pointer;color: #2476F8" @click="goDetail(scope.row)">
                {{ scope.row[item.prop] }}
              </span>
            </template>
            <span v-else>{{ scope.row[item.prop] }}</span>
          </template>
        </af-table-column>
        <!--   编辑与删除   -->
        <el-table-column
          align="center"
          fixed="right"
          label="操作"
          width="160"
        >
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
            <el-button
              size="mini"
              type="warning"
              @click="download(scope.row)"
            >下载
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>

    <!--<upload-file ref="uploadRef" @success="successUpload" />-->
  </div>
</template>

<script>
import oaDocument from '@/api/oaWorkOrder/oaDocument';
import eHeader from './module/header';
import CRUD, { form, presenter } from '@crud/crud';
import crudOperation from '@crud/CRUD.operation';
import pagination from '@crud/Pagination';
import udOperation from '@crud/UD.operation';
import { downloadUrl, bytesToMBRounded, isImage } from '@/utils/index';
import { mapGetters } from 'vuex'
// import uploadFile from '@/views/oaWorkOrder/oaProject/components/uploadFile';
import updateButton from '@/components/UpdateButton/index'
import oaPmTree from '@/api/oaWorkOrder/oaPmTree'

const defaultForm = { id: null }
export default {
  name: 'Document',
  components: { udOperation, eHeader, crudOperation, pagination, updateButton },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: false,
      title: '文档',
      url: 'api/oaDocument/small',
      sort: ['createTime,desc'],
      query: { enabled: 1, createTime: [] },
      crudMethod: { ...oaDocument },
      optShow: {
        add: false,
        edit: false,
        del: true,
        download: false,
        reset: true,
        rightGroup: false
      }
    })
  },

  mixins: [presenter(), form(defaultForm)],
  data() {
    return {
      permission: {
        del: ['admin', 'oaDocument:del'],
        add: ['admin', 'oaDocument:add'],
        updateT: ['admin', 'oaDocument:updateFormStruct'],
        updateR: ['admin', 'oaDocument:updateRelation']
      },
      tableData: [],
      tableHeader: [],
      bindId: ''
    };
  },
  computed: {
    ...mapGetters([
      'preViewUrl'
    ])
  },
  watch: {
    'crud.data': {
      handler(val) {
        this.formatterTableHeader(val)
        this.formatterTableData(val)
      },
      deep: true
    }
  },
  async created() {
    const queryInfo = this.$route.query
    this.crud.query.createTime = queryInfo.createTime || []
    await this.$nextTick()
    if (queryInfo.createBy) {
      await this.$refs.eHeaderRef.remoteSelectUsers(queryInfo.createBy, queryInfo.type)
    }
    if (queryInfo.dataName) {
      await this.$refs.eHeaderRef.remoteSelectProject(queryInfo.dataName, queryInfo.type)
    }
    this.crud.toQuery()
  },
  methods: {
    [CRUD.HOOK.beforeRefresh]() {
      console.log(this.$route.name, '<===>', 'this.$router.name')
      const { id } = this.$route.query
      this.bindId = id;
    },
    download(row) {
      downloadUrl(row.url, row.fileName)
    },
    preView(url) {
      window.open(`${this.preViewUrl}/onlinePreview?url=${encodeURIComponent(btoa(url))}`);
    },
    async goDetail(data) {
      const { fv18 } = data;
      const { otherInfo, bindId } = this.$config.projects_keys
      const projectList = await this.getProjectByName({ name: fv18, bindId })
      let projectInfo = {}
      let id = ''
      if (projectList && projectList.length > 0) {
        projectInfo = projectList[0]
        id = projectInfo.id;
      }
      if (id) {
        this.$router.push({
          name: 'OaPproject',
          query: { projectId: id, id: String(bindId), docId: otherInfo, name: fv18 }
        })
      }
    },
    async getProjectByName({ name, bindId }) {
      const params = {
        fv1: '项目',
        enabled: 1,
        name,
        bindId,
        page: 0,
        size: 99
      };
      const res = await oaPmTree.getPmTreeSmall(params);
      return res.content;
    },
    /**
		 * 格式化表格数据
		 * @param {Array} val - 表格数据数组
		 */
    formatterTableData(val) {
      if (!val && !val.length) {
        this.tableData = [];
        return
      }
      const mapItem = (item) => {
        const baseJson = {
          id: item.id,
          fv18: item.fv18,
          createBy: item.createBy,
          createTime: item.createTime,
          pmTreeName: item.transMap.pmTreeName
        };
        const ft1Data = (item.ft1 && JSON.parse(item.ft1)) || {};
        const fileData = ft1Data.response || ft1Data.raw.response || {};
        // const pathList = [...item.pathList, item.transMap.pmTreeName]
        const pathList = [item.transMap.pmTreeName]

        return {
          ...baseJson,
          pathList: pathList.reverse(),
          ft1: ft1Data,
          ...(fileData && {
            fileName: fileData.originalFilename,
            fileExt: fileData.ext,
            fileCreateBy: fileData.createBy,
            url: fileData.url,
            thUrl: isImage(fileData.ext) ? `${fileData.url}?x-oss-process=image/resize,h_100,m_lfit` : '',
            size: `${bytesToMBRounded(fileData.size)}M`
          })
        };
      };
      this.tableData = val.map(mapItem);
      console.log(this.tableData, '<===>', 'this.tableData')
    },
    /**
		 * 格式化表头
		 * @param {Array} val - 表格数据数组
		 */
    formatterTableHeader(val) {
      const tableHeader = [
        { prop: 'fv18', label: '项目名称', align: 'left', fixed: 'left' },
        // { prop: 'fileExt', label: '后缀', align: 'left' },
        { prop: 'pathList', label: '目录', align: 'left' },
        { prop: 'fileName', label: '文档名称', align: 'left' },
        { prop: 'filePre', label: '预览', align: 'center', width: '100', fit: false },
        { prop: 'size', label: '文档大小', align: 'right', width: '100', fit: false },
        { prop: 'fileCreateBy', label: '上传人', align: 'center', width: '100', fit: false },
        { prop: 'createTime', label: '上传时间', width: '150', fit: false }
      ]
      const otherHeader = [];
      this.tableHeader = [...tableHeader, ...otherHeader];
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
