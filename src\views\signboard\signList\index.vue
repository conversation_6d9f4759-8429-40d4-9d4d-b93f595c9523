<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input v-model="query.title" clearable size="small" placeholder="输入部件名称搜索" style="width: 200px;" class="filter-item" />
        <el-select
          v-model="query.status"
          clearable
          size="small"
          placeholder="请选择巡检结果"
          class="filter-item"
          style="width: 200px"
        >
          <el-option
            v-for="item in dict.inspects_results"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-input v-model="query.ft3" clearable size="small" placeholder="输入情况描述搜索" style="width: 200px;" class="filter-item" />
        <!-- <date-range-picker v-model="query.createTime" class="date-item" /> -->
        <rrOperation>
          <el-button
            slot="right"
            class="filter-item"
            type="primary"
            icon="el-icon-eleme"
            size="mini"
            @click="preView"
          >
            预览
          </el-button>
          <el-button
            slot="right"
            class="filter-item"
            type="primary"
            icon="el-icon-download"
            size="mini"
            @click="downLoad"
          >
            导出
          </el-button>
        </rrOperation>
      </div>
      <crudOperation :permission="permission">
        <el-button
          slot="left"
          v-permission="permission.add"
          class="filter-item"
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="addTable"
        >
          新建
        </el-button>
        <el-button
          slot="left"
          v-permission="permission.upload"
          size="mini"
          icon="el-icon-plus"
          type="success"
          class="filter-item"
          @click="addFile"
        >上传</el-button>
        <update-button
          slot="left"
          :permission="permission"
          :bind-id="bindId"
          :enabled="[1]"
        />
      </crudOperation>
    </div>
    <el-table
      ref="table"
      v-loading="crud.loading"
      :data="tableData"
      row-key="id"
      @select="crud.selectChange"
      @select-all="crud.selectAllChange"
      @selection-change="crud.selectionChangeHandler"
    >
      <el-table-column
        v-for="item in tableHeader"
        :key="item.prop"
        :show-overflow-tooltip="true"
        :prop="item.prop"
        :label="item.label"
        :width="item.width || '150'"
        :align="item.align || 'center'"
      >
        <template slot-scope="scope">
          <template v-if="item.prop.indexOf('img') !='-1'">
            <template v-for="(item,index) in scope.row[item.prop]">
              <el-image
                v-if="index == 0"
                :key="item.id"
                :src="item.url"
                :preview-src-list="[item.url]"
                fit="contain"
                lazy
                class="el-avatar"
              >
                <div slot="error">
                  <i class="el-icon-document" />
                </div>
              </el-image>
            </template>

          </template>
          <template v-else-if="item.label =='路口名称'">
            <span class="table-colume-title" @click="detail(scope.row)">{{ scope.row[item.prop] }}</span>
          </template>
          <span v-else>{{ scope.row[item.prop] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="250" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button type="success" size="mini" @click="toInspects(scope.row)">巡检记录</el-button>
          <el-button v-permission="permission.edit" style="margin-left:0;" type="primary" size="mini" @click="editItem(scope.row)">编辑</el-button>
          <el-popconfirm
            confirm-button-text="确认"
            :hide-icon="true"
            cancel-button-text="取消"
            icon-color="red"
            title="确认要删除该条数据？"
            @confirm="deleteItem(scope.row)"
          >
            <el-button slot="reference" v-permission="permission.del" type="danger" size="mini">删除</el-button>
          </el-popconfirm>
          <!-- <el-button type="success" size="mini" @click="detail(scope.row)">查看</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination />
    <uploadExcel ref="uploadExcel" @getlist="crud.refresh()" />
  </div>
</template>

<script>
import uploadExcel from './components/uploadExcel'
import crudTable from '@/api/parts/assets'
import CRUD, { presenter, header, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import { getConfig } from '@/utils/getConfigData.js'
// import DateRangePicker from '@/components/DateRangePicker'
import { downloadUrl } from '@/utils/index'
import updateButton from '@/components/UpdateButton/index'
import { mapGetters } from 'vuex'
export default {
  name: 'SignList',
  components: { crudOperation, rrOperation, pagination, uploadExcel, updateButton },
  cruds() {
    return CRUD({
      title: '资产管理',
      url: 'api/omAsset/small',
      query: { enabled: 1 },
      crudMethod: { ...crudTable },
      optShow: {
        add: false, edit: false, del: false, download: false, reset: true
      }
    })
  },
  mixins: [presenter(), header(), crud()],
  dicts: ['inspects_results'],
  data() {
    return {
      nofistLoad: false,
      tableData: [],
      tableHeader: [],
      permission: {
        add: ['admin', 'omAsset:add'],
        edit: ['admin', 'omAsset:edit'],
        del: ['admin', 'omAsset:del'],
        upload: ['admin', 'omAsset:importXlsWithRule'],
        updateT: ['admin', 'omAsset:updateFormStruct'],
        updateR: ['admin', 'omAsset:updateRelation'],
        updateG: ['admin', 'omAsset:toRedisGeoIndex']
      },
      props: {
        value: 'id',
        label: 'label',
        emitPath: false
      },
      bindId: '',
      inspectBindID: ''
    }
  },
  computed: {
    ...mapGetters([
      'baseApi',
      'omAssetAPi'
    ])
  },
  watch: {
    'crud.data': {
      handler(val, oldVal) {
        if (val && val.length && val.length > 0) {
          const newVal = val;
          const publicfilters = ['text_', 'divider_', 'editor_', 'subform'];
          const uniqueFilters = ['file_'];
          const publicLable = ['栅格布局']
          const uniqueLable = ['序号']
          const filterLable = [...publicLable, ...uniqueLable]
          const filterTableHeader = [...publicfilters, ...uniqueFilters]
          const tableHeader = newVal[0].extend.tableHeader.filter((item) => {
            return filterLable.every(subItem => item.label !== subItem) && filterTableHeader.every(subItem => item.prop.indexOf(subItem) == '-1')
          });
          const otherTableHeader = [
            // { prop: 'createBy', label: '发布人员' },
            // { prop: 'createTime', label: '发布时间' }
          ];
          this.tableHeader = [...tableHeader, ...otherTableHeader];
          let tableData = [];
          tableData = newVal.map(item => {
            const json = item.extend.data;
            json.id = item.id;
            json.createBy = item.createBy;
            json.createTime = item.createTime;
            json.title = item.title;
            return json;
          });
          this.tableData = tableData;
        } else {
          this.tableHeader = [];
          this.tableData = [];
        }
      },
      deep: true
    }
  },
  created() {
    this.getConfigData();
  },
  activated() {
    if (this.nofistLoad) {
      this.crud.refresh();
    }
    this.nofistLoad = false
  },
  methods: {
    [CRUD.HOOK.beforeRefresh]() {
      this.crud.query.bindId = this.$route.query.id;
      this.crud.query.categoryId = this.$route.query.category;
      this.bindId = this.$route.query.id;
    },
    getTime() {
      if (this.query.createTime) {
        return this.query.createTime
      }
      const end = `${this.$dayJS().format('YYYY-MM-DD')} 12:00:00`
      const start = `${this.$dayJS().subtract(1, 'day').format('YYYY-MM-DD')} 12:00:00`
      return [start, end]
    },
    preView() {
      console.log(this.omAssetAPi);
      // const params = qs.stringify(data, { indices: false })
      // const params = `page=0&size=99999&createTime=${this.getTime()[0]}&createTime=${this.getTime()[1]}&status=${this.query.status || ''}&enabled=1&ft3=${this.query.ft3}&title=${this.query.title}`
      const params = `page=0&size=99999&enabled=1`
      const url = `${this.omAssetAPi}/asset/html?${params}`;
      window.open(url)
    },
    downLoad() {
      const params = `page=0&size=99999&enabled=1`
      // const params = `page=0&size=99999&createTime=${this.getTime()[0]}&createTime=${this.getTime()[1]}&status=${this.query.status || ''}&enabled=1&ft3=${this.query.ft3 || ''}&title=${this.query.title || ''}&fileName=路口信息`
      const url = `${this.omAssetAPi}/asset/xls?${params}&fileName=路口基础信息`;
      downloadUrl(url)
    },
    // 获取巡检记录的绑定ID
    getConfigData() {
      const data = {
        key: 'signboard_inspects_list'
      }
      getConfig(data).then(res => {
        this.inspectBindID = res.extend.data.bindId;
      })
    },
    // 点击巡检记录
    toInspects(row) {
      this.$router.push({ name: 'SignInspects', query: { id: this.inspectBindID, omAssetID: row.id, omAssetTitle: row.title, category: this.$route.query.category }})
    },
    addFile() {
      this.$refs.uploadExcel.init();
    },
    addTable() {
      this.$router.push({ name: 'SignCreate', query: { id: this.$route.query.id, category: this.$route.query.category }});
      this.nofistLoad = true;
    },
    editItem(row) {
      this.$router.push({ name: 'SignCreate', query: { id: this.$route.query.id, rowId: row.id, type: 'edit', category: this.$route.query.category }});
      this.nofistLoad = true;
    },
    detail(row) {
      this.$router.push({ name: 'SignCreate', query: { id: this.$route.query.id, rowId: row.id, type: 'see' }})
    },
    deleteItem(row) {
      crudTable.del([row.id]).then(response => {
        this.crud.refresh();
        this.$notify({
          title: '数据已删除',
          type: 'success',
          duration: 2500
        })
      })
    },
    getJson(item) {
      const json = {}
      item.forEach(i => {
        for (const name in i) {
          json[name] = i[name];
        }
      })
      return json;
    },
    getArr(item) {
      const arr = [];
      item.forEach(i => {
        i.forEach(j => {
          arr.push(j)
        })
      })
      return arr;
    }
  },
  [CRUD.HOOK.beforeRefresh]() {
    console.log(123);
    // console.log(this.$router.query.id);
    // crud.query.bindId = this.routerId
  }
}
</script>
<style  rel="stylesheet/scss" lang="scss" scoped>
.tableImg {
  width: 50px;height: 50px;
}
.table-colume-title {
  cursor: pointer;
  color: #2476F8;
}
</style>
