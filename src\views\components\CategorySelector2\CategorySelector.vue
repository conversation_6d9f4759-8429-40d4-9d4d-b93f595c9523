<template>
  <div class="category-selector">
    <div class="category-selector-main">
      <h3 v-if="topName" class="category-title">{{ topName }}：</h3>
      <div class="category-group">
        <el-checkbox-group v-model="localSelectedCategories" class="category-checkbox-group">
          <el-checkbox
            v-for="category in categoryList"
            :key="category.id"
            :label="category.id"
            class="category-checkbox"
            @change="handleCategoryCheckboxChange(category)"
          >
            <span
              :style="{ paddingLeft: '10px', color: category.chlildren && category.children.length && selectedCategory === category ? '#1890ff' : '' }"
              @click.prevent="handleCategoryClick(category)"
            >
              {{ category.name }}
              <template v-if="category.children && category.children.length">
                <i
                  :class="selectedCategory === category ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
                  :style="{ color: selectedCategory === category ? '#1890ff' : '' }"
                />
              </template>
            </span>
          </el-checkbox>
        </el-checkbox-group>
        <div v-if="selectedCategory && selectedCategory.children" class="subcategory-group">
          <el-checkbox-group
            v-model="localSelectedCategories"
            class="subcategory-checkbox-group category-checkbox-group2"
          >
            <el-checkbox
              v-for="subCategory in selectedCategory.children"
              :key="subCategory.id"
              :label="subCategory.id"
              class="subcategory-checkbox"
              @change="handleCategoryCheckboxChange(subCategory)"
            >
              <span
                :style="{ paddingLeft: '10px', color: subCategory.children && subCategory.children.length && selectedSubCategory === subCategory ? '#1890ff' : '' }"
                @click.prevent="handleSubCategoryClick(subCategory)"
              >
                {{ subCategory.name }}
                <template v-if="subCategory.children && subCategory.children.length">
                  <i
                    :class="selectedSubCategory === subCategory ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
                    :style="{ color: selectedSubCategory === subCategory ? '#1890ff' : '' }"
                  />
                </template>
              </span>
            </el-checkbox>
          </el-checkbox-group>
        </div>
        <div v-if="selectedSubCategory && selectedSubCategory.children" class="subcategory-group">
          <el-checkbox-group v-model="localSelectedCategories" class="subcategory-checkbox-group">
            <el-checkbox
              v-for="subSubCategory in selectedSubCategory.children"
              :key="subSubCategory.id"
              :label="subSubCategory.id"
              class="subcategory-checkbox"
            >
              {{ subSubCategory.name }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
    </div>
    <div v-if="localSelectedCategories.length" class="selected-tags">
      <el-tag
        v-for="item in selectedTags"
        :key="item.id"
        :closable="true"
        style="margin-right:8px;margin-bottom:6px;"
        @close="removeTag(item.id)"
      >
        {{ item.name }}
      </el-tag>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CategorySelector',
  props: {
    categoryList: {
      type: Array,
      required: true
    },
    categoryBaseInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      localSelectedCategories: [],
      selectedCategory: null,
      selectedSubCategory: null,
      topName: '',
      selectedTags: [],
      isResetting: false // 用于控制 watch 的标志位
    };
  },
  watch: {
    localSelectedCategories: {
      handler(newVal) {
        if (this.isResetting) {
          this.isResetting = false;
          return;
        }
        const [selectedIds, selectedObjects] = this.getLeafSelectedCategories(newVal);
        const newSelectedObjects = this.processCategoryBaseInfo(selectedObjects);
        // this.selectedTags = newSelectedObjects;//只展示返回的最后一级标签
        this.selectedTags = this.getAllSelectedTags(newVal, this.categoryList);
        this.$emit('update:selectedCategories', selectedIds, newSelectedObjects);
      },
      deep: true
    }
  },
  created() {
    this.initializeTopName();
  },
  methods: {
    initializeTopName() {
      try {
        if (this.categoryBaseInfo && this.categoryBaseInfo.otherInfo) {
          const otherInfo = JSON.parse(this.categoryBaseInfo.otherInfo);
          this.topName = otherInfo.topName || '';
        }
      } catch (error) {
        console.log('Error parsing categoryBaseInfo.otherInfo:', error);
        this.topName = '';
      }
    },
    async handleCategoryClick(category) {
      if (category.children && category.children.length) {
        if (this.selectedCategory !== category) {
          this.selectedCategory = category;
          if (!category.children) {
            category.children = await this.getCategoryList(category.id);
          }
          this.selectedSubCategory = null; // 清空子子分类
        } else {
          this.selectedCategory = null; // 关闭当前分类
          this.selectedSubCategory = null; // 清空子子分类
        }
      } else {
        // 当没有子集时，触发复选框选择
        this.toggleCategorySelection(category);
      }
    },
    async handleSubCategoryClick(subCategory) {
      if (subCategory.children && subCategory.children.length) {
        if (this.selectedSubCategory !== subCategory) {
          this.selectedSubCategory = subCategory;
          if (!subCategory.children) {
            subCategory.children = await this.getCategoryList(subCategory.id);
          }
        } else {
          this.selectedSubCategory = null; // 关闭当前子分类
        }
      } else {
        // 当没有子集时，触发复选框选择
        this.toggleCategorySelection(subCategory);
      }
    },
    toggleCategorySelection(category) {
      // 新增方法，切换复选框的选择状态
      const index = this.localSelectedCategories.indexOf(category.id);
      if (index === -1) {
        this.localSelectedCategories.push(category.id);
      } else {
        this.localSelectedCategories.splice(index, 1);
      }
    },
    handleCategoryCheckboxChange(category) {
    },
    getAllChildrenIds(category) {
      const childrenIds = category.children ? category.children.map(child => child.id) : [];
      const grandChildrenIds = category.children ? category.children.flatMap(child => this.getAllChildrenIds(child)) : [];
      return [...childrenIds, ...grandChildrenIds];
    },
    getLeafSelectedCategories(categories) {
      const leafSelectedCategories = [];
      const leafSelectedObjects = [];
      const categoryMap = this.buildCategoryMap(this.categoryList);

      const isLeafSelected = (category) => {
        if (!category.children || category.children.length === 0) {
          return categories.includes(category.id);
        }
        for (const child of category.children) {
          if (categories.includes(child.id)) {
            return false;
          }
        }
        return categories.includes(category.id);
      };

      categories.forEach(categoryId => {
        const category = categoryMap[categoryId];
        if (category && isLeafSelected(category)) {
          leafSelectedCategories.push(categoryId);
          leafSelectedObjects.push(category);
        }
      });

      return [leafSelectedCategories, leafSelectedObjects];
    },
    buildCategoryMap(categories) {
      const categoryMap = {};
      const traverse = (categoryList) => {
        categoryList.forEach(category => {
          categoryMap[category.id] = category;
          if (category.children) {
            traverse(category.children);
          }
        });
      };
      traverse(categories);
      return categoryMap;
    },
    processCategoryBaseInfo(selectedObjects) {
      let objArr = selectedObjects;
      try {
        if (this.categoryBaseInfo && this.categoryBaseInfo.otherInfo) {
          const otherInfo = JSON.parse(this.categoryBaseInfo.otherInfo);
          if (otherInfo.isTop) {
            const topCategoryId = this.categoryBaseInfo.categoryId;
            objArr = selectedObjects.map(obj => {
              return { ...obj, topCategoryId };
            });
          }
        }
      } catch (error) {
        console.log('Error processing category base info:', error);
      }
      return objArr;
    },
    async getCategoryList(categoryId) {
      // 这里你需要实现获取子分类的逻辑
      // 返回子分类列表
      return [];
    },
    getAllSelectedTags(selectedIds, categories) {
      const selectedTags = [];

      const findCategoryById = (id, categories) => {
        for (const category of categories) {
          if (category.id === id) {
            return category;
          }
          if (category.children) {
            const result = findCategoryById(id, category.children);
            if (result) {
              return result;
            }
          }
        }
        return null;
      };

      selectedIds.forEach(id => {
        const category = findCategoryById(id, categories);
        if (category) {
          selectedTags.push(category);
        }
      });

      return selectedTags;
    },
    // 现有内容省略
    removeTag(tagId) {
      this.localSelectedCategories = this.localSelectedCategories.filter(id => id !== tagId);
    },
    resetLocalSelectedCategories() {
      this.isResetting = true;
      this.localSelectedCategories = [];
      this.$emit('update:selectedCategories', [], [], true); // 传递第三个参数表示是主动清空
    }
  }
};
</script>

<style scoped>
.category-selector {
	padding: 10px 0 15px 0;
}

.category-selector-main {
	display: flex;
	flex-wrap: wrap;
}

.category-title {
	/* margin-bottom: 6px; */
	font-weight: bold;
	text-align: right;
	line-height: 21px;
	padding-right: 10px;
}

.category-group {
	flex: 1;
}

.subcategory-group {
	margin-top: 10px;
}

.category-checkbox-group, .category-checkbox-group2 {
	margin-bottom: 20px;
}

.subcategory-checkbox-group {
	display: flex;
	flex-wrap: wrap;
}

.selected-tags {
	margin-top: 5px;
}

::v-deep .category-checkbox-group .el-checkbox__label,
::v-deep .category-checkbox-group2 .el-checkbox__label {
	padding-left: 0 !important;
}

::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
	color: #606266;
}
</style>
