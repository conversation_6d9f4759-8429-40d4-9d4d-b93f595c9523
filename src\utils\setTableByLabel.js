/**
 * 根据 label 通过标签搜素设置表头样式
 * @param column
 * @param query
 * @param backgroundColor
 * @returns {string}
 */
export function tableRowClassName({ column, query, backgroundColor = '#1890FF' }) {
  const property = column.property;
  // 获取去重后的 top.values 数组

  let topValuesUnique = []
  if (query.tag) {
    topValuesUnique = Object.keys(query.tag);
  }
  // 定义默认的行样式
  const tableStyle = {
    backgroundColor: `${backgroundColor}`,
    color: '#ffffff'
  };

  // 检查是否有匹配的属性，并返回样式
  if (topValuesUnique.includes(property)) {
    return tableStyle;
  }

  // 默认返回空字符串
  return { color: '#364359' };
}

// 去重函数
export function getUniqueArray(arr) {
  let uniqueArr = [];
  if (arr && arr.length) {
    uniqueArr = arr.filter((item, index) => arr.indexOf(item) === index);
  }
  return uniqueArr
}

/**
 * 将标签搜索到的表头排序
 * @param arrayOfObjects
 * @param arrayToMatch
 * @param key
 * @returns {*}
 */
export function sortDynamicHeader(arrayOfObjects, arrayToMatch, key) {
  const result = arrayOfObjects.sort((a, b) => {
    const indexA = arrayToMatch.indexOf(a[key]);
    const indexB = arrayToMatch.indexOf(b[key]);

    if (indexA === -1) return 1; // a.prop 不在 arrayToMatch 中，排在后面
    if (indexB === -1) return -1; // b.prop 不在 arrayToMatch 中，排在后面
    return indexA - indexB; // 按 arrayToMatch 的顺序排序
  });
  return result;
}

export function closeTags(tags, tag) {

}

export function findTag(tagList, fv10, fv11) {
  return tagList.find(tag => tag.fv10 === fv10 && tag.fv11 === fv11);
}
