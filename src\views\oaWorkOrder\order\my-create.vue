<!--我创建的-->
<template>
  <div class="app-container">
    <el-card class="box-card">
      <el-form ref="listQuery" :model="listQuery" :inline="true">
        <WorkOrderSearch :genre="'my-create'" @handleSearch="handleSearch" @reset="reset" />
      </el-form>

      <el-table v-loading="loading" :data="ticketList" @selection-change="handleSelectionChange">
        <!-- <el-table-column type="selection" width="55" align="center" /> -->
        <!-- <el-table-column label="ID" prop="id" width="120" /> -->
        <el-table-column label="标题" prop="title" min-width="120" :show-overflow-tooltip="true" />
        <el-table-column label="流程" prop="process.name" :show-overflow-tooltip="true" />
        <el-table-column label="当前状态" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>
              {{ scope.row.state ? scope.row.state[0].label : '-' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="当前处理人" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>
              <template v-for="item in scope.row.current">
                {{ item }}
              </template>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="优先级" :show-overflow-tooltip="true" align="left">
          <template slot-scope="scope">
            <span v-if="scope.row.priority===2">
              <el-tag type="warning">紧急</el-tag>
            </span>
            <span v-else-if="scope.row.priority===3">
              <el-tag type="danger">非常紧急</el-tag>
            </span>
            <span v-else>
              <el-tag type="success">一般</el-tag>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="是否结束" :show-overflow-tooltip="true" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.isEnd===0" size="mini" type="success">否</el-tag>
            <el-tag v-else size="mini" type="danger">是</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="是否已撤销" :show-overflow-tooltip="true" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.isEnd===2" size="mini" type="danger">是</el-tag>
            <el-tag v-else size="mini" type="success">否</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="create_time">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="left" class-name="small-padding fixed-width" width="180">
          <template slot-scope="scope">
            <el-button
              v-permission="['admin','oaWorkOrderInfo:list']"
              size="mini"
              type="primary"
              @click="handleView(scope.row)"
            >查看</el-button>
            <el-button
              v-if="scope.row.isEnd!==2 && scope.row.isEnd===0"
              v-permission="['admin','oaWorkOrderInfo:cancel']"
              size="mini"
              type="danger"
              @click="cancelOrder(scope.row)"
            >撤销</el-button>
            <!--<el-button-->
            <!--v-permission="['admin','myCreate:reopen']"-->
            <!--size="mini"-->
            <!--type="text"-->
            <!--icon="el-icon-refresh-right"-->
            <!--@click="handleReopen(scope.row.id)"-->
            <!--&gt;重开</el-button>-->
            <!--<el-button-->
            <!--v-if="scope.row.isEnd===0"-->
            <!--v-permission="['admin','upcoming:urge']"-->
            <!--size="mini"-->
            <!--type="text"-->
            <!--icon="el-icon-alarm-clock"-->
            <!--@click="handleUrge(scope.row)"-->
            <!--&gt;催办</el-button>-->
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <Paging :page="pageInfo" @handleCurrentChange="getList()" />
    </el-card>
  </div>
</template>

<script>

import { cancelWorkOrder, getWorkOrderInfo } from '@/api/oaWorkOrder/workOrder';
import WorkOrderSearch from './components/search.vue'
import { mapGetters } from 'vuex'
export default {
  components: { WorkOrderSearch },
  data() {
    return {
      queryParams: {},
      total: 0,
      loading: false,
      ticketList: [],
      pageInfo: {
        size: 10,
        page: 1,
        total: 0
      },
      listQuery: {}
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  created() {
    this.getList();
  },
  methods: {
    reset() {
      this.listQuery = {};
      this.getList();
    },
    // 获取我创建的工单
    getList() {
      this.loading = true
      const data = {
        page: this.pageInfo.page - 1,
        size: this.pageInfo.size,
        sort: 'id,desc',
        creator: this.user.id,
        // title:
        enabled: true,
        ...this.listQuery
      }
      // this.listQuery.classify = 2
      getWorkOrderInfo(data).then(response => {
        this.ticketList = response.content
        this.pageInfo.total = response.totalElements
        this.loading = false
      })
    },
    handleSearch(val) {
      for (var k in val) {
        this.listQuery[k] = val[k]
      }
      this.getList()
    },
    handleView(row) {
      this.$router.push({ name: 'ProcessListHandle', query: { workOrderId: row.id, processId: row.process }})
    },
    // 撤销工单
    cancelOrder(row) {
      this.$confirm('确定要撤销这个工单吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'danger'
      }).then(() => {
        cancelWorkOrder({
          id: row.id
        }).then(response => {
          this.$message({
            type: 'success',
            message: '已撤销!'
          })
          this.getList();
        })
      })
    },
    // handleReopen(id) {
    //   this.$confirm('根据此工单新建一个新的工单?', '提示', {
    //     confirmButtonText: '确定',
    //     cancelButtonText: '取消',
    //     type: 'info'
    //   }).then(() => {
    //     reopenWorkOrder(id).then(res => {
    //       this.getList()
    //       this.$message({
    //         type: 'success',
    //         message: '成功!'
    //       })
    //     })
    //   })
    // },
    handleSelectionChange() {}
    // handleUrge(row) {
    //   this.$confirm('<span style="font-size:15px ">对此工单处理人进行催办通知提醒, 是否继续?</span><br><span style="color: #c33; font-size: 10px">注意：十分钟内只能催办一次。</span>', '催办', {
    //     dangerouslyUseHTMLString: true,
    //     confirmButtonText: '确定',
    //     cancelButtonText: '取消',
    //     type: 'warning'
    //   }).then(() => {
    //     urgeWorkOrder({
    //       workOrderId: row.id
    //     }).then(response => {
    //       this.$message({
    //         type: 'success',
    //         message: '已进行催办通知!'
    //       })
    //     })
    //   }).catch(() => {
    //     this.$message({
    //       type: 'info',
    //       message: '已取消'
    //     })
    //   })
    // }
  }
}
</script>

<style scoped>

</style>
