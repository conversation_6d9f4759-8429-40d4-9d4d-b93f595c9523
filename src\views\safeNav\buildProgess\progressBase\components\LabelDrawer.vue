<template>
  <div id="drawer-container" ref="drawerContainer">
    <el-drawer
      ref="drawer"
      :title="drawerOptions.title"
      :visible.sync="labelVisible"
      :direction="drawerOptions.direction"
      :size="drawerOptions.size"
      :append-to-body="false"
      :modal="false"
      :wrapper-closable="true"
    >
      <MultiCategorySelector
        ref="multiCategorySelector"
        :categories="categoryData"
        @update:selectedCategories="updateSelectedCategories"
      />
    </el-drawer>
  </div>
</template>

<script>
import { getConfig } from '@/utils/getConfigData.js'
import MultiCategorySelector from '@/views/components/CategorySelector/MultiCategorySelector.vue';
import {
  getCategoryList,
  checkAndTagToQuery,
  formatRequestJson
} from '../utils/commonFun';

export default {
  name: 'LabelDrawer',
  components: { MultiCategorySelector },
  props: {
    drawerOptions: {
      type: Object,
      default: () => ({
        title: '标签选择',
        direction: 'ltr',
        size: '35%'
      })
    },
    crud: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      selectedCategories: [],
      categoryData: [],
      labelVisible: false,
      labelConfig: {}
    };
  },
  async created() {
    this.labelConfig = await this.getConfigData();
    this.getOption();
  },
  methods: {
    async getConfigData() {
      try {
        const res = await getConfig({
          key: 'build_progess'
        });
        const arr = res.extend.data.configInfo;
        const currentRouteName = this.$route.name;
        const configData = arr.find(item => item.key === currentRouteName);
        const labelConfig = { ...configData, ...JSON.parse(configData.otherInfo) };
        return labelConfig;
      } catch (err) {
        console.error(err);
        throw err; // 重新抛出错误，以便在调用方进行处理
      }
    },
    async getOption() {
      const categoryBaseInfo = this.labelConfig;
      const { bindId, categoryId } = categoryBaseInfo;
      const categoryList = await getCategoryList(categoryId, bindId);
      this.categoryData = [
        {
          categoryList,
          categoryBaseInfo
        }
      ];
    },
    updateSelectedCategories(index, newSelectedCategories, newSelectedCategoriesObj, isFromClear) {
      if (isFromClear) return;// 如果是主动关闭抽屉，不去重新请求
      this.selectedCategories = newSelectedCategoriesObj;
      const requestJson = formatRequestJson(this.selectedCategories, this.labelConfig);
      this.crud.query = { ...this.crud.query, ...requestJson };
      checkAndTagToQuery(this.crud, this.selectedCategories, ['fv7', 'fv6', 'fv4OrTitle']); // 调用检查和更新API的方法
    },
    disableBodyScroll() {
      document.body.style.overflow = 'hidden';
    },
    enableBodyScroll() {
      document.body.style.overflow = '';
    },
    closeDrawer() {
      // clearTagAssetToQuery(this.crud);
      // this.$refs.multiCategorySelector.resetAllCategories();
    }

  }
};
</script>

<style scoped>
#drawer-container {
  position: absolute;
  left: 0;
  top: 0;
}

 ::v-deep .el-drawer__header {
  margin-bottom: 10px !important;
}
 ::v-deep .el-drawer__body {
  padding: 0 20px !important;
}
 ::v-deep .el-drawer__header {
  font-size: 17px !important;
  font-weight: bold !important;
  color: #000 !important;
}
</style>
