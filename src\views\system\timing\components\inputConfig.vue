<template>
  <div class="app-container">
    <el-form ref="form" :model="formData" label-width="180px" :rules="rules">
      <add-input :arr-value="formData.urls" label-info="目标URL/要爬取的列表" tip-info="目标URL/要爬取的列表" />
    </el-form>
  </div>
</template>

<script>
import addInput from '@/views/system/timing/common/addInput'
import { delArrInvalid } from '@/views/system/timing/utils/formate'
export default {
  name: 'InputConfig',
  components: { addInput },
  data() {
    return {
      formData: {
        'urls': []
      },
      rules: {
        // urls: [
        //   { required: true, message: '目标URL/要爬取的列表不能为空', trigger: 'blur' }
        // ]
      }
    };
  },

  mounted() {

  },

  methods: {
    getFormData() {
      return {
        input: delArrInvalid(this.formData)
      };
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
