<template>
  <div
    v-loading="pageLoading"
    class="app-container oa-pm"
    element-loading-spinner="el-icon-loading"
    element-loading-text="更新中"
  >
    <!--工具栏-->
    <div class="head-container">
      <search-header :dict="dict" :permission="permission" />
      <crudOperation :permission="permission">
        <!--一系列更新按钮-->
        <custom-action-button
          slot="right"
          :bind-id="bindId"
          :current-crud="crud"
          :permission="permission"
          @importProject="importProject"
          @successUpdateInfo="successUpdateInfo"
        />
      </crudOperation>
    </div>
    <!--表格渲染-->
    <div class="project-table-content">
      <Hamburger
        :is-active="operateShow"
        class="hamburger-container"
        @toggleClick="toggleSideBar"
      />
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="tableData"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        class="table-hover"
        lazy
        row-key="id"
        @select="crud.selectChange"
        @select-all="crud.selectAllChange"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column
          v-for="(header, initIndex) in tableHeaders"
          :key="header.label+initIndex"
          :align="header.align"
          :fixed="header.fixed"
          :label="header.label"
          :prop="header.prop"
          :show-overflow-tooltip="true"
          :sortable="header.sortable || false"
          :width="header.width"
        >
          <template slot-scope="scope">
            <see-cell
              :current-scope="scope"
              :header="header"
              :permission="permission"
            />
          </template>
        </el-table-column>
        <transition name="fade">

          <el-table-column
            v-if="operateShow"
            fixed="right"
            label="操作"
            width="180"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
                msg="确定删除吗,此操作不能撤销！"
              />
            </template>
          </el-table-column>
        </transition>
      </el-table>

      <!--分页组件-->
      <pagination />
    </div>
    <!--上传图纸-->
    <upload-draw ref="uploadDrawRef" @success="crud.toQuery()" />
  </div>
</template>

<script>
import crudTable from '@/api/safeNav/omAssetAffiliated'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination';
import SeeCell from './components/SeeCell';
import CustomActionButton from './components/CustomActionButton';
import SearchHeader from './components/SearchHeader';
import { mapGetters } from 'vuex'
import Hamburger from '@/components/Hamburger'
import UploadDraw from './components/UploadDraw.vue';
import {
  formatterTableData,
  formatterTableHeader,
  toggleSideBarFun
} from './utils/commonFun'
import {
  permission
} from './utils/field'
// crud交由presenter持有
const defaultForm = {
  id: null
}
export default {
  name: 'RectifyLedger',
  components: {
    SearchHeader, crudOperation, pagination,
    CustomActionButton,
    SeeCell,
    UploadDraw,
    udOperation,
    Hamburger
  },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: false,
      title: '路口图纸',
      url: 'api/omAssetAffiliated/small',
      sort: [],
      query: { enabled: 1, status: '', fv4OrTitle: '', fv2With69: [], fv1: '' },
      crudMethod: { ...crudTable },
      optShow: {
        add: false,
        edit: false,
        del: false,
        download: false,
        reset: true,
        rightGroup: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: [],
  data() {
    return {
      pageLoading: false,
      operateShow: false, // 操作列是否显示
      tableData: [],
      permission,
      bindId: '',
      tableHeaders: []
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  watch: {
    'crud.data': {
      async handler(val, oldVal) {
        this.tableHeaders = formatterTableHeader(val)
        this.tableData = formatterTableData(val)
        await this.$nextTick()
        this.$refs.table.doLayout()
      },
      deep: true
    }
  },
  async created() {
    const { bindId } = this.$config.corss_draw_key
    this.bindId = bindId;
    this.setAssetConfig()
    this.crud.toQuery();
  },
  methods: {
    // 成功更新之后的操作
    successUpdateInfo(type) {
      if (type == 1) {
        this.pageLoading = true
      } else if (type == 2) {
        this.pageLoading = false
      } else {
        this.pageLoading = false
        this.crud.toQuery();
      }
    },
    // 导入数据
    importProject() {
      this.$refs.uploadDrawRef.init();
    },

    [CRUD.HOOK.beforeRefresh]() {
      this.setAssetConfig()
    },
    [CRUD.HOOK.beforeToAdd]() {
      const query = {
        name: 'CrossDrawingForm'
      }
      this.$router.push(query)
    },
    [CRUD.HOOK.beforeToEdit](crud, form) {
      const { id } = form
      this.$router.push({ name: 'CrossDrawingForm', query: { id }})
    },
    // 点击操作列头
    async toggleSideBar() {
      await toggleSideBarFun(this)
    },
    //   设置配置项
    setAssetConfig() {
      const { bindId } = this.$config.corss_draw_key
      this.crud.query.bindId = bindId
    }
  }
}
</script>

<style>
.table-hover .el-table__body tr.hover-row.current-row > td.el-table__cell, .el-table__body tr.hover-row.el-table__row--striped.current-row > td.el-table__cell, .el-table__body tr.hover-row.el-table__row--striped > td.el-table__cell, .el-table__body tr.hover-row > td.el-table__cell {
	background-color: transparent !important;
}
</style>
<style lang="scss" rel="stylesheet/scss" scoped>

//操作按钮相关
.operate-button {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.project-table-content {
	position: relative;

	.hamburger-container {
		position: absolute;
		right: 0;
		top: 0;
		z-index: 8;
		line-height: 40px;
		cursor: pointer;
		transition: background .3s;
		-webkit-tap-highlight-color: transparent;
		background: rgba(0, 0, 0, .09);

		&:hover {
			background: rgba(0, 0, 0, .19)
		}
	}
}

::v-deep .el-input-number .el-input__inner {
	text-align: left;
}

::v-deep .vue-treeselect__control, ::v-deep .vue-treeselect__placeholder, ::v-deep .vue-treeselect__single-value {
	height: 30px;
	line-height: 30px;
}

.table-fixed {
	//max-height: 760px;
	// 表格合计样式
}

</style>
