import axios from 'axios'
import router from '@/router/routers'
import { Notification } from 'element-ui'
import store from '../store'
import { getToken } from '@/utils/auth'
import Config from '@/settings'
import Cookies from 'js-cookie'
import { delCode } from './delCode'
import { addSource, removeSource } from './requestCancel'
// 创建axios实例
const service = axios.create({
  // baseURL: process.env.NODE_ENV === 'production' ? process.env.VUE_APP_BASE_API : 'https://routine.fatoan.com', // api 的 base_url
  baseURL: process.env.NODE_ENV === 'production' ? process.env.VUE_APP_BASE_API : '/', // api 的 base_url
  timeout: Config.timeout // 请求超时时间
})
// request拦截器
service.interceptors.request.use(
  config => {
    if (getToken()) {
      config.headers['Authorization'] = getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    // 使用另外一个接口
    if (config.url.indexOf('spider') > -1) {
      config.url = config.url.slice(6)
      config.baseURL = process.env.VUE_APP_BASE_API2;
    }

    // 针对需要取消接口
    if (config?.isCancel) {
      const uniqueKey = config.cancelKey || `${config.url}-${config.method}`
      config.cancelToken = addSource(uniqueKey);
    }

    config.headers['Content-Type'] = 'application/json'
    return config
  },
  error => {
    Promise.reject(error)
  }
)

// response 拦截器
service.interceptors.response.use(
  response => {
    const { config } = response
    if (config?.isCancel) {
      const uniqueKey = config.cancelKey || `${config.url}-${config.method}`
      removeSource(uniqueKey); // 请求成功后移除取消令牌
    }
    return response.data
  },
  error => {
    if (axios.isCancel(error)) {
      console.log('Request canceled:', error.message);
      return Promise.reject(error);
    }
    // 兼容blob下载出错json提示
    if (error.response.data instanceof Blob && error.response.data.type.toLowerCase().indexOf('json') !== -1) {
      const reader = new FileReader()
      reader.readAsText(error.response.data, 'utf-8')
      reader.onload = function(e) {
        const errorMsg = JSON.parse(reader.result).message
        Notification.error({
          title: errorMsg,
          duration: 5000
        })
      }
    } else {
      let code = 0
      try {
        code = error.response.data.status
      } catch (e) {
        if (error.toString().indexOf('Error: timeout') !== -1) {
          Notification.error({
            title: '网络请求超时',
            duration: 5000
          })
          return Promise.reject(error)
        }
      }
      if (code) {
        if (code === 401) {
          console.log(error);
          store.dispatch('LogOut').then(() => {
            // 用户登录界面提示
            Cookies.set('point', 401)
            location.reload()
          })
        } else if (code === 403) {
          router.push({ path: '/401' })
        } else if (code === 406) {
          const errorMsg = error.response.data.message;
          Notification.error({
            title: errorMsg,
            duration: 3000,
            onClose() {
              delCode()
            }
          })
        } else {
          const errorMsg = error.response.data.message;
          if (errorMsg !== undefined) {
            Notification.error({
              title: errorMsg,
              duration: 5000,
              onClose() {
                delCode()
              }
            })
          } else {
            Notification.error({
              title: '接口请求失败',
              duration: 5000,
              onClose() {
                delCode()
              }
            })
          }
        }
      } else {
        Notification.error({
          title: '接口请求失败',
          duration: 5000,
          onClose() {
            delCode()
          }
        })
      }
    }
    return Promise.reject(error)
  }
)
// function delCode() {
//   if (window.location.search) {
//     const search = window.location.search;
//     const code = search.substring(search.indexOf('code=') + 5, search.indexOf('&state='));
//     if (code) {
//       window.location.href = window.location.href.replace(`?code=${code}&state=`, '')
//     }
//   }
// }
export default service
