<template>
  <div>
    <div v-show="isShowOptions.formShow" class="search-box">
      <el-select
        v-model="tagTop"
        class="filter-item"
        placeholder="请选择"
        size="small"
        style="width: 200px"
        @change="changeTagTop"
      >
        <el-option
          v-for="item in tagTopList"
          :key="item.id"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>

    <div class="map-tip">
      <ul>
        <li v-for="item in networkMapTip" :key="item.id">
          <img :src="`/mapIcon/${item.color}.png`" alt="">
          <span> {{ item.tipTitle }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
/* eslint-disable */
import { getOmAssetAutoPilot } from '@/api/parts/assets';
import MultiMarker from '../SupportingFile/MultiMarker';
import {
	getCode,
	getPosition,
	setConfig, autoPilotsContent, findMaxSortColor, updateStatus, getFinalColor
} from '../SupportingFile/commonFun'
import { getBulidenConfig } from "@/utils/getConfigData";
import { findFilterItem } from "@/api/safeNav/omAssetTag";

const currentConfig = setConfig()
/** 给信息框的点击事件添加方法 */
var cthis = null;
var cdata = null;
var mapClick = null;

function formatAssets(categoryId, obj) {
	for (const key in obj) {
		if (obj[key].categoryId == categoryId) {
			return obj[key];
		}
	}
	return null; // 如果未找到则返回null
}

window.infoClick = function (e) {
	const pointData = formatAssets(cdata.geometry.properties.type, currentConfig);
	const properties = cdata.geometry.properties;
	const position = cdata.geometry.position;

	function navigateTo(name, query) {
		cthis.$router.push({name, query});
	}

	if (e === 'xunjian') {
		navigateTo(pointData.inspectsName, {
			id: pointData.inspectsBindId,
			category: properties.type,
			omAssetID: properties.backstageId,
			omAssetTitle: properties.title
		});
	} else if (e === 'details') {
		navigateTo(pointData.name, {
			id: pointData.bindId,
			rowId: properties.backstageId,
			type: 'see'
		});
	} else if (e === 'edit') {
		const mapClick = document.getElementById('mapContainer');
		cthis.isShowOptions.isForm = true;
		cthis.isShowOptions.formShow = false;
		mapClick.style.cursor = 'crosshair';
		cthis.markerLayer.setGeometries([]);
		cthis.id = properties.backstageId + '';
		cthis.name = properties.title + '';
		cthis.editMarker[0] = {
			id: 1000000, //点标记唯一标识
			styleId: 'iconidet', //指定样式id
			position: new TMap.LatLng(position.lat, position.lng), //点标记坐标位置
			properties: {
				title: properties.title,
				type: properties.type,
				backstageId: properties.backstageId
			}
		};
		cthis.lat = position.lat;
		cthis.lng = position.lng;
		cthis.markerLayer.setGeometries(cthis.editMarker);
		cthis.map.on('click', cthis.onMapClick);
	} else if (e === 'xlAsset') {
		navigateTo(pointData.name, {
			bindId: pointData.bindId,
			categoryId: pointData.categoryId,
			fv4: properties.fv4,
		});
	} else if (e === 'xlAssetInspect') {
		navigateTo(pointData.inspectsName, {
			omAssetID: properties.id,
			omAssetTitle: properties.title,
			fv4: properties.fv4,
			type: 1
		});
	} else if (e === 'autoPilots') {
		navigateTo(pointData.name, {
			fv4: properties.fv4,
		});
	}
	cthis.infoWindow.close();
};
export default {
	name: 'MarkerList',
	components: {},
	props: {
		map: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	dicts: ['networt_map'],
	data() {
		return {
			isLoadAll: true,
			markerLayer: null,
			markerLabel: null,
			infoWindow: null,
			query: {
				title: '',
				category: ""
			},
			markList: [],
			labelList: [],
			assetsList: [],
			page: 0,
			//修改位置
			lat: '',
			lng: '',
			id: '',
			name: '',
			editMarker: [],
			isShowOptions: {
				isForm: false,
				formShow: true
			},
			searchTitlePlaceholder: '输入路口名称',
			searchTitle: 'title',
			isFist: true,
			isSearch: true,
			currentConfig,
			showTgaDisabled: false,
			tagTopList: [],
			tagTop: '',
			labelConfig: {},
			tagTopKey: '',
			networkMapTip: {},
		};
	},
	watch: {
		map(val, old) {
			this.initData();
		},
	},
	created() {
		// 为了防止本地联调时多次调用，特意加上判断，如果需要加载全部数据(点位)，请注释下面这行代码
		// this.isLoadAll = isOnline()

	},
	mounted() {
		cthis = this;
	},
	beforeDestroy() {
		this.markList = [];
		this.markerLayer = null;
		this.markerLabel = null;
		this.infoWindow = null;
		this.lat = '';
		this.lng = '';
		this.id = '';
		this.name = '';
		cthis = null;
		cdata = null;
		mapClick = null;
	},
	methods: {
		/** 地图初始化操作 */
		initData() {
			// 初始化marker
			this.setMaker();
			// 信息弹窗
			this.infoWin();
			// 地图放大缩小
			this.mapZoomChange();
			// 初始化label
			this.infoLabel()
			this.query.category = currentConfig.auto_pilot3_key.categoryId
		},
		/** 通过条件搜索 */
		async searchMap() {
			this.markList = [];
			this.labelList = [];
			this.page = 0;
			this.infoWindow.close();
			await this.setMaker();
		},
    /**
     * 解决刷新页面dict还没有拿到就去执行后面代码导致地图不展示的bug
     */
    async getAwaitDict(){
      // 等待 this.dict.network_counter 有值
      if (!this.dict.networt_map || this.dict.networt_map.length === 0) {
        // 使用循环等待直到 dict 数据加载完成
        await new Promise(resolve => {
          const checkDictInterval = setInterval(() => {
            if (this.dict.networt_map && this.dict.networt_map.length > 0) {
              clearInterval(checkDictInterval)
              resolve()
            }
          }, 100)
        })
      }
    },
		/** 获取标签数据 */
		async initTagTap() {
      await this.getAwaitDict()

			this.tagTopList = this.dict.networt_map
			this.tagTop = this.tagTopList[0].value
			this.labelConfig = await getBulidenConfig('InfrastructurePlanP')
			await this.setCommonFun()
			this.isFist = false;
		},
		// 配置公共信息
		async setCommonFun() {
			this.tagTopKey = this.tagTopList.find(item => item.value === this.tagTop).label
			this.networkMapTip = JSON.parse(this.$config[this.tagTopKey].otherInfo)
		},
		async getTagList() {
			const query = {
				'type.values': this.labelConfig.topName,
				'top.fieldName': 'fv10',
				'type.fieldName': 'fv1',
				'top.values': this.tagTop
			}
			let queryResult = await findFilterItem(query)
			if (queryResult && queryResult.length > 0) {
				await this.getAssetListByLabel(this.handleQuery(queryResult))
			}
		},
		handleQuery(queryResult) {
			let tagObj = queryResult[0].detail[0];
			const json = {
				dateFormat: 'yyyy-MM-dd',
				indexName: 'fv2',
				type: {values: [this.labelConfig.topName], fieldName: 'fv1'},
				tag: {}
			};
			json.tag[tagObj['fv10']] = tagObj.tag.map(item => item.fv11);
			// json.tag[tagObj['fv10']] = ['机场']
			// json.tag['046-车网数据回传'] = ['机场']
			return json;
		},
		changeTagTop(val) {
			// this.isSearch = true;
			this.setCommonFun()
			this.setMaker()
		},
		/** 设置点位 */
		async setMaker() {
			if (this.isFist) {
				await this.initTagTap()
			}
			await this.getAssetListByLabel()

			if (this.markerLayer) {
        // if (this.isSearch) {
				this.markerLayer.setMap(null);
				this.markerLayer = new TMap.MultiMarker(MultiMarker(this, this.markList));
				// } else {
				// 	this.markerLayer.setGeometries([]);
				// 	this.markerLayer.setGeometries(this.markList);
				// }
				this.markerLabel.setGeometries([]);
			} else {
				this.markerLayer = new TMap.MultiMarker(MultiMarker(this, this.markList));
			}
      if (this.markerLabel) {
				this.markerLabel.updateGeometries([])
				this.markerLabel.updateGeometries(this.labelList);
			}
			// 监听marker点击事件,解决接口慢导致的点击事件添加失败不能点击点位的问题
      setTimeout(()=>{
        this.markerLayer.on('click', this.clickMaker);
      },2000)

		},
		/** 点击maker点位打开信息窗 */
		clickMaker(evt) {
			let info = evt.geometry
			cdata = evt;
			let content = ''
			content = autoPilotsContent(info)
			this.infoWindow.open(); // 打开信息窗
			this.infoWindow.setPosition(evt.geometry.position); // 设置信息窗位置
			this.infoWindow.setContent(content); // 设置信息窗内容
		},
		/** 创建信息窗口 */
		infoWin() {
			this.infoWindow = new TMap.InfoWindow({
				map: this.map,
				position: new TMap.LatLng(0, 0),
				offset: {x: -12, y: -32} // 设置信息窗相对position偏移像素，为了使其显示在Marker的上方
			});
			this.infoWindow.close(); // 初始关闭信息窗关闭
		},
		/** 创建label */
		infoLabel() {
			this.markerLabel = new TMap.MultiLabel({
				id: 'label-layer',
				map: this.map,
				styles: {
					label: new TMap.LabelStyle({
						color: '#050504', // 颜色属性
						size: 8, // 文字大小属性
						offset: {x: 0, y: 4}, // 文字偏移属性单位为像素
						angle: 0, // 文字旋转属性
						alignment: 'center', // 文字水平对齐属性
						verticalAlignment: 'middle', // 文字垂直对齐属性
					}),
				},
				geometries: [],
			});
			// this.infoLabel.close(); // 初始关闭信息窗关闭
		},
		/**处理点位 */
		processData(arr, isXlAsset, totalElements, request) {
			if (arr && arr.length) {
				const {markList, labelList} = this.createMarkersAndLabels(arr, isXlAsset);
				this.updateMapLayers(markList, labelList);
				// if (this.isLoadAll && this.markList.length < totalElements) {
				// 	request && request();
				// }
			} else {
				this.updateMapLayers(this.markList, this.labelList);
				// this.isSearch = false;
			}
			// this.isSearch = false;
		},
		/**处理点位参数 */
		createMarkersAndLabels(arr, isXlAsset) {
			const markList = [];
			const labelList = [];
			const zoom = this.map.getZoom().toFixed(0) < 13 ? '1' : '';
			// const scaleStart = this.isSearch ? 3 : 1;
			const scaleStart = 3
			for (const item of arr) {
				if (item.color) {
					const code = getCode(item);
					const position = isXlAsset ? getPosition(item, 1) : getPosition(item);

					const baseMarker = {
						id: item.id,
						styleId: this.styleIcon(item.color, zoom,),
						position: position,
						markerAnimation: {
							enter: {
								scaleStart: scaleStart,
								scaleEnd: 1,
								duration: 2000,
							},
						},
						properties: {
							id: item.id,
							title: item.title,
							type: this.query.category,
							backstageId: item.id,
							fv4: item.fv4 || '',
							code: code,
							fv19: item.fv19 || '',
						}
					};
					markList.push(baseMarker);

					const baseLabel = {
						id: item.id,
						styleId: 'label',
						position: position,
						content: item.fv4,
						properties: {
							title: item.title,
							type: this.query.category,
							backstageId: item.id,
							code: code || '',
						}
					};
					labelList.push(baseLabel);
				}
			}
			return {markList, labelList};
		},
		/**更新点位数据 */
		updateMapLayers(markList, labelList) {
			const zoomSize = this.map.getZoom().toFixed(0);

			this.markList = this.markList.concat(markList);
			this.labelList = this.labelList.concat(labelList);

			if (this.markerLayer) {
				// this.markerLayer.setGeometries(this.markList);
				// if (this.isSearch) {
				this.markerLayer.setMap(null);
				setTimeout(() => {
					this.markerLayer = new TMap.MultiMarker(MultiMarker(this, this.markList));
				}, 300)

				// } else {
				// 	this.markerLayer.setGeometries([]);
				// 	this.markerLayer.setGeometries(this.markList);
				// }
				this.markerLabel.setGeometries([]);
			}
			if (this.markerLabel) {
				this.markerLabel.setGeometries(this.labelList);
				this.markerLabel.setVisible(zoomSize > 12);
			}
		},
		/** marker点位的icon */
		styleIcon(color, zoom,) {
			const zoomStr = zoom.toString();  // 将 zoom 转换为字符串，以便拼接
			return color + zoomStr;
		},
		/** 地图缩放级别显示的内容 */
		mapZoomChange() {
			this.map.on('zoom', () => {
				if (this.isShowOptions.formShow) {
					if (this.map.getZoom() >= 14) {
						this.editMakerIcon('del');
					} else if (this.map.getZoom() <= 13) {
						this.editMakerIcon('add');
					}
				}
				// 	自动驾驶
				if (this.query.category == currentConfig.auto_pilot3_key.categoryId) {
					const zoomToSize = {
						10: 0,
						11: 8,
						12: 8,
						13: 8,
						14: 8,
						15: 10,
						16: 10,
						17: 10,
						18: 14,
						19: 14,
						20: 14
					};
					const size = this.map.getZoom().toFixed(0);
					if (size <= 12) {
						this.markerLabel.setVisible(false)
					} else {
						this.markerLabel.setVisible(true)
					}

					const newSize = zoomToSize[size] || 8; // 获取对应的文字大小，默认大小为8
					let y = 4;
					if (size > 13) {
						y = 10
					}
					this.markerLabel.setStyles({
						label: new TMap.LabelStyle({
							color: '#050504', // 保持颜色属性不变
							size: newSize, // 根据缩放级别调整文字大小
							offset: {x: 0, y: y}, // 保持文字偏移属性不变
							angle: 0, // 保持文字旋转属性不变
							alignment: 'center', // 保持文字水平对齐属性不变
							verticalAlignment: 'middle' // 保持文字垂直对齐属性不变
						})
					});
				}
			});
		},
		/** 地图缩放级别坐标的样式图片 */
		editMakerIcon(idType) {
			if (this.markList.length !== 0) {
				this.markList.map(item => {
					if (idType === 'add') {
						if (item.styleId.indexOf('1') === -1) {
							item.styleId += '1';
						}
					} else if (idType === 'del') {
						if (item.styleId.indexOf('1') !== -1) {
							item.styleId = item.styleId.replace('1', '');
						}
					}
				});

				this.markerLayer.setGeometries([]);
				this.markerLayer.setGeometries(this.markList);
			}
		},
		/** 给地图添加点击事件 */
		onMapClick(evt) {
			this.editMarker[1] = {
				id: 1000000, //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
				styleId: 'iconidet', //指定样式id
				position: new TMap.LatLng(evt.latLng.lat, evt.latLng.lng)//点标记坐标位置
			};
			this.markerLayer.setGeometries([]);
			this.markerLayer.setGeometries(this.editMarker);
			this.lat = evt.latLng.getLat().toFixed(6) + '';
			this.lng = evt.latLng.getLng().toFixed(6) + '';
			this.markerLayer.on('click', this.obtain);
		},
		obtain(evt) {
			this.lat = evt.latLng.getLat().toFixed(6) + '';
			this.lng = evt.latLng.getLng().toFixed(6) + '';
			this.infoWindow.close();
		},
		success() {
			/**移除点击事件 */
			this.removeClick(true);
		},
		/** 取消修改 */
		cancel() {
			/**移除点击事件 */
			this.removeClick();
		},
		/**移除点击事件后的处理方法 */
		removeClick(isSuccess) {
			this.map.off('click', this.onMapClick);
			this.isShowOptions.isForm = false;
			this.isShowOptions.formShow = true;
			mapClick.style.cursor = 'default';
			if (this.markerLayer) {
				this.markerLayer.setGeometries([]);
			}
			if (isSuccess) {
				this.searchMap();
			} else {
				this.setMaker();
			}
		},
		async getAssetListByLabel(query) {
			this.markList = []
			const {bindId} = this.$config.auto_pilot3_key;
			let tagNameMap = this.networkMapTip.filter(item => item.key).map(item => item.key);
			const json = {
				page: 0,
				size: 9999,
				bindId,
				enabled: [1],
				// fv4OrTitle: 383,
				tagName: tagNameMap,
				'type.values': this.labelConfig.topName,
				// ...query
			};
			const res = await getOmAssetAutoPilot(json, {isCancel: true, cancelKey: 'omAssetSmall'});
			const arr = res.content;
			const totalElements = res.totalElements;
			// 对获取的数据做处理
			let finalData = arr.map((item, index) => {
				let baseStatus = JSON.parse(JSON.stringify(this.networkMapTip));
				let hasStatus = updateStatus(baseStatus, item.tagMap);
				let color = getFinalColor(hasStatus)
				return {
					...item,
					color
				}
			})

			this.processData(finalData, false, totalElements, () => this.getAssetListByLabel(query));
		}
	}
};
</script>
<style lang="scss" scoped>
.search-box {
	position: absolute;
	width: 100%;
	left: 20px;
	top: 20px;
	z-index: 2000;
	padding: 10px 0 0 10px;
}

.icon-open {
	position: absolute;
	left: 30px;
	top: 62px;
	font-size: 24px;
	z-index: 2000;
	cursor: pointer;
}

.view-tag {
	position: absolute;
	left: 30px;
	top: 72px;
	cursor: pointer;
	z-index: 2000;
}

.map-tip {
	position: absolute;
	right: 120px;
	top: 50px;
	width: auto;
	padding: 20px;
	box-sizing: border-box;
	//border: 1px solid #ddd;
	//box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	border-radius: 4px;
	background: rgba(255, 255, 255, 0.7);

	li {
		display: flex;
		align-items: center;
		justify-content: flex-start;

		img {
			width: 25px;
			height: 25px;
			margin-right: 10px;
		}

		span {
			//color: #fff;
		}
	}
}

</style>
