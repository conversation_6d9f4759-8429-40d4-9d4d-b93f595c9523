const getters = {
  deployUploadApi: state => state.api.deployUploadApi,
  preViewUrl: state => state.api.preViewUrl,
  databaseUploadApi: state => state.api.databaseUploadApi,
  size: state => state.app.size,
  sidebar: state => state.app.sidebar,
  device: state => state.app.device,
  token: state => state.user.token,
  visitedViews: state => state.tagsView.visitedViews,
  cachedViews: state => state.tagsView.cachedViews,
  roles: state => state.user.roles,
  user: state => state.user.user,
  loadMenus: state => state.user.loadMenus,
  permission_routers: state => state.permission.routers,
  addRouters: state => state.permission.addRouters,
  socketApi: state => state.api.socketApi,
  imagesUploadApi: state => state.api.imagesUploadApi,
  baseApi: state => state.api.baseApi,
  fileUploadApi: state => state.api.fileUploadApi,
  cloudfileUploadApi: state => state.api.cloudfileUploadApi,
  cloudImgUploadApi: state => state.api.cloudImgUploadApi,
  omInspectAPi: state => state.api.omInspectAPi,
  omAssetAPi: state => state.api.omAssetAPi,
  omOaAPi: state => state.api.omOaAPi,
  omAssetAffiliated: state => state.api.omAssetAffiliated,
  amApi: state => state.api.amApi,
  updateAvatarApi: state => state.api.updateAvatarApi,
  qiNiuUploadApi: state => state.api.qiNiuUploadApi,
  sqlApi: state => state.api.sqlApi,
  swaggerApi: state => state.api.swaggerApi,
  sidebarRouters: state => state.permission.sidebarRouters,
  currentProject: state => state.currentProject.projectInfo,
  projectLabels: state => state.currentProject.projectLabels,
  isAllUpload: state => state.formMaking.isAllUpload
}
export default getters
