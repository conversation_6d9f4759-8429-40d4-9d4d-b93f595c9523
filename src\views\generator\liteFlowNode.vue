<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <el-input v-model="query.name" clearable size="small" placeholder="请输入标题" style="width: 200px;" class="filter-item" />
        <rrOperation />
      </div>
    </div>
    <crudOperation />
    <!--表格渲染-->
    <el-table ref="table" v-loading="crud.loading" :data="tableData" style="width: 100%;">
      <el-table-column
        v-for="item in tableHeader"
        :key="item.prop"
        :show-overflow-tooltip="true"
        :prop="item.prop"
        :label="item.label"
        :width="item.width || ''"
        :align="item.align || 'center'"
      >
        <template slot-scope="scope">
          <template v-if="item.prop.indexOf('img') !='-1'">
            <template v-for="(item,index) in scope.row[item.prop]">
              <el-image
                v-if="index == 0"
                :key="item.id"
                :src="item.url"
                :preview-src-list="[item.url]"
                fit="contain"
                lazy
                class="el-avatar"
              >
                <div slot="error">
                  <i class="el-icon-document" />
                </div>
              </el-image>
            </template>

          </template>
          <span v-else>{{ scope.row[item.prop] }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination />
  </div>
</template>

<script>
import CRUD, { presenter, header } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import pagination from '@crud/Pagination'
import crudOperation from '@crud/CRUD.operation'
export default {
  name: 'LiteFlowNode',
  components: { pagination, crudOperation, rrOperation },
  cruds() {
    return CRUD({ url: 'api/liteFlow/node' })
  },
  mixins: [presenter(), header()],
  data() {
    return {
      tableData: [],
      tableHeader: []
    }
  },
  watch: {
    'crud.data': {
      handler(val, oldVal) {
        if (val && val.length && val.length > 0) {
          const newVal = val;
          this.tableHeader = [
            { prop: 'name', label: '标题' },
            { prop: 'key', label: '关键字' }

          ];
          this.tableData = newVal.reduce((pre, cur) => {
            const name = {
              name: Object.keys(cur)[0],
              key: Object.values(cur)[0]
            }
            pre.push(name)
            return pre;
          }, []);
        } else {
          this.tableHeader = [];
          this.tableData = [];
        }
      },
      deep: true
    }
  },
  created() {
    this.crud.optShow = { add: false, edit: false, del: false, download: false }
  },
  methods: {}
}
</script>

<style scoped>

</style>
