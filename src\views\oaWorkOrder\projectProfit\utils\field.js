// 项目列表所有权限
export const permission = {
  add: ['admin', 'oaPmTree:add'],
  edit: ['admin', 'oaPmTree:edit'],
  del: ['admin', 'oaPmTree:del'],
  addMember: ['admin', 'oaPmMember:edit'],
  editMember: ['admin', 'oaPmTreeMember:add'],
  updateT: ['admin', 'oaPmTree:updateFormStruct'],
  updateR: ['admin', 'oaPmTree:updateRelation'],
  cateNamesToIds: ['admin', 'oaPmTree:cateNamesToIds'],
  addToMemberAndAuth: ['admin', 'oaPmTree:addToMemberAndAuth'],
  oneLevelSubproject: ['admin', 'oaPmTree:oneLevelSubproject'],
  importXlsWithRule: ['admin', 'oaPmTree:importXlsWithRule'],
  updateFieldsByRule: ['admin', 'oaPmTree:updateFieldsByRule'],
  seeRemarks: ['admin', 'oaPmTree:seeRemarks'],
  delRemarks: ['admin', 'oaPmTree:delRemarks'],
  addRemarks: ['admin', 'oaPmTree:addRemarks'],
  replyRemarks: ['admin', 'oaPmTree:replyRemarks'],
  seeProjectLog: ['admin', 'oaPmTree:seeProjectLog'],
  delProjectLog: ['admin', 'oaPmTree:delProjectLog'],
  addProjectLog: ['admin', 'oaPmTree:addProjectLog'],
  exportProject: ['admin', 'oaPmTree:exportProject'],
  previewProject: ['admin', 'oaPmTree:previewProject']
}

export const manyOption = [
  { name: '新建子项目', command: '1', permission: permission.add, fun: 'addProject', always: true },
  // { name: '新建子目录', command: '2', permission: permission.add, fun: 'addProject', always: true },
  { name: '项目成员', command: '3', permission: permission.addMember, fun: 'handleUsers', always: false },
  { name: '项目权限', command: '4', permission: permission.editMember, fun: 'handleAuth', always: false },
  { name: '项目派单', command: '5', permission: permission.edit, fun: 'onDispatch', always: true }
  // { name: '项目阶段', command: '6', permission: ['admin', 'oaPmTree:list'], fun: 'onDrawer', always: true }
]

// 项目列表表头
export const tableHeader = [
  { label: '项目名称', prop: '项目名称', fixed: 'left', align: 'left', width: 300, selectedOption: [] },
  { label: '分类', prop: '分类', align: 'center', width: 150, type: 'select', dict: 'department', showTip: true },
  { label: '项目类别', prop: '项目类别', align: 'center', width: 120 },
  { label: '项目签约含税金额', prop: '项目签约含税金额', align: 'center', width: 300 },
  { label: '税率', prop: '税率', width: 90, align: 'left', type: 'select', dict: 'project_info_type' },
  { label: '2024年_销售应回款金额', prop: '2024年_销售应回款金额', width: 105, align: 'left', type: 'selectSearch' },
  { label: '2024年_不含税金额', prop: `2024年_不含税金额`, width: 110, align: 'left', type: 'cascader', mapKey: 'fv6' },
  { label: '2024年_销项税金', prop: `2024年_销项税金`, width: 110, align: 'left', type: 'cascader', mapKey: 'fv6' },
  { label: '2025年_销售应回款金额', prop: '2025年_销售应回款金额', width: 100, align: 'left', type: 'select', dict: 'project_info_stage' },
  { label: '2025年_不含税金额', prop: '2025年_不含税金额', width: 100, align: 'left', type: 'select', dict: 'project_info_status' },
  { label: '2025年_销项税金', prop: '2025年_销项税金', width: 150, sortable: 'custom', align: 'right' }, // 不编辑
  {
    label: '2026年_销售应回款金额',
    prop: '2026年_销售应回款金额',
    width: 130,
    sortable: 'custom',
    align: 'right',
    type: 'input',
    regular: '/^-?\\d*\\.?\\d+$/'
  },
  {
    label: '2026年_不含税金额',
    prop: '2026年_不含税金额',
    width: 150,
    sortable: 'custom',
    align: 'right',
    type: 'input',
    regular: '/^-?\\d*\\.?\\d+$/'
  },
  { label: '2026年_销项税金', prop: '2026年_销项税金', width: 150, sortable: 'custom', align: 'right' }, // 不编辑
  {
    label: '2027年_销售应回款金额',
    prop: '2027年_销售应回款金额',
    width: 150,
    sortable: 'custom',
    align: 'right',
    type: 'input',
    regular: '/^-?\\d*\\.?\\d+$/'
  },
  {
    label: '2027年_不含税金额',
    prop: '2027年_不含税金额',
    width: 160,
    sortable: 'custom',
    align: 'right',
    type: 'input',
    regular: '/^-?\\d*\\.?\\d+$/'
  },
  {
    label: '2027年_销项税金',
    prop: '2027年_销项税金',
    width: 160,
    sortable: 'custom',
    align: 'right',
    type: 'input',
    regular: '/^-?\\d*\\.?\\d+$/'
  }
]

// 定义按钮组
export const updateButtonsLists = [
  {
    id: '1',
    label: '导入',
    permission: permission.importXlsWithRule,
    fun: 'importProject',
    size: 'mini',
    className: [],
    icon: 'plus',
    type: 'primary'
  }
  // {
  //  id: '6',
  //  label: '导出项目',
  //  permission: permission.exportProject,
  //  fun: 'exportProject',
  //  size: 'mini',
  //  className: [],
  //  icon: 'download',
  //  type: 'primary',
  //  query: {
  //    fileType: 'xls'
  //  }
  // },
  // {
  //  id: '7',
  //  label: '预览项目',
  //  permission: permission.previewProject,
  //  fun: 'exportProject',
  //  size: 'mini',
  //  className: [],
  //  icon: 'view',
  //  type: 'primary',
  //  query: {
  //    fileType: 'html'
  //  }
  // }
]
