<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input
          v-model="query.formData"
          class="filter-item"
          clearable
          placeholder="输入关键字搜索"
          size="small"
          style="width: 200px;"
        />
        <rrOperation />
      </div>
      <crudOperation :permission="permission">
        <el-button
          slot="left"
          v-permission="permission.add"
          class="filter-item"
          icon="el-icon-plus"
          size="mini"
          type="primary"
          @click="addTable"
        >
          新建
        </el-button>
        <el-button
          slot="left"
          v-permission="permission.upload"
          class="filter-item"
          icon="el-icon-plus"
          size="mini"
          type="success"
          @click="addFile"
        >上传
        </el-button>
        <el-button
          slot="left"

          class="filter-item"
          icon="el-icon-refresh"
          size="mini"
          type="primary"
          @click="updateTemplate"
        >更新模板
        </el-button>
        <el-button
          slot="left"
          v-permission="permission.updateR"
          class="filter-item"
          icon="el-icon-refresh"
          size="mini"
          type="success"
          @click="updateRelation"
        >更新绑定
        </el-button>
      </crudOperation>
    </div>
    <el-table
      ref="table"
      v-loading="crud.loading"
      :data="tableData"
      row-key="id"
      @select="crud.selectChange"
      @select-all="crud.selectAllChange"
      @selection-change="crud.selectionChangeHandler"
    >
      <el-table-column
        v-for="item in tableHeader"
        :key="item.prop"
        :align="item.align || 'center'"
        :label="item.label"
        :prop="item.prop"
        :show-overflow-tooltip="true"
        :width="item.width || ''"
      >
        <template slot-scope="scope">
          <template v-if="item.prop.indexOf('img') !='-1'">
            <template v-for="(item,index) in scope.row[item.prop]">
              <el-image
                v-if="index == 0"
                :key="item.id"
                :preview-src-list="[item.url]"
                :src="item.url"
                class="el-avatar"
                fit="contain"
                lazy
              >
                <div slot="error">
                  <i class="el-icon-document" />
                </div>
              </el-image>
            </template>

          </template>
          <!-- <template v-else-if="item.prop.indexOf('cascader') !='-1'">
				<span>{{ scope.row[item.prop] ? scope.row[item.prop].join('-') : '' }}</span>
			</template> -->
          <span v-else>{{ scope.row[item.prop] }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="300">
        <template slot-scope="scope">
          <min-crud-operation
            :handle-many="handleMany"
            :many-option="manyOption"
            :scope="scope"
            :show-item="showItem"
            title="666"
          />
          <el-button v-permission="permission.edit" size="mini" type="primary" @click="editItem(scope.row)">编辑
          </el-button>
          <el-popconfirm
            :hide-icon="true"
            cancel-button-text="取消"
            confirm-button-text="确认"
            icon-color="red"
            title="确认要删除该条数据？"
            @confirm="deleteItem(scope.row)"
          >
            <el-button slot="reference" v-permission="permission.del" size="mini" type="danger">删除</el-button>
          </el-popconfirm>
          <el-button size="mini" type="success" @click="detail(scope.row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination />
    <uploadExcel ref="uploadExcel" @getlist="crud.refresh()" />
  </div>
</template>

<script>
import uploadExcel from './components/uploadExcel'
import crudTable from '@/api/system/table'
import CRUD, { presenter, header, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import minCrudOperation from '@crud/UD.operation.mini'
import pagination from '@crud/Pagination'

export default {
  name: 'List',
  components: { crudOperation, rrOperation, pagination, uploadExcel, minCrudOperation },
  cruds() {
    return CRUD({ title: '分类', url: 'api/extendSampleCase', query: { enabled: 1 }, crudMethod: { ...crudTable }})
  },
  mixins: [presenter(), header(), crud()],
  data() {
    return {
      nofistLoad: false,
      tableData: [],
      tableHeader: [],
      permission: {
        add: ['admin', 'extendSampleCase:add'],
        edit: ['admin', 'extendSampleCase:edit'],
        del: ['admin', 'extendSampleCase:del'],
        upload: ['admin', 'extendSampleCase:importXlsWithRule'],
        updateT: ['admin', 'extendSampleCase:updateFormStruc'],
        updateR: ['admin', 'extendSampleCase:updateRelation']
      },
      manyOption: [
        { name: '新建子项目', command: '1', permission: ['admin', 'oaPmTree:add'], fun: 'addProject', always: true },
        { name: '新建子目录', command: '2', permission: ['admin', 'oaPmTree:add'], fun: 'addProject', always: true },
        { name: '项目成员', command: '3', permission: ['admin', 'oaPmMember:edit'], fun: 'handleUsers', always: false },
        { name: '项目权限', command: '4', permission: ['admin', 'oaPmTree:edit'], fun: 'handleAuth', always: false },
        { name: '项目派单', command: '5', permission: ['admin', 'oaPmTree:edit'], fun: 'onDispatch', always: true },
        { name: '项目阶段', command: '6', permission: ['admin', ''], fun: 'onDrawer', always: true }
      ],
      props: {
        value: 'id',
        label: 'label',
        emitPath: false
      },
      bindId: ''
    }
  },
  computed: {
    showItem() {
      return (item, row) => {
        if (row.noTop && !item.always) {
          return false;
        }
        return true;
      };
    }
  },
  watch: {
    'crud.data': {
      handler(val, oldVal) {
        if (val && val.length && val.length > 0) {
          const newVal = val;
          const publicfilters = ['text_', 'divider_', 'editor_', 'subform'];
          const uniqueFilters = ['file_'];
          const filterTableHeader = [...publicfilters, ...uniqueFilters]
          const tableHeader = newVal[0].extend.tableHeader.filter((item) => {
            return item.label !== '栅格布局' && filterTableHeader.every(subItem => item.prop.indexOf(subItem) == '-1')
          });
          const otherTableHeader = [
            // { prop: 'createBy', label: '发布人员' },
            // { prop: 'createTime', label: '发布时间' }
          ];
          this.tableHeader = [...tableHeader, ...otherTableHeader];
          let tableData = [];
          tableData = newVal.map(item => {
            const json = item.extend.data;
            json.id = item.id;
            json.createBy = item.createBy;
            json.createTime = item.createTime;
            return json;
          });
          this.tableData = tableData;
        } else {
          this.tableHeader = [];
          this.tableData = [];
        }
      },
      deep: true
    }
  },
  created() {
    this.crud.optShow = { add: false, edit: false, del: false, download: false, reset: true }
  },
  activated() {
    if (this.nofistLoad) {
      this.crud.refresh();
    }
    this.nofistLoad = false
  },
  methods: {
    [CRUD.HOOK.beforeRefresh]() {
      this.crud.query.bindId = this.$route.query.id;
      this.bindId = this.$route.query.id;
    },
    handleMany(data) {
      const { command, row, item } = data
      typeof this[item.fun] === 'function' && this[item.fun](row, command);
    },
    addProject() {
      console.log(666, '<===>', '666')
    },
    updateTemplate() {
      crudTable.updateFormStruct({ bindId: Number(this.bindId), enabled: 1 })
        .then(res => {
          this.$notify({
            title: '更新成功',
            type: 'success',
            duration: 2500
          })
        })
    },
    updateRelation() {
      crudTable.updateRelation({ bindId: Number(this.bindId), enabled: 1 })
        .then(res => {
          this.$notify({
            title: '更新成功',
            type: 'success',
            duration: 2500
          })
          this.crud.refresh();
        })
    },
    addFile() {
      this.$refs.uploadExcel.init();
    },
    addTable() {
      this.$router.push({ name: 'ExampleTplCreate', query: { id: this.$route.query.id }});
      this.nofistLoad = true;
    },
    editItem(row) {
      this.$router.push({ name: 'ExampleTplCreate', query: { id: this.$route.query.id, rowId: row.id, type: 'edit' }});
      this.nofistLoad = true;
    },
    detail(row) {
      this.$router.push({ name: 'ExampleTplCreate', query: { id: this.$route.query.id, rowId: row.id, type: 'see' }})
    },
    deleteItem(row) {
      crudTable.del([row.id]).then(response => {
        this.crud.refresh();
        this.$notify({
          title: '数据已删除',
          type: 'success',
          duration: 2500
        })
      })
    },
    getJson(item) {
      const json = {}
      item.forEach(i => {
        for (const name in i) {
          json[name] = i[name];
        }
      })
      return json;
    },
    getArr(item) {
      const arr = [];
      item.forEach(i => {
        i.forEach(j => {
          arr.push(j)
        })
      })
      return arr;
    }
  },
  [CRUD.HOOK.beforeRefresh]() {
    console.log(123);
    // console.log(this.$router.query.id);
    // crud.query.bindId = this.routerId
  }
}
</script>
<style>
.tableImg {
	width: 50px;
	height: 50px;
}
</style>
