<template>
  <div class="app-container">
    <el-card class="box-card" style="margin-top: 10px">
      <div slot="header" class="clearfix">
        <span>表单信息</span>
      </div>
      <el-form ref="ruleForm" :model="ruleForm" label-width="120px">
        <!-- <el-form-item label="选择:" prop="name">
<el-input v-model="ruleForm.name" clearable size="small" placeholder="输入关键字搜索" />
</el-form-item> -->
      </el-form>
      <div v-if="showFormData" class="text item">
        <fm-generate-form
          :ref="'generateForm'"
          :data="formStruct"
          :preview="viewOrEdit"
          :remote="remoteFunc"
          :value="formData"
        >
          <template v-slot:selfName="{ model }">
            <el-input v-model="formData.selfName" placeholder="请输入" />
          </template>
          <template v-slot:88="{ model }">
            <el-input v-model="formData[88]" placeholder="请输入" />
          </template>

        </fm-generate-form>
      </div>
      <hr style="background-color: #d9d9d9; border:0; height:1px;">
      <div class="text item" style="text-align: center;margin-top:18px">
        <el-button v-if="!viewOrEdit" :disabled="submitDisabled" type="primary" @click="submitAction">提交</el-button>
        <el-button @click="concelForm">{{ viewOrEdit == false ? '取 消' : '返回' }}</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>

import Vue from 'vue'
import { GenerateForm } from '@/components/VueFormMaking'
import 'form-making/dist/FormMaking.css'

Vue.component(GenerateForm.name, GenerateForm)
import { getToken } from '@/utils/auth'
import { get, add, edit } from '@/api/system/table'
import extendBindTpl from '@/api/system/extendBindTpl'
import { getUser } from '@/api/system/user'
import oaPmTree from '@/api/oaWorkOrder/oaPmTree';
import { LOAD_CHILDREN_OPTIONS } from '@riophae/vue-treeselect';

export default {
  name: 'Create',
  data() {
    return {
      viewOrEdit: false, // 默认是编辑
      submitDisabled: false,
      processStructureValue: {},
      ruleForm: {},
      jsonData: {},
      formStruct: {},
      formData: {},
      showFormData: false,
      remoteFunc: {
        funcGetToken(resolve) {
          resolve(getToken())
        },
        // 获取用户列表
        userList(resolve, option) {
          console.log(option, '<===>', 'query')
          // 测试下拉搜索
          // const data = {
          //   enabled: 1,
          //   userName: option?.query,
          //   size: 99
          // }
          // getByName(data).then(res => {
          //   const options = res
          //   resolve(options)
          // })
          getUser().then(response => {
            const options = response.content
            resolve(options)
          })
        },
        // 对级联选择器数据进行处理
        handleData(option) {
          option.map(item => {
            item.value = item.name;
            if (item.hasChildren) {
              this.handleData(option.children);
            }
          });
        },
        projectFun(option, resolve) {
          if (option.pid) {
            // 编辑
            oaPmTree.getPmTreeSuperior(option.pid).then(res => {
              const data = res.content
              this.buildProject(data)
              resolve(data)
            })
          } else {
            // 添加
            oaPmTree.getPmTree({ enabled: 1, size: 9999 }).then(res => {
              const data = res.content.map(function(obj) {
                if (obj.hasChildren) {
                  obj.children = null
                }
                return obj
              })
              resolve(data)
            })
          }
        },
        buildProject(depts) {
          depts.forEach(data => {
            if (data.children) {
              this.buildProject(data.children)
            }
            if (data.hasChildren && !data.children) {
              data.children = null
            }
          })
        },
        // 懒加载函数
        loadProject({ action, parentNode, callback }) {
          if (action === LOAD_CHILDREN_OPTIONS) {
            oaPmTree.getPmTree({ enabled: '1', pid: parentNode.id }).then(res => {
              parentNode.children = res.content.map(function(obj) {
                if (obj.hasChildren) {
                  obj.children = null
                }
                return obj
              })
              setTimeout(() => {
                callback()
              }, 100)
            })
          }
        }
      },
      from: {
        blank_name: '555'
      }
    }
  },
  created() {
    this.getInitData();
  },
  methods: {
    getInitData() {
      if (this.$route.query && this.$route.query.rowId) {
        this.getContent();
        this.viewOrEdit = this.$route.query.type == 'see';
      } else {
        // 创建的时候
        this.getProcessNodeList();
      }
    },
    getContent() {
      get({ id: this.$route.query.rowId }).then(res => {
        if (res && res.content && res.content.length) {
          const jsonData = res.content[0];
          this.processStructureValue = jsonData;
          this.formStruct = JSON.parse(jsonData.formStruct);
          this.formData = JSON.parse(jsonData.formData);
          this.jsonData = jsonData;
          this.showFormData = true;
        }
      })
    },
    getProcessNodeList() {
      const data = { id: this.$route.query.id, enabled: 1 }
      extendBindTpl.get(data).then(res => {
        if (res && res.content && res.content.length) {
          this.processStructureValue = res.content[0];
          const FormStruct = JSON.parse(this.processStructureValue.extendTpl.formStruct);
          this.formStruct = FormStruct;
        }
        this.showFormData = true;
      });
    },
    submitAction() {
      let isSubmit = false;
      this.$refs['ruleForm'].validate((valid) => {
        if (!valid) {
          isSubmit = true;
          return false;
        }
      })
      this.submitDisabled = true;
      const subData = {
        bindId: this.$route.query.id,
        enabled: 1,
        formData: '',
        formStruct: JSON.stringify(this.formStruct),
        formBindToVar: true,
        relation: this.processStructureValue.relation
      };
      this.$refs['generateForm'].getData().then(values => {
        subData.formData = JSON.stringify(values);
      }).catch(() => {
        isSubmit = true;
      })
      console.log(this.formData, '<===>', 'this')
      console.log(subData, '<===>', 'subData')
      setTimeout(() => {
        if (isSubmit) {
          this.submitDisabled = false
          this.$notify({
            title: '请根据提示填写表单信息',
            type: 'info',
            duration: 2500
          });
        } else {
          let request = add;
          if (this.jsonData && this.jsonData.id) {
            subData.id = this.jsonData.id;
            request = edit;
          }
          console.log(subData, '<===>', 'subData')
          request(subData).then(response => {
            this.concelForm();
          }).catch(() => {
            this.submitDisabled = false
          })
        }
      })
    },
    concelForm() {
      this.$router.go(-1);
    }
  }
}
</script>
<style lang="scss" rel="stylesheet/scss" scoped>
::v-deep .vue-treeselect__control, ::v-deep .vue-treeselect__placeholder, ::v-deep .vue-treeselect__single-value {
	height: 30px;
	line-height: 30px;
}
</style>
