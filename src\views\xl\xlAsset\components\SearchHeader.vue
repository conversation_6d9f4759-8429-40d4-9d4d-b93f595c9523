<template>
  <div v-if="crud.props.searchToggle" class="">
    <el-input
      v-model="query.title"
      class="filter-item"
      clearable
      placeholder="请输入立杆位置搜索"
      size="small"
      style="width: 180px;"
      @keyup.enter.native="crud.toQuery"
    />
    <el-input
      v-model="query.fv4"
      class="filter-item"
      clearable
      placeholder="请输入融合后点位搜索"
      size="small"
      style="width: 180px;"
      @keyup.enter.native="crud.toQuery"
    />
    <self-select
      :options="dict.xl_link"
      :select-value.sync="query.fv9"
      :tags="2"
      class="filter-item"
      clearable
      placeholder="请选择融合后链路"
      size="small"
      style="width: 180px;"
      @selectChange="(val)=>selectChange(val, 'fv9')"
    />
    <self-select
      :options="dict.xl_police_station"
      :select-value.sync="query.fv8"
      :tags="2"
      class="filter-item"
      clearable
      placeholder="请选择派出所"
      size="small"
      style="width: 180px;"
      @selectChange="(val)=>selectChange(val, 'fv8')"
    />
    <rrOperation />
  </div>
</template>

<script>
import { header } from '@crud/crud';
import rrOperation from '@crud/RR.operation';

export default {
  components: { rrOperation },
  mixins: [header()],
  props: {
    permission: {
      type: Object,
      required: true
    },
    dict: {
      type: Object,
      required: true
    }
  },
  data() {
    return {}
  },

  computed: {},
  watch: {},
  created() {
  },
  methods: {
    selectChange(val, type) {
      this.crud.query[type] = val;
      this.crud.toQuery();
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
