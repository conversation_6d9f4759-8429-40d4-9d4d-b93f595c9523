<template>
  <div
    ref="scrollContainer"
    v-loading="pageLoading"
    class="app-container oa-pm"
    element-loading-spinner="el-icon-loading"
    element-loading-text="更新中"
  >
    <!--工具栏-->
    <div class="head-container">
      <search-header :dict="dict" :permission="permission" />
      <crudOperation :permission="permission" :show-fixed="true">
        <!--一系列更新按钮-->
        <custom-action-button
          slot="right"
          :bind-id="bindId"
          :current-crud="crud"
          :permission="permission"
          @importProject="importProject"
          @successUpdateInfo="successUpdateInfo"
        />
        <fixed-button slot="fixed" @openTagHistory="openTagHistory" />
        <vxe-toolbar
          ref="xToolbar1"
          slot="iconButton"
          class-name="info-vxe-toolbar"
          custom
          size="mini"
        />
      </crudOperation>
    </div>
    <!--表格渲染-->
    <div :class="isSummary?'summary-table':''" class="project-table-content">
      <Hamburger
        :is-active="operateShow"
        class="hamburger-container"
        @toggleClick="toggleSideBar"
      />
      <vxe-table
        :id="toolbarID"
        ref="table"
        v-loading="crud.loading"
        :custom-config="{storage: true,checkMethod: checkColumnMethod}"
        :data="tableData"
        :footer-data="currentSummaryMethod"
        :header-cell-style="(params)=>tableRowClassName({column: params.column, query: crud.query})"
        :scroll-x="{enabled: true, gt: 10, scrollToLeftOnChange:true}"
        :scroll-y="{enabled: true, gt: 10, scrollToTopOnChange:true}"
        :show-footer="isNeedTotal"
        border="inner"
        footer-align="right"
        header-row-class-name="vxeTableClassName"
        max-height="750"
        show-overflow="tooltip"
        size="small"
      >
        <vxe-column
          v-for="(header,headerIndex) in tableHeaders"
          :key="header.label+headerIndex"
          :align="vxeColumnAlign(header.prop)"
          :field="header.prop"
          :fixed="header.fixed"
          :show-overflow="header.showOverflow"
          :title="header.label"
          :width="header.width || 150"
        >
          <template v-slot:default="{ row }">
            <!-- 插槽内容 -->
            <see-cell
              :crud="crud"
              :header="header"
              :permission="permission"
              :row="row"
            />
          </template>
        </vxe-column>

        <vxe-column
          v-if="operateShow"
          fixed="right"
          title="操作"
          width="180"
        >
          <template v-slot="{ row }">
            <udOperation
              :data="row"
              :permission="permission"
              msg="确定删除吗,此操作不能撤销！"
            />
          </template>
        </vxe-column>
      </vxe-table>
      <!--分页组件-->
      <pagination />
    </div>
    <!--导入项目-->
    <upload-excel v-if="uploadExcelShow" ref="uploadExcel" @getlist="uploadSuccess" />
    <!--标签历史数据-->
    <tag-history
      ref="tagHistory"
      :drawer-options="tagDrawerOptions"
      :select-query="tagSelectOptions"
    />
  </div>
</template>

<script>
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination';
import UploadExcel from './components/UploadExcel'
import SeeCell from './components/SeeCell';
import CustomActionButton from './components/CustomActionButton';
import FixedButton from './components/FixedButton';
import SearchHeader from './components/SearchHeader';
import { mapGetters } from 'vuex'
import Hamburger from '@/components/Hamburger'
import TagHistory from '@/views/components/UploadTagHistory/index';
import { tableRowClassName } from '@/utils/setTableByLabel';
import {
  formatterTableData,
  formatterTableHeader,
  toggleSideBarFun,
  // handleSummaries,
  dictConfig,
  getConfigData
} from './utils/commonFun'
import {
  tagDrawerOptions,
  tagSelectOptions
} from './utils/field'
import highlight from 'mavon-editor/src/lib/core/highlight';
import { isNumber } from '@/utils/is'
// crud交由presenter持有
const defaultForm = {
  id: null
}

export default {
  name: 'InformationBaseTep',
  components: {
    TagHistory,
    UploadExcel,
    SearchHeader, crudOperation, pagination,
    CustomActionButton,
    FixedButton,
    SeeCell,
    udOperation,
    Hamburger
  },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: false,
      title: '资产基础模板',
      url: '',
      sort: [],
      query: {
        enabled: [1],
        fv6: [],
        fv7: [],
        fv4OrTitle: '',
        'type.fieldName': 'fv1',
        'type.values': ''
      },
      crudMethod: {},
      optShow: {
        add: false,
        edit: false,
        del: false,
        download: false,
        reset: true,
        rightGroup: true,
        rightGroup4: false
      },
      requestConfig: { isCancel: true, cancelKey: 'omAssetAffiliated', method: 'get' }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // dicts: ['xyTown_type', 'partition_type'],
  dicts: [],
  data() {
    return {
      uploadExcelShow: false,
      isShowTag: false, // 控制tag
      pageLoading: false,
      operateShow: false, // 操作列是否显示
      tableData: [],
      permission: {},
      bindId: '',
      categoryId: '',
      tableHeaders: [],
      labelConfig: {},
      isNeedTotal: false,
      toolbarID: 'toolbarID',
      tagDrawerOptions,
      tagSelectOptions
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ]),
    currentSummaryMethod({ isNeedTotal }) {
      return isNeedTotal ? this.getSummaries() : []
    },
    dictApiData() {
      return dictConfig(this.labelConfig?.request, this.labelConfig?.key);
    },
    isSummary() {
      return this.crud?.metaData?.total;
    },
    vxeColumnAlign() {
      const { total } = this.crud?.metaData;
      return (prop) => {
        if (total) {
          if (total.hasOwnProperty(prop)) {
            return 'right'
          }
        }
      }
    }
  },
  watch: {
    'crud.data': {
      handler(val, oldVal) {
        const dynamicHeader = this.crud?.metaData?.tableHeader;
        const { total } = this.crud?.metaData
        if (total) {
          this.isNeedTotal = true
        } else {
          this.isNeedTotal = false
        }
        this.tableHeaders = formatterTableHeader(val, dynamicHeader, this.crud.query, this.labelConfig?.request, this.labelConfig?.key)
        this.tableData = formatterTableData(val)
      },
      deep: true
    }
  },
  async created() {
    console.log(123, '<===>', '123')
    this.crud.operToggle = false;
    this.labelConfig = await getConfigData();
    console.log(this.labelConfig, '<===>', ' this.labelConfig')
    console.log(this.labelConfig.configKey, '<===>', 'this.labelConfig.configKey')
    const { bindId, categoryId } = this.$config[this.labelConfig.configKey];
    console.log(this.labelConfig, '<===>', 'this.labelConfig')
    this.bindId = bindId;
    this.categoryId = categoryId;
    this.crud.url = this.dictApiData.url;
    this.permission = this.dictApiData.permission;
    this.setAssetConfig();
    this.crud.toQuery();
    this.$nextTick(() => {
      // 手动将表格和工具栏进行关联
      this.$refs.table.connect(this.$refs.xToolbar1)
    })
  },
  methods: {
    highlight,
    checkColumnMethod({ column }) {
      const array = ['fv4', 'title']
      if (array.includes(column.field)) {
        return false
      }
      return true
    },
    tableRowClassName,
    // 金额合计
    getSummaries() {
      const { total } = this.crud?.metaData;
      for (const key in total) {
        const num = parseFloat(total[key]);
        if (isNumber(num)) {
          total[key] = parseFloat(total[key])
        }
      }
      const amount = [{ fv4: '合计', ...total }]
      return amount
    },
    // 成功更新之后的操作
    successUpdateInfo(type) {
      if (type == 1) {
        this.pageLoading = true
      } else if (type == 2) {
        this.pageLoading = false
      } else {
        this.pageLoading = false
        this.crud.toQuery();
      }
    },
    // 导入数据
    async importProject() {
      this.uploadExcelShow = true
      await this.$nextTick()
      const { bindId, categoryId } = this.$config[this.labelConfig.configKey]
      this.$refs.uploadExcel.init({ bindId, categoryId, labelConfig: this.labelConfig });
    },
    uploadSuccess() {
      this.crud.toQuery()
      this.uploadExcelShow = false
    },
    // 到其他页面
    toInspects(row) {
      this.$router.push({
        name: 'XlInspects',
        query: {
          fv4: row.fv4,
          omAssetID: row.id,
          omAssetTitle: row.title
        }
      })
    },

    [CRUD.HOOK.beforeRefresh]() {
      this.setAssetConfig()
    },
    [CRUD.HOOK.beforeToAdd]() {
      const query = {
        name: 'InformationBaseTepForm'
      }
      this.$router.push(query)
    },
    [CRUD.HOOK.beforeToEdit](crud, form) {
      const { id } = form
      this.$router.push({ name: 'InformationBaseTepForm', query: { id }})
    },
    // 点击操作列头
    async toggleSideBar() {
      await toggleSideBarFun(this)
    },
    //   设置配置项
    setAssetConfig() {
      const { bindId } = this.$config[this.labelConfig.configKey]
      this.crud.query.bindId = bindId
      // this.crud.query.categoryId = categoryId;
      this.crud.crudMethod = this.dictApiData.crud;
      this.toolbarID = this.labelConfig.key;
      if (this.crud.url === this.dictApiData['url']) {
        this.crud.query['type.values'] = this.labelConfig.topName;
      }
      //   配置
      this.tagDrawerOptions.title = `${this.labelConfig.topName}-导入历史`
      this.tagSelectOptions['type.values'] = this.labelConfig.topName;
    },
    openTagHistory() {
      this.setAssetConfig()
      this.$refs.tagHistory.initData();
    }
  }
}
</script>

<style>
.table-hover .el-table__body tr.hover-row.current-row > td.el-table__cell, .el-table__body tr.hover-row.el-table__row--striped.current-row > td.el-table__cell, .el-table__body tr.hover-row.el-table__row--striped > td.el-table__cell, .el-table__body tr.hover-row > td.el-table__cell {
	background-color: transparent !important;
}
</style>
<style lang="scss" rel="stylesheet/scss" scoped>

//操作按钮相关
.operate-button {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.project-table-content {
	position: relative;

	.hamburger-container {
		position: absolute;
		right: 0;
		top: 0px;
		z-index: 8;
		line-height: 40px;
		cursor: pointer;
		transition: background .3s;
		-webkit-tap-highlight-color: transparent;
		background: rgba(0, 0, 0, .09);

		&:hover {
			background: rgba(0, 0, 0, .19)
		}
	}
}

::v-deep .el-input-number .el-input__inner {
	text-align: left;
}

::v-deep .vue-treeselect__control, ::v-deep .vue-treeselect__placeholder, ::v-deep .vue-treeselect__single-value {
	height: 30px;
	line-height: 30px;
}

.table-fixed {
	//max-height: 760px;
	// 表格合计样式
	::v-deep.el-table__footer {
		tr {
			td {
				font-size: 14px;
				color: #606266;
				font-weight: 700;
				text-align: right;
				font-family: Sans-serif;

				//&:first-child {
				//	text-align: left;
				//}
			}
		}
	}

	::v-deep .el-table__fixed-body-wrapper {
		padding-bottom: 17px;
		box-sizing: content-box;
	}
}

.el-table__fixed::before, .el-table__fixed-right::before {
	background-color: transparent;
}

.summary-table {
	::v-deep .el-table__fixed {
		height: calc(100% - 27px) !important;
	}

	::v-deep .el-table--scrollable-x .el-table__body-wrapper {
		padding-bottom: 60px !important;
	}

	::v-deep .el-table__footer-wrapper {
		position: absolute !important;
		left: 0 !important;
		bottom: 27px !important;
	}
}

::v-deep .el-table__fixed {
	height: calc(100% - 10px) !important;
}

::v-deep .el-table--scrollable-x .el-table__body-wrapper {
	padding-bottom: 10px !important;
}

// ::v-deep .el-table__footer-wrapper {
//     position: absolute !important;
//     left:0 !important;
//     bottom:5px !important;
// }
.info-vxe-toolbar {
	padding: 0 !important;
	width: 28px !important;
	height: 28px !important;

	.vxe-button.vxe-tools--operate.type--button {
		min-width: 28px !important;
		height: 28px !important;
		padding: 0px 12px !important;

	}

	.vxe-tools--operate.is--circle {
		border-radius: 0 !important;
	}

}

.c--tooltip {
	text-overflow: clip !important;
}

.vxeTableClassName {
	white-space: pre-wrap;
	word-wrap: break-word;
}
</style>
