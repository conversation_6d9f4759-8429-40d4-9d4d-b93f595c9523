<template>
  <div v-if="crud.props.searchToggle" class="">
    <el-input
      v-model="query.fv4OrTitle"
      class="filter-item"
      clearable
      placeholder="请输入编号或路口名称"
      size="small"
      style="width: 180px;"
      @keyup.enter.native="crud.toQuery"
    />
    <el-input
      v-model="query.fv5"
      class="filter-item"
      clearable
      placeholder="请输入杆体编号"
      size="small"
      style="width: 180px;"
      @keyup.enter.native="crud.toQuery"
    />
    <el-select
      v-model="query.fv1"
      class="filter-item"
      clearable
      placeholder="请选择位置描述"
      size="small"
      style="width: 180px"
      @change="crud.toQuery"
    >
      <el-option
        v-for="item in dict.direction_intersection"
        :key="item.id"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <el-select
      v-model="query.review"
      class="filter-item"
      clearable
      placeholder="请选择状态"
      size="small"
      style="width: 180px"
      @change="crud.toQuery"
    >
      <el-option label="合格" value="合格" />
      <el-option label="不合格" value="不合格" />
    </el-select>
    <date-range-picker
      v-model="query.createTime"
      class="date-item"
      end-placeholder="结束日期"
      start-placeholder="开始日期"
    />
    <rrOperation />
  </div>
</template>

<script>
import { header } from '@crud/crud';
import rrOperation from '@crud/RR.operation';
import DateRangePicker from '@/components/DateRangePicker/index.vue';

export default {
  components: { rrOperation, DateRangePicker },
  mixins: [header()],
  props: {
    permission: {
      type: Object,
      required: true
    },
    dict: {
      type: Object,
      required: true
    }
  },
  data() {
    return {}
  },

  computed: {},
  watch: {},
  created() {
  },
  methods: {
    selectChange(val, type) {
      this.crud.query[type] = val;
      this.crud.toQuery();
    }
  }
}
</script>
