<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-select
          v-if="filterAssetShow"
          v-model="query.assetId"
          class="filter-item"
          clearable
          filterable
          placeholder="请选择部件名称"
        >
          <el-option
            v-for="item in partsList"
            :key="item.id"
            :label="item.title"
            :value="item.id"
            style="width: 200px"
          />
        </el-select>
        <el-select v-model="query.status" class="filter-item" clearable filterable placeholder="请选择巡检结果">
          <el-option
            v-for="item in inspectsRlist"
            :key="item.id"
            :label="item.label"
            :value="item.label"
            style="width: 200px"
          />
        </el-select>
        <el-input
          v-model="query.fv6"
          class="filter-item"
          clearable
          placeholder="请输入情况描述"
          size="small"
          style="width: 200px;"
        />
        <date-range-picker
          v-model="query.createTime"
          class="date-item"
          style="width:400px !important"
          type="datetimerange"
        />
        <rrOperation>
          <el-button
            slot="right"
            class="filter-item"
            icon="el-icon-eleme"
            size="mini"
            type="primary"
            @click="preView"
          >
            预览
          </el-button>
          <el-button
            slot="right"
            class="filter-item"
            icon="el-icon-download"
            size="mini"
            type="primary"
            @click="downLoad"
          >
            导出word
          </el-button>
          <el-button
            slot="right"
            class="filter-item"
            icon="el-icon-download"
            size="mini"
            type="primary"
            @click="downLoadExcel"
          >
            导出excel
          </el-button>
        </rrOperation>
      </div>
      <crudOperation :permission="permission">
        <el-button
          slot="left"
          v-permission="permission.add"
          class="filter-item"
          icon="el-icon-plus"
          size="mini"
          type="primary"
          @click="addTable"
        >
          新建
        </el-button>
        <update-button
          slot="left"
          :bind-id="bindId"
          :enabled="[1]"
          :permission="permission"
        />
      </crudOperation>
    </div>
    <el-table
      ref="table"
      v-loading="crud.loading"
      :data="tableData"
      row-key="id"
      @select="crud.selectChange"
      @select-all="crud.selectAllChange"
      @selection-change="crud.selectionChangeHandler"
    >
      <el-table-column
        v-for="item in tableHeader"
        :key="item.prop"
        :align="item.align || 'center'"
        :label="item.label"
        :prop="item.prop"
        :show-overflow-tooltip="true"
        :width="item.width || ''"
      >
        <template slot-scope="scope">
          <template v-if="item.prop =='ft1'">
            <template v-for="(item,index) in scope.row[item.prop]">
              <!--{{ item.url }}-->
              <el-image
                v-if="index == 0"
                :key="item.id"
                :preview-src-list="[item.url]"
                :src="item.thUrl"
                class="el-avatar"
                fit="contain"
              >
                <div slot="error">
                  <i class="el-icon-document" />
                </div>
              </el-image>
            </template>
          </template>
          <template v-else-if="item.prop =='fv20'">
            <span class="table-colume-title" @click="detail(scope.row)">{{ scope.row[item.prop] }}</span>
          </template>
          <span v-else>{{ scope.row[item.prop] }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="checkPer(['admin','omInspect:edit','omInspect:del'])"
        align="center"
        fixed="right"
        label="操作"
        width="250"
      >
        <template slot-scope="scope">
          <el-button v-permission="permission.edit" size="mini" type="primary" @click="editItem(scope.row)">编辑
          </el-button>
          <el-popconfirm
            :hide-icon="true"
            cancel-button-text="取消"
            confirm-button-text="确认"
            icon-color="red"
            title="确认要删除该条数据？"
            @confirm="deleteItem(scope.row)"
          >
            <el-button slot="reference" v-permission="permission.del" size="mini" type="danger">删除</el-button>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import crudTable from '@/api/parts/inspect'
import CRUD, { presenter, header, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import DateRangePicker from '@/components/DateRangePicker'
import { getConfig } from '@/utils/getConfigData'
import omAsset from '@/api/parts/assets'
import crudDictDetail from '@/api/system/dictDetail'
import { downloadUrl } from '@/utils/index'
import updateButton from '@/components/UpdateButton/index'

export default {
  name: 'Inspects',
  components: { crudOperation, rrOperation, pagination, DateRangePicker, updateButton },
  cruds() {
    return CRUD({
      title: '资产管理',
      url: 'api/omInspect/small',
      query: { enabled: 1, sort: 'createTime,desc' },
      crudMethod: { ...crudTable },
      optShow: { add: false, edit: false, del: false, download: false, reset: true }
    })
  },

  mixins: [presenter(), header(), crud()],
  dicts: ['inspects_results'],
  data() {
    return {
      enabledTypeOptions: [
        { key: 'true', display_name: '激活' },
        { key: 'false', display_name: '锁定' }
      ],
      nofistLoad: false,
      tableData: [],
      tableHeader: [],
      permission: {
        add: ['admin', 'omInspect:add'],
        edit: ['admin', 'omInspect:edit'],
        del: ['admin', 'omInspect:del'],
        upload: ['admin', 'omInspect:importXlsWithRule'],
        updateT: ['admin', 'omInspect:updateFormStruc'],
        updateR: ['admin', 'omInspect:updateRelation']
      },
      props: {
        value: 'id',
        label: 'label',
        emitPath: false
      },
      bindId: '',
      partsList: [],
      inspectsRlist: [],
      filterAssetShow: true,
      omAssetTitle: '',
      partsListInfo: {
        page: -1,
        size: 10,
        categoryId: '',
        bindId: '',
        enabled: 1
      }
    }
  },
  computed: {
    ...mapGetters([
      'baseApi',
      'omInspectAPi'
    ])
  },
  watch: {
    'crud.data': {
      handler(val, oldVal) {
        this.formatterTableHeader(val)
        this.formatterTableData(val)
      },
      deep: true
    }
  },
  created() {
    this.getPartsNames();
    this.getInspectsResults();
  },
  activated() {
    if (this.nofistLoad) {
      this.crud.refresh();
    }
    this.nofistLoad = false
  },
  methods: {
    [CRUD.HOOK.beforeRefresh]() {
      this.crud.query.bindId = this.$route.query.id;
      this.crud.query.categoryId = this.$route.query.category;
      if (this.$route.query.omAssetID) {
        this.crud.query.assetId = this.$route.query.omAssetID
      }
      if (this.$route.query.omAssetTitle) {
        this.omAssetTitle = this.$route.query.omAssetTitle
      }
      this.filterAssetShow = !this.$route.query.omAssetID
      this.bindId = this.$route.query.id;
    },
    getTime() {
      if (this.query.createTime) {
        return this.query.createTime
      }
      const end = `${this.$dayJS().format('YYYY-MM-DD')} 12:00:00`
      const start = `${this.$dayJS().subtract(1, 'day').format('YYYY-MM-DD')} 12:00:00`
      return [start, end]
    },
    preView() {
      // const params = `page=0&size=99999&inspect.createTime=${this.getTime()[0]}&inspect.createTime=${this.getTime()[1]}&inspect.status=${this.query.status || ''}&asset.status=${this.query.status || '故障'}&inspect.enabled=1`
      const { category } = this.$route.query
      const otherParams = 'page=0&size=99999&inspect.enabled=1';
      const time = `&inspect.createTime=${this.getTime()[0]}&inspect.createTime=${this.getTime()[1]}`
      const status = `&inspect.status=${this.query.status || ''}&asset.status=${this.query.status || '故障'}`
      const categoryId = `&asset.categoryId=${category}`
      const params = `${otherParams}${time}${status}${categoryId}`
      const url = `${this.omInspectAPi}/index/html?${params}`;
      window.open(url)
    },
    downLoad() {
      // const params = `page=0&size=99999&inspect.createTime=${this.getTime()[0]}&inspect.createTime=${this.getTime()[1]}&inspect.status=${this.query.status || ''}&asset.status=${this.query.status || '故障'}&inspect.enabled=1&fileName=交通信号灯运维日报`
      const { category } = this.$route.query
      const otherParams = 'page=0&size=99999&inspect.enabled=1';
      const time = `&inspect.createTime=${this.getTime()[0]}&inspect.createTime=${this.getTime()[1]}`
      const status = `&inspect.status=${this.query.status || ''}&asset.status=${this.query.status || '故障'}`
      const categoryId = `&asset.categoryId=${category}`
      const params = `${otherParams}${time}${status}${categoryId}`
      const url = `${this.omInspectAPi}/index/doc?${params}`;
      downloadUrl(url)
    },
    downLoadExcel() {
      const { category } = this.$route.query
      const otherParams = 'page=0&size=99999&enabled=1&fileName=交通信号灯运维日报';
      const createTime = `${this.getTime()[0]}&createTime=${this.getTime()[1]}`;
      const status = `${this.query.status || ''}`
      const categoryId = category
      const params = `${otherParams}&createTime=${createTime}&status=${status}&categoryId=${categoryId}`
      const url = `${this.omInspectAPi}/xls?${params}`;
      downloadUrl(url)
    },
    getInspectsResults() {
      crudDictDetail.get('inspects_results').then(response => {
        this.inspectsRlist = response.content
      })
    },
    async getPartsNames() {
      const data = { key: 'Junction_list' }
      await getConfig(data).then(res => {
        this.partsListInfo.categoryId = res.extend.data.categoryId
        this.partsListInfo.bindId = res.extend.data.bindId
        this.getPartsList();
      });
    },
    getPartsList() {
      this.partsListInfo.page++;
      omAsset.getOmAssetSmall(this.partsListInfo).then(res => {
        this.partsList = this.partsList.concat(res.content);
        if (res.totalElements == this.partsList.length) {
          return
        } else {
          this.getPartsList();
        }
      })
    },
    addTable() {
      const query = {
        id: this.$route.query.id,
        omAssetID: this.$route.query.omAssetID || '',
        categoryID: this.$route.query.category,
        omAssetTitle: this.omAssetTitle
      }
      this.$router.push({ name: 'InspectsCreate', query });
      this.nofistLoad = true;
    },
    editItem(row) {
      this.$router.push({
        name: 'InspectsCreate',
        query: { id: this.$route.query.id, rowId: row.id, omAssetTitle: this.omAssetTitle, type: 'edit' }
      });
      this.nofistLoad = true;
    },
    detail(row) {
      this.$router.push({
        name: 'InspectsCreate',
        query: { id: this.$route.query.id, rowId: row.id, omAssetTitle: this.omAssetTitle, type: 'see' }
      })
    },
    deleteItem(row) {
      crudTable.del([row.id]).then(res => {
        this.crud.refresh();
        this.$notify({
          title: '数据已删除',
          type: 'success',
          duration: 2500
        })
      })
    },
    getJson(item) {
      const json = {}
      item.forEach(i => {
        for (const name in i) {
          json[name] = i[name];
        }
      })
      return json;
    },
    getArr(item) {
      const arr = [];
      item.forEach(i => {
        i.forEach(j => {
          arr.push(j)
        })
      })
      return arr;
    },
    formatImageArr(val) {
      if (typeof val === 'string') {
        return JSON.parse(val);
      } else {
        return val;
      }
    },
    formatterTableData(val) {
      if (!val && !val.length) {
        this.tableData = [];
        return
      }
      const mapItem = (item) => {
        const baseJson = {
          id: item.id,
          assetTitle: item.asset.title,
          createBy: item.createBy,
          createTime: item.createTime,
          status: item.status,
          ft1: JSON.parse(item.ft1),
          fv20: item.asset.fv20,
          fv19: item.asset.fv19,
          fv18: item.asset.fv18,
          ...item.extend.data
        };

        return {
          ...baseJson
        };
      };
      this.tableData = val.map(mapItem);
    },
    formatterTableHeader(val) {
      const tableHeader = [
        { prop: 'fv20', label: '路口(新)', align: 'left', fixed: 'left' },
        { prop: 'fv19', label: '路口(802)' },
        { prop: 'fv18', label: '编号' },
        // { prop: 'assetTitle', label: '部件名称' },
        { prop: 'createBy', label: '巡检人' },
        { prop: 'createTime', label: '巡检时间' },
        { prop: 'status', label: '巡检结果' },
        { prop: 'ft1', label: '巡检图片' }
      ]
      const otherHeader = [];
      this.tableHeader = [...tableHeader, ...otherHeader];
    }
  }
}
</script>

<style scoped>
.table-colume-title {
	cursor: pointer;
	color: #2476F8;
}
</style>
