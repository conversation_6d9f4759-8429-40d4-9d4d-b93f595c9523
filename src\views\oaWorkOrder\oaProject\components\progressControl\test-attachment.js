// 测试附件功能的辅助函数

// 模拟管控点数据（包含附件）
const mockControlPointWithAttachments = {
  id: 1,
  name: '测试管控点',
  remark: '完成情况说明',
  weight: 10,
  attachments: JSON.stringify([
    {
      name: '设计图纸.pdf',
      url: 'https://example.com/files/design.pdf',
      uid: '1001',
      status: 'success'
    },
    {
      name: '施工照片.jpg',
      url: 'https://example.com/files/photo.jpg',
      uid: '1002',
      status: 'success'
    }
  ])
}

// 模拟管控点数据（无附件）
const mockControlPointWithoutAttachments = {
  id: 2,
  name: '无附件管控点',
  remark: '无附件的管控点',
  weight: 5,
  attachments: null
}

// 测试获取附件数量的函数
function getAttachmentCount(controlPoint) {
  if (!controlPoint.attachments) return 0

  try {
    // 如果attachments是字符串，尝试解析为JSON
    if (typeof controlPoint.attachments === 'string') {
      const attachments = JSON.parse(controlPoint.attachments)
      return Array.isArray(attachments) ? attachments.length : 0
    }
    // 如果attachments已经是数组
    if (Array.isArray(controlPoint.attachments)) {
      return controlPoint.attachments.length
    }
    return 0
  } catch (error) {
    console.error('解析附件数据失败:', error)
    return 0
  }
}

// 测试解析附件数据的函数
function parseAttachments(controlPoint) {
  let attachments = []
  if (controlPoint.attachments) {
    try {
      // 如果是字符串，尝试解析为JSON
      if (typeof controlPoint.attachments === 'string') {
        attachments = JSON.parse(controlPoint.attachments)
      } else if (Array.isArray(controlPoint.attachments)) {
        attachments = controlPoint.attachments
      }
    } catch (error) {
      console.error('解析附件数据失败:', error)
      attachments = []
    }
  }
  return attachments
}

// 运行测试
console.log('=== 附件功能测试 ===')

console.log('\n1. 测试有附件的管控点:')
console.log('管控点数据:', mockControlPointWithAttachments)
console.log('附件数量:', getAttachmentCount(mockControlPointWithAttachments))
console.log('解析的附件:', parseAttachments(mockControlPointWithAttachments))

console.log('\n2. 测试无附件的管控点:')
console.log('管控点数据:', mockControlPointWithoutAttachments)
console.log('附件数量:', getAttachmentCount(mockControlPointWithoutAttachments))
console.log('解析的附件:', parseAttachments(mockControlPointWithoutAttachments))

console.log('\n3. 测试附件数组格式:')
const mockWithArrayAttachments = {
  ...mockControlPointWithAttachments,
  attachments: [
    { name: 'file1.pdf', url: 'url1', uid: '1', status: 'success' },
    { name: 'file2.jpg', url: 'url2', uid: '2', status: 'success' }
  ]
}
console.log('附件数量:', getAttachmentCount(mockWithArrayAttachments))
console.log('解析的附件:', parseAttachments(mockWithArrayAttachments))

console.log('\n4. 测试错误的JSON格式:')
const mockWithBadJson = {
  ...mockControlPointWithAttachments,
  attachments: '{"invalid": json}'
}
console.log('附件数量:', getAttachmentCount(mockWithBadJson))
console.log('解析的附件:', parseAttachments(mockWithBadJson))

export {
  mockControlPointWithAttachments,
  mockControlPointWithoutAttachments,
  getAttachmentCount,
  parseAttachments
}
