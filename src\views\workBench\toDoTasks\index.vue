<template>
  <div class="app-container">
    <project-list :project-list="projectList" title="我的待办" @changeProjectId="getToDoItem" />
    <el-card style="margin-top: 20px;">
      <work-bench-task
        ref="workTaskRef"
        :custom-crud-option="CRUDOPTION"
        :permission="permission"
        :table-data="tableData"
        :table-header="TATBLEHEADER"
      >
        <template v-slot:workTaskRef="{data}">
          <el-button
            size="mini"
            type="primary"
            @click="handleClick(data)"
          >
            处理
          </el-button>
        </template>

      </work-bench-task>
    </el-card>

  </div>
</template>

<script>
// 顶部组件
import ProjectList from '@/views/workBench/components/ProjectList.vue';
import WorkBenchTask from '@/views/workBench/components/WorkBenchTask.vue';
import { getTodoByPmName } from '@/api/workBench/oaPmTaskTodo'
import { mapGetters } from 'vuex';
import { mapTaskItem } from '@/views/workBench/utils/handelData';

const CRUDOPTION = {
  url: 'api/oaPmTaskTodo'
}
const TATBLEHEADER = [
  {
    prop: 'name',
    label: '任务标题'
  },
  {
    prop: 'fv9',
    label: '处理人'
  },
  {
    prop: 'fv12',
    label: '当前状态'
  },
  {
    prop: 'currentStatusVal',
    label: '是否结束'
  },
  {
    prop: 'taskCreateTime',
    label: '创建时间'
  },
  {
    prop: 'fv13',
    label: '开始时间'
  },
  {
    prop: 'fv11',
    label: '预计结束时间'
  }
]
export default {
  name: 'ToDoTasks',
  components: {
    ProjectList,
    WorkBenchTask
  },
  data() {
    return {
      projectList: [],
      tableHeader: [],
      permission: [],
      CRUDOPTION,
      TATBLEHEADER,
      tableData: []
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  async created() {
    await this.getToDoProject()
  },
  methods: {
    // 获取待办项目
    getToDoProject() {
      const query = {
        page: 0,
        size: 999,
        enabled: 1,
        sort: 'createTime,desc',
        userName: this.user.username
      }
      getTodoByPmName(query).then(res => {
        this.projectList = res
      })
    },

    getToDoItem(val) {
      const { crud } = this.$refs.workTaskRef
      crud.query.pmName = val
      crud.query.userName = this.user.username
      crud.refresh().then(res => {
        // 获取到数据
        const data = res.content;
        this.tableData = data.map(mapTaskItem);
      })
    },
    // 处理函数
    handleClick(data) {
      this.$router.push({
        name: 'DoTask',
        query: {
          id: data.taskId,
          type: 1
        }
      })
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
