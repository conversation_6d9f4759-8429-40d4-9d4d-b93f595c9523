<template>
  <div>
    <el-dialog append-to-body :close-on-click-modal="false" :before-close="handleClose" :visible.sync="historyVisible" title="历史记录" width="800px">
      <el-table :data="tableData" size="small" style="width: 100%;margin-bottom: 15px">
        <el-table-column prop="ft1" label="配时方案" align="center" />
        <el-table-column prop="updateTime" label="修改时间" align="center" />
        <el-table-column prop="ft2" label="配时图片" align="center">
          <template v-if="scope.row.ft2 && scope.row.ft2.length>0" slot-scope="scope">
            <template v-for="item in scope.row.ft2">
              <el-image
                :key="item.id"
                :src="item.url"
                :preview-src-list="[item.url]"
                fit="contain"
                lazy
                class="el-avatar"
              >
                <div slot="error">
                  <i class="el-icon-document" />
                </div>
              </el-image>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="handleClose">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { get } from '@/api/parts/changeLog'
export default {
  name: 'History',
  data() {
    return {
      historyVisible: false,
      tableData: []
    }
  },
  created() {
  },
  methods: {
    init(data) {
      this.historyVisible = true;
      get({ assetId: data.assetId, ft1: 1 }).then(res => {
        const data = res.content;
        this.tableData = data.map(item => {
          item.ft2 = this.formatString(item.ft2);
          return item;
        });
      });
    },
    handleClose() {
      this.historyVisible = false;
      this.tableData = [];
    },
    formatString(str) {
      if (str) {
        return JSON.parse(str);
      } else {
        return [];
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.el-avatar{
  margin-right:10px;
}
</style>
