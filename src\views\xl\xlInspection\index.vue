<template>
  <div
    v-loading="pageLoading"
    class="app-container oa-pm"
    element-loading-spinner="el-icon-loading"
    element-loading-text="更新中"
  >
    <!--工具栏-->
    <div class="head-container">
      <search-header :dict="dict" :permission="permission" />
      <crudOperation :permission="permission">
        <!--一系列更新按钮-->
        <custom-action-button
          slot="right"
          :bind-id="bindId"
          :current-crud="crud"
          :permission="permission"
          @successUpdateInfo="successUpdateInfo"
        />
      </crudOperation>
    </div>
    <!--表格渲染-->
    <div ref="projectBody" class="project-table-content">
      <Hamburger
        :is-active="operateShow"
        class="hamburger-container"
        @toggleClick="toggleSideBar"
      />
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="tableData"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        class="table-fixed"
        lazy
        row-key="id"
        @select="crud.selectChange"
        @select-all="crud.selectAllChange"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column
          v-for="(header, initIndex) in tableHeaders"
          :key="header.label+initIndex"
          :align="header.align"
          :fixed="header.fixed"
          :label="header.label"
          :prop="header.prop"
          :show-overflow-tooltip="true"
          :sortable="header.sortable || false"
          :width="header.width"
        >
          <template slot-scope="scope">
            <see-cell
              :current-scope="scope"
              :header="header"
              :permission="permission"
              @toDetail="toDetail(scope.row)"
            />
          </template>
        </el-table-column>
        <transition name="fade">
          <el-table-column
            v-if="operateShow "
            fixed="right"
            label="操作"
            width="150"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
                msg="确定删除吗,此操作不能撤销！"
              />
            </template>
          </el-table-column>
        </transition>
      </el-table>

      <!--分页组件-->
      <pagination />
    </div>
    <!--导入项目-->
    <!-- <upload-excel ref="uploadExcel" @getlist="crud.toQuery()" /> -->

  </div>
</template>

<script>
import crudTable from '@/api/parts/inspect'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination';
// import UploadExcel from '@/views/xl/xlInspection/components/UploadExcel'
import SeeCell from '@/views/xl/xlInspection/components/SeeCell';
import CustomActionButton from '@/views/xl/xlInspection/components/CustomActionButton';
import SearchHeader from '@/views/xl/xlInspection/components/SearchHeader';
import { mapGetters } from 'vuex'
import Hamburger from '@/components/Hamburger'
import {
  formatterTableData,
  formatterTableHeader,
  toggleSideBarFun
} from '@/views/xl/xlInspection/utils/commonFun'
import {
  permission
} from '@/views/xl/xlInspection/utils/field'
// crud交由presenter持有
const defaultForm = {
  id: null
}
export default {
  name: 'XlInspects',
  components: {
    // UploadExcel,
    SearchHeader, crudOperation, udOperation, pagination,
    Hamburger,
    CustomActionButton,
    SeeCell
  },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: false,
      title: '雪亮巡检列表',
      url: 'api/omInspect/small',
      sort: [],
      query: { enabled: 1, sort: 'createTime,desc', fv4: '', fv8: [], fv9: [], title: '' },
      crudMethod: { ...crudTable },
      optShow: {
        add: true,
        edit: false,
        del: false,
        download: false,
        reset: true,
        rightGroup: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['xl_link', 'xl_police_station'],
  data() {
    return {
      pageLoading: false,
      operateShow: false, // 操作列是否显示
      tableData: [],
      permission,
      bindId: '',
      categoryId: '',
      tableHeaders: []
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ])
    // setOperateShow() {
    //   return this.$setArrPermission(['add', 'del'], this.permission)
    // }
  },
  watch: {
    'crud.data': {
      async handler(val, oldVal) {
        this.tableHeaders = await formatterTableHeader(val)
        this.tableData = formatterTableData(val)
        await this.$nextTick()
        this.$refs.table.doLayout()
      },
      deep: true
    }
  },
  async created() {
    // this.crud.operToggle = false
    const { bindId, categoryId } = this.$config.xl_inspection_key
    this.bindId = bindId;
    this.categoryId = categoryId
    this.setAssetConfig()
    this.crud.toQuery();
  },
  methods: {
    // 成功更新之后的操作
    successUpdateInfo(type) {
      if (type == 1) {
        this.pageLoading = true
      } else if (type == 2) {
        this.pageLoading = false
      } else {
        this.pageLoading = false
        this.crud.toQuery();
      }
    },
    // 导入雪亮资产
    // importProject() {
    //   const { bindId, categoryId } = this.$config.xl_asset_key
    //   this.$refs.uploadExcel.init({ bindId, categoryId });
    // },

    [CRUD.HOOK.beforeRefresh]() {
      this.setAssetConfig()
    },
    [CRUD.HOOK.beforeResetQuery]() {
      this.$refs.table.clearSort()
    },
    [CRUD.HOOK.beforeToAdd]() {
      const query = {
        type: 0,
        fv4: this.$route.query.fv4 || '',
        omAssetID: this.$route.query.omAssetID || ''
      }
      this.$router.push({ name: 'XlInspectsForm', query })
    },
    [CRUD.HOOK.beforeToEdit](crud, form) {
      const { id, fv4, asset } = form;
      this.$router.push({ name: 'XlInspectsForm', query: { rowId: id, fv4, omAssetID: asset.id, type: 0 }})
    },
    toDetail(row) {
      const { id, fv4, asset } = row;
      this.$router.push({ name: 'XlInspectsForm', query: { rowId: id, fv4, omAssetID: asset.id, type: '1' }})
    },
    // 点击操作列头
    async toggleSideBar() {
      await toggleSideBarFun(this)
    },

    //   设置配置项
    setAssetConfig() {
      const { bindId, categoryId } = this.$config.xl_inspection_key
      this.crud.query.bindId = bindId
      this.crud.query.categoryId = categoryId;
      this.crud.query.fv4 = this.$route.query.fv4;
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>

//操作按钮相关
.operate-button {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.project-table-content {
	position: relative;

	.hamburger-container {
		position: absolute;
		right: 0;
		top: 0;
		z-index: 8;
		line-height: 40px;
		cursor: pointer;
		transition: background .3s;
		-webkit-tap-highlight-color: transparent;
		background: rgba(0, 0, 0, .09);

		&:hover {
			background: rgba(0, 0, 0, .19)
		}
	}
}

::v-deep .el-input-number .el-input__inner {
	text-align: left;
}

::v-deep .vue-treeselect__control, ::v-deep .vue-treeselect__placeholder, ::v-deep .vue-treeselect__single-value {
	height: 30px;
	line-height: 30px;
}

.table-fixed {
	//max-height: 760px;
	// 表格合计样式
}

</style>
