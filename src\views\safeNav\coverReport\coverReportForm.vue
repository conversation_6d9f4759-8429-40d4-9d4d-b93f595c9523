<template>
  <div class="app-container">
    <el-card class="box-card" style="margin-top: 10px">
      <div slot="header" class="clearfix">
        <span>遮挡上报</span>
      </div>
      <el-form ref="elFormRef" :model="elForm" :rules="elRules" label-width="130px" size="small">
        <!--额外的表单-->
      </el-form>
      <div v-if="showFormData" class="text item">
        <fm-generate-form
          :ref="'generateForm'"
          :data="formStruct"
          :preview="viewOrEdit"
          :remote="remoteFunc"
          :value="formData"
        >
          <template v-slot:1="{ model }">
            <el-select
              v-model="formData[1]"
              :remote-method="(query) => publicRemoteMethod(query,'1',assetRequest)"
              filterable
              placeholder="请输入路口编号"
              remote
              style="width: 50%;"
              @change="publicChange('1')"
            >
              <el-option
                v-for="item in autoPilotList"
                :key="item.id"
                :label="item.fv4"
                :value="item.fv4"
              />
            </el-select>
          </template>
          <template v-slot:2="{ model }">
            <el-select
              v-model="formData[2]"
              :remote-method="(query) => publicRemoteMethod(query,2,assetRequest)"
              filterable
              placeholder="请输入路口名称"
              remote
              style="width: 50%;"
              @change="publicChange('2')"
            >
              <el-option
                v-for="item in autoPilotList"
                :key="item.id"
                :label="item.title"
                :value="item.title"
              />
            </el-select>
            <div v-if="drawUrlArr && drawUrlArr[0]" class="draw-box">
              <el-image
                v-if="drawUrlArr[0]"
                :preview-src-list="drawUrlArr"
                :src="drawUrlArr[drawUrlArr.length - 1]"
                style="width: 100px; height: 100px"
              />
            </div>
          </template>
          <template v-slot:3="{ model }">
            <el-select
              v-model="formData[3]"
              placeholder="请输入杆体编号"
              style="width: 50%;"
            >
              <el-option
                v-for="item in poleList"
                :key="item.id"
                :label="item.fv4"
                :value="item.fv4"
              />
            </el-select>
          </template>
          <!-- <template v-slot:6="{ model }">
		<el-input
			v-model="formData[6]"
			disabled
			placeholder="路口分区"
			style="width: 100%;"
		/>
	</template> -->
        </fm-generate-form>
      </div>
      <hr style="background-color: #d9d9d9; border:0; height:1px;">
      <div class="text item" style="text-align: center;margin-top:18px">
        <el-button v-if="!viewOrEdit" :disabled="submitDisabled" type="primary" @click="submitAction">提交</el-button>
        <el-button @click="concelForm">{{ viewOrEdit == false ? '取 消' : '返回' }}</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import Vue from 'vue'
import { GenerateForm } from '@/components/VueFormMaking'
import 'form-making/dist/FormMaking.css'
import assetCrud from '@/api/parts/assets'

Vue.component(GenerateForm.name, GenerateForm)
import extendBindTpl from '@/api/system/extendBindTpl';
import { getToken } from '@/utils/auth';
import crudDictDetail from '@/api/system/dictDetail';
import crudTable from '@/api/safeNav/omAssetAffiliated'
import { allKey } from './utils/field'

export default {
  name: 'CoverReportForm',
  components: {},
  data() {
    return {
      allKey,
      elForm: {
        enabled: 1
      },
      elRules: {},
      formStruct: {},
      formData: {
        1: null,
        2: null,
        3: null
      },
      assetRequest: {
        request: assetCrud.getOmAssetAutoPilot,
        typeValues: '路口',
        configKey: 'auto_pilot3_key',
        listName: 'autoPilotList'
      },
      assetAffiliatedRequest: {
        request: crudTable.getOmAssetAutoPilot,
        typeValues: '杆体信息',
        configKey: 'integratedPoleP_key',
        listName: 'poleList'
      },
      remoteFunc: {
        funcGetToken(resolve) {
          resolve(getToken())
        },
        getDictDetail(resolve, dictName) {
          crudDictDetail.get(dictName).then(res => {
            const options = res.content
            resolve(options)
          })
        },
        getPositionDescribe(resolve) {
          // 路口方向描述 位置描述
          this.getDictDetail(resolve, 'direction_intersection');
        }
      },
      submitDisabled: false,
      viewOrEdit: false,
      showFormData: false,
      autoPilotList: [],
      poleList: [],
      drawUrlArr: []

    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      const { id, type } = this.$route.query
      this.viewOrEdit = (type == '1')
      if (id) {
        // 编辑或者查看
        this.getContent(id)
      } else {
        // 添加
        this.getProcessNodeList()
      }
    },
    getContent(id) {
      crudTable.get({ id, enabled: 1 }).then(res => {
        if (res && res.content && res.content.length) {
          const jsonData = res.content[0];
          this.processStructureValue = jsonData;
          this.formStruct = JSON.parse(jsonData.formStruct);
          this.formData = this.parseFormData(jsonData.formData);
          this.jsonData = jsonData;
          this.showFormData = true;
          this.elForm = jsonData
        }
      })
    },
    parseFormData(formDataString) {
      const parsedData = {};
      try {
        const formData = JSON.parse(formDataString);
        if (formData && typeof formData === 'object') {
          Object.entries(formData).forEach(([key, value]) => {
            if (typeof value === 'string') {
              try {
                const parsedValue = JSON.parse(value);
                parsedData[key] = Array.isArray(parsedValue) ? parsedValue : value;
              } catch (e) {
                parsedData[key] = value; // 保持原字符串
              }
            } else {
              parsedData[key] = value; // 保持原来的值
            }
          });
        } else {
          console.error('formData 不是一个有效的对象');
        }
      } catch (error) {
        console.error('解析 formData 失败', error);
      }

      return parsedData;
    },
    getProcessNodeList() {
      const data = { id: this.$config.cover_report_key.bindId, enabled: 1 }
      extendBindTpl.get(data).then(res => {
        if (res && res.content && res.content.length) {
          this.processStructureValue = res.content[0];
          const FormStruct = JSON.parse(this.processStructureValue.extendTpl.formStruct);
          this.formStruct = FormStruct;
        }
        this.showFormData = true;
      });
    },
    submitAction() {
      this.$refs['elFormRef'].validate((valid) => {
        if (!valid) {
          return false;
        } else {
          this.checkModule().then(res => {
            if (!res.flag) {
              return false;
            } else {
              const subData = { ...this.elForm, ...res.subData };
              let request = crudTable.add;
              let title = '添加'
              if (subData.id) {
                request = crudTable.edit;
                title = '编辑'
              }
              console.log(subData, '<===>', 'subData')
              request(subData).then(response => {
                this.$notify({
                  title: `${title}成功`,
                  type: 'success',
                  duration: 2500
                })
                this.concelForm();
              }).catch((e) => {
                console.log(e);
              })
            }
          })
        }
      })
    },
    async checkModule() {
      const subData = {
        bindId: this.$config.cover_report_key.bindId,
        categoryId: this.$config.cover_report_key.categoryId,
        formData: null,
        formStruct: null,
        formBindToVar: true,
        relation: this.processStructureValue.relation
      };
      if (this.showFormData) {
        const p = await this.$refs['generateForm'].getData().then(values => {
          subData.formData = JSON.stringify(values);
          subData.formStruct = JSON.stringify(this.formStruct);
          return {
            flag: true,
            subData
          };
        }).catch(() => {
          return {
            flag: false
          };
        })
        return p;
      } else {
        return Promise.resolve({
          subData,
          flag: true
        });
      }
    },
    concelForm() {
      const query = {
        bindId: this.$config.cover_report_key.bindId,
        categoryId: this.$config.cover_report_key.categoryId
      }
      this.$router.push({ name: 'CoverReport', query });
    },
    publicRemoteMethod(query, key, requestData) {
      this[requestData.listName] = [];
      const { bindId, categoryId } = this.$config[requestData.configKey]
      const data = {
        page: 0,
        size: 9999999,
        enabled: 1,
        fv4OrTitle: query,
        bindId,
        categoryId,
        'type.fieldName': 'fv1',
        'type.values': requestData.typeValues
      }
      requestData.request(data).then(res => {
        const data = res.content
        if (data.length > 0) {
          this[requestData.listName] = data;
        } else {
          this[requestData.listName] = [];
        }
      })
    },
    publicChange(key) {
      const currentKey = this.allKey[key]
      const obj = this.autoPilotList.find(item => item[currentKey.comType] === this.formData[key])
      this.formData[currentKey.target] = obj[currentKey.targetType];
      this.getDrawList();
      this.formData[3] = null;
      this.publicRemoteMethod(this.formData[1], '3', this.assetAffiliatedRequest);
    },
    getDrawList() {
      const { bindId } = this.$config.corss_draw_key;
      crudTable.getSmall({ enabled: 1, bindId: bindId, fv4OrTitle: this.formData[1] }).then(res => {
        if (res.content && res.content[0] && res.content[0].ft1) {
          const ft1Arr = JSON.parse(res.content[0].ft1);
          this.drawUrlArr = ft1Arr.map(ftItem => {
            if (ftItem?.raw?.response?.url) {
              return ftItem?.raw?.response?.url;
            } else {
              return ftItem.url;
            }
          });
        }
      })
    }

  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.draw-box {
	margin-top: 20px;
}
</style>
