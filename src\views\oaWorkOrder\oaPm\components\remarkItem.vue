<template>
  <div class="remarks-content">
    <ul class="remarks-list">
      <li v-for="(item) in oldRemarks" :key="item.id" :style="{ marginLeft:marginLeft }">
        <template v-if="item.isDelete === 0">
          <div class="remarks-time">
            <span class="remarks-label">{{ item.username }}:</span>
            <span>{{ item.currentTime }}   {{ parentInfo.username ? `回复${ parentInfo.username }` : '' }}</span>
          </div>
          <div class="remarks-detail">
            <span class="remarks-label" />
            <span class="remarks-info">{{ item.detail }}</span>
            <el-popconfirm
              cancel-button-text="取消"
              confirm-button-text="确定"
              icon="el-icon-info"
              icon-color="red"
              title="将删除本条信息和回复信息？"
              @confirm="delRemarks(item)"
            >
              <el-button
                slot="reference"
                v-permission="permission.delRemarks"
                class="reply-button"
                size="mini"
                type="danger"
              >删除
              </el-button>
            </el-popconfirm>

            <el-button
              v-permission="permission.replyRemarks"
              class="reply-button"
              size="mini"
              type="warning"
              @click="clickReplyButton(item)"
            >回复
            </el-button>
          </div>
          <template v-if="item.id == replayId">
            <div class="remark-reply">
              <div class="reply-input">
                <span class="remarks-label" />
                <el-input
                  v-model="reply.detail"
                  :autosize="{ minRows: 5, maxRows: 20}"
                  :placeholder="reply.placeholder"
                  type="textarea"
                />
              </div>
              <div class="reply-button-sec">
                <el-button
                  v-permission="permission.replyRemarks"
                  class="submit-button"
                  type="primary"
                  @click="clickSubmitReplyAction(item)"
                >
                  提交回复
                </el-button>
                <el-button class="submit-button" type="info" @click="cancleReplyAction">取消</el-button>
              </div>
            </div>
          </template>

          <template v-if="item.reply && item.reply.length>0">
            <remark-item
              :all-remarks="allRemarks"
              :indent-num="indentNumVal"
              :old-remarks="item.reply"
              :parent-info="item"
              :permission="permission"
              :project-id="projectId"
              :user="user"
            />
          </template>

        </template>
      </li>
    </ul>
  </div>
</template>

<script>
import { createID } from '@/utils';
import oaPmTree from '@/api/oaWorkOrder/oaPmTree'

export default {
  name: 'RemarkItem',
  components: {},

  props: {
    oldRemarks: {
      type: Array,
      default: () => []
    },
    permission: {
      type: Object,
      default: () => {
        return {}
      }
    },
    user: {
      type: Object,
      default: () => {
        return {}
      }
    },
    indentNum: {
      type: Number,
      default: 0
    },
    parentInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    projectId: {
      type: [String, Number],
      default: ''
    },
    allRemarks: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      reply: {
        isShow: false,
        pid: '',
        id: '',
        detail: '',
        reply: [],
        username: '',
        currentTime: '',
        isDelete: 0, // 0是默认值 1是删除了
        placeholder: '请输入回复内容'
      },
      replayId: '',
      replyDetail: ''
    }
  },
  computed: {
    indentNumVal() {
      return this.indentNum + 1;
    },
    marginLeft() {
      return (this.indentNum) * 20 + 'px';
    }

  },
  watch: {},
  created() {
  },
  methods: {
    clickReplyButton(item) {
      this.reply.isShow = true;
      this.reply.id = createID();
      this.replayId = item.id;
      this.reply.placeholder = `请输入对${item.username}的回复`;
    },
    // 点击提交回复
    clickSubmitReplyAction(item) {
      this.reply.pid = item.id;
      this.reply.username = this.user.username;
      this.reply.currentTime = this.$dayJS().format('YYYY-MM-DD HH:mm:ss');
      const obj = JSON.parse(JSON.stringify(this.reply));
      if (!item.reply) {
        item.reply = [];
      }
      item.reply.push(obj);
      this.submitReplyAction()
      this.cancleReplyAction()
    },
    // 取消回复
    cancleReplyAction() {
      this.reply.isShow = false;
      this.replayId = ''
      this.reply.detail = '';
      this.reply.placeholder = '请输入回复内容';
      this.reply.id = '';
      this.reply.username = '';
      this.reply.currentTime = '';
    },
    // 删除回复
    delRemarks(item) {
      item.isDelete = 1;
      this.commonApiFun({ title: '删除成功' })
    },
    // 提交回复
    submitReplyAction() {
      this.commonApiFun({ title: '回复成功' });
      this.cancleReplyAction()
    },
    // 提交回复统一函数  回复和删除
    async commonApiFun({ title }) {
      const data = {
        enabled: 1,
        ft1: JSON.stringify(this.allRemarks),
        id: this.projectId,
        ft2: 2
      }
      await oaPmTree.edit(data)
      this.$message({
        message: title,
        type: 'success'
      })
    }
    // editclickReplyButton(item) {
    //   console.log(item, '<===>', 'item')
    //   item.isShow = false;
    //   item.pid = 0;
    //   item.id = createID();
    //   item.reply = [];
    //   item.placeholder = `请输入的回复内容`;
    //   console.log(item, '<===>', 'item')
    //   this.commonApiFun2({ title: '修改成功' })
    // },
    // async commonApiFun2({ title }) {
    //   const data = {
    //     enabled: 1,
    //     ft1: JSON.stringify(this.allRemarks),
    //     id: this.projectId,
    //     ft2: 1
    //   }
    //   await oaPmTree.edit(data)
    //   this.$message({
    //     message: title,
    //     type: 'success'
    //   })
    // }

  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.remarks-content {
	margin-top: 20px;

	.remarks-list {
		display: flex;
		flex-direction: column;

		.remarks-label {
			display: block;
			width: 88px;
			text-align: right;
			font-size: 14px;
			margin-right: 10px;
		}

		li {
			margin-bottom: 6px;

			.remarks-time {
				display: flex;
				alice-items: center;
				margin-bottom: 10px;

			}

			.remarks-detail {
				display: flex;
				align-items: center;
				mlgin-left: 98px;

				.remarks-info {
					width: calc(100% - 98px);
					//white-space: pre-wrap;
					white-space: pre-line;
					background-color: #e8f4ff;
					border-radius: 5px;
					padding: 20px 10px;
				}

				.reply-button {
					margin-left: 10px;
					height: 30px;
					display: flex;
					align-items: flex-end;
				}

			}

			.remark-reply {

				margin-top: 10px;

				.reply-input {
					display: flex;
				}

				.reply-button-sec {
					display: flex;
					flex-direction: row-reverse
				}

			}

			.submit-button {
				margin-top: 10px;
				min-width: 80px;
				//float: right;
				text-align: center;
				margin-left: 10px;
			}

		}
	}
}
</style>
