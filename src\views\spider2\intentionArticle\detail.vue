<template>
  <div class="app-container spider-detail">
    <h2>附件信息</h2>
    <ul>
      <li v-for="(item,index) in detailInfo.formData.attachment" :key="index" @click="downLoadAttach(item)">

        <el-link type="success">{{ item.name }}</el-link>
      </li>
    </ul>
    <h2>文章地址</h2>
    <el-link type="primary" @click="goSpider()">{{ detailInfo.formData.href }}</el-link>
    <h2>意向详情信息</h2>
    <span class="content-info" v-html="detailInfo.formData.content" />
    <el-form ref="form" :model="detailInfo.formData" label-width="130px">
      <el-form-item label="采购项目名称:">
        {{ detailInfo.formData['采购项目名称'] }}
      </el-form-item>
      <el-form-item label="预算单位名称:">
        {{ detailInfo.formData['预算单位名称'] }}
      </el-form-item>
      <el-form-item label="采购需求概况:">
        {{ detailInfo.formData['采购需求概况'] }}
      </el-form-item>
      <el-form-item label="预计采购时间:">
        {{ detailInfo.formData['预计采购时间'] }}
      </el-form-item>
      <el-form-item label="预算金额（万元）:">
        {{ detailInfo.formData['预算金额（万元）'] }}
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import spiderArticleApi from '@/api/spider2/spiderArticle'
import { downloadUrl } from '@/utils/index'

export default {
  name: 'SpiderWebAdminDetail',

  data() {
    return {
      queryInfo: {},
      detailInfo: {
        formData: {
          attachment: [] // 初始化 attachment 为一个空数组，以防止未定义错误
        }
      }
    };
  },

  created() {
    this.queryInfo = this.$route.query
    this.getDetail()
  },

  methods: {
    getDetail() {
      const query = {
        id: this.queryInfo.rowId,
        enabled: 1
      }
      spiderArticleApi.getManySmall(query).then(res => {
        const data = res.content[0] || {};
        if (data) {
          this.detailInfo = data;
          this.detailInfo.formData = JSON.parse(data.formData) || {}
        }
      })
    },
    goSpider() {
      window.open(this.detailInfo.formData.href, '_blank');
    },
    downLoadAttach(item) {
      downloadUrl(item.href, item.name)
    }
  }
};
</script>

<style lang="scss" scoped>
.spider-detail {
	padding-left: 40px;

	.content-info {
		white-space: pre-wrap;
		white-space: -moz-pre-wrap;
		white-space: -pre-wrap;
		white-space: -o-pre-wrap;
		*word-wrap: break-word;
		*white-space: normal;
	}

	h2 {
		font-weight: 700;
		color: #000;
		font-size: 20px;
		line-height: 50px;
		position: relative;
		padding-left: 20px;

		&::after {
			content: '';
			position: absolute;
			left: 0;
			top: 10px;
			width: 4px;
			height: 30px;
			background: #374C86;
		}

	}
}
</style>
