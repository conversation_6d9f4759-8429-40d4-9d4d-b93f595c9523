<template>
  <div class="app-container map-box">
    <mapContainer ref="map-container" />
    <markerList ref="marker-list" :map="map" />
  </div>
</template>

<script>
import markerList from './components/marker'
import mapContainer from '@/components/map'
import Bus from '@/utils/bus';
export default {
  name: 'ReportMap',
  components: {
    mapContainer,
    markerList
  },
  data() {
    return {
      map: {}
    }
  },
  mounted() {
    Bus.$on('sendBybus', data => {
      this.map = data;
    });
  }
}
</script>

<style scoped>
#mapContainer {
  height: calc(100vh - 150px);
}
.map-box{
  position: relative;
}
.search-box{
  position: absolute;
  width:100%;
  left:20px;
  top:20px;
  z-index: 2000;
  padding: 10px 0 0 10px;
}
</style>
