<template>
  <div>
    <!-- 显示输入框数组 -->
    <el-form-item :label="labelInfo">
      <el-button type="primary" class="add-buton" @click="addInput">添加</el-button>
      <div v-for="(item, index) in arrValue" :key="index" class="add-item">
        <el-input v-model="arrValue[index]" style="width: 60%;" :placeholder="tipInfo" />
        <el-button class="del-buton" type="text" @click="removeInput(index)">删除</el-button>
      </div>
    </el-form-item>
    <!-- 添加按钮 -->

  </div>
</template>

<script>
export default {
  props: {
    arrValue: {
      type: Array,
      default: () => {
        return []
      }
    },
    labelInfo: {
      type: String,
      default: ''
    },
    tipInfo: {
      type: String,
      default: ''
    }
  },
  methods: {
    // 添加输入框
    addInput() {
      this.arrValue.push('');
    },
    // 删除输入框
    removeInput(index) {
      this.arrValue.splice(index, 1);
    }
  }
};
</script>
<style lang="scss" scoped>
.add-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  &:last-child{
    margin-bottom: 0;
  }
  .del-buton {
    margin-left: 20px;
  }

}
.add-buton {
    margin-bottom: 10px;
}
</style>
