<template>
  <div
    class="app-container oa-pm"
    element-loading-spinner="el-icon-loading"
    element-loading-text="更新中"
  >
    <!--工具栏-->
    <div class="head-container">
      <el-button type="success">导入</el-button>
    </div>
    <!--表格渲染-->
    <div ref="projectBody" class="project-table-content">
      <el-table
        ref="table"
        :data="tableData"
        lazy
        row-key="id"
      >
        <el-table-column
          v-for="(header, initIndex) in tableHeaders"
          :key="header.label+initIndex"
          :align="header.align"
          :fixed="header.fixed"
          :label="header.label"
          :prop="header.prop"
          :width="header.width"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <see-cell
              :current-cell="scope"
              :header="header"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <el-pagination
        :page-size="pageSize"
        :pager-count="11"
        :total="zhichu.length"
        layout="prev, pager, next"
        @current-change="handleCurrentChange"
      />
    </div>

    <!--导入项目-->
    <upload-file ref="refUploadFile" @getlist="crud.toQuery()" />
  </div>
</template>

<script>
import UploadFile from './components/uploadFile.vue'
import SeeCell from './components/seeCell.vue';
import { mapGetters } from 'vuex'
import {
  formatterTableHeader
} from './utils/commonFun'
import { shouru, zhichu } from './utils/data';

export default {
  name: 'Storeroom',
  components: {
    UploadFile,
    SeeCell
  },
  dicts: [],
  data() {
    return {
      tableData: [],
      tableHeaders: [],
      shouru,
      zhichu,
      currentPage: 1,
      pageSize: 10 // 每页显示条数
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  async created() {
    this.tableData = [...this.getElementsByNumber(this.currentPage)];
    this.tableHeaders = formatterTableHeader(this.zhichu)
  },
  methods: {
    handleCurrentChange(val) {
      this.currentPage = val; // 更新当前页码
      this.tableData = [...this.getElementsByNumber(val)]; // 根据页码获取新数据
    },
    getElementsByNumber(n) {
      const start = this.pageSize * (n - 1); // 计算起始索引
      const end = this.pageSize * n; // 计算结束索引
      const data = this.zhichu.slice(start, end);
      console.log(data, '<===>', 'data')
      return data // 使用slice方法取出元素
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
//操作按钮相关
.operate-button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;

}

.project-table-content {
  position: relative;

  .hamburger-container {
    position: absolute;
    right: 0;
    top: 0;
    z-index: 8;
    line-height: 50px;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color: transparent;
    background: rgba(0, 0, 0, .09);

    &:hover {
      background: rgba(0, 0, 0, .19)
    }
  }
}

::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

::v-deep .vue-treeselect__control, ::v-deep .vue-treeselect__placeholder, ::v-deep .vue-treeselect__single-value {
  height: 30px;
  line-height: 30px;
}

.info-vxe-toolbar {
  padding: 0 !important;
  width: 28px !important;
  height: 28px !important;

  .vxe-button.vxe-tools--operate.type--button {
    min-width: 28px !important;
    height: 28px !important;
    padding: 0px 12px !important;

  }

  .vxe-tools--operate.is--circle {
    border-radius: 0 !important;
  }

}

.custom-filter {
  padding: 8px;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
}
.filter-select-dropdown {
  z-index: 999999 !important;
}
.my-filter {
  padding: 12px;
  .filter-footer {
    margin-top: 12px;
    text-align: right;
    > button {
      margin-left: 8px;
    }
  }
}
.custom-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 4px;

  .el-icon-arrow-down {
    cursor: pointer;
    padding: 0 4px;
    font-size: 16px;
    color: #909399;
    transition: transform .3s;

    &:hover {
      color: #409EFF;
    }

    &.is-active {
      color: #409EFF;
      transform: rotate(180deg);
    }
  }
}

.filter-content {
  padding: 12px;
  .filter-footer {
    margin-top: 12px;
    text-align: right;
    > button {
      margin-left: 8px;
    }
  }
}
</style>

<style lang="scss">
.filter-popover {
  min-width: 240px !important;
}
</style>
