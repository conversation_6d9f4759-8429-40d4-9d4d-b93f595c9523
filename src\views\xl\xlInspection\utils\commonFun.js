import { tableHeader } from './field'
import crudDictDetail from '@/api/system/dictDetail'

/**
 * 格式化表格数据
 * @param {Array} val - 表格数据数组
 */
export function formatterTableData(val) {
  if (val && val.length) {
    return val.map(item => {
      const json = item.extend.data || {};

      const dynamicFields = {};
      if (item.ft9) {
        try {
          const arr = JSON.parse(item.ft9);
          if (Array.isArray(arr)) {
            arr.forEach(field => {
              const fieldName = field.fv5 + 'number';
              dynamicFields[fieldName] = field.fv7 || ''; // 处理动态字段
            });
          }
        } catch (error) {
          console.error('Failed to parse ft9:', error);
        }
      }

      return {
        ...json,
        ...item,
        ...dynamicFields
      };
    });
  } else {
    return [];
  }
}

/**
 * 格式化表头
 * @param {Array} val - 表格数据数组
 */
export async function formatterTableHeader(val) {
  const arrHeader = [];

  // 获取所有的数量和标签
  const response = await crudDictDetail.get('xl_camera_type');
  const options = response.content;

  // 初始化一个 Set 来收集所有出现过的动态字段
  const allFieldsSet = new Set();

  // 遍历 val 数组中的每个元素，收集所有出现过的动态字段
  val.forEach(item => {
    if (item.ft9) {
      try {
        const arr = JSON.parse(item.ft9);
        if (Array.isArray(arr)) {
          arr.forEach(field => {
            allFieldsSet.add(field.fv5);
          });
        }
      } catch (error) {
        console.error('Failed to parse ft9:', error);
      }
    }
  });

  // 构建 arrHeader，确保标签和动态字段匹配
  allFieldsSet.forEach(fieldName => {
    const matchingOption = options.find(option => option.value === fieldName);
    if (matchingOption) {
      arrHeader.push({ label: matchingOption.label, prop: fieldName + 'number', width: 120, align: 'left' });
    }
  });
  // 找到 '总摄像头数' 这一项的索引
  const insertIndex = tableHeader.findIndex(header => header.label === '总摄像头数') + 1 || tableHeader.length;

  // 在指定位置插入 arrHeader
  const updatedTableHeader = [
    ...tableHeader.slice(0, insertIndex),
    ...arrHeader,
    ...tableHeader.slice(insertIndex)
  ];
  return updatedTableHeader;
}

/**
 * 点击是否显示操作列
 * @param vueInstance
 * @returns {Promise<void>}
 */
export async function toggleSideBarFun(vueInstance) {
  vueInstance.operateShow = !vueInstance.operateShow;
  await vueInstance.$nextTick()
  vueInstance.$refs.table.doLayout()
}

export function mergeCell({ row, column, rowIndex, columnIndex, tableData }) {
  if (columnIndex === 1 || columnIndex === 2 || columnIndex === 3) {
    const arr = getSpanArray(tableData, 'fv4');
    return getSpan(arr, rowIndex);
  } else if (columnIndex === 4 || columnIndex === 5) {
    const arr = getSpanArray(tableData, 'fv4', 'fv5');
    return getSpan(arr, rowIndex);
  }
  return { rowspan: 1, colspan: 1 };
}

function getSpanArray(list, key1, key2) {
  const spanArray = [];
  for (let i = 0; i < list.length; i++) {
    if (i === 0) {
      spanArray.push({ row: 1, col: 1 });
    } else {
      const isSame = key2
        ? list[i][key1] === list[i - 1][key1] && list[i][key2] === list[i - 1][key2]
        : list[i][key1] === list[i - 1][key1];
      if (isSame) {
        spanArray.push({ row: 0, col: 0 });
        const index = spanArray.findIndex((_, idx) => list[idx][key1] === list[i][key1] && (!key2 || list[idx][key2] === list[i][key2]));
        spanArray[index].row++;
      } else {
        spanArray.push({ row: 1, col: 1 });
      }
    }
  }
  return spanArray;
}

function getSpan(arr, rowIndex) {
  return {
    rowspan: arr[rowIndex].row,
    colspan: arr[rowIndex].col
  };
}
