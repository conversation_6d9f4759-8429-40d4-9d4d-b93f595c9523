<template>
  <div class="category-selector">
    <div class="category-group">
      <template v-for="(item,index) in categoryList">
        <div :key="item.fv10">
          <el-checkbox v-model="item.isCheckedAll" @change="(val)=>handleCheckAllChange(val, item)">
            <span>
              {{ item.fv10 }}
              <!--<template v-if="item.tag && item.tag.length">-->
              <!--  <i :class="item.isOpen ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" />-->
              <!--</template>-->
            </span>

            <!--<i class="el-icon-arrow-up" />-->
          </el-checkbox>
        </div>
        <div :key="item.fv10 + index" :style="{ paddingLeft: '10px' }">
          <el-checkbox
            v-for="subItem in item.tag"
            :key="subItem.uid"
            v-model="subItem.isChecked"
            :label="subItem.uid"
            @change="()=>handleSubCategoryClick(item,subItem)"
          >
            <span>
              {{ subItem.fv11 }}
            </span>
          </el-checkbox>
        </div>

      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CategorySelector',
  props: {
    tagItem: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      categoryList: [],
      currentGroupName: '',
      currentSelectTag: [],
      noEmit: false
    };
  },
  watch: {
    currentSelectTag: {
      handler(val) {
        this.editInitTags(val)
        this.$emit('updateSelectedTags', this.currentSelectTag, this.currentGroupName)
      },
      deep: false
    }
  },
  created() {
    this.initData();
  },
  methods: {
    editInitTags(tag) {
      const tagLookup = new Set(tag.map(t => t.uid));
      this.categoryList = this.categoryList.map(item => {
        // 使用every方法检查item.tag数组是否全部选中
        const isCheckedAll = item.tag.every(subItem => tagLookup.has(subItem.uid));

        // 更新tag数组，设置isChecked属性
        const updatedTag = item.tag.map(subItem => ({
          ...subItem,
          isChecked: tagLookup.has(subItem.uid)
        }));

        // 返回更新后的item对象
        return {
          ...item,
          tag: updatedTag,
          isCheckedAll: isCheckedAll
        };
      });
    },
    initData() {
      const { detail, fv1 } = this.tagItem;
      this.currentGroupName = fv1;
      this.categoryList = detail;
    },
    handleSubCategoryClick(item, subItem) {
      item.isCheckedAll = item.tag.every(subItem => subItem.isChecked);
      this.updateTags(subItem)
    },
    handleCheckAllChange(val, item) {
      item.isCheckedAll = val;
      item.tag = item.tag.map(tag => {
        tag.isChecked = val;
        return tag;
      });
      if (val) {
        //   把item.tag 全加入
        item.tag.forEach(subItem => {
          if (!this.currentSelectTag.some(tag => tag.uid === subItem.uid)) {
            this.currentSelectTag.push(subItem);
          }
        });
      } else {
        // 创建一个数组来存储所有需要移除的uid
        const uidToRemove = item.tag.map(subItem => subItem.uid);
        // 遍历currentSelectTag，并移除那些uid在uidsToRemove中的元素
        this.currentSelectTag = this.currentSelectTag.filter(tag => {
          // 检查tag的uid是否在uidsToRemove中
          return uidToRemove.indexOf(tag.uid) === -1;
        });
      }
    },
    updateTags(subItem) {
      const index = this.currentSelectTag.findIndex(item => item.uid === subItem.uid);
      if (index !== -1) {
        // 如果找到匹配项，currentSelectTag
        this.currentSelectTag.splice(index, 1);
      } else {
        // 如果没有找到匹配项且shouldAdd为true，则添加
        this.currentSelectTag.push(subItem);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.category-selector {
	.category-group {
		::v-deep.el-checkbox {

			span {
				padding-left: 10px;
				color: #606266;
				white-space: normal;
			}
		}
	}
}

</style>
