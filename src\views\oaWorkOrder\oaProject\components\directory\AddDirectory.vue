<template>
  <div>
    <el-dialog
      :before-close="resetModule"
      :close-on-click-modal="false"
      :title="type==1 || type==2 ? '新建目录' : '编辑目录'"
      :visible.sync="visible"
      append-to-body
      width="550px"
    >
      <el-form ref="ruleForm" :model="projectForm" :rules="rules" inline label-width="130px" size="small">
        <el-form-item label="目录名称" prop="name">
          <el-input v-model="projectForm.name" style="width: 350px;" />
        </el-form-item>
        <el-form-item label="目录类型:" prop="ft2">
          <el-select
            v-model="projectForm.ft2"
            class="filter-item"
            clearable
            filterable
            placeholder="请选择目录类型"
            size="small"
            style="width: 350px;"
          >
            <el-option
              v-for="item in dict.directory_classify"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="目录排序" prop="sort">
          <el-input-number
            v-model="projectForm.sort"
            :max="99999999"
            :min="0"
            class="number-input-left"
            controls-position="right"
            style="width: 350px;"
          />
        </el-form-item>
        <el-form-item label="索引" prop="fv16">
          <el-input v-model.trim="projectForm.fv16" placeholder="索引格式1.1.1" style="width: 350px;" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="resetModule">取消</el-button>
        <el-button type="primary" @click="submitAction">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import oaPmTree from '@/api/oaWorkOrder/oaPmTree'
import { mapGetters } from 'vuex'
import extendBindTpl from '@/api/system/extendBindTpl';

export default {
  name: 'AddDirectory',
  dicts: ['directory_classify'],
  data() {
    const isValidVersion = (version) => {
      const versionPattern = /^\d+(\.\d+)*$/;
      return versionPattern.test(version);
    };
    const validFv16 = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入正确的索引'))
      } else if (!isValidVersion(value)) {
        callback(new Error('请输入正确索引'))
      } else {
        callback()
      }
    };
    return {
      projectForm: {
        fv18: '',
        name: null,
        fv16: '', // 索引
        sort: 1,
        enabled: '1',
        fv1: '目录',
        pid: null,
        bindId: null,
        ft2: null
      },
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请输入序号', trigger: 'blur', type: 'number' }
        ],
        fv16: [
          { required: true, message: '请输入索引', trigger: 'blur' },
          { required: true, message: '请输入正确索引', trigger: 'change', validator: validFv16 }
        ]
      },
      showFormData: false,
      jsonData: {},
      formStruct: {},
      formData: {},
      visible: false,
      bindId: '',
      params: {},
      type: 1 // 1 添加 2 编辑
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  methods: {
    init(params) {
      this.visible = true;
      this.params = params;
      const { id, type, pid, fv18 } = params;
      const { bindId } = this.$config.task_keys;
      this.bindId = bindId;
      this.type = type;
      this.projectForm.pid = pid
      this.projectForm.fv18 = fv18;
      this.getProcessNodeList(bindId);
      if (type == 3) {
        // 编辑
        this.getDetail(id);
      }
    },

    // 获取模板信息
    getProcessNodeList(id) {
      const data = { id, enabled: 1 }
      extendBindTpl.get(data).then(res => {
        if (res && res.content && res.content.length) {
          this.processStructureValue = res.content[0];
          const FormStruct = JSON.parse(this.processStructureValue.extendTpl.formStruct);
          this.formStruct = FormStruct;
        }
      });
    },
    // 获取-详情
    getDetail(rowId) {
      if (rowId) {
        oaPmTree.getPmTree({ id: rowId, enabled: 1 }).then(res => {
          if (res && res.content && res.content[0]) {
            const jsonData = res.content[0];
            this.projectForm = jsonData

            // this.processStructureValue = res.content[0];
            // const FormStruct = JSON.parse(this.processStructureValue.extendTpl.formStruct);
            // this.formStruct = FormStruct;
          }
        });
      }
    },
    // 关闭弹框
    resetModule() {
      this.$refs.ruleForm.resetFields();
      this.visible = false;
      setTimeout(() => {
        this.$emit('success', { ...this.params });
      }, 300)
    },
    submitAction() {
      this.$refs['ruleForm'].validate((valid) => {
        if (!valid) {
          return false;
        } else {
          this.checkModule().then(res => {
            if (!res.flag) {
              return false;
            } else {
              const subData = { ...this.projectForm, ...res.subData };
              let request = oaPmTree.add;
              if (subData.id) {
                request = oaPmTree.edit;
              } else {
                const items = [
                  {
                    user: { id: this.user.id, enabled: 1 },
                    enabled: 1
                  }
                ];
                subData['items'] = items
              }
              request(subData).then(response => {
                this.$notify({
                  title: '操作成功',
                  type: 'success',
                  duration: 2500
                })
                this.resetModule();
              }).catch((e) => {
                console.log(e);
              })
            }
          })
        }
      })
    },
    async checkModule() {
      const subData = {
        bindId: this.bindId,
        formData: null,
        formStruct: null,
        formBindToVar: 0,
        relation: this.processStructureValue.relation
      };
      if (this.showFormData) {
        const p = await this.$refs['generateForm'].getData().then(values => {
          subData.formData = JSON.stringify(values);
          subData.formStruct = JSON.stringify(this.formStruct);
          return {
            flag: true,
            subData
          };
        }).catch(() => {
          return {
            flag: false
          };
        })
        return p;
      } else {
        subData.formStruct = JSON.stringify(this.formStruct);
        return Promise.resolve({
          subData,
          flag: true
        });
      }
    }

  }
}

</script>

<style scoped>
.number-input-left {
	& .el-input__inner {
		text-align: left;
	}
}
</style>
