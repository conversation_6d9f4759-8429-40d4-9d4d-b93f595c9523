<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input
          v-model="query.title"
          class="filter-item"
          clearable
          placeholder="输入路口名称搜索"
          size="small"
          style="width: 200px;"
        />
        <el-select
          v-model="query.status"
          class="filter-item"
          clearable
          placeholder="请选择巡检结果"
          size="small"
          style="width: 200px"
        >
          <el-option
            v-for="item in dict.inspects_results"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-input
          v-model="query.ft3"
          class="filter-item"
          clearable
          placeholder="输入情况描述搜索"
          size="small"
          style="width: 200px;"
        />
        <!-- <date-range-picker v-model="query.createTime" class="date-item" /> -->
        <rrOperation>
          <el-button
            slot="right"
            class="filter-item"
            icon="el-icon-eleme"
            size="mini"
            type="primary"
            @click="preView"
          >
            预览
          </el-button>
          <el-button
            slot="right"
            class="filter-item"
            icon="el-icon-download"
            size="mini"
            type="primary"
            @click="downLoad"
          >
            导出
          </el-button>
        </rrOperation>
      </div>
      <crudOperation :permission="permission">
        <el-button
          slot="left"
          v-permission="permission.add"
          class="filter-item"
          icon="el-icon-plus"
          size="mini"
          type="primary"
          @click="addTable"
        >
          新建
        </el-button>
        <el-button
          slot="left"
          v-permission="permission.upload"
          class="filter-item"
          icon="el-icon-plus"
          size="mini"
          type="success"
          @click="addFile"
        >上传
        </el-button>
        <update-button
          v-if="bindId"
          slot="left"
          :bind-id="bindId"
          :enabled="[1]"
          :permission="permission"
        />
      </crudOperation>
    </div>
    <el-table
      ref="table"
      v-loading="crud.loading"
      :data="tableData"
      row-key="id"
      @select="crud.selectChange"
      @select-all="crud.selectAllChange"
      @selection-change="crud.selectionChangeHandler"
    >
      <el-table-column
        v-for="item in tableHeader"
        :key="item.prop"
        :align="item.align || 'center'"
        :label="item.label"
        :prop="item.prop"
        :show-overflow-tooltip="true"
        :width="item.width || '180'"
      >
        <template slot-scope="scope">
          <template v-if="item.prop.indexOf('img') !='-1'">
            <template v-for="(item,index) in scope.row[item.prop]">
              <el-image
                v-if="index == 0"
                :key="item.id"
                :preview-src-list="[item.url]"
                :src="item.url"
                class="el-avatar"
                fit="contain"
                lazy
              >
                <div slot="error">
                  <i class="el-icon-document" />
                </div>
              </el-image>
            </template>

          </template>
          <template v-else-if="item.prop =='fv20'">
            <span class="table-colume-title" @click="detail(scope.row)">{{ scope.row[item.prop] }}</span>
          </template>
          <template v-else-if="item.prop =='fv18'">
            <span class="table-colume-title" @click="goJunctionList2(scope.row)">{{ scope.row[item.prop] }}</span>
          </template>
          <span v-else>{{ scope.row[item.prop] }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="250">
        <template slot-scope="scope">
          <el-button size="mini" type="success" @click="toInspects(scope.row)">巡检记录</el-button>
          <el-button
            v-permission="permission.edit"
            size="mini"
            style="margin-left:0;"
            type="primary"
            @click="editItem(scope.row)"
          >编辑
          </el-button>
          <el-popconfirm
            :hide-icon="true"
            cancel-button-text="取消"
            confirm-button-text="确认"
            icon-color="red"
            title="确认要删除该条数据？"
            @confirm="deleteItem(scope.row)"
          >
            <el-button slot="reference" v-permission="permission.del" size="mini" type="danger">删除</el-button>
          </el-popconfirm>
          <!-- <el-button type="success" size="mini" @click="detail(scope.row)">查看</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination />
    <uploadExcel ref="uploadExcel" @getlist="crud.refresh()" />
  </div>
</template>

<script>
import uploadExcel from './components/uploadExcel'
import crudTable from '@/api/parts/assets'
import CRUD, { presenter, header, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import { getConfig } from '@/utils/getConfigData.js'
// import DateRangePicker from '@/components/DateRangePicker'
import { downloadUrl } from '@/utils/index'
import updateButton from '@/components/UpdateButton/index'
import { mapGetters } from 'vuex'

export default {
  name: 'JunctionList',
  components: { crudOperation, rrOperation, pagination, uploadExcel, updateButton },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: false,
      title: '资产管理',
      url: 'api/omAsset/small',
      query: { enabled: 1 },
      crudMethod: { ...crudTable },
      optShow: {
        add: false, edit: false, del: false, download: false, reset: true
      }
    })
  },
  mixins: [presenter(), header(), crud()],
  dicts: ['inspects_results'],
  data() {
    return {
      nofistLoad: false,
      tableData: [],
      tableHeader: [],
      permission: {
        add: ['admin', 'omAsset:add'],
        edit: ['admin', 'omAsset:edit'],
        del: ['admin', 'omAsset:del'],
        upload: ['admin', 'omAsset:importXlsWithRule'],
        updateT: ['admin', 'omAsset:updateFormStruct'],
        updateR: ['admin', 'omAsset:updateRelation'],
        updateG: ['admin', 'omAsset:toRedisGeoIndex']
      },
      props: {
        value: 'id',
        label: 'label',
        emitPath: false
      },
      bindId: '',
      inspectBindID: ''
    }
  },
  computed: {
    ...mapGetters([
      'baseApi',
      'omAssetAPi'
    ])
  },
  watch: {
    'crud.data': {
      handler(val, oldVal) {
        this.formatterTableHeader(val)
        this.formatterTableData(val)
      },
      deep: true
    }
  },
  created() {
    this.getConfigData();
  },
  activated() {
    this.crud.toQuery();
    // if (this.nofistLoad) {
    //   console.log(5555, '<===>', '5555')
    //   this.crud.toQuery();
    // }
    // this.nofistLoad = false
  },
  methods: {
    [CRUD.HOOK.beforeRefresh]() {
      this.crud.query.bindId = this.$route.query.id;
      this.crud.query.categoryId = this.$route.query.category;
      if (this.$route.query.title) {
        this.$set(this.crud.query, 'title', this.$route.query.title);
      }

      this.bindId = this.$route.query.id;
    },
    getTime() {
      if (this.query.createTime) {
        return this.query.createTime
      }
      const end = `${this.$dayJS().format('YYYY-MM-DD')} 12:00:00`
      const start = `${this.$dayJS().subtract(1, 'day').format('YYYY-MM-DD')} 12:00:00`
      return [start, end]
    },
    preView() {
      console.log(this.omAssetAPi);
      // const params = qs.stringify(data, { indices: false })
      // const params = `page=0&size=99999&createTime=${this.getTime()[0]}&createTime=${this.getTime()[1]}&status=${this.query.status || ''}&enabled=1&ft3=${this.query.ft3}&title=${this.query.title}`
      const params = `page=0&size=99999&enabled=1&asset.categoryId=${this.$route.query.category}`
      const url = `${this.omAssetAPi}/asset/html?${params}`;
      window.open(url)
    },
    downLoad() {
      const params = `page=0&size=99999&enabled=1&asset.categoryId=${this.$route.query.category}`
      // const params = `page=0&size=99999&createTime=${this.getTime()[0]}&createTime=${this.getTime()[1]}&status=${this.query.status || ''}&enabled=1&ft3=${this.query.ft3 || ''}&title=${this.query.title || ''}&fileName=路口信息`
      const url = `${this.omAssetAPi}/asset/xls?${params}&fileName=路口基础信息`;
      downloadUrl(url)
    },
    // 获取巡检记录的绑定ID
    getConfigData() {
      const data = {
        key: 'inspects_list'
      }
      getConfig(data).then(res => {
        this.inspectBindID = res.extend.data.bindId;
      })
    },
    // 点击巡检记录
    toInspects(row) {
      this.$router.push({
        name: 'Inspects',
        query: {
          id: this.inspectBindID,
          omAssetID: row.id,
          omAssetTitle: row.title,
          category: this.$route.query.category
        }
      })
    },
    addFile() {
      this.$refs.uploadExcel.init();
    },
    addTable() {
      this.$router.push({
        name: 'JunctionCreate',
        query: { id: this.$route.query.id, category: this.$route.query.category }
      });
      this.nofistLoad = true;
    },
    editItem(row) {
      this.$router.push({
        name: 'JunctionCreate',
        query: { id: this.$route.query.id, rowId: row.id, type: 'edit', category: this.$route.query.category }
      });
      this.nofistLoad = true;
    },
    detail(row) {
      this.$router.push({ name: 'JunctionCreate', query: { id: this.$route.query.id, rowId: row.id, type: 'see' }})
    },
    deleteItem(row) {
      crudTable.del([row.id]).then(response => {
        this.crud.refresh();
        this.$notify({
          title: '数据已删除',
          type: 'success',
          duration: 2500
        })
      })
    },
    getJson(item) {
      const json = {}
      item.forEach(i => {
        for (const name in i) {
          json[name] = i[name];
        }
      })
      return json;
    },
    getArr(item) {
      const arr = [];
      item.forEach(i => {
        i.forEach(j => {
          arr.push(j)
        })
      })
      return arr;
    },
    formatterTableData(val) {
      if (!val && !val.length) {
        this.tableData = [];
        return
      }
      const mapItem = (item) => {
        const baseJson = {
          id: item.id,
          title: item.title,
          createBy: item.createBy,
          createTime: item.createTime,
          fv20: item.fv20,
          fv19: item.fv19,
          fv18: item.fv18,
          ...item.extend.data
        };

        return {
          ...baseJson
        };
      };
      this.tableData = val.map(mapItem);
    },
    formatterTableHeader(val) {
      const tableHeader = [
        { prop: 'fv20', label: '路口(新)', align: 'left', fixed: 'left' },
        { prop: 'fv19', label: '路口(802)' },
        { prop: 'fv18', label: '编号' },
        { prop: '3', label: '状态' },
        { prop: '6', label: '路口类型' },
        { prop: '7', label: '路口形状' },
        { prop: '8', label: '六环内/外' },
        { prop: '13', label: '所属支队' }
      ]
      const otherHeader = [];
      this.tableHeader = [...tableHeader, ...otherHeader];
    },
    goJunctionList2(data) {
      const junctionList2 = this.$config.Junction_list2;
      const query = {
        id: String(junctionList2.bindId),
        category: junctionList2.categoryId,
        formData: data.fv18
      }
      this.$router.push({ name: 'JunctionList2', query })
    }
  }
}
</script>
<style lang="scss" rel="stylesheet/scss" scoped>
.tableImg {
	width: 50px;
	height: 50px;
}

.table-colume-title {
	cursor: pointer;
	color: #2476F8;
}
</style>
