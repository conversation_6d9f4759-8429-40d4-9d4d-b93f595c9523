<!--信息公共-->
<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--侧边部门数据-->
      <el-col :lg="3" :md="3" :sm="3" :xl="3" :xs="3">
        <div class="head-container">
          <el-input
            v-model="searchKey"
            class="filter-item"
            clearable
            placeholder="输入分类名称搜索"
            prefix-icon="el-icon-search"
            size="small"
            @input="getclassifyDatas"
          />
        </div>
        <el-tree
          :data="classifyDatas"
          :expand-on-click-node="false"
          :load="getclassifyDatas"
          :props="defaultProps"
          lazy
          @node-click="handleNodeClick"
        />
      </el-col>
      <el-col :lg="21" :md="21" :sm="21" :xl="21" :xs="21">
        <div class="head-container">
          <e-header :permission="permission" />
          <crudOperation :permission="permission">
            <update-button
              slot="right"
              :bind-id="bindId"
              :enabled="[1]"
              :permission="permission"
            />
          </crudOperation>
        </div>
        <!-- 表格 -->
        <div class="body-box">
          <el-table
            ref="table"
            v-loading="crud.loading"
            :data="tableData"
            :header-cell-style="{'text-align':'center'}"
            style="width: 100%;"
            @select="crud.selectChange"
            @select-all="crud.selectAllChange"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column :fixed="true" type="selection" width="55" />
            <el-table-column
              v-for="item in tableHeader"
              :key="item.prop"
              :align="item.align || 'center'"
              :fixed="item.fixed || false"
              :label="item.label"
              :prop="item.prop"
              :show-overflow-tooltip="true"
              :sortable="item.sortable || ''"
              :width="item.width"
            >
              <template slot-scope="scope">
                <template v-if="item.label == '预览图'">
                  <el-image
                    :key="item.id"
                    :preview-src-list="[scope.row.ft1[0].url]"
                    :src="scope.row.ft1[0].url"
                    class="el-avatar"
                    fit="contain"
                    lazy
                  >
                    <div slot="error">
                      <i class="el-icon-document" />
                    </div>
                  </el-image>
                </template>
                <template v-else-if="item.label == '状态'">
                  <el-tag :type="scope.row.status == '发布' ? 'success' :'info'">{{ scope.row.status }}</el-tag>
                </template>
                <span v-else>{{ scope.row[item.prop] }}</span>
              </template>
            </el-table-column>
            <!--   编辑与删除   -->
            <el-table-column
              align="center"
              fixed="right"
              label="操作"
              width="240"
            >
              <template slot-scope="scope">
                <udOperation
                  :data="scope.row"
                  :permission="permission"
                />
                <el-button type="success" @click="geDetail(scope.row)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import spiderArticleApi from '@/api/spider2/spiderArticle'
import eHeader from './module/header'
import updateButton from '@/components/UpdateButton/index'
import CRUD, { presenter, form } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import udOperation from '@crud/UD.operation'
import { getCategory } from '@/api/system/category'
import { header } from '@crud/crud'
import { cleanValue } from '@/utils/index'

const defaultForm = { id: null }
export default {
  name: 'IntentionArticle',
  components: { eHeader, crudOperation, pagination, updateButton, udOperation },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: true,
      title: '文章信息',
      url: 'api/spiderArticle/small',
      sort: ['createTime,asc'],
      query: { enabled: 1, categoryId: null },
      crudMethod: { ...spiderArticleApi },
      optShow: {
        add: false,
        edit: false,
        del: true,
        download: false,
        reset: true,
        rightGroup: false
      }
    })
  },
  mixins: [presenter(), form(defaultForm), header()],
  data() {
    return {
      permission: {
        add: ['admin', 'spiderArticle:add'],
        // edit: ['admin', 'spiderArticle:edit'],
        del: ['admin', 'spiderArticle:del'],
        updateT: ['admin', 'spiderArticle:updateFormStruct'],
        updateR: ['admin', 'spiderArticle:updateRelation']
      },
      tableData: [],
      tableHeader: [],
      bindId: '',
      defaultProps: { children: 'children', label: 'name', isLeaf: 'leaf' },
      classifyDatas: [],
      searchKey: ''
    };
  },
  watch: {
    'crud.data': {
      handler(val, oldVal) {
        if (val && val.length && val.length > 0) {
          const newVal = val;
          let tableData = [];
          let tableHeader = []
          tableHeader = [
            { prop: 'item_id', label: '项目编号', align: 'center', sortable: true },
            { prop: 'item_name', label: '项目名称', align: 'left', sortable: true },
            { prop: 'item_amount', label: '预算（万元）', align: 'right', sortable: true },
            { prop: 'item_max', label: '最高限价（万元）', align: 'right', sortable: true },
            { prop: '文章类型', label: '文章类型', align: 'center', sortable: true, width: 150 },
            { prop: '发布时间', label: '发布时间', align: 'center', sortable: true, width: 150 }
          ];
          this.tableHeader = tableHeader;
          tableData = newVal.map(item => {
            const json = JSON.parse(item.formData) || {};
            json.id = item.id;
            json.createBy = item.createBy;
            json.createTime = item.createTime;
            json.item_amount = cleanValue(json.item_amount);
            json.item_max = cleanValue(json.item_max);
            return json;
          });
          this.tableData = tableData;
        } else {
          this.tableHeader = [];
          this.tableData = [];
        }
      },
      deep: true
    }
  },
  methods: {
    geDetail(data) {
      const query = {
        id: this.bindId,
        rowId: data.id
      }
      this.$router.push({ name: 'SpiderArticleDetail', query });
    },
    [CRUD.HOOK.beforeRefresh]() {
      const { id } = this.$route.query
      this.bindId = id;
      this.crud.query.bindId = id;
    },
    // 获取左侧分类数据
    getclassifyDatas(node, resolve) {
      const sort = 'id,desc'
      const params = { sort: sort, enabled: 1 }
      if (typeof node !== 'object') {
        if (node) {
          params['name'] = node
        }
      } else if (node.level !== 0) {
        params['pid'] = node.data.id
      }
      if (node.level === 0 || !node) {
        const { pid } = this.$route.query
        params['pid'] = pid
      }
      setTimeout(() => {
        getCategory(params).then(res => {
          if (resolve) {
            resolve(res.content)
          } else {
            this.classifyDatas = res.content
          }
        })
      }, 100)
    },
    // 切换分类
    handleNodeClick(data) {
      if (data.pid === 0) {
        this.query.categoryId = null
      } else {
        this.query.categoryId = data.id
      }
      this.crud.toQuery()
    }
    // [CRUD.HOOK.beforeToAdd]() {
    //   const query = {
    //     id: this.bindId
    //   }
    //   this.$router.push({ name: 'AddBanner', query });
    // },
    // [CRUD.HOOK.beforeToEdit](data) {
    //   const query = {
    //     rowId: data.form.id,
    //     id: this.bindId
    //   }
    //   this.$router.push({ name: 'EditBanner', query });
    // }
  }
};
</script>

<style lang="scss" scoped>

</style>
