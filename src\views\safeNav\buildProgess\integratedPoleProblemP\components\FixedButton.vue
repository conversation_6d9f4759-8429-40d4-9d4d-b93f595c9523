<template>
  <span class="tag-btn-box">
    <el-button slot="right" class="filter-item" size="mini" type="success" @click="handleImport">导入标签
    </el-button>
    <upload-tag ref="uploadTag" />
  </span>
</template>
<script>
import UploadTag from '@/views/components/UploadTag/UploadExcel'

export default {
  name: '',
  components: { UploadTag },
  props: {
  },
  data() {
    return {
    }
  },
  watch: {},
  created() {
  },
  methods: {
    handleImport() {
      // 标签路口对应关系
      const { bindId, categoryId } = this.$config.labelToCross_key
      this.$refs.uploadTag.init({ bindId, categoryId });
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.tag-btn-box{
  display: inline-block;
}
</style>
