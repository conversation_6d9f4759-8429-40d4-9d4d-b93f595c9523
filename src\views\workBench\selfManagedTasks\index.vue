<template>
  <div class="app-container">
    <project-list :project-list="projectList" title="自建任务" @changeProjectId="getToDoItem" />
    <el-card style="margin-top: 20px;">
      <work-bench-task
        ref="workTaskRef"
        :custom-crud-option="updatedCRUDOPTION"
        :permission="permission"
        :table-data="tableData"
        :table-header="TATBLEHEADER"
      >
        <template v-slot:workTaskRef="{data}">
          <el-button
            size="mini"
            type="primary"
            @click="handleClick(data)"
          >
            详情
          </el-button>
        </template>

      </work-bench-task>
    </el-card>

  </div>
</template>

<script>
// 顶部组件
import ProjectList from '@/views/workBench/components/ProjectList.vue';
import WorkBenchTask from '@/views/workBench/components/WorkBenchTask.vue';
import { getCountGroupByPmName } from '@/api/oaWorkOrder/oaPmTree'
import { mapGetters } from 'vuex';
import { mapPmTree } from '@/views/workBench/utils/handelData';

const TATBLEHEADER = [
  {
    prop: 'name',
    label: '任务标题'
  },
  {
    prop: 'fv9',
    label: '处理人'
  },
  {
    prop: 'fv12',
    label: '当前状态'
  },
  {
    prop: 'currentStatusVal',
    label: '是否结束'
  },
  {
    prop: 'createTime',
    label: '创建时间'
  },
  {
    prop: 'fv13',
    label: '开始时间'
  },
  {
    prop: 'fv11',
    label: '预计结束时间'
  }
]
export default {
  name: 'SelfManagedTasks',
  components: {
    ProjectList,
    WorkBenchTask
  },
  data() {
    return {
      projectList: [],
      tableHeader: [],
      permission: [],
      CRUDOPTION: {
        url: 'api/oaPmTree/small',
        query: { enabled: 1, createBy: '' } // 初始值
      },
      TATBLEHEADER,
      tableData: []
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ]),
    updatedCRUDOPTION() {
      return {
        ...this.CRUDOPTION,
        query: {
          ...this.CRUDOPTION.query,
          createBy: this.user.username // 使用 user.username 更新
        }
      };
    }
  },
  created() {
    this.getToDoProject();
  },
  methods: {
    // 获取待办项目
    getToDoProject() {
      const query = {
        page: 0,
        size: 999,
        sort: 'createTime,desc',
        fv1: '任务',
        enabled: 1,
        createBy: this.user.username
      }
      getCountGroupByPmName(query).then(res => {
        this.projectList = res
      })
    },

    getToDoItem(val) {
      const { crud } = this.$refs.workTaskRef
      crud.query.pmName = val
      crud.query.createBy = this.user.username
      crud.query.fv1 = '任务'
      crud.refresh().then(res => {
        // 获取到数据
        const data = res.content;
        this.tableData = data.map(mapPmTree);
      })
    },
    // 处理函数
    handleClick(data) {
      this.$router.push({
        name: 'DoTask',
        query: {
          id: data.taskId,
          type: 2
        }
      })
      console.log(data, '<===>', 'data')
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
