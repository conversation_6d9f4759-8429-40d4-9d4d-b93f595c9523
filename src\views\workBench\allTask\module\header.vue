<template>
  <div
    v-if="crud.props.searchToggle"
    class="search-container"
  >
    <el-input
      v-model="query.name"
      class="filter-item"
      clearable
      placeholder="输入任务名称"
      size="small"
      style="width: 200px;"
    />
    <el-select
      v-model="query.fv12"
      class="date-item"
      clearable
      placeholder="请选择当前状态"
      size="small"
      style="width: 120px;"
      @change="crud.toQuery"
    >
      <el-option
        v-for="item in dict.task_status"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <date-range-picker v-model="query.createTime" class="date-item" />
    <rrOperation />
  </div>
</template>

<script>
import { header } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import DateRangePicker from '@/components/DateRangePicker'

export default {
  components: { rrOperation, DateRangePicker },
  mixins: [header()],
  props: {
    permission: {
      type: Object,
      required: true
    }
  },
  dicts: ['task_status'],
  data() {
    return {}
  }
}
</script>
<style lang="scss" rel="stylesheet/scss" scoped>/* Treeselect */
.search-container {
	display: flex;
	aice-align: center;
	margin-bottom: 10px;
}

.filter-item {
	margin-right: 10px;
	margin-bottom: 10px;
}

.date-item {
	margin-right: 10px;
	margin-bottom: 10px;
}

::v-deep .treeselect-main {
	width: 200px;
	line-height: 24px !important;
	height: 24px !important;

	.vue-treeselect__placeholder {
		line-height: 24px !important;
	}

	.vue-treeselect__control {
		height: 24px !important;
		line-height: 24px !important;
	}
}

//::v-deep .vue-treeselect__control, ::v-deep .vue-treeselect__placeholder, ::v-deep .vue-treeselect__single-value {
//  height: 30px;
//  line-height: 30px;
//}
</style>
