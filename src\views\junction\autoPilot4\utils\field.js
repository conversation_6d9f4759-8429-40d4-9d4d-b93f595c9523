// 项目列表所有权限
export const permission = {
  add: ['admin', 'omAsset:add'],
  edit: ['admin', 'omAsset:edit'],
  del: ['admin', 'omAsset:del'],
  upload: ['admin', 'omAsset:importXlsWithRule'],
  updateT: ['admin', 'omAsset:updateFormStruct'],
  updateR: ['admin', 'omAsset:updateRelation']
  // updateG: ['admin', 'omAsset:toRedisGeoIndex']
}

// 项目列表表头
export const tableHeader = [
  // { label: '序号', prop: '0', fixed: 'left', align: 'left', width: 80 },
  { label: '路口编号', prop: 'fv4', align: 'left', width: 100 },
  { label: '路口名称', prop: 'title', width: 180, align: 'left' },
  { label: '踏勘分组', prop: '3', align: 'left', width: 80 },
  { label: '路口类型', prop: '4', width: 80, align: 'left' },
  { label: '区域', prop: '5', width: 80, align: 'left' },
  { label: '是否灯控路口', prop: '6', width: 120, align: 'left' },
  { label: '备注', prop: '7', width: 120, align: 'left' },
  { label: '是否已踏勘', prop: '8', align: 'left', width: 100 },
  { label: '情况分析', prop: '9', align: 'left', width: 160 },
  { label: '状态', prop: '10', align: 'left', width: 160 },
  { label: '经度', prop: 'fv2', align: 'left', width: 120 },
  { label: '维度', prop: 'fv3', align: 'left', width: 120 },
  { label: '创建日期', prop: 'createTime', width: 150 }
]

// 定义按钮组
export const updateButtonsLists = [
  // {
  //   id: '1',
  //   label: '导入',
  //   permission: permission.upload,
  //   fun: 'importProject',
  //   size: 'mini',
  //   className: [],
  //   icon: 'plus',
  //   type: 'primary'
  // },
  // {
  //   id: '2',
  //   label: '预览',
  //   permission: permission.upload,
  //   fun: 'exportProject',
  //   size: 'mini',
  //   className: [],
  //   icon: 'view',
  //   type: 'success',
  //   query: {
  //     fileType: 'html'
  //   }
  // },
  // {
  //   id: '3',
  //   label: '导出',
  //   permission: permission.upload,
  //   fun: 'exportProject',
  //   size: 'mini',
  //   className: [],
  //   icon: 'download',
  //   type: 'primary',
  //   query: {
  //     fileType: 'xls'
  //   }
  // }
]

// 详情表头
export const tableHeaderDetail = [
  { label: '摄像头', prop: 'fv5', fixed: 'left', align: 'left', width: 80 },
  { label: '设备厂商', prop: 'fv10', align: 'left', width: 100 },
  { label: '设备型号', prop: 'fv11', align: 'left', width: 180 },
  { label: '产权单位', prop: 'fv12', width: 120, align: 'left' },
  { label: '融合后链路', prop: 'fv9', width: 120, align: 'left' },
  { label: '状态', prop: 'fv13', width: 120, align: 'left' }
]
// 联合key
export const allKey = {
  '1': {
    target: '2',
    comType: 'fv4',
    targetType: 'title',
    list: 'pointList'
  },
  '2': {
    target: '1',
    comType: 'title',
    targetType: 'fv4',
    list: 'positionList'
  }
}

export const tagDrawerOptions = {
  title: '路口标签-导入历史',
  direction: 'rtl',
  size: '40%'
}
export const tagSelectOptions = {
  'type.fieldName': 'fv1',
  'type.values': '路口',
  'top.fieldName': 'fv10'
}
