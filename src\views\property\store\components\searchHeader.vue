<template>
  <div v-if="crud.props.searchToggle">
    <!-- 搜索 -->
    <el-input
      v-model="query.basicNo"
      class="filter-item"
      clearable
      placeholder="请输入资产编号"
      size="small"
      style="width: 180px;"
      @keyup.enter.native="crud.toQuery"
    />
    <el-input
      v-model="query.orderNo"
      class="filter-item"
      clearable
      placeholder="请输入订单编号"
      size="small"
      style="width: 200px;"
      @keyup.enter.native="crud.toQuery"
    />
    <el-select
      v-model="query.orderType"
      placeholder="请选择入库类型"
      class="filter-item"
      clearable
      style="width: 230px;"
      @change="crud.toQuery"
    >
      <el-option label="新设备" value="1" />
      <el-option label="拆回" value="2" />
      <el-option label="维修返回" value="3" />
      <el-option label="后期接收" value="4" />
    </el-select>
    <el-select
      v-model="query.depotId"
      class="filter-item"
      clearable
      placeholder="请选择仓库"
      style="width: 230px;"
      @change="crud.toQuery"
    >
      <el-option v-for="item in storeroomList" :key="item.id" :label="item.title" :value="item.id" />
    </el-select>
    <el-cascader
      v-model="categoryValue"
      class="filter-item"
      :options="Devices"
      :props="cascaderProps"
      :show-all-levels="true"
      clearable
      placeholder="选择资产类别"
      style="width: 250px"
      @change="handleCategoryChange"
    />
    <date-range-picker v-model="query.createTime" start-placeholder="入库开始时间" class="date-item" />
    <rrOperation />
  </div>
</template>

<script>
import rrOperation from '@crud/RR.operation';
import crudAmCategory from '@/api/property/amCategory';
import CRUD from '@crud/crud';
import { header } from '@crud/crud';
import DateRangePicker from '@/components/DateRangePicker/index.vue';
import amDepots from '@/api/property/amDepot';
export default {
  components: { DateRangePicker, rrOperation },
  mixins: [header()],
  props: {
    permission: {
      type: Object,
      required: true
    },
    dict: {
      type: Object,
      required: true
    },
    categoryList: {
      type: Array,
      required: true
    }
  },

  data() {
    return {
      storeroomList: [],
      userList: [],
      selectLoading: false,
      categoryId: '',
      categoryValue: [],
      Devices: [],
      cascaderProps: {
        lazy: true, // 开启懒加载
        value: 'id', // 将每个选项的值设置为 'id' 属性
        label: 'label', // 用于显示选项名称的属性
        children: 'children', // 包含子选项的属性
        isLeaf: 'isLeaf', // 指示选项是否为叶子节点的属性
        lazyLoad: this.lazyLoad
      }
    }
  },
  computed: {
  },
  created() {
    this.initStoreroomList()
  },
  methods: {
    [CRUD.HOOK.beforeResetQuery]() {
      this.categoryValue = []
    },
    // 获取仓库列表
    async initStoreroomList() {
      try {
        const json = {
          enabled: 1,
          page: 0,
          size: 99999,
          sort: 'createTime,desc',
          bindId: this.$config.storeroom_key.bindId
        }
        const res = await amDepots.get(json);
        if (res && res.content) {
          this.storeroomList = res.content;
        }
      } catch (error) {
        console.error('获取仓库列表失败:', error)
      }
    },
    // 懒加载数据
    lazyLoad(node, resolve) {
      if (node.isLeaf) {
        // 如果是叶子节点，不需要加载数据
        resolve([]);
      } else {
        const parentId = node.value; // 假设当前节点的 'id' 是父节点的 ID
        crudAmCategory.getAmCategory({ enabled: '1', pid: parentId || null }).then((children) => {
          resolve(children.content);
        });
      }
    },
    handleCategoryChange(value) {
      console.log(value, '<===>', 'value')
      // 根据选择层级动态赋值
      const levels = [
        { level: 0, property: 'deviceId' },
        { level: 1, property: 'brandId' },
        { level: 2, property: 'modelId' }
      ];

      levels.forEach(({ level, property }) => {
        if (value && value.length > level) {
          if (value[level]) {
            this.query[property] = value[level];
          } else {
            delete this.query[property];
          }
        } else {
          delete this.query[property];
        }
      });
    }
  }
}
</script>
<style lang="scss" rel="stylesheet/scss" scoped>
::v-deep .vue-treeselect__placeholder {
  line-height: 28px;
  font-size: 14px;
  margin-left: 5px;
}

::v-deep .vue-treeselect__control {
  height: 28px;

  .vue-treeselect__single-value {
    margin-left: 5px;
    line-height: 28px;
    color: #606266;
    font-size: 14px;
  }
}

::v-deep .vue-treeselect__input-container {
  line-height: 30px;
  height: 28px;
}
</style>
