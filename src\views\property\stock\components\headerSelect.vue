<template>
  <el-popover
    placement="bottom"
    popper-class="filter-popover"
    trigger="click"
  >
    <div class="filter-content">
      <remote-self-select
        :filterable="true"
        :options="currentOption"
        :select-value.sync="currentHeader.selectedOption"
        :tags="2"
        clearable
        custom-label="label"
        custom-value="value"
        placeholder="请选择要修改的项目"
        size="small"
        style="width: 100%;"
        @remoteSearch="remoteSearch"
        @selectChange="(val)=>selectChange(val,'ft3')"
      />
    </div>
    <template slot="reference">
      <svg-icon :icon-class="iconClass" />
    </template>
  </el-popover>
</template>
<script>

export default {
  name: 'TableHeader',
  props: {
    icon: {
      type: String,
      default: 'filter'
    },
    activeIcon: {
      type: String,
      default: 'activeFilter'
    },
    currentCrud: {
      type: Object,
      default: () => {
      }
    },
    currentHeader: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    return {
      currentOption: []
    }
  },
  computed: {
    iconClass() {
      const { selectedOption } = this.currentHeader
      return selectedOption && selectedOption.length > 0 ? this.activeIcon : this.icon;
    }
  },
  methods: {
    // 远程搜索
    remoteSearch(val) {
      if (val !== '') {
        this.getOption(val)
      } else {
        this.currentOption = []
      }
    },
    selectChange(val) {
      this.currentHeader.selectedOption = val;
    },
    getOption(title) {
      const query = {
        size: 99,
        enabled: 1,
        title
      }
      console.log(query, '<===>', 'query')
      // 调用接口
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-footer {
  margin-top: 20px;
}
</style>
