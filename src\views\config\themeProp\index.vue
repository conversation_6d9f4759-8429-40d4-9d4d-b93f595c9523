<template>
  <div class="app-container" style="padding: 20px;">
    <div class="theme-box">
      <div class="header-box clearfix">
        <p>当前启用：<b>{{ nowTheme || '无' }}</b></p>
        <div class="button-right">
          <el-upload
            ref="upload"
            :limit="1"
            :before-upload="beforeUpload"
            :auto-upload="true"
            :headers="headers"
            :on-success="handleSuccess"
            :on-error="handleError"
            :action="baseApi+'/api/extendThemeProperty/upload' + '?name='"
          >
            <el-button size="small" type="primary">导入模板</el-button>
            <div slot="tip" class="el-upload__tip">请可上传模板zip包压缩文件，且不超过10M</div>
          </el-upload>
        </div>
      </div>
      <el-row>
        <el-col v-for="(item, index) in themeList" :key="index" :span="6" style="margin-bottom:18px;">
          <div style="padding-right:20px;">
            <el-card :body-style="{ padding: '0px' }">
              <h3 class="title-theme">{{ item.title }}</h3>
              <img :src="baseApi+'/'+item.extend.data.screenshot" class="image">
              <div style="padding: 15px;">
                <div class="bottom clearfix">
                  <template>
                    <el-button v-if="item.active===1" size="medium" type="text" class="button-box active" @click="setAcitive(item,0)"><i class="el-icon-unlock" />已启用</el-button>
                    <el-button v-else size="medium" type="text" class="button-box" @click="setAcitive(item,1)"><i class="el-icon-lock" />启用</el-button>
                  </template>
                  <el-button size="medium" type="text" class="button-box" @click="themeSetting(item)"><i class="el-icon-setting" />设置</el-button>
                  <el-dropdown class="button-box" trigger="click" @command="e=>{deleteTheme(e,item)}">
                    <span class="el-dropdown-link">更多<i class="el-icon-arrow-down el-icon--right" />
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item command="删除">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </div>
            </el-card>
          </div>
        </el-col>
      </el-row>
    </div>
    <!-- <div class="page-box">
      <Paging :page="pageInfo" @handleCurrentChange="getList()" />
    </div> -->
  </div>
</template>

<script>
import { get, del, editOption } from '@/api/config/theme';
import { mapGetters } from 'vuex';
import { getToken } from '@/utils/auth';
export default {
  name: 'ThemeProp',
  data() {
    return {
      headers: { 'Authorization': getToken() },
      themeList: [],
      nowTheme: ''
      // pageInfo: {
      //   size: 12,
      //   page: 1,
      //   total: 0,
      //   pageSizes: [12, 24, 32, 40, 80, 120]
      // }
    }
  },
  computed: {
    ...mapGetters([
      'baseApi'
    ])
  },
  created() {
    this.getTheme();
  },
  methods: {
    getTheme() {
      this.nowTheme = '';
      get({ enabled: 1 }).then(res => {
        if (res && res.content) {
          this.themeList = res.content;
          this.nowTheme = this.themeList.filter(item => {
            return item.active === 1;
          })[0].title;
        }
      });
    },
    // 上传文件
    upload() {
      this.$refs.upload.submit()
    },
    beforeUpload(file) {
      let isLt2M = true
      isLt2M = file.size / 1024 / 1024 < 10
      if (!isLt2M) {
        this.loading = false
        this.$message.error('上传文件大小不能超过 10MB!')
      }
      return isLt2M;
    },
    handleSuccess(response, file, fileList) {
      this.$refs.upload.clearFiles();
      this.$notify({
        title: '上传成功',
        type: 'success',
        duration: 2500
      });
      this.getTheme();
    },
    // 监听上传失败
    handleError(e, file, fileList) {
      const msg = JSON.parse(e.message)
      this.$notify({
        title: msg.message,
        type: 'error',
        duration: 2500
      })
      this.loading = false
    },
    themeSetting(item) {
      this.$router.push({ name: 'ThemeSetting', query: { key: item.key, id: item.id }});
    },
    setAcitive(item, val) {
      editOption({ enabled: val, type: item.type, key: item.key }).then(res => {
        this.$notify({
          title: '操作成功',
          type: 'success',
          duration: 2500
        });
        this.getTheme();
      })
    },
    deleteTheme(e, item) {
      if (e === '删除') {
        this.$confirm('此操作将删除该模板, 是否继续？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          del([item.id]).then(response => {
            this.$notify({
              title: '数据已删除',
              type: 'success',
              duration: 2500
            });
            this.getTheme();
          })
        });
      }
    }
  }
}
</script>

<style scoped lang="scss">
.theme-box{
  padding-left:20px;
  .header-box{
    margin-bottom:20px;
    >p{
      float: left;
    }
    .button-right{
      float:right;
      margin-right:20px;
      margin-top:7px;
    }
  }
  .title-theme{
    padding:10px;
    margin:0;
    line-height:1.5;
  }

  .image {
    width: 100%;
    display: block;
    min-height:155px;
    max-height:180px;
    object-fit: cover;
  }

  .button-box {
    padding: 0;
    float: left;
    width:33.33%;
    margin:0;
    color:#444;
    text-align: center;
    cursor: pointer;
  }
  .button-box.active{
    color:#13ce66;
  }

  .clearfix:before,
  .clearfix:after {
      display: table;
      content: "";
  }

  .clearfix:after {
      clear: both
  }
}
// .page-box{
//   text-align: center;
// }

</style>
