<template>
  <el-form ref="ruleForm" :model="formData" :rules="rules" label-width="80px" class="demo-ruleForm">
    <el-row>
      <el-col :span="6">
        <el-form-item label="部件名称" prop="name">
          <el-input v-model="name" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="经度" prop="longitude">
          <el-input v-model="formData.longitude" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="纬度" prop="latitude">
          <el-input v-model="formData.latitude" />
        </el-form-item>
      </el-col>
      <el-col :span="6" class="btn">
        <el-button style="margin-left:20px" class="search" :disabled="isSubmit" @click="submitForm('ruleForm')">保存</el-button>
        <el-button class="back" @click="resetForm('ruleForm')">取消</el-button>
      </el-col>
    </el-row>
    <p style="color:red;text-align:left;">*请在地图上选择位置</p>
  </el-form>
</template>
<script>
import { get, edit } from '@/api/parts/assets';

export default {
  props: {
    cid: String,
    cname: String,
    longitude: String,
    latitude: String
  },
  data() {
    return {
      name: '',
      formData: {
        longitude: '',
        latitude: ''
      },
      isSubmit: false,
      id: null,
      rules: {
        longitude: [{ required: true, message: '请输入经度', trigger: 'blur' }],
        latitude: [{ required: true, message: '请输入纬度', trigger: 'blur' }]
      },
      jsonData: {}
    };
  },
  watch: {
    cid(val, lod) {
      this.id = val;
      get({ id: this.id }).then(res => {
        if (res && res.content && res.content.length) {
          this.jsonData = res.content[0];
        }
      });
    },
    cname(val, lod) {
      this.name = val;
    },
    longitude(val, lod) {
      this.formData.longitude = val;
    },
    latitude(val, lod) {
      this.formData.latitude = val;
    }
  },
  mounted() {},
  methods: {
    submitForm(form) {
      this.isSubmit = true;
      this.$refs[form].validate(valid => {
        if (valid) {
          const json = this.jsonData;
          json.formBindToVar = false;
          json.fv2 = this.formData.longitude;// 经度
          json.fv3 = this.formData.latitude;// 纬度
          edit(json).then(res => {
            this.$message.success('修改成功');
            this.$emit('success');
            this.isSubmit = false;
          });
        } else {
          this.isSubmit = false;
          console.log('error submit!!');
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.$emit('cancel');
    }
  }
};
</script>
<style lang="scss" scoped>
  .demo-ruleForm{
    position: absolute;
    width:100%;
    left:20px;
    top:20px;
    z-index: 2000;
    padding: 10px 0 0 10px;
  }
</style>
