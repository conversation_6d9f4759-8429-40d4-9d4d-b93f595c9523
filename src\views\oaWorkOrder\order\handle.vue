<!--详情查看-处理-->
<template>
  <div class="app-container">
    <div v-if="isLoadingStatus" />
    <div v-else>
      <!-- <el-card class="box-card">
		<div class="text item">
			<el-steps v-if="currentNode.clazz !== undefined && currentNode.clazz !== null && currentNode.clazz !== ''" :active="activeIndex" finish-status="success">
				<template v-for="(item, index) in nodeStepList">
					<el-step
						v-if="item.isHideNode === false ||
							item.isHideNode === undefined ||
							item.isHideNode == null ||
							item.id === processStructureValue.state[0].id"
						:key="index"
						:title="item.label"
					/>
				</template>
			</el-steps>
			<div v-else>
				<el-alert
					show-icon
					title="未找到当前工单流程信息，请确认当前工单绑定的流程是否存在。"
					type="warning"
				/>
			</div>
		</div>
	</el-card> -->

      <!-- <el-alert
		v-if="activeIndex !== nodeStepList.length && processStructureValue.isEnd===1"
		style="margin-top: 15px"
		:title="alertMessage"
		type="error"
		:closable="false"
	/> -->

      <el-card class="box-card" style="margin-top: 15px">
        <div slot="header" class="clearfix">
          <span>公共信息</span>
        </div>
        <div class="text item">
          <el-form label-position="left" label-width="100px">
            <el-row>
              <el-col :offset="1" :span="12">
                <el-form-item label="标题:" style="margin-bottom: 5px">
                  <span>{{ processStructureValue.title }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="优先级:" style="margin-bottom: 0">
                  <span v-if="processStructureValue.priority===2">
                    <el-tag type="warning">紧急</el-tag>
                  </span>
                  <span v-else-if="processStructureValue.priority===3">
                    <el-tag type="danger">非常紧急</el-tag>
                  </span>
                  <span v-else>
                    <el-tag type="success">一般</el-tag>
                  </span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :offset="1" :span="12">
                <el-form-item label="申请人:" style="margin-bottom: 5px">
                  <span>{{ processStructureValue.createBy }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="当前处理人:" style="margin-bottom: 5px">
                  <template v-for="item in processStructureValue.current">
                    {{ item }}
                  </template>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-card>

      <el-card class="box-card" style="margin-top: 15px;">
        <div slot="header" class="clearfix">
          <span>表单信息</span>
        </div>
        <div class="text item">
          <template v-for="(tplItem) in processStructureValue.data">
            <fm-generate-form
              v-show="currentNode.hideTpls===undefined ||
                currentNode.hideTpls===null ||
                currentNode.hideTpls.indexOf(tplItem.formStructure.id)===-1"
              :key="tplItem.id"
              :ref="'generateForm-'+tplItem.id"
              :data="tplItem.formStructure"
              :preview="(currentNode.hideTpls!==undefined &&
                currentNode.hideTpls!==null &&
                currentNode.hideTpls.indexOf(tplItem.tplId)!==-1) ||
                (currentNode.writeTpls===undefined ||
                currentNode.writeTpls===null ||
                currentNode.writeTpls.indexOf(tplItem.tplId)===-1)||
                (isActiveProcessing && currentNode.activeOrder)?true:false"
              :remote="remoteFunc"
              :value="tplItem.formData"
            />
          </template>
        </div>
        <div>
          <hr style="background-color: #d9d9d9; border:0; height:1px; margin-bottom: 15px">
          <div>
            <el-input
              v-if="processStructureValue.isEnd===0 && comeForm === 'upcoming'"
              v-model="remarks"
              :autosize="{ minRows: 3, maxRows: 99}"
              maxlength="200"
              placeholder="请输入备注信息"
              show-word-limit
              type="textarea"
            />
          </div>
          <div class="text item" style="text-align: center;margin-top:18px">
            <div>
              <template v-for="(item, index) in processStructureValue.edges">
                <el-button
                  v-if="processStructureValue.isEnd===0 && item.source===currentNode.id && comeForm === 'upcoming'"
                  :key="index"
                  type="primary"
                  @click="submitAction(item)"
                >
                  {{ item.label }}
                </el-button>
              </template>
            </div>
          </div>
        </div>
      </el-card>

      <el-card class="box-card" style="margin-top: 15px">
        <div slot="header" class="clearfix">
          <span>工单流转历史</span>
        </div>
        <div class="text item">
          <el-table
            :data="circulationHistoryList"
            border
            style="width: 100%"
          >
            <el-table-column
              label="节点"
              prop="state"
            />
            <el-table-column
              label="流转"
              prop="circulation"
            />
            <el-table-column
              label="处理人"
              prop="processor"
            />
            <el-table-column
              label="处理时间"
              prop="createTime"
            />
            <el-table-column
              label="备注"
              prop="remarks"
            />
          </el-table>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import {
  GenerateForm
} from '@/components/VueFormMaking'
import 'form-making/dist/FormMaking.css'

Vue.component(GenerateForm.name, GenerateForm)
import { getWorkOrderInfo, handleWorkOrder } from '@/api/oaWorkOrder/workOrder';
import crudDictDetail from '@/api/system/dictDetail'
// import { activeOrder } from '@/api/process/work-order'
import { getUser } from '@/api/system/user'
import { mapGetters } from 'vuex'
import oaPmTree from '@/api/oaWorkOrder/oaPmTree';
import { LOAD_CHILDREN_OPTIONS } from '@riophae/vue-treeselect';

export default {
  data() {
    return {
      comeForm: '',
      isLoadingStatus: true,
      currentNode: {
        hideTpls: null,
        writeTpls: null
      },
      isActiveProcessing: false,
      tpls: [],
      remarks: '', // 备注信息
      alertMessage: '',
      nodeStepList: [],
      circulationHistoryList: [],
      activeIndex: 0,
      processStructureValue: {
        workOrder: { title: '' }
      },
      // ruleForm: {
      //   title: '',
      //   process: '',
      //   classify: '',
      //   state_id: '',
      //   state: '',
      //   source_state: '',
      //   processor: '',
      //   process_method: '',
      //   tpls: [],
      //   tasks: []
      // },
      ruleForm: {
        enabled: true,
        title: '',
        priority: '',
        process: {
          id: ''
        },
        classify: '',
        data: [],
        history: {
          remarks: '',
          status: '',
          circulation: ''
        }
      },
      userInfo: {},
      remoteFunc: {
        // 获取用户列表
        userList(resolve) {
          getUser({ enabled: 1, size: 9999 }).then(response => {
            const options = response.content
            resolve(options)
          })
        },
        // 获取字典详情
        getDictDetail(resolve, dictName) {
          crudDictDetail.get(dictName).then(response => {
            const options = response.content
            resolve(options)
          })
        },
        // 文档类型
        docTypeList(resolve) {
          this.getDictDetail(resolve, 'doc_type');
        },
        // 获取所有单位
        deptList(resolve) {
          // getUser({ enabled: 1, size: 9999 }).then(response => {
          //   const options = response.content
          //   resolve(options)
          // })
        },
        projectFun(option, resolve) {
          if (option.pid) {
            // 编辑
            oaPmTree.getPmTreeSuperior(option.pid).then(res => {
              const data = res.content
              this.buildProject(data)
              resolve(data)
            })
          } else {
            // 添加
            oaPmTree.getPmTree({ enabled: 1, size: 9999 }).then(res => {
              const data = res.content.map(function(obj) {
                if (obj.hasChildren) {
                  obj.children = null
                }
                return obj
              })
              resolve(data)
            })
          }
        },
        buildProject(depts) {
          depts.forEach(data => {
            if (data.children) {
              this.buildDepts(data.children)
            }
            if (data.hasChildren && !data.children) {
              data.children = null
            }
          })
        },
        // 懒加载函数
        loadProject({ action, parentNode, callback }) {
          if (action === LOAD_CHILDREN_OPTIONS) {
            oaPmTree.getPmTree({ enabled: '1', pid: parentNode.id }).then(res => {
              parentNode.children = res.content.map(function(obj) {
                if (obj.hasChildren) {
                  obj.children = null
                }
                return obj
              })
              setTimeout(() => {
                callback()
              }, 100)
            })
          }
        }
        // 获取所有会议室
        // meetingRoomList(resolve) {
        //   meeting.get({ enabled: 1, size: 9999 }).then(response => {
        //     const options = response.content
        //     resolve(options)
        //   })
        // }
      }
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  created() {
    this.getProcessNodeList();
    this.comeForm = this.$route.query.name ? this.$route.query.name : '';
  },
  methods: {
    // 获取工单
    getProcessNodeList() {
      getWorkOrderInfo({
        id: this.$route.query.workOrderId, enabled: true
      }).then(response => {
        // this.isActiveProcessing = false
        this.processStructureValue = response.content[0];
        this.circulationHistoryList = this.processStructureValue ? this.processStructureValue.history : '';
        this.processStructureValue.data.map(item => {
          item.formStructure = JSON.parse(item.formStructure)
          item.formData = JSON.parse(item.formData)
        });
        console.log(this.processStructureValue);

        // // 获取当前展示节点列表
        // this.nodeStepList = []
        // for (var i = 0; i < this.processStructureValue.nodes.length; i++) {
        //   if (this.processStructureValue.nodes[i].id === this.processStructureValue.state[0].id) {
        //     // 当前节点
        //     this.nodeStepList.push(this.processStructureValue.nodes[i])
        //     this.activeIndex = this.nodeStepList.length - 1
        //     if (i + 1 === this.processStructureValue.nodes.length) {
        //       this.activeIndex = this.nodeStepList.length
        //     }
        //     this.currentNode = this.processStructureValue.nodes[i]
        //   } else if (!this.processStructureValue.nodes[i].isHideNode) {
        //     // 非隐藏节点
        //     this.nodeStepList.push(this.processStructureValue.nodes[i])
        //   }
        // }

        // // // 如果回退到初始节点则可编辑。
        // if (this.currentNode.clazz === 'start') {
        //   this.currentNode.writeTpls = []
        //   for (var tplTmp of this.processStructureValue.data) {
        //     this.currentNode.writeTpls.push(tplTmp.tplId)
        //   }
        // }
        // // 判断是否需要主动处理
        // for (var stateValue of this.processStructureValue.oaWorkOrder.state) {
        //   if (this.processStructureValue.oaWorkOrder.current_state === stateValue.id && stateValue.processor.length > 1) {
        //     this.isActiveProcessing = true
        //     break
        //   }
        // }
        // 修改字段
        // 获取当前展示节点列表
        const structure = JSON.parse(this.processStructureValue.process.structure);
        this.nodeStepList = []
        for (var i = 0; i < structure.nodes.length; i++) {
          if (structure.nodes[i].id === this.processStructureValue.state[0].id) {
            // 当前节点
            this.nodeStepList.push(structure.nodes[i])
            this.activeIndex = this.nodeStepList.length - 1
            if (i + 1 === structure.nodes.length) {
              this.activeIndex = this.nodeStepList.length
            }
            this.currentNode = structure.nodes[i]
          } else if (!structure.nodes[i].isHideNode) {
            // 非隐藏节点
            this.nodeStepList.push(structure.nodes[i])
          }
        }

        // // 如果回退到初始节点则可编辑。
        if (this.currentNode.clazz === 'start') {
          this.currentNode.writeTpls = []
          for (var tplTmp of this.processStructureValue.data) {
            this.currentNode.writeTpls.push(tplTmp.tplId)
          }
        }
        // // 判断是否需要主动处理
        // for (var stateValue of this.processStructureValue.oaWorkOrder.state) {
        //   if (this.processStructureValue.oaWorkOrder.current_state === stateValue.id && stateValue.processor.length > 1) {
        //     this.isActiveProcessing = true
        //     break
        //   }
        // }
        this.isLoadingStatus = false
        this.getAlertMessage();
      })
    },
    // 处理工单
    submitAction(item) {
      Promise.all(this.processData(item)).then(() => {
        this.handleOrderFun();
      }).catch(() => {
        this.notAllowedSubmit()
      });
    },
    processData(item) {
      this.ruleForm = {
        enabled: true,
        id: this.processStructureValue.id,
        classify: this.processStructureValue.process.classify.id,
        title: this.processStructureValue.title,
        creator: this.user.id,
        isDenied: this.processStructureValue.isDenied,
        isEnd: this.processStructureValue.isEnd,
        priority: this.processStructureValue.priority,
        pmId: this.processStructureValue.pmId,
        history: {
          remarks: this.remarks,
          status: item.flowProperties,
          circulation: item.label
        },
        data: []
      }
      const JsonData = [];
      const promises = [];
      console.log('<===>', this.processStructureValue.data)
      for (const tpl of this.processStructureValue.data) {
        const json = {
          'formStructure': tpl.formStructure,
          'formData': '',
          'enabled': true,
          'tplId': tpl.tplId,
          'id': tpl.id
        };
        const promise = this.$refs['generateForm-' + tpl.id][0].getData().then(res => {
          json.formData = res;
        });
        JsonData.push(json);
        promises.push(promise);
      }
      this.ruleForm.data = JsonData;
      return promises
    },
    notAllowedSubmit() {
      this.submitDisabled = false;
      this.$notify({
        title: '请根据提示填写表单信息',
        type: 'info',
        duration: 2500
      });
    },
    handleOrderFun() {
      handleWorkOrder(this.ruleForm).then(response => {
        this.$router.go(-1);
        // this.$router.push({ path: '/process/upcoming' })
      }).catch(() => {
        // this.submitDisabled = false
      })
    },
    // 获取提示消息
    getAlertMessage() {
      if (this.processStructureValue.isEnd === 1) {
        this.alertMessage = '当前工单已结束。'
      }
    }
    // activeOrderActive() {
    //   var jsonData = [{
    //     id: this.nodeStepList[this.activeIndex].id,
    //     label: this.nodeStepList[this.activeIndex].label,
    //     process_method: 'person',
    //     processor: [this.user.id]
    //   }]
    //   activeOrder(jsonData, this.$route.query.workOrderId).then(() => {
    //     this.getProcessNodeList()
    //   })
    // }
  }
}
</script>
