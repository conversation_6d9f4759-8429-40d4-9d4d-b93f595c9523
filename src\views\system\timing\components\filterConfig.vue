<template>
  <div class="app-container">
    <el-form ref="form" :model="formData" label-width="250px">
      <add-input :arr-value="formData.output_match_regex" label-info="要在输出 URL 上匹配的正则表达式或正则表达式列表（cli，file）" tip-info="要在输出 URL 上匹配的正则表达式或正则表达式列表（cli，file）" />
      <add-input :arr-value="formData.output_filter_regex" label-info="要在输出 URL 上过滤的正则表达式或正则表达式列表（cli，file）" tip-info="要在输出 URL 上过滤的正则表达式或正则表达式列表（cli，file）" />
      <add-input :arr-value="formData.extensions_match" label-info="匹配给定扩展名的输出" tip-info="例如，-em php,html,js" />
      <add-input :arr-value="formData.extension_filter" label-info="过滤给定扩展名的输出" tip-info="例如，-ef png,css）" />

      <el-form-item label="要在输出中显示的字段（%s）">
        <el-input v-model="formData.fields" placeholder="要在输出中显示的字段（%s）" />
      </el-form-item>
      <el-form-item label="要在每个主机输出中存储的字段（%s）">
        <el-input v-model="formData.store_fields" placeholder="要在每个主机输出中存储的字段（%s）" />
      </el-form-item>
      <el-form-item label="使用基于DSL的条件匹配响应">
        <el-input v-model="formData.output_match_condition" placeholder="使用基于DSL的条件匹配响应" />
      </el-form-item>
      <el-form-item label="使用基于DSL的条件过滤响应">
        <el-input v-model="formData.output_filter_condition" placeholder="使用基于DSL的条件过滤响应" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import addInput from '@/views/system/timing/common/addInput'
import { delArrInvalid } from '@/views/system/timing/utils/formate'
export default {
  name: 'FilterConfig',

  components: { addInput },
  data() {
    return {
      formData: {
        output_match_regex: [], // []string
        output_filter_regex: [], // []string
        extensions_match: [], // []string
        extension_filter: [], // []string
        fields: null, //
        store_fields: null, //
        output_match_condition: null, //
        output_filter_condition: null //
      }
    };
  },

  mounted() {

  },

  methods: {
    getFormData() {
      return {
        filter: delArrInvalid(this.formData)
      };
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
