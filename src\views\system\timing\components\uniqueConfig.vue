<template>
  <div class="app-container">
    <el-form ref="form" :model="formData" label-width="150px">
      <el-form-item label="是否去重">
        <el-radio-group v-model="formData.unique">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { delArrInvalid } from '@/views/system/timing/utils/formate'
export default {
  name: 'UniqueConfig',

  data() {
    return {
      formData: {
        unique: null // 默认值为false
        // 其他表单字段的初始值
      }
    };
  },

  mounted() {

  },

  methods: {
    // 添加一个获取 formData 的方法
    getFormData() {
      return delArrInvalid(this.formData);
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
