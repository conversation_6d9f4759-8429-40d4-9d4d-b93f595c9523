<template>
  <div class="container">
    <!-- 切换左侧面板的按钮 -->
    <el-button v-if="isShowPre" class="toggle-left-button" type="text" @click="toggleLeftPanel">
      <!-- <el-icon :class="showLeftPanel ? 'el-icon-arrow-left' : 'el-icon-arrow-right'" /> -->
      <span>{{ showLeftPanel ? '隐藏上周周报' : '展开上周周报' }}</span>
    </el-button>
    <el-row :gutter="20">
      <!--项目数据-->
      <transition v-if="isShowPre" name="slide-left">
        <el-col
          v-show="showLeftPanel"
          :key="showLeftPanel"
          :lg="showRightPanel ? 8 : 24"
          :md="showRightPanel ? 8 : 24"
          :sm="showRightPanel ? 8 : 24"
          :xl="showRightPanel ? 8 : 24"
          :xs="showRightPanel ? 8 : 24"
        >
          <div class="pre-week-box">
            <h3 class="title">第{{ preWeekDate.index }}周 {{ preWeekDate.text }}周报</h3>
            <div class="con-box">
              <div class="con-box">
                <h4 v-if="nextWeeklyTypeLabel">【{{ nextWeeklyTypeLabel }}】</h4>
                <ul v-if="preWeekNextList && preWeekNextList.length" class="next-plan">
                  <li v-for="(item,index) in preWeekNextList" :key="index">
                    <p style="font-weight: 500;margin-bottom: 8px;" v-html="item.fv7+'：'" />
                    <p style="white-space: pre-wrap;color:#444" v-html="item.fv8" />
                  </li>
                </ul>
                <div v-for="(item,index) in preWeekOther" :key="index" class="need-coordinate">
                  <h4 v-if="needCoordinate">【{{ needCoordinate }}】</h4>
                  <p style="white-space: pre-wrap;color:#444">{{ item.fv8 }}</p>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </transition>
      <transition name="slide-right">
        <el-col
          v-show="showRightPanel"
          :key="showRightPanel"
          :lg="showLeftPanel ? 16 : 24"
          :md="showLeftPanel ? 16 : 24"
          :sm="showLeftPanel ? 16 : 24"
          :xl="showLeftPanel ? 16 : 24"
          :xs="showLeftPanel ? 16 : 24"
        >
          <div class="current-week-box">
            <h2 class="weekly-title">写周报</h2>
            <div class="data-box">
              <h3 class="form-data-title">第{{ currentTime.index }}周 周报日期:</h3>
              <date-range-picker
                v-model="form.createTime"
                :picker-options="datePickerOptions"
                class="date-item"
                disabled
                end-placeholder="本周结束时间"
                start-placeholder="本周开始时间"
                value-format="yyyy-MM-dd"
                @change="changeWeek"
              />
            </div>
            <div class="week-top-box">
              <div class="t-header">
                <h3 v-if="thisWeeklyTypeLabel">{{ thisWeeklyTypeLabel }}</h3>
                <h3 v-if="nextWeeklyTypeLabel">{{ nextWeeklyTypeLabel }}</h3>
              </div>
              <ul class="con-list">
                <li v-for="(item,index) in dict.weekly_work_classify" :key="index">
                  <h4>{{ item.label }}</h4>
                  <div class="t-body">
                    <el-input
                      v-if="textareaData[index] && textareaData[index][item.label] && textareaData[index][item.label][thisWeeklyTypeLabel]"
                      v-model="textareaData[index][item.label][thisWeeklyTypeLabel].text"
                      :autosize="{ minRows: 4, maxRows: 600}"
                      placeholder="请输入内容"
                      type="textarea"
                    />
                    <el-input
                      v-if="textareaData[index] && textareaData[index][item.label] && textareaData[index][item.label][nextWeeklyTypeLabel]"
                      v-model="textareaData[index][item.label][nextWeeklyTypeLabel].text"
                      :autosize="{ minRows: 4, maxRows: 600}"
                      placeholder="请输入内容"
                      type="textarea"
                    />
                  </div>
                </li>
              </ul>
            </div>
            <div class="week-bottom-box">
              <ul class="con-list">
                <li>
                  <h4 v-if="dict.weekly_type[2]">{{ dict.weekly_type[2].label }}</h4>
                  <div class="t-body">
                    <el-input
                      v-if="dict.weekly_type[2] && otherTextarea[dict.weekly_type[2].label]"
                      v-model="otherTextarea[dict.weekly_type[2].label].text"
                      :autosize="{ minRows: 4, maxRows: 600}"
                      placeholder="请输入内容"
                      type="textarea"
                    />
                  </div>
                </li>
              </ul>
            </div>
            <!-- <hr style="background-color: #e6ebf5; border:0; height:1px;margin-top:30px;"> -->
            <div class="text item" style="text-align: center;">
              <el-button :disabled="submitDisabled" type="primary" @click="submitAction">提交</el-button>
              <el-button @click="concelForm">放弃填写</el-button>
            </div>
          </div>
        </el-col>
      </transition>
    </el-row>
    <!-- 切换右侧面板的按钮 -->
    <el-button v-if="isShowPre" class="toggle-right-button" type="text" @click="toggleRightPanel">
      <!-- <el-icon :class="showRightPanel ? 'el-icon-arrow-right' : 'el-icon-arrow-left'" /> -->
      <span>{{ showRightPanel ? '隐藏当前周报' : '展开当前周报' }}</span>
    </el-button>
  </div>
</template>

<script>
import { add, get, del } from '@/api/oaWorkOrder/oaPmWeeklyReport'
import DateRangePicker from '@/components/DateRangePicker'
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn'; // 按需引入中文语言包
import weekOfYear from 'dayjs/plugin/weekOfYear'; // 引入 weekOfYear 插件
import extendBindTpl from '@/api/system/extendBindTpl';
// eslint-disable-next-line no-unused-vars
import isoWeek from 'dayjs/plugin/isoWeek';

dayjs.extend(weekOfYear);
dayjs.locale('zh-cn'); // 设置为中文

export default {
  name: 'WeeklyForm',
  components: { DateRangePicker },
  dicts: ['weekly_work_classify', 'weekly_type'],
  props: {
    weeklyData: {
      type: Array,
      default: () => ([])
    },
    currentTime: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    const _this = this;
    return {
      form: {
        createTime: []
      },
      datePickerOptions: {
        shortcuts: [
          // {
          //   text: '今天',
          //   onClick(picker) {
          //     const end = new Date();
          //     const start = new Date();
          //     picker.$emit('pick', [start, end]);
          //   }
          // },
          {
            text: '本周',
            onClick(picker) {
              const now = dayjs();
              // 确定今天是周几，dayjs的周从0（周日）到6（周六）
              const dayOfWeek = now.day();

              let lastSaturday;
              if (dayOfWeek === 0) { // 如果今天是周日
                // 上周六就是昨天
                lastSaturday = now.subtract(1, 'day');
              } else if (dayOfWeek === 6) { // 如果今天是周六
                // 上周六就是今天
                lastSaturday = now;
              } else {
                // 否则，找到这周的周六（上周六），需要回退到上周六
                lastSaturday = now.subtract(dayOfWeek + 1, 'day');
              }

              // 获取本周的周五，从上周六开始加上6天
              const thisFriday = lastSaturday.add(6, 'day');

              // 设置picker的值
              picker.$emit('pick', [lastSaturday.toDate(), thisFriday.toDate()]);
            }
          }],
        // disabledDate(time) {
        //   const today = new Date();
        //   const lastDayOfWeek = new Date(today.setDate(today.getDate() - today.getDay() + 7));
        //   lastDayOfWeek.setHours(23, 59, 59, 999); // 设置为本周最后一天的最后一秒
        //   return time.getTime() > lastDayOfWeek.getTime(); // 禁用所有本周之后的日期
        // },
        disabledDate(time) {
          const isOpenFlag = JSON.parse(_this.$config?.isOpenWeekly?.otherInfo || 'false');

          console.log('是否打开周报限制：' + isOpenFlag)
          // 通过配置项来控制是否打开写周报的时间限制
          if (isOpenFlag) {
            // 处理打开状态
            // return false;
          } else {
            // 周六到周五为一周   周六和周日可以补写上周的周报   周一至周五只能写本周的周报
            const today = dayjs();
            let startSelectableSaturday;

            // 如果今天是周六
            if (today.day() === 6) {
              startSelectableSaturday = today.subtract(7, 'day');
            } else if (today.day() === 0) {
              // 周日
              startSelectableSaturday = today.subtract(1, 'day').subtract(7, 'days');
            } else {
              // 周一至周五
              startSelectableSaturday = today.subtract(today.day() + 1, 'days');
            }

            const givenDay = dayjs(time);
            const isSelectableDay = givenDay.day() === 5 || givenDay.day() === 6;
            // 直接使用日期比较逻辑来判断是否在目标周的周六之后
            const isAfterStartSelectableSaturday = givenDay.diff(startSelectableSaturday, 'day') >= 0;

            // 如果是周六或周五，并且在目标周的周六之后，则不禁用（即可选）
            return !(isSelectableDay && isAfterStartSelectableSaturday);
          }
        }
      },
      thisWeekData: [],
      nextWeekData: [],
      submitDisabled: false,
      lastFid: 0, // 用于记录下一个分配的 fid
      preWeekDate: {},
      preWeekOther: [],
      preWeekNextList: [],
      showLeftPanel: true,
      showRightPanel: true,
      textareaData: [],
      otherTextarea: {},
      relation: '',
      formStruct: {},
      isShowPre: true
    }
  },
  computed: {
    thisWeeklyTypeLabel() {
      if (this.dict && this.dict.weekly_type && this.dict.weekly_type.length > 0) {
        return this.dict.weekly_type[0].label;
      }
      return ''; // 如果数据不可用，返回一个默认值（如空字符串）
    },
    nextWeeklyTypeLabel() {
      if (this.dict && this.dict.weekly_type && this.dict.weekly_type.length > 0) {
        return this.dict.weekly_type[1].label;
      }
      return ''; // 如果数据不可用，返回一个默认值（如空字符串）
    },
    needCoordinate() {
      if (this.dict && this.dict.weekly_type && this.dict.weekly_type.length > 0) {
        return this.dict.weekly_type[2].label;
      }
      return ''; // 如果数据不可用，返回一个默认值（如空字符串）
    },
    combinedDataReady() {
      return this.dict.weekly_type && this.dict.weekly_type.length > 0 &&
					this.dict.weekly_work_classify && this.dict.weekly_work_classify.length > 0;
    }
  },
  watch: {
    combinedDataReady(newVal) {
      if (newVal) {
        this.initializeTextareaData();
      }
    },
    'dict.weekly_type': {
      immediate: true,
      handler(newVal, oldVal) {
        if (newVal && newVal.length) {
          this.initializeOtherTextarea();
        }
      }
    }
  },
  created() {
    this.init();
  },
  mounted() {
    this.$nextTick(() => {
      // const today = this.getDefaultWeekRange();
      // this.form.createTime = today;
      // this.preWeekDate = this.getPreWeekDate();
      // this.getPreContent();
      // this.getCurrentAlreadyWeekly(this.form.createTime);

      //  跟新之后
      this.form.createTime = [this.currentTime.s, this.currentTime.e]
      const currentValue = Number(this.currentTime.index)
      if (currentValue === 1) {
        // 说明是今年的第一周 不需要去取上一周的内容
        this.toggleLeftPanel()
        this.isShowPre = false;
      } else {
        const lastValue = currentValue - 1
        this.preWeekDate = this.weeklyData.find(item => item.index === lastValue)
        this.getPreContent();
      }
      this.getCurrentAlreadyWeekly(this.form.createTime);
    })
  },
  methods: {
    initializeTextareaData() {
      this.textareaData = this.dict.weekly_work_classify.map(item => {
        const typeObj = {};

        // 处理前两个 type 元素
        this.dict.weekly_type.slice(0, 2).forEach(type => {
          if (type && type.label) {
            typeObj[type.label] = { text: '', id: null };
          }
        });

        return {
          [item.label]: typeObj
        };
      });
    },

    initializeOtherTextarea() {
      if (this.dict.weekly_type && this.dict.weekly_type.length >= 3) {
        const thirdTypeLabel = this.dict.weekly_type[2].label;
        if (thirdTypeLabel && typeof thirdTypeLabel === 'string') {
          this.$set(this.otherTextarea, thirdTypeLabel, { text: '', id: null });
        }
      } else {
        this.otherTextarea = {};
      }
    },
    async init(info) {
      this.bindId = this.$config['pm_weekly'].bindId;
      this.getProcessNodeList(this.bindId);
    },
    generateJsonArray() {
      const jsonArray = [];

      // 对 textareaData 处理
      this.textareaData.forEach((obj, key) => {
        Object.keys(obj).forEach(itemKey => {
          Object.keys(obj[itemKey]).forEach(typeKey => {
            const entry = obj[itemKey][typeKey];
            const newJson = {
              fv6: typeKey, // 类型
              fv7: itemKey, // 类别
              fv8: entry.text, // 文本内容
              id: entry.id // ID
            }
            // 解决编辑时候时间和创建人丢失的问题
            if (entry.createBy) {
              newJson.createBy = entry.createBy;
            }
            if (entry.createTime) {
              newJson.createTime = entry.createTime;
            }
            jsonArray.push(newJson);
          });
        });
      });

      // 对 otherTextarea 处理
      for (const key in this.otherTextarea) {
        if (this.otherTextarea.hasOwnProperty(key)) {
          const entry = this.otherTextarea[key];
          const newJson = {
            fv6: key, // 类型
            fv8: entry.text, // 文本内容
            id: entry.id // ID
          }
          if (entry.createBy) {
            newJson.createBy = entry.createBy;
          }
          if (entry.createTime) {
            newJson.createTime = entry.createTime;
          }
          jsonArray.push(newJson);
        }
      }

      return jsonArray;
    },
    getProcessNodeList(id) {
      const data = { id, enabled: 1 }
      extendBindTpl.get(data).then(res => {
        if (res && res.content && res.content.length) {
          const processStructureValue = res.content[0];
          const FormStruct = processStructureValue.extendTpl.formStruct;
          this.relation = processStructureValue.relation;
          this.formStruct = FormStruct;
        }
      });
    },
    deleteItemById(id) {
      del([id]).then(response => {
        console.log('删除成功')
      }).catch(error => {
        console.error(`Error deleting item with id ${id}:`, error);
      });
    },
    submitAction() {
      const subArr = this.generateJsonArray();
      const json = {
        fv1: this.$route.query.name, // 项目名称
        oaPmTree: {
          id: this.$route.query.projectId // 项目id
        },
        fv3: this.form.createTime[0], // 开始时间
        fv4: this.form.createTime[1], // 结束时间
        fv11: this.getWeeklyDataInfo(this.form.createTime), // 第几周
        bindId: this.bindId,
        categoryId: this.$route.query.categoryId || '',
        enabled: 1,
        formData: {},
        formStruct: this.formStruct,
        formBindToVar: true,
        relation: this.relation
      }
      let newSubArr = subArr.map(item => {
        const newItem = { ...item, ...json };
        if (!item.id && item.id !== 0) { // 这里加上对 0 的检查，以便在 id 为 0 时保留 id
          delete newItem.id; // 如果 id 不存在、为 null、空字符串或 undefined，则删除 id
        }
        return newItem;
      });

      // 定义一个数组，包含所有需要排除的值
      const excludeValues = ['', '无', '空', 'null', '没有', '空白'];

      // 首先找出需要删除的条目
      newSubArr.forEach(item => {
        const fv8Trimmed = item.fv8 && item.fv8.trim();
        if (item.id && (fv8Trimmed === '' || excludeValues.includes(fv8Trimmed))) {
          // 调用删除接口
          this.deleteItemById(item.id);
        }
      });

      // 过滤掉 fv8 为空或在排除列表中的项，这里移除了对 item.id 的检查
      newSubArr = newSubArr.filter(item => {
        const fv8Trimmed = item.fv8 && item.fv8.trim();
        return fv8Trimmed && !excludeValues.includes(fv8Trimmed);
      });

      // 检查过滤后的数组是否还有有效项
      if (newSubArr.length > 0) {
        this.submitDisabled = true;
        add(newSubArr).then(response => {
          this.$message.success('提交成功');
          this.concelForm();
        }).catch(error => {
          console.error('Submission error:', error);
          this.$message.error('提交失败');
        }).finally(() => {
          this.submitDisabled = false;
        });
      } else {
        this.$notify({
          title: '请填写有效的周报内容',
          type: 'info',
          duration: 2500
        });
      }
    },
    getDefaultWeekRange() {
      const now = dayjs();
      // 确定今天是周几，dayjs的周从0（周日）到6（周六）
      const dayOfWeek = now.day();

      let lastSaturday;
      if (dayOfWeek === 0) { // 如果今天是周日
        // 上周六就是昨天
        lastSaturday = now.subtract(1, 'day');
      } else if (dayOfWeek === 6) { // 如果今天是周六
        // 上周六就是今天
        lastSaturday = now;
      } else {
        // 否则，找到这周的周六（上周六），需要回退到上周六
        lastSaturday = now.subtract(dayOfWeek + 1, 'day');
      }

      // 获取本周的周五，从上周六开始加上6天
      const thisFriday = lastSaturday.add(6, 'day');

      // 返回格式化的日期字符串
      return [lastSaturday.format('YYYY-MM-DD'), thisFriday.format('YYYY-MM-DD')];
    },
    concelForm() {
      this.$emit('updateListFlag', true);
    },
    getPreWeekDate() {
      // 获取当前日期
      const now = dayjs();

      // 计算当前日期是周几，dayjs的周从0（周日）到6（周六）
      const dayOfWeek = now.day();

      let daysToLastSaturday;
      if (dayOfWeek === 6) { // 如果今天是周六
        daysToLastSaturday = 7; // 直接回溯7天到上周的周六
      } else {
        // 对于周日到周五，需要找到这周的周六，然后再回溯7天到上周的周六
        daysToLastSaturday = dayOfWeek + 1 + 7;
      }

      // 获取上周的周六
      const lastSaturday = now.subtract(daysToLastSaturday, 'day');

      // 获取上周的周五，从上周的周六开始加上6天
      const lastFriday = lastSaturday.add(6, 'day');

      // 构建返回对象
      const weekObj = {
        text: `${lastSaturday.format('YYYY-MM-DD')}~${lastFriday.format('YYYY-MM-DD')}`,
        year: lastSaturday.year(),
        md: `${lastSaturday.format('MM/DD')}~${lastFriday.format('MM/DD')}`,
        s: lastSaturday.format('YYYY-MM-DD'),
        e: lastFriday.format('YYYY-MM-DD')
      };

      return weekObj;
    },
    getPreContent() {
      const json = {
        fv3: this.preWeekDate.s,
        fv4: this.preWeekDate.e,
        enabled: 1,
        bindId: this.bindId,
        // sort: 'id,desc',
        fv1: this.$route.query.name,
        pmId: this.$route.query.projectId, // 项目id
        page: 0,
        size: 9999999
      }
      get(json).then(res => {
        if (res && res.content && res.content.length) {
          const preWeeklyList = res.content;
          this.preWeekNextList = preWeeklyList.filter(item => item.fv6 == this.nextWeeklyTypeLabel);
          this.preWeekOther = preWeeklyList.filter(item => item.fv6 == this.needCoordinate);
        }
      })
    },
    toggleLeftPanel() {
      this.showLeftPanel = !this.showLeftPanel;
      if (!this.showLeftPanel) this.showRightPanel = true;
    },
    toggleRightPanel() {
      this.showRightPanel = !this.showRightPanel;
      if (!this.showRightPanel) this.showLeftPanel = true;
    },
    resetFormData() {
      Object.keys(this.otherTextarea).forEach(key => {
        this.$set(this.otherTextarea, key, { text: '', id: null });
      });
      this.textareaData.forEach(obj => {
        const key = Object.keys(obj)[0];
        const typeObj = obj[key];
        Object.keys(typeObj).forEach(type => {
          this.$set(typeObj, type, { text: '', id: null });
        });
      });
    },
    getCurrentAlreadyWeekly(createTime) {
      // 首先清空表单数据，确保不会有旧数据残留
      this.resetFormData();
      const json = {
        fv3: createTime[0],
        fv4: createTime[1],
        sort: 'id,desc',
        enabled: 1,
        bindId: this.bindId,
        fv1: this.$route.query.name,
        pmId: this.$route.query.projectId, // 项目id
        page: 0,
        size: 9999999
      }
      get(json).then(res => {
        if (res && res.content && res.content.length) {
          const currentAlreadyWeeklyList = res.content;
          currentAlreadyWeeklyList.forEach(item => {
            if (this.otherTextarea.hasOwnProperty(item.fv6)) {
              this.otherTextarea[item.fv6] = {
                text: item.fv8,
                id: item.id,
                createBy: item.createBy,
                createTime: item.createTime
              };
            } else {
              const targetObjIndex = this.textareaData.findIndex(obj => obj.hasOwnProperty(item.fv7));
              if (targetObjIndex !== -1) {
                this.$set(this.textareaData[targetObjIndex][item.fv7], item.fv6, {
                  text: item.fv8,
                  id: item.id,
                  createBy: item.createBy,
                  createTime: item.createTime
                });
              }
            }
          });
        } else {
          this.resetFormData();
        }
      })
    },
    changeWeek(e) {
      this.getCurrentAlreadyWeekly(e);
    },
    getWeeklyDataInfo(createTime) {
      // 拼接 createTime 数组的值形成目标字符串
      const targetText = `${createTime[0]}~${createTime[1]}`;

      // 查找与 targetText 匹配的 weeklyData 项
      const matchedItem = this.weeklyData.find(item => item.text === targetText);

      // 如果找到匹配项，返回该项的周信息；否则返回 null
      return matchedItem.index || '';
    }
  }
};
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
/* 定义渐变动画 */
/* 左侧面板的滑动动画 */
.slide-left-enter-active, .slide-left-leave-active {
	transition: transform 0.5s, opacity 0.5s;
}

.slide-left-enter, .slide-left-leave-to {
	transform: translateX(-100%);
	opacity: 0;
}

/* 右侧面板的滑动动画 */
.slide-right-enter-active, .slide-right-leave-active {
	transition: transform 0.5s, opacity 0.5s;
}

.slide-right-enter, .slide-right-leave-to {
	transform: translateX(100%);
	opacity: 0;
}

/* 定义渐变动画 */
.container {
	position: relative;
	padding-top: 35px;

	.toggle-left-button, .toggle-right-button {
		position: absolute;
		top: 0; /* Adjust as needed */
	}

	/* Adjust the button position according to your layout */
	.toggle-left-button {
		left: 10px; /* Adjust as needed */
	}

	.toggle-right-button {
		right: 10px; /* Adjust as needed */
	}
}

.pre-week-box {
	.title {
		font-size: 18px;
		font-weight: bold;
		margin-bottom: 15px;
	}

	.con-box {
		margin-bottom: 20px;

		> h4 {
			margin-bottom: 10px;
			font-size: 16px;
		}

		.next-plan {
			> li {
				margin-bottom: 20px;

				p {
					line-height: 30px;
					font-size: 16px;
					padding-left: 14px;
				}
			}
		}

		.need-coordinate {
			margin-bottom: 8px;

			> h4 {
				margin-bottom: 10px;
				font-size: 16px;
			}

			> p {
				line-height: 30px;
				padding-left: 14px;
				font-size: 16px;
			}
		}

	}
}

.weekly-title {
	text-align: center;
	font-size: 18px;
	margin-bottom: 15px;
	font-weight: 600;
}

.data-box {
	margin-bottom: 25px;

	.form-data-title {
		font-size: 16px;
		line-height: 26px;
		display: inline-block;
		margin-right: 10px;
	}
}

.week-top-box {
	.t-header {
		display: flex;

		h3 {
			flex: 1;
			text-align: center;
			font-size: 16px;
			font-weight: 500;
		}
	}

	.con-list {
		li {
			margin-bottom: 15px;

			h4 {
				font-weight: 400;
				margin-bottom: 5px;
			}

			.t-body {
				display: flex;

				.el-textarea {
					flex: 1;
				}

				.el-textarea + .el-textarea {
					margin-left: 10px; /* 仅在元素之间添加10px的左边距 */
				}
			}
		}
	}
}

.week-bottom-box {
	margin-bottom: 20px;

	.con-list {
		li {
			h4 {
				font-weight: 400;
				margin-bottom: 5px;
			}
		}
	}

}
</style>
