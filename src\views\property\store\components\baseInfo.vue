<template>
  <el-form ref="baseInfo" :model="baseInfo" label-width="130px">
    <el-form-item label="库房">
      {{ safeDisplay(baseInfo.depot, 'title') }}
    </el-form-item>
    <el-form-item label="项目">
      {{ safeDisplay(baseInfo.pm, 'name') }}
    </el-form-item>
    <el-form-item label="设备">
      {{ safeDisplay(baseInfo.device, 'name') }}
    </el-form-item>
    <el-form-item label="品牌">
      {{ safeDisplay(baseInfo.brand, 'name') }}
    </el-form-item>
    <el-form-item label="型号">
      {{ safeDisplay(baseInfo.model, 'name') }}
    </el-form-item>
    <el-form-item label="到货数量">
      {{ assetInfo.amount }}
    </el-form-item>
  </el-form>

</template>

<script>
export default {
  components: {},
  props: {
    baseInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    assetInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {

    }
  },

  methods: {
    safeDisplay(obj, key) {
      return obj && obj[key] ? obj[key] : '';
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.el-picker-panel__footer .el-button--text.el-picker-panel__link-btn {
  display: none;
}

</style>

<style lang="scss" rel="stylesheet/scss">

.no-atTheMoment {
  .el-picker-panel__footer .el-button--text.el-picker-panel__link-btn {
    display: none;
  }
}
.task-dialog .dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style>
