<template>
  <el-drawer
    :before-close="beforeClose"
    :close-on-press-escape="false"
    :destroy-on-close="true"
    :visible.sync="selectVisible"
    :wrapper-closable="false"
    size="85%"
    title="选择项目成员!"
  >
    <div class="user-container">
      <el-row :gutter="20">
        <!--侧边部门数据-->
        <el-col :lg="4" :md="5" :sm="6" :xl="4" :xs="9">
          <div class="head-container">
            <el-input
              v-model="deptName"
              class="filter-item"
              clearable
              placeholder="输入部门名称搜索"
              prefix-icon="el-icon-search"
              size="small"
              @input="getDeptDatas"
            />
          </div>
          <el-tree
            :data="deptDatas"
            :expand-on-click-node="false"
            :load="getDeptDatas"
            :props="defaultProps"
            lazy
            @node-click="handleNodeClick"
          />
          <div v-if="crud.selections && crud.selections.length" class="project-users">
            <div class="user-lable">项目成员({{ crud.selections.length }})人</div>
            <div class="user-list">
              <el-tag
                v-for="(item,index) in crud.selections"
                :key="item.id"
                closable
                @close="deleteProjectUser(item,index)"
              >
                {{ item.username }}
              </el-tag>
            </div>

          </div>
        </el-col>
        <!--用户数据-->
        <el-col :lg="20" :md="19" :sm="18" :xl="20" :xs="15">
          <!--工具栏-->
          <div class="head-container">
            <div>
              <!-- 搜索 -->
              <el-input
                v-model="query.blurry"
                class="filter-item"
                clearable
                placeholder="输入名称或者邮箱搜索"
                size="small"
                style="width: 200px;"
                @keyup.enter.native="crud.toQuery"
              />
              <date-range-picker v-model="query.createTime" class="date-item" />
              <rrOperation />
              <el-button class="filter-item" icon="el-icon-check" size="mini" type="success" @click="onsubmit">
                添加成员
              </el-button>
            </div>
          </div>
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            :data="crud.data"
            row-key="id"
            style="width: 100%;"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column :reserve-selection="true" type="selection" width="55" />
            <el-table-column :show-overflow-tooltip="true" fixed="left" label="用户名" prop="username" />
            <el-table-column :show-overflow-tooltip="true" fixed="left" label="姓名" prop="nickName" />
            <el-table-column :show-overflow-tooltip="true" label="手机号" prop="phone" />
            <el-table-column :show-overflow-tooltip="true" label="邮箱" prop="email" />
            <el-table-column
              :show-overflow-tooltip="true"
              label="角色"
              prop="roles"
            >
              <template slot-scope="scope">
                {{
                  scope.row.roles.map((itme) => {
                    return itme.name
                  }).join(',')
                }}
              </template>
            </el-table-column>
            <el-table-column label="性别" prop="gender" />
            <el-table-column :show-overflow-tooltip="true" label="部门" prop="dept">
              <template slot-scope="scope">
                <div>{{ scope.row.dept ? scope.row.dept.name : '暂无部门' }}</div>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" label="创建日期" prop="createTime" width="135" />
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </div>

  </el-drawer>
</template>

<script>
import rrOperation from '@crud/RR.operation.vue'
import DateRangePicker from '@/components/DateRangePicker/index.vue'
import CRUD, { crud, header, presenter } from '@crud/crud'
import crudUser from '@/api/system/user'
import oaPmMember from '@/api/oaWorkOrder/oaPmMember'
import { getDeptsAll } from '@/api/system/dept';
import pagination from '@crud/Pagination.vue'

export default {
  name: 'SelectUser',
  components: { DateRangePicker, rrOperation, pagination },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: false,
      url: 'api/users',
      query: { enabled: 1 },
      crudMethod: { ...crudUser }
    })
  },
  mixins: [presenter(), header(), crud()],
  data() {
    return {
      deptName: '',
      selectVisible: false,
      defaultProps: { children: 'children', label: 'name', isLeaf: 'leaf' },
      deptDatas: [],
      projectInfo: {}
    }
  },
  methods: {
    async openSelectUser(data) {
      this.selectVisible = true;
      this.projectInfo = data;
      this.getSelectUsers(this.projectInfo.id);
    },
    // 通过项目获取用户列表
    getSelectUsers(pmId) {
      const params = {
        enabled: 1,
        pmId,
        page: 0,
        size: 9999
      }
      oaPmMember.get(params).then(res => {
        const data = res.content;
        data.map(item => {
          this.$refs.table.toggleRowSelection(item.user, true);
        })
        this.crud.toQuery()
      })
    },
    // 获取左侧部门数据
    getDeptDatas(node, resolve) {
      const sort = 'id,desc'
      const params = { sort: sort, enabled: 1 }
      if (typeof node !== 'object') {
        if (node) {
          params['name'] = node
        }
      } else if (node.level !== 0) {
        params['pid'] = node.data.id
      }
      setTimeout(() => {
        getDeptsAll(params).then(res => {
          if (resolve) {
            resolve(res.content)
          } else {
            this.deptDatas = res.content
          }
        })
      }, 100)
    },
    // 切换部门
    handleNodeClick(data) {
      if (data.pid === 0) {
        this.query.deptId = null
      } else {
        this.query.deptId = data.id
      }
      this.crud.toQuery()
    },
    // 删除项目成员
    async deleteProjectUser(data, index) {
      const deldata = this.crud.data.find(item => {
        return item.id == data.id
      })
      if (deldata) {
        this.$refs.table.toggleRowSelection(deldata);
      } else {
        const findIndex = this.crud.data.findIndex(item => {
          return item.id == data.id
        })
        if (findIndex > -1) {
          this.crud.selections.splice(findIndex, 1)
        } else {
          this.crud.selections.splice(index, 1)
        }
        this.$refs.table.toggleRowSelection(data);
      }
    },
    onsubmit() {
      const { id: pmId } = this.projectInfo;
      let selectUser = this.crud.selections;
      selectUser = selectUser.map(item => {
        return {
          user: {
            id: item.id
          },
          pmId,
          enabled: 1
        }
      })
      oaPmMember.add(selectUser).then(res => {
        this.$notify({
          title: '添加成功',
          type: 'success',
          duration: 2500
        });
        this.beforeClose();
      })
    },
    beforeClose() {
      this.$emit('succeSubmit', { type: 'user' });
      this.selectVisible = false;
      // 恢复data中的初始值
      Object.assign(this.$data, this.$options.data.call(this));
    }
  }
}

</script>
<style lang="scss" rel="stylesheet/scss" scoped>
.user-container {
	padding: 20px !important;
}

.project-users {
	min-height: 200px;

	.user-lable {
		line-height: 20px;
		font-size: 18px;
		margin-top: 20px;
	}

	.user-list {
		padding: 20px;
		padding-left: 0;
		display: flex;
		align-items: center;
		flex-wrap: wrap;

		.el-tag {
			margin: 0 10px 10px 0;
		}

	}
}
</style>
