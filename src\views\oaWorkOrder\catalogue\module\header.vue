<template>
  <div
    v-if="crud.props.searchToggle"
  >
    <el-input
      v-model="query.title"
      class="filter-item"
      clearable
      placeholder="请输入模板名称搜索"
      size="small"
      style="width: 200px;"
    />
    <el-select
      v-model="query.status"
      class="filter-item"
      clearable
      placeholder="请选择状态"
      size="small"
      style="width: 180px"
    >
      <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
    <rrOperation />
  </div>
</template>

<script>
import { header } from '@crud/crud';
import rrOperation from '@crud/RR.operation';
import { statusList } from '@/views/oaWorkOrder/catalogue/utils/catalogue';

export default {
  components: { rrOperation },
  mixins: [header()],
  props: {
    permission: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      statusList
    }
  },
  methods: {}
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>

</style>
