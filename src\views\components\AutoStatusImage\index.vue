<template>
  <span>
    <div class="auto-img-desc">
      <span>图例说明:</span>
      <div v-for="item in imgList" :key="item.img" class="auto-img-item">
        <img :alt="item.name" :src="getImagePath(item.img)">
        <span>{{ item.name }}</span>
      </div>
    </div>
  </span>
</template>

<script>
const autoStatusImage = [
  { name: '完成', img: '完成', bgColor: '#1890FF' },
  { name: '利旧', img: '利旧', bgColor: '#13CE66' },
  { name: '施工中', img: '施工中', bgColor: '#1890FF' },
  { name: '未开始', img: '未开始', bgColor: '#13CE66' },
  { name: '问题', img: '问题', bgColor: '#1890FF' }
]
export default {
  name: 'AutoStatusImage',
  components: {},

  props: {
    imgList: {
      type: Array,
      default: () => {
        return autoStatusImage
      }
    }
  },
  data() {
    return {}
  },
  computed: {
    getImagePath() {
      return (img) => {
        return require(`@/assets/images//${img}.png`);
      };
    }
  },
  watch: {},
  created() {
  },
  methods: {}
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.auto-img-desc {
	display: flex;
	align-items: center;

	.auto-img-item {
		margin-right: 10px;
		padding: 0px 10px;
		border-radius: 4px;
		box-sizing: border-box;

		img {
			width: 24px;
			height: 24px;
		}

		span {
			font-size: 12px;
			color: #000;
			font-weight: 500;
		}
	}
}
</style>
