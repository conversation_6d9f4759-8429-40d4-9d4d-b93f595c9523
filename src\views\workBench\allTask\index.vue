<template>
  <div class="app-container">
    <div class="head-container">
      <e-header :permission="permission" />
      <crudOperation :permission="permission" />
    </div>
    <!-- 表格 -->
    <div class="body-box">
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="tableData"
        style="width: 100%;"
        @select="crud.selectChange"
        @select-all="crud.selectAllChange"
        @selection-change="crud.selectionChangeHandler"
      >
        <!-- <el-table-column type="selection" width="55" :fixed="true" /> -->
        <el-table-column
          v-for="item in tableHeader"
          :key="item.prop"
          :align="item.align || 'center'"
          :fixed="item.fixed || false"
          :label="item.label"
          :prop="item.prop"
          :show-overflow-tooltip="true"
          :width="item.width || ''"
        >
          <template slot-scope="scope">
            <span v-if="item.prop === 'currentStatusVal'">
              <el-tag v-if="scope.row.currentStatusVal" size="mini" type="success">是</el-tag>
              <el-tag v-else size="mini" type="danger">否</el-tag>
            </span>
            <span v-else>{{ scope.row[item.prop] }}</span>
          </template>
        </el-table-column>
        <!--   编辑与删除   -->
        <el-table-column
          align="left"
          fixed="right"
          label="操作"
          width="160"
        >
          <template slot-scope="scope">
            <el-button size="mini" type="success" @click="handleClick(scope.row)">详情</el-button>

          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import oaPmTree from '@/api/oaWorkOrder/oaPmTree'
import eHeader from './module/header.vue'
import CRUD, { presenter } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import { mapPmTree } from '@/views/workBench/utils/handelData';

export default {
  name: 'AllTask',
  components: { eHeader, crudOperation, pagination },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: true,
      title: '全部任务',
      url: 'api/oaPmTree/small',
      sort: ['createTime,desc'],
      query: { enabled: 1, pidNotNull: true, fv1: '任务' },
      crudMethod: { ...oaPmTree },
      optShow: {
        add: false,
        edit: false,
        del: false,
        download: false,
        reset: true,
        rightGroup: false
      }
    })
  },
  mixins: [presenter()],
  data() {
    return {
      permission: {
        add: ['admin', 'smsContent:add'],
        edit: ['admin', 'smsContent:edit'],
        del: ['admin', 'smsContent:del'],
        updateT: ['admin', 'smsContent:updateFormStruct'],
        updateR: ['admin', 'smsContent:updateRelation']
      },
      tableData: [],
      tableHeader: [],
      bindId: '',
      renderSmsForm: false,
      renderReissue: false
    };
  },
  computed: {
    currentStatus() {
      const statusMap = {
        '已审核': true,
        '已拒绝': true
      };
      return (data) => {
        return statusMap[data] || false
      };
    }
  },
  watch: {
    'crud.data': {
      async handler(val, oldVal) {
        if (val && val.length && val.length > 0) {
          const newVal = val;
          let tableData = [];
          let tableHeader = []
          tableHeader = [
            { prop: 'name', label: '任务标题', fixed: 'left', width: '200' },
            { prop: 'fv18', label: '项目名称' },
            { prop: 'fv9', label: '处理人' },
            { prop: 'fv12', label: '当前状态' },
            { prop: 'currentStatusVal', label: '是否结束' },
            // { prop: 'fv7', label: '里程碑' },
            { prop: 'createTime', label: '创建时间' },
            { prop: 'fv13', label: '开始时间' },
            { prop: 'fv11', label: '预计结束时间' }
          ];
          this.tableHeader = tableHeader;
          tableData = newVal.map(mapPmTree)
          // tableData = newVal.map(item => {
          //   const json = item.extend.data || {};
          //   return json;
          // });
          this.tableData = tableData;
        } else {
          this.tableHeader = [];
          this.tableData = [];
        }
      },
      deep: true
    }
  },
  methods: {
    [CRUD.HOOK.beforeRefresh]() {

    },
    handleClick(data) {
      this.$router.push({
        name: 'DoTask',
        query: {
          id: data.taskId,
          type: 2 // 查看详情
        }
      })
    }

  }
};
</script>

<style lang="scss" scoped>

</style>
