// 项目列表所有权限
export const permission = {
  add: ['admin', 'omAssetAffiliated:add'],
  edit: ['admin', 'omAssetAffiliated:edit'],
  del: ['admin', 'omAssetAffiliated:del'],
  upload: ['admin', 'omAssetAffiliated:importXlsWithRule'],
  updateT: ['admin', 'omAssetAffiliated:updateFormStruct'],
  updateR: ['admin', 'omAssetAffiliated:updateRelation']
  // updateG: ['admin', 'omAssetAffiliated:toRedisGeoIndex']
}
// 项目列表表头
export const tableHeader = [
  { label: '路口编号', prop: 'fv4', align: 'left', width: 100 },
  { label: '基础开挖数量', prop: 'fv1', width: 180, align: 'left' },
  { label: '混凝土浇筑数量', prop: 'fv2', align: 'left', width: 100 },
  { label: '综合箱安装完成数量', prop: 'fv3', width: 120, align: 'left' },
  { label: '基础开挖', prop: 'fv5', width: 120, align: 'left' },
  { label: '钢筋绑扎', prop: 'fv6', width: 120, align: 'left' },
  { label: '接地安装', prop: 'fv7', width: 120, align: 'left' },
  { label: '地脚安装', prop: 'fv8', width: 120, align: 'left' },
  { label: '预埋管安装', prop: 'fv9', width: 150 },
  { label: '混凝土浇筑', prop: 'fv10', align: 'left', width: 100 },
  { label: '综合箱安装', prop: 'fv11', align: 'left', width: 100 },
  { label: '状态', prop: 'status', width: 150 }
]

// 定义按钮组
export const updateButtonsLists = [
  {
    id: '1',
    label: '导入',
    permission: permission.upload,
    fun: 'importProject',
    size: 'mini',
    className: [],
    icon: 'plus',
    type: 'primary'
  }
  // {
  //   id: '2',
  //   label: '预览',
  //   permission: permission.upload,
  //   fun: 'exportProject',
  //   size: 'mini',
  //   className: [],
  //   icon: 'view',
  //   type: 'success',
  //   query: {
  //     fileType: 'html'
  //   }
  // },
  // {
  //   id: '3',
  //   label: '导出',
  //   permission: permission.upload,
  //   fun: 'exportProject',
  //   size: 'mini',
  //   className: [],
  //   icon: 'download',
  //   type: 'primary',
  //   query: {
  //     fileType: 'xls'
  //   }
  // }
]

// 联合key
export const allKey = {
  '1': {
    target: '2',
    comType: 'fv4',
    targetType: 'title',
    list: 'pointList'
  },
  '2': {
    target: '1',
    comType: 'title',
    targetType: 'fv4',
    list: 'positionList'
  }
}
