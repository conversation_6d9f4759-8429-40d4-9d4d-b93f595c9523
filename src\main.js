import Vue from 'vue'

import Cookies from 'js-cookie'
import './assets/styles/reset.css'
import 'normalize.css/normalize.css'

import Element from 'element-ui'
//
import mavonEditor from 'mavon-editor'
import 'mavon-editor/dist/css/index.css'

// 数据字典
import dict from './components/Dict'

import JsonViewer from 'vue-json-viewer'
import 'vue-json-viewer/style.css'

Vue.use(JsonViewer)
// 权限指令
import checkPer, { setArrPermission } from '@/utils/permission'
import permission from './components/Permission'
import './assets/styles/element-variables.scss'
// global css
import './assets/styles/index.scss'

// 代码高亮
import VueHighlightJS from 'vue-highlightjs'
import 'highlight.js/styles/atom-one-dark.css'

import '@/components/global' // 引入全局组件

import App from './App'
import store from './store'
import router from './router/routers'

import './assets/icons' // icon
import './router/index' // permission control
import { parseTime, resetForm, addDateRange, selectDictLabel } from '@/utils/costum'

import iconPicker from 'e-icon-picker'
import 'e-icon-picker/dist/index.css'// 基础样式
import 'e-icon-picker/dist/main.css' // fontAwesome 图标库样式

import Pagination from '@/components/Pagination'

Vue.component('Pagination', Pagination)

import wxlogin from 'vue-wxlogin'; // 二维码登录

Vue.component('wxlogin', wxlogin)

import { GenerateForm } from '@/components/VueFormMaking' // 表单渲染器
import 'form-making/dist/FormMaking.css'

Vue.component(GenerateForm.name, GenerateForm)

import axios from 'axios'
import '@/utils/codeLogin'
import dayjs from 'dayjs'; // 时间
import '@/directives/index'

import VXETable from 'vxe-table' // 解决表格各种问题
import 'vxe-table/lib/style.css'

import VueEditor from 'vue2-editor'; // 富文本编辑器

import AFTableColumn from 'af-table-column' // 自适应宽度
// 全局方法挂载
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.$setArrPermission = setArrPermission
Vue.prototype.$axios = axios
Vue.prototype.$dayJS = dayjs
Vue.use(VXETable)
Vue.use(AFTableColumn)
Vue.use(checkPer)
Vue.use(VueHighlightJS)
Vue.use(mavonEditor)
Vue.use(permission)
Vue.use(dict)
Vue.use(iconPicker)
Vue.use(VueEditor);
Vue.use(Element, {
  size: Cookies.get('size') || 'small' // set element-ui default size
})

Vue.config.productionTip = false
new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
