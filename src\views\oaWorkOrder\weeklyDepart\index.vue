<template>
  <div class="app-container">
    <div class="head-container">
      <e-header ref="eheader" :permission="permission" />
      <crudOperation :permission="permission">
        <update-button
          slot="right"
          :bind-id="bindId"
          :enabled="[1]"
          :permission="permission"
        />
        <el-button
          slot="right"
          class="filter-item"
          icon="el-icon-eleme"
          size="mini"
          style="margin-right:7px;"
          type="success"
          @click="preView('deptWeekly')"
        >
          按列表预览周报
        </el-button>
        <el-button
          slot="right"
          class="filter-item"
          icon="el-icon-download"
          size="mini"
          style="margin-right:7px;"
          type="primary"
          @click="downLoad('deptWeekly')"
        >
          按列表导出周报
        </el-button>
        <el-button
          slot="right"
          class="filter-item"
          icon="el-icon-eleme"
          size="mini"
          style="margin-right:7px;"
          type="success"
          @click="preView('deptWeeklyReport')"
        >
          按部门预览周报
        </el-button>
        <el-button
          slot="right"
          class="filter-item"
          icon="el-icon-download"
          size="mini"
          type="primary"
          @click="downLoad('deptWeeklyReport')"
        >
          按部门导出周报
        </el-button>
      </crudOperation>
    </div>
    <!-- 表格 -->
    <div class="body-box">
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="tableData"
        style="width: 100%;"
        @select="crud.selectChange"
        @select-all="crud.selectAllChange"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column :fixed="true" type="selection" width="55" />
        <el-table-column
          v-for="item in tableHeader"
          :key="item.prop"
          :align="item.align"
          :fixed="item.fixed || false"
          :label="item.label"
          :prop="item.prop"
          :show-overflow-tooltip="true"
          :width="item.width || 180"
        >
          <template slot-scope="scope">
            <template v-if="item.label == '预览图'">
              <el-image
                :key="item.id"
                :preview-src-list="[scope.row.ft1[0].url]"
                :src="scope.row.ft1[0].url"
                class="el-avatar"
                fit="contain"
                lazy
              >
                <div slot="error">
                  <i class="el-icon-document" />
                </div>
              </el-image>
            </template>
            <a
              v-else-if="item.label == '项目名称'"
              href="javascript:;"
              style="color:#1890ff"
              @click="goProject(scope.row)"
            >{{ scope.row.fv1 }}</a>
            <span v-else>{{ scope.row[item.prop] }}</span>
          </template>
        </el-table-column>
        <!--   编辑与删除   -->
        <el-table-column
          align="center"
          fixed="right"
          label="操作"
          width="100"
        >
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>

    <!--<upload-file ref="uploadRef" @success="successUpload" />-->
  </div>
</template>

<script>
import oaPmWeeklyReport from '@/api/oaWorkOrder/oaPmWeeklyReport';
import oaPmTree from '@/api/oaWorkOrder/oaPmTree';
import eHeader from './module/header';
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation';
import pagination from '@crud/Pagination';
import udOperation from '@crud/UD.operation';
import updateButton from '@/components/UpdateButton/index'
import { downloadUrl } from '@/utils/index'
import { mapGetters } from 'vuex'

const defaultForm = { id: null }
export default {
  name: 'WeeklyDepart',
  components: { udOperation, eHeader, crudOperation, pagination, updateButton },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: false,
      title: '部门周报',
      url: 'api/oaPmWeeklyReport/small',
      sort: ['createTime,desc'],
      query: { enabled: 1, phase: [], fv1: '', fv6: '', fv7: '', fv3: '', fv4: '', pmId: '', createBy: '', updateBy: [], pmLeader: [] },
      crudMethod: { ...oaPmWeeklyReport },
      optShow: {
        add: false,
        edit: false,
        del: true,
        download: false,
        reset: true,
        rightGroup: false
      }
    })
  },

  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      permission: {
        del: ['admin', 'oaPmWeeklyReport:del'],
        add: ['admin', 'oaPmWeeklyReport:add'],
        updateT: ['admin', 'oaPmWeeklyReport:updateFormStruct'],
        updateR: ['admin', 'oaPmWeeklyReport:updateRelation']
      },
      tableData: [],
      tableHeader: [],
      bindId: '',
      categoryId: ''
    };
  },
  computed: {
    ...mapGetters([
      'preViewUrl',
      'omOaAPi'
    ])
  },
  watch: {
    'crud.data': {
      handler(val) {
        this.formatterTableHeader(val)
        this.formatterTableData(val)
      },
      deep: true
    }
  },
  created() {
    this.initData();
  },
  methods: {
    initData() {
      this.bindId = this.$config['pm_weekly']?.bindId;
      this.categoryId = this.$config['depart_weekly_categorize']?.categoryId;
      this.$nextTick(() => {
        this.crud.toQuery();
      })
    },
    [CRUD.HOOK.beforeRefresh]() {
      this.crud.query.bindId = this.bindId;
      this.crud.query.categoryId = this.categoryId;
    },
    /**
		 * 格式化表格数据
		 * @param {Array} val - 表格数据数组
		 */
    formatterTableData(val) {
      if (val && val.length) {
        let tableData = [];
        tableData = val.map(item => {
          const json = item.extend.data || {};
          json.id = item.id;
          json.fv1 = item.fv1;
          json.fv6 = item.fv6;
          json.fv7 = item.fv7;
          json.fv8 = item.fv8;
          json.fv11 = item.fv11 ? `第${item.fv11}周` : '';
          json.weekTime = `${item.fv3}~${item.fv4}`;
          json.updateBy = item.updateBy;
          json.createTime = item.createTime;
          json.pmTreeCreateBy = item.oaPmTree?.createBy;
          json.origin = item;
          return json;
        });
        this.tableData = tableData;
      } else {
        this.tableData = [];
      }
    },
    /**
		 * 格式化表头
		 * @param {Array} val - 表格数据数组
		 */
    formatterTableHeader(val) {
      const tableHeader = [
        { prop: 'fv1', label: '名称', width: 280 },
        { prop: 'pmTreeCreateBy', label: '负责人', width: 85 },
        { prop: 'updateBy', label: '填报人', width: 60 },
        { prop: 'fv6', label: '周报分类', width: 100 },
        { prop: 'fv7', label: '内容分类', width: 100 },
        { prop: 'fv8', label: '工作内容', width: 400 },
        { prop: 'createTime', label: '提交时间', width: 150 },
        { prop: 'fv11', label: '周数', width: 100 },
        { prop: 'weekTime', label: '周报时间', width: 150 }
      ]
      const otherHeader = [];
      this.tableHeader = [...tableHeader, ...otherHeader];
    },
    async getProjectByName({ name, bindId }) {
      const params = {
        fv1: '项目',
        enabled: 1,
        name,
        bindId,
        page: 0,
        size: 99
      };
      const res = await oaPmTree.getPmTreeSmall(params);
      return res.content;
    },
    async goProject(row) {
      const { otherInfo, bindId } = this.$config.projects_keys;
      const projectList = await this.getProjectByName({ name: row.fv1, bindId });
      let projectInfo = {}
      let id = ''
      if (projectList && projectList.length > 0) {
        projectInfo = projectList[0]
        id = projectInfo.id;
      }
      if (id) {
        this.$router.push({
          name: 'OaPproject',
          query: { projectId: id, id: String(bindId), docId: otherInfo, name: row.fv1, from: '4' }
        })
      }
    },
    preView(file) {
      const weekIndex = this.$refs.eheader.weekIndex;
      const otherParams = 'page=0&size=99999999&weeklyReport.enabled=1&sort=oaPmTree.createBy,desc';
      let fileName = '';
      if (this.query.fv3 && this.query.fv4) {
        fileName = `部门周报~${this.query.fv3}~${this.query.fv4}`;
      }
      const fv1 = `${this.query.fv1 || ''}`
      const fv6 = `${this.query.fv6 || ''}`
      const fv7 = `${this.query.fv7 || ''}`
      const fv3 = `${this.query.fv3 || ''}`
      const fv4 = `${this.query.fv4 || ''}`
      const pmId = `${this.query.pmId || ''}`
      const createBy = `${this.query.createBy || ''}`
      const updateBy = `${this.query.updateBy || ''}`
      const pmLeader = `${this.query.pmLeader || ''}`
      const params = `${otherParams}&weekIndex=${weekIndex}&fileName=${fileName}&weeklyReport.createBy=${createBy}&weeklyReport.updateBy=${updateBy}&weeklyReport.pmLeader=${pmLeader}&weeklyReport.fv3=${fv3}&weeklyReport.fv4=${fv4}&weeklyReport.fv1=${fv1}&weeklyReport.fv6=${fv6}&weeklyReport.fv7=${fv7}&weeklyReport.pmId=${pmId}&weeklyReport.bindId=${this.bindId}&weeklyReport.categoryId=${this.categoryId}`
      const url = `${this.omOaAPi}/${file}/html?${params}`;
      window.open(url)
    },
    downLoad(file) {
      const otherParams = 'page=0&size=99999999&weeklyReport.enabled=1&sort=oaPmTree.createBy,desc';
      let fileName = '';
      if (this.query.fv3 && this.query.fv4) {
        fileName = `部门周报~${this.query.fv3}~${this.query.fv4}`;
      }
      const fv1 = `${this.query.fv1 || ''}`
      const fv6 = `${this.query.fv6 || ''}`
      const fv7 = `${this.query.fv7 || ''}`
      const fv3 = `${this.query.fv3 || ''}`
      const fv4 = `${this.query.fv4 || ''}`
      const pmId = `${this.query.pmId || ''}`
      const createBy = `${this.query.createBy || ''}`
      const updateBy = `${this.query.updateBy || ''}`
      const pmLeader = `${this.query.pmLeader || ''}`
      const params = `${otherParams}&fileName=${fileName}&weeklyReport.createBy=${createBy}&weeklyReport.updateBy=${updateBy}&weeklyReport.pmLeader=${pmLeader}&weeklyReport.fv3=${fv3}&weeklyReport.fv4=${fv4}&weeklyReport.fv1=${fv1}&weeklyReport.fv6=${fv6}&weeklyReport.fv7=${fv7}&weeklyReport.pmId=${pmId}&weeklyReport.bindId=${this.bindId}&weeklyReport.categoryId=${this.categoryId}`
      const url = `${this.omOaAPi}/${file}/xls?${params}`;
      console.log(url);
      downloadUrl(url)
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
