<template>
  <div class="milestone">
    <el-row :gutter="20">
      <el-col :lg="7" :md="7" :sm="7" :xl="7" :xs="7">
        <div class="btn-box">
          <el-button
            v-permission="permission.add"
            size="mini"
            type="success"
            @click="addMilestone"
          >
            创建里程碑
          </el-button>
          <update-button
            v-if="bindId"
            :bind-id="bindId"
            :callback-fun="getList"
            :crud-method="oaPmMilestone"
            :enabled="[1]"
            :permission="permission"
          />
        </div>

        <ul class="milestone-list">
          <li
            v-for="(milestone,index) in dataList"
            :key="milestone.id"
            :class="{ 'selected': index === selectedMilestoneIndex }"
            @click="selectMilestone(index,milestone)"
          >
            <el-card shadow="always">
              <div>
                <h3>{{ milestone.title }}</h3>
                <p>{{ formatDateStringFromJson(milestone.fv2) }}</p>
                <el-progress
                  :percentage="getNumericValue(milestone.percent)"
                  :stroke-width="18"
                  :text-inside="true"
                  status="success"
                />
              </div>
              <el-dropdown
                class="dropdown-box"
                trigger="click"
                @command="commandValue => handleSetSize(milestone, commandValue)"
              >
                <div>
                  <i class="el-icon-more" />
                </div>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-for="item of btnOptions" :key="item.value" :command="item.value">
                    {{ item.label }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </el-card>
          </li>
        </ul>
        <Paging :page="pageInfo" @handleCurrentChange="getList()" />
      </el-col>
      <el-col :lg="17" :md="17" :sm="17" :xl="17" :xs="17">
        <!--任务列表-->
        <TaskListMilestone ref="taskListMilestoneRef" />
      </el-col>
    </el-row>
    <milestoneDialog ref="milestoneDialog" @succeSubmit="getList" />
  </div>

</template>

<script>
import milestoneDialog from '@/views/oaWorkOrder/oaProject/components/milestone/milestoneDialog.vue';
import oaPmMilestone from '@/api/oaWorkOrder/oaPmMilestone'
import updateButton from '@/components/UpdateButton2/index.vue'
import TaskListMilestone from '@/views/oaWorkOrder/oaProject/components/milestone/TaskListMilestone.vue';

export default {
  name: 'Milestone',
  components: { milestoneDialog, updateButton, TaskListMilestone },
  props: {},
  data() {
    return {
      permission: {
        del: ['admin', 'oaPmMilestone:del'],
        add: ['admin', 'oaPmMilestone:add'],
        edit: ['admin', 'oaPmMilestone:edit'],
        updateT: ['admin', 'oaPmMilestone:updateFormStruct'],
        updateR: ['admin', 'oaPmMilestone:updateRelation']
      },
      pageInfo: {
        size: 10,
        page: 1,
        total: 0
        // pageSizes: [2, 5, 10]
      },
      dataList: [],
      bindId: '',
      oaPmMilestone: { ...oaPmMilestone },
      btnOptions: [
        { label: '编辑', value: '编辑' },
        { label: '删除', value: '删除' }
      ],
      selectedMilestoneIndex: 0
    }
  },

  computed: {
    getNumericValue() {
      return (percent) => {
        if (percent) {
          return parseInt(percent.replace('%', ''), 10);
        }
        return 0;
      }
    }
  },
  watch: {},
  created() {
    this.initData();
  },
  methods: {
    selectMilestone(index, item) {
      this.selectedMilestoneIndex = index;
      // 这里可以添加其他逻辑，比如控制右侧列表的展示
      if (this.$refs.taskListMilestoneRef) {
        this.$refs.taskListMilestoneRef.upDataTableData(item);
      }
    },
    handleSetSize(item, val) {
      switch (val) {
        case '编辑':
          this.editForm(item)
          break;
        case '删除':
          this.handleDelete(item)
          break;
        default:
          break;
      }
    },
    async initData() {
      const { bindId } = this.$config.milestone_keys
      this.bindId = bindId;
      await this.getList();
      this.selectMilestone(0, this.dataList[0])
    },
    addMilestone() {
      this.$refs.milestoneDialog.init({ bindId: this.bindId });
    },
    /** 查询表单列表 */
    async getList() {
      this.loading = true;
      const { projectId: pmId, name: fv18 } = this.$route.query
      const data = {
        page: this.pageInfo.page - 1,
        size: this.pageInfo.size,
        enabled: 1,
        sort: 'id,desc',
        bindId: this.bindId,
        fv12: '已审核',
        fv18,
        pmId,
        fv1: '任务'
      };
      await oaPmMilestone.get(data).then(response => {
        this.dataList = response.content;
        this.pageInfo.total = response.totalElements;
        this.loading = false;
      })
    },
    editForm(item) {
      this.$refs.milestoneDialog.init({ bindId: this.bindId, rowId: item.id });
    },
    handleQuery() {
      this.pageInfo.size = 10;
      this.pageInfo.page = 1;
      this.getList()
    },
    handleDelete(row) {
      this.$confirm('确定删除该里程碑, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        oaPmMilestone.del([row.id]).then(() => {
          this.getList()
          this.$notify({
            title: '删除成功',
            type: 'success',
            duration: 2500
          })
        })
      }).catch(() => {
        this.$notify({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    handleSelectionChange() {
    },
    formatDateStringFromJson(jsonString) {
      try {
        const [startDate, endDate] = JSON.parse(jsonString);
        if (startDate && endDate) {
          return `${startDate} ~ ${endDate}`;
        }
      } catch (e) {
        // 如果出现错误，不做任何处理，直接返回原始字符串
      }
      return jsonString;
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.btn-box {
	margin-bottom: 15px;
}

.milestone-list {
	> li {
		margin-bottom: 10px;
		cursor: pointer;
		position: relative;

		h3 {
			font-size: 16px;
		}

		p {
			font-size: 14px;
			margin-bottom: 5px;
		}

		.dropdown-box {
			position: absolute;
			right: 20px;
			top: 10px;
		}
	}

	> li.selected {
		::v-deep .el-card {
			background: #d4e4ff !important;
		}

	}
}
</style>
