<template>
  <div
    v-if="crud.props.searchToggle"
  >
    <el-input
      v-model="query.name"
      class="filter-item"
      clearable
      placeholder="输入任务名称"
      size="small"
      style="width: 200px;"
    />
    <!--<date-range-picker v-model="query.createTime" class="date-item" />-->
    <rrOperation />
  </div>
</template>

<script>
import { header } from '@crud/crud';
import rrOperation from '@crud/RR.operation.vue';
// import DateRangePicker from '@/components/DateRangePicker';

export default {
  components: { rrOperation },
  mixins: [header()],
  props: {
    permission: {
      type: Object,
      required: true
    }
  }
}
</script>
