<template>
  <el-dialog :visible.sync="dialog" append-to-body title="执行脚本" fullscreen>
    <el-row>
      <el-col :span="12">
        <div class="grid-content bg-purple">
          <Yaml v-if="isYamlReady" ref="yaml" style="width: 100%;" :value="value" :height="height" />
          <el-button type="success" class="do-run-btn" @click="doScriptRun">运行</el-button>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="grid-content bg-purple-light">
          <json-viewer
            :value="result"
            :expand-depth="50"
            copyable
            boxed
            sort
          />
        </div>
      </el-col>
    </el-row>
  </el-dialog>
</template>

<script>
import Yaml from '@/components/YamlEdit/index'
import crudJob from '@/api/system/timing'
export default {
  components: { Yaml },
  data() {
    return {
      height: 500 + 'px',
      value: '',
      dialog: false,
      isYamlReady: false,
      result: ''
    }
  },
  mounted() {
    setTimeout(() => {
      this.isYamlReady = true;
    }, 2000);
  },
  methods: {
    doScriptRun() {
      this.result = ''
      this.$refs.yaml.getValue()
      crudJob.runSql({ text: this.$refs.yaml.getValue() }).then(res => {
        this.result = res;
      })
    }
  }
}
</script>

<style scoped>
.do-run-btn {
  margin: 20px 0;
  float: right;

}
</style>
