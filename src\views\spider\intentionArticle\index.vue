<template>
  <!--意向招标-->
  <div class="app-container">
    <!-- 表格 -->
    <div class="body-box">
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="tableData"
        :header-cell-class-name="handleHeadAddClass"
        style="width: 100%;"
        @select="crud.selectChange"
        @select-all="crud.selectAllChange"
        @selection-change="crud.selectionChangeHandler"
        @sort-change="sortChangeHandler"
      >
        <el-table-column :fixed="true" type="selection" width="55" />
        <el-table-column
          v-for="item in tableHeader"
          :key="item.prop"
          :align="item.align || 'center'"
          :fixed="item.fixed || false"
          :header-align="item.headerAlign || 'center'"
          :label="item.label"
          :prop="item.prop"
          :show-overflow-tooltip="true"
          :sortable="item.sortable || ''"
          :width="item.width"
        >
          <template #default="{row}">
            <template v-if="item.prop === 'fv3'">
              {{ dict.label['announcement_type'][row[item.prop]] }}
            </template>
            <span v-else>{{ row[item.prop] }}</span>
          </template>
        </el-table-column>
        <!--   编辑与删除   -->
        <el-table-column
          align="left"
          fixed="right"
          label="操作"
          width="300"
        >
          <template slot-scope="scope">
            <el-button size="mini" type="success" @click="toDetail(scope.row,1)">详情</el-button>
            <el-button v-permission="permission.labelEdit" size="mini" type="primary" @click="toDetail(scope.row,2)">
              标签
            </el-button>
            <el-button v-permission="permission.attention" size="mini" type="warning" @click="addTtention(scope.row)">
              {{ attention[scope.row.status] }}
            </el-button>
            <el-button
              v-if="scope.row.type == '采购意向'"
              v-permission="permission.relevance"
              size="mini"
              type="primary"
              @click="toRelevance(scope.row)"
            >关联
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import spiderArticleApi from '@/api/spider/spiderArticle'
// import spiderReadAttention from '@/api/spider2/spiderReadAttention';
import {
  formatterTableData,
  formatterIntentTableHeader,
  follow,
  toDetail,
  toRelevance,
  publicChangeHandler
} from '@/views/spider/utils/spider';
import { permission } from '@/views/spider/utils/field';
import CRUD, { presenter, form } from '@crud/crud'
import pagination from '@crud/Pagination'
import { header } from '@crud/crud'
import { mapGetters } from 'vuex';

const defaultForm = { id: null }
const attention = {
  unAttention: '关注',
  attention: '取关'
}
export default {
  name: 'IntentionArticle',
  components: { pagination },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: true,
      title: '文章信息',
      url: 'spider/api/viewArticleBase',
      sort: [],
      query: { enabled: 1, categoryId: null, fv3: 0 },
      crudMethod: { ...spiderArticleApi },
      optShow: {
        add: false,
        edit: false,
        del: false,
        download: true,
        reset: true,
        rightGroup: false
      }
    })
  },
  mixins: [presenter(), form(defaultForm), header()],
  dicts: ['announcement_type'],
  data() {
    return {
      permission,
      attention,
      tableData: [],
      tableHeader: [],
      bindId: '',
      isFirstEnter: true,
      sortField: {}
    };
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  watch: {
    'crud.data': {
      handler(val, oldVal) {
        this.tableData = formatterTableData(val)
        this.tableHeader = formatterIntentTableHeader()
      },
      deep: true
    }
  },
  created() {
    this.isFirstEnter = false
  },
  activated() {
    if (this.isFirstEnter) {
      return
    }
    this.crud.refresh();
  },
  methods: {
    [CRUD.HOOK.beforeResetQuery]() {
      this.crud.sort = []
      this.$refs.table.$el.querySelectorAll('.is-sortable').forEach((item) => {
        // 移除table表头中的排序样式descending和ascending
        item.classList.remove('descending');
        item.classList.remove('ascending');
      });
      Object.getOwnPropertyNames(this.sortField).forEach(key => {
        delete this.sortField[key];
      });
    },
    addTtention(row) {
      try {
        follow(row, {}, this.fllowCallback)
      } catch (error) {
        console.error('Error toggling attention:', error);
      }
    },
    fllowCallback(message) {
      this.$notify({
        title: message,
        type: 'success',
        duration: 2500
      });
      this.crud.refresh();
    },
    sortChangeHandler(data) {
      this.crud.sort = publicChangeHandler(data, this.sortField)
      this.crud.refresh();
    },

    handleHeadAddClass({ column }) {
      if (this.sortField[column.property]) {
        column.order = this.sortField[column.property];
      }
    },
    toDetail,
    toRelevance
  }
};
</script>

<style lang="scss" scoped>

</style>
