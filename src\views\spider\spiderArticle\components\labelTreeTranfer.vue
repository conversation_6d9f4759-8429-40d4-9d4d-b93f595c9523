<template>
  <div class="label-tree-tranfer">
    <el-card class="label-tree-card card-item">
      <div slot="header" class="box-card-cus">
        <span>全部标签</span>
      </div>
      <el-tree
        ref="leftTree"
        :check-strictly="true"
        :data="projectLabels"
        :props="defaultProps"
        default-expand-all
        node-key="id"
        show-checkbox
      />
    </el-card>
    <div class="card-item-center">
      <el-button @click="concelSelect">去左边</el-button>
      <el-button @click="sureSelect">去右边</el-button>

    </div>
    <el-card class="label-tree-card card-item">
      <div slot="header" class="box-card-cus clearfix">
        <span>当前标签</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="saveLabel">保存标签</el-button>
      </div>
      <el-tree
        ref="rightTree"
        :check-strictly="true"
        :data="selectedData"
        :props="defaultProps"
        default-expand-all
        empty-text="暂无当前标签"
        node-key="id"
        show-checkbox
      />
    </el-card>

  </div>
</template>

<script>

import crudCategory from '@/api/system/category';

export default {
  name: 'LabelTreeTranfer',
  props: {
    initLabel: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      selectedData: [],
      projectLabels: []
    };
  },
  // computed: {
  //   ...mapGetters([
  //     'projectLabels'
  //   ])
  // },
  async created() {
    await this.getProjectLabels()
  },
  mounted() {
    this.selectedData = this.initLabel ? JSON.parse(this.initLabel) : [];
    this.selectLeftTree()
  },
  methods: {
    getProjectLabels() {
      const pid = this.$config.labelTree_key.categoryId;
      crudCategory.getChildren([pid]).then((res) => {
        const data = res.content;
        const childrenData = data[0].children;
        this.projectLabels = childrenData.map(item => {
          item.disabled = item.hasChildren
          return item
        })
      })
    },
    concelSelect() {
      const rightSelectData = this.$refs.rightTree.getCheckedNodes();
      this.selectedData = this.selectedData.filter(obj => !rightSelectData.some(item => item.id === obj.id));
      this.selectLeftTree()
    },
    sureSelect() {
      this.selectedData = this.$refs.leftTree.getCheckedNodes();
    },
    saveLabel() {
      this.$emit('saveLabel', this.selectedData);
    },
    selectLeftTree() {
      const checkedKeys = this.selectedData.map(obj => obj.id);
      this.$refs.leftTree.setCheckedKeys(checkedKeys);
    }
  }

};
</script>

<style lang="scss" scoped>
.label-tree-tranfer {
	display: flex;
	align-items: center;
	justify-content: center;

	.card-item {
		width: 300px;
		height: 380px !important;
		overflow: hidden;

		::v-deep.el-card__body {
			height: 330px;
			overflow: auto !important;
		}
	}

	.card-item-center {
		width: 150px;
		margin: 0 20px;
	}

	.label-tree-card {
		position: relative;

		.el-card__header {
			position: sticky;
			top: 58px;

		}
	}
}
</style>
