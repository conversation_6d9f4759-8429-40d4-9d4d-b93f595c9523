<template>
  <div class="app-container spider-detail">
    <template v-for="item in detailInfo">
      <el-card :key="item.id" class="box-card">
        <div slot="header" class=" box-card-cus">
          <span>{{ dict.label.announcement_type[item.fv3] }}</span>
          <div>
            <el-button style="padding: 3px 0;margin-right: 10px;" type="text" @click="goSpider(item)">原文链接</el-button>
          </div>
        </div>
        <!--固定好的字段信息-->
        <el-descriptions :column="Column" size="medium">
          <el-descriptions-item v-for="filedItem in field[item.fv3].fields" :key="filedItem.id" :label="filedItem.label">
            {{ filedShowText(item[filedItem.value]) }}
          </el-descriptions-item>
        </el-descriptions>
        <!--补充的字段信息-->
        <el-descriptions :column="Column" size="medium">
          <el-descriptions-item v-for="otherfiledItem in item.ft2" :key="otherfiledItem.id" :label="otherfiledItem.label">
            {{ otherfiledItem.value }}
          </el-descriptions-item>
        </el-descriptions>
        <!--附件信息-->
        <el-descriptions :column="Column" size="medium">
          <el-descriptions-item label="附件信息">
            <div class="attachment-list">
              <p v-for="subItem in item.attachment" :key="subItem.name" @click="downLoadAttach(subItem)">
                <el-link type="success">
                  {{ subItem.name }}
                </el-link>
              </p>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </template>
    <el-card>
      <relevanxy-list @toRelevance="toRelevance" />

    </el-card>

  </div>
</template>

<script>
import spiderArticleApi, { relevance } from '@/api/spider/spiderArticle'
import { downloadUrl } from '@/utils/index'
import { field } from '@/views/spider/utils/field';
import { mapGetters } from 'vuex';
import RelevanxyList from '@/views/spider/spiderArticle/components/relevanxyList.vue';

const Column = 2
export default {
  name: 'SpiderWebAdminDetail',
  dicts: ['announcement_type'],
  components: {
    RelevanxyList
  },
  data() {
    return {
      detailInfo: [],
      Column,
      field,
      correctionFormVisible: false,
      detailType: 1
    };
  },
  computed: {
    filedShowText() {
      return function(value) {
        if (value === null || value === undefined || value === 'null') {
          return ''
        }
        return value
      }
    },
    ...mapGetters([
      'currentProject',
      'projectLabels'
    ])
  },
  created() {
    this.getDetail()
  },
  methods: {
    getDetail() {
      const query = {
        fv1: this.currentProject.projectName,
        enabled: 1,
        sort: 'fv11,desc'
      }
      spiderArticleApi.get(query).then(res => {
        const data = res.content || [];
        this.detailInfo = data.map(item => {
          item.attachment = JSON.parse(item.formData)?.attachment || [];
          item.ft2 = item.ft2 ? JSON.parse(item.ft2).custom : [];
          return item
        })
      })
    },
    downLoadAttach(item) {
      downloadUrl(item.href, item.name)
    },
    toRelevance(row) {
      const data = {
        fv1: this.currentProject.projectName,
        fv2: row.projectName,
        fv3: row.projectNo,
        enabled: 1
      }
      relevance(data).then(res => {
        this.$notify({
          title: '操作成功',
          type: 'success',
          duration: 2500
        })
        this.$router.go(-1);
      })
    }
  }
};
</script>

<style lang="scss" scoped>
.spider-detail {
	padding-left: 40px;

	.box-card {
		margin-bottom: 20px;

		.box-card-cus {
			padding: 0 16px;
			display: flex;
			justify-content: space-between;
		}
	}
}

.attachment-list {
	display: flex;
	flex-direction: column;
}

</style>
