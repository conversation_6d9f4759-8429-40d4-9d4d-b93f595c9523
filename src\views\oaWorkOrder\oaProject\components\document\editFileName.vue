<template>
  <el-dialog
    v-dialog-drag
    :before-close="concelForm"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :title="title"
    :visible.sync="visible"
    append-to-body
    width="600px"
  >
    <el-form ref="elForm" :model="elForm" :rules="elRule" label-width="120px" size="small">
      <el-form-item label="旧文件名称">
        {{ oldFileName }}
      </el-form-item>
      <el-form-item label="新文件名称" prop="fileName">
        <el-input v-model="elForm.fileName" placeholder="请输入内容" style="width: 100%;">
          <template slot="append">{{ elForm.fileFormat }}</template>
        </el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button type="text" @click="concelForm">取消</el-button>
      <el-button
        :disabled="submitDisabled"
        type="primary"
        @click="submitAction"
      >
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import oaDocument from '@/api/oaWorkOrder/oaDocument';
import { splitFileName } from '@/utils'

export default {
  name: 'EditFileName',
  data() {
    return {
      submitDisabled: false,
      visible: false,
      title: '修改文件名称',
      oldFileName: '',
      elForm: {
        fileName: '',
        fileFormat: ''
      },
      ft1: '',
      elRule: {
        fileName: [
          { required: true, message: '请输入新的目录名称', trigger: 'change' }
        ]
      }
    }
  },
  created() {
  },
  methods: {
    initForm(data) {
      this.oldFileName = data.fileName
      this.visible = true;
      const { fileName, fileFormat } = splitFileName(data.fileName);
      this.ft1 = data.ft1
      this.elForm = {
        ...JSON.parse(JSON.stringify(data)),
        fileName,
        fileFormat
      };
    },

    concelForm() {
      this.visible = false
      this.$refs['elForm'].resetFields();
    },

    handleFinalData() {
      const { fileName, fileFormat } = this.elForm; // 获取pmId
      // 遍历并处理this.selections数组
      this.ft1.raw.response.originalFilename = `${fileName}.${fileFormat}`
      return {
        id: this.elForm.id,
        enabled: 1,
        ft1: JSON.stringify(this.ft1)
      }
    },
    submitAction() {
      this.$refs['elForm'].validate((valid) => {
        if (valid) {
          const data = this.handleFinalData()
          oaDocument.edit([data]).then(response => {
            this.$notify({
              title: `修改成功`,
              type: 'success',
              duration: 2500
            })
            this.concelForm();
            this.$emit('success')
          }).catch((e) => {
            console.log(e);
          })
        } else {
          this.submitDisabled = false
          return false
        }
      });
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
