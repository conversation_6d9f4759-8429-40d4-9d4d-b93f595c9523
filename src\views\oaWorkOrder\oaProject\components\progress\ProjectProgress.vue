<template>
  <div class="project-tree">
    <div class="head-container">
      <el-input
        v-model="projectKey"
        class="filter-item"
        clearable
        placeholder="输入名称搜索"
        prefix-icon="el-icon-search"
        size="small"
        style="width: 300px;"
        @input="loadProjectNode"
      />
    </div>
    <div class="tree-box">
      <el-tree
        ref="tree"
        :data="projectList"
        :expand-on-click-node="false"
        :load="loadProjectNode"
        :props="defaultProps"
        highlight-current
        lazy
        node-key="id"
      >
        <template v-slot="{ node, data }">
          <span class="custom-tree-node">
            <template v-if="checkPer(permission.edit)">
              <el-popover
                placement="right"
                popper-class="poject-tree-el-popover"
                trigger="hover"
              >
                <el-button
                  size="mini"
                  type="text"
                  @click="editProjress(data)"
                >编辑
                </el-button>
                <span
                  slot="reference"
                  class="node-label"
                >
                  <tree-node-item :tree-data="data" />
                </span>
              </el-popover>
            </template>

            <template v-else>
              <tree-node-item :tree-data="data" />
            </template>

          </span>
        </template>
      </el-tree>
    </div>
    <!-- 进度弹框 -->
    <progress-form ref="progressRef" @success="loadProjectNode" />
  </div>
</template>

<script>
import oaPmTree from '@/api/oaWorkOrder/oaPmTree'
import { mapGetters } from 'vuex';
import ProgressForm from '@/views/oaWorkOrder/oaProject/components/progress/ProgressForm';
import TreeNodeItem from '@/views/oaWorkOrder/oaProject/components/progress/TreeNodeItem.vue';

export default {
  name: 'ProjectTree',
  components: { ProgressForm, TreeNodeItem },

  data() {
    return {
      addDirectoryVisible: true,
      projectKey: '',
      defaultProps: {
        isLeaf: 'leaf',
        children: 'children', // 子节点字段名
        label: 'label' // 节点文本字段名
      },
      permission: {
        add: ['admin', 'oaPmTree:add'],
        edit: ['admin', 'oaPmTree:edit'],
        del: ['admin', 'oaPmTree:del'],
        addMember: ['admin', 'oaPmMember:edit'],
        updateT: ['admin', 'oaPmTree:updateFormStruct'],
        updateR: ['admin', 'oaPmTree:updateRelation'],
        upload: ['admin', 'oaDocument:add'],
        dispatch: ['admin', 'oaWorkOrderInfo:add']
      },
      bindId: '',
      projectList: [],
      projectId: '',
      type: 1,
      currentProjectName: ''
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ]),

    statusColor() {
      // const colors = ['red', '#1890ff'];
      const colors = {
        '已审核': '#1890ff',
        '逾期': 'red'
      }
      return data => colors[data.fv12] || 'red';// 1 是已完成状态，0 是未完成状态
    }
  },
  methods: {

    // 懒加载数据
    async loadProjectNode(node, resolve) {
      const { projectId, name } = this.$route.query;
      const sort = 'sort,asc';
      const params = { fv18: name, sort, enabled: 1, size: 9999, fv1: '目录' };

      if (typeof node !== 'object') {
        if (node) {
          params['name'] = node;
        } else {
          params['pid'] = projectId;
        }
      } else {
        if (node.level !== 0) {
          params['pid'] = node.data.id;
        } else if (projectId) {
          params['pid'] = projectId;
        }
      }
      try {
        const response = await oaPmTree.getPmTreeSmall(params);
        const data = response.content;
        if (resolve) {
          resolve(data);
        } else {
          this.projectList = data;
        }
      } catch (error) {
        // 处理错误
        console.error('Failed to load project nodes:', error);
      }
    },
    editProjress(data) {
      this.$refs.progressRef.initForm(data);
    }

  }
}
</script>

<style lang="scss" scoped>
.project-tree {
	.tree-box {
		//padding: 20px 0;

		.current-project {
			max-width: 80%;
			margin-bottom: 10px;
		}

		::v-deep .el-tree--highlight-current {
			.el-tree-node.is-current
			> .el-tree-node__content {
				background: #ececec;

				.node-label {
					color: #409eff;
				}
			}
		}

		::v-deep .el-tree-node:hover > .el-tree-node__content {

			background: #ececec;

			.node-label {
				color: #409eff;
			}

		}

	}

}

</style>

<style>
.poject-tree-el-popover {
	padding: 5px !important
}
</style>
