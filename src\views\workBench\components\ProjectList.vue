<template>
  <el-card>
    <div v-loading="headerLoding" class="project-list">
      <div class="top-title">{{ title }}</div>
      <div class="pre-line" />
      <div ref="headerListCom" class="header-box">
        <ul class="header-list">
          <li
            v-for="(item) in projectList"
            :key="Object.keys(item)[0]"
            :class="Object.keys(item)[0]== projectId ? 'active' :'' "
            @click="toQuery(item)"
          >
            <span>{{ Object.keys(item)[0] }}</span>
            <span>({{ Object.values(item)[0] }})</span>
            <i class="el-icon-d-arrow-right" @click.stop="toDeskList(item)" />
          </li>
        </ul>
        <div v-if="isOpenShowCom" class="open-Btn" @click="openShow($refs.headerListCom,'isOpenCom')">
          <i :class="isOpenCom ? 'el-icon-arrow-up' :'el-icon-arrow-down'" />
          <span>{{ isOpenCom ? '收起' : '展开' }}</span>
        </div>
        <div class="header-line" />
      </div>
    </div>

  </el-card>
</template>

<script>

export default {
  name: 'ProjectList',

  props: {
    title: {
      type: String,
      default: '项目列表'
    },
    projectList: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      projectId: '',
      isOpenShowCom: false,
      isOpenCom: false,
      headerLoding: true
    }
  },

  computed: {},
  watch: {
    projectList: {
      handler(newVal, oldVal) {
        this.initFun()
      },
      deep: true // 开启深度监听
    },
    projectId: {
      handler(newVal, oldVal) {
        this.$emit('changeProjectId', this.projectId)
      }
    }
  },
  methods: {
    async initFun() {
      if (this.projectList.length > 0) {
        this.projectId = Object.keys(this.projectList[0])[0]
        setTimeout(() => {
          this.headerLoding = false;
        }, 500)
        await this.$nextTick()
        this.justHeight(this.$refs.headerListCom, 'isOpenShowCom');
      } else {
        this.headerLoding = false;
      }
    },
    // 到其他页面
    toDeskList(data) {
      console.log(data, '<===>', 'data')
    },
    // 查询
    toQuery(data) {
      this.projectId = Object.keys(data)[0]
    },
    justHeight($ele, isShow) {
      setTimeout(() => {
        this.headerLoding = false;
        this.loding = false;
        if ($ele.offsetHeight >= 41) {
          $ele.style.height = '40px';
          this[isShow] = true
        } else {
          $ele.style.height = '40px';
          this[isShow] = false
        }
      }, 500)
    },
    openShow($ele, isOpen) {
      this[isOpen] = !this[isOpen];
      if (this[isOpen]) {
        $ele.style.height = 'auto'
        $ele.style.maxHeight = '';
      } else {
        $ele.style.height = '40px'
      }
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.project-list {
	display: flex;
	//background-color: red;
	width: 100%;

	.top-title {
		min-width: 60px;
		font-weight: 500;
		font-size: 14px;
		line-height: 30px;
		color: #364359;
		margin-right: 0px;
	}

	.pre-line {
		width: 1px;
		height: auto;
		background: #E9ECF1;
		margin: 0 20px;
	}

	.header-box {
		width: 100%;
		position: relative;
		display: flex;

		.open-Btn {
			display: flex;
			align-items: center;
			float: right;
			width: 60px;
			height: 40px;
			margin-left: 20px;
			cursor: pointer;

			i {
				color: #374C86;
				margin-right: 6px;
			}

			span {
				font-size: 14px;
				line-height: 30px;
				color: #364359;

			}
		}

		.header-list {
			width: 99%;
			display: flex;
			flex-wrap: wrap;
			overflow: hidden;

			li {
				height: 30px;
				line-height: 30px;
				font-size: 14px;
				color: #364359;
				margin-right: 20px;
				margin-bottom: 10px;
				cursor: pointer;
				transition: all 0.4s ease;

				i {
					opacity: 0;
				}

				&:hover, &.active {
					span {
						color: #409EFF;
					}
				}

				&:hover {
					i {
						opacity: 1;
						color: #409EFF;
					}
				}
			}
		}

		.header-line {
			position: absolute;
			left: 0;
			bottom: 0;
			width: 100%;
			height: 1px;
			background: #E9ECF1;
		}
	}
}
</style>
