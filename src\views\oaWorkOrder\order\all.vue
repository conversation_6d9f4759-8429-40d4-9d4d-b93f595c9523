<template>
  <div class="app-container">
    <el-card class="box-card">
      <el-form ref="listQuery" :model="listQuery" :inline="true">
        <WorkOrderSearch :genre="'all'" @handleSearch="handleSearch" @reset="reset" />
      </el-form>
      <el-button v-if="isExport" type="success" style="float:right;margin:10px" @click="dowProcess">导出</el-button>
      <el-table v-loading="loading" :data="ticketList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column label="ID" prop="id" width="120" /> -->
        <el-table-column label="标题" prop="title" min-width="120" :show-overflow-tooltip="true" />
        <el-table-column label="流程" prop="process.name" :show-overflow-tooltip="true" />
        <el-table-column label="当前状态" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>
              {{ scope.row.state ? scope.row.state[0].label : '-' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="申请人" prop="createBy" :show-overflow-tooltip="true" />
        <el-table-column label="当前处理人" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>
              <template v-for="item in scope.row.current">
                {{ item }}
              </template>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="优先级" :show-overflow-tooltip="true" width="120" align="left">
          <template slot-scope="scope">
            <span v-if="scope.row.priority===2">
              <el-tag type="warning">紧急</el-tag>
            </span>
            <span v-else-if="scope.row.priority===3">
              <el-tag type="danger">非常紧急</el-tag>
            </span>
            <span v-else>
              <el-tag type="success">一般</el-tag>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="是否结束" :show-overflow-tooltip="true" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.isEnd===0" size="mini" type="success">否</el-tag>
            <el-tag v-else size="mini" type="danger">是</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="create_time">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="left" class-name="small-padding fixed-width" width="320">
          <template slot-scope="scope">
            <el-button
              v-permission="['admin','oaWorkOrderInfo:list']"
              size="mini"
              type="primary"
              @click="handleView(scope.row)"
            >查看</el-button>
            <el-button
              v-if="scope.row.isEnd===0"
              v-permission="['admin','oaWorkOrderInfo:forward']"
              size="mini"
              type="success"
              @click="handleInversion(scope.row)"
            >转交</el-button>
            <!-- <el-button-->
            <!--v-if="scope.row.isEnd===0"-->
            <!--v-permission="['admin','all:end']"-->
            <!--size="mini"-->
            <!--type="text"-->
            <!--icon="el-icon-switch-button"-->
            <!--@click="handleUnity(scope.row)"-->
            <!--&gt;结单</el-button> -->
            <el-button
              v-permission="['admin','oaWorkOrderInfo:del']"
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
            >删除</el-button>
            <el-button
              v-if="scope.row.isEnd===0"
              v-permission="['admin','oaWorkOrderInfo:end']"
              size="mini"
              type="warning"
              @click="handleUnity(scope.row)"
            >结单</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--转交组件-->
      <forward-orde ref="forwardOrde" @reset="getList" />
      <!--分页组件-->
      <Paging :page="pageInfo" @handleCurrentChange="getList()" />
    </el-card>
  </div>
</template>

<script>
import { delWorkOrderInfo, endWorkOrder, getWorkOrderInfo } from '@/api/oaWorkOrder/workOrder';
import WorkOrderSearch from './components/search.vue'
import ForwardOrde from './components/forwardOrde.vue';
import { exportProcess } from '@/api/process/oaWorkOrderInfo'
import { downloadFile } from '@/utils/index'
export default {
  components: { WorkOrderSearch, ForwardOrde },
  data() {
    return {
      users: [],
      dialogVisible: false,
      queryParams: {},
      total: 0,
      loading: false,
      ticketList: [],
      listQuery: {}, // 查询参数
      pageInfo: {
        size: 10,
        page: 1,
        total: 0
      },

      isExport: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    reset() {
      this.listQuery = {};
      this.getList();
    },
    // 获取---全部工单
    getList() {
      this.loading = true;
      const data = {
        page: this.pageInfo.page - 1,
        size: this.pageInfo.size,
        sort: 'id,desc',
        // title:
        enabled: true,
        ...this.listQuery
      }
      getWorkOrderInfo(data).then(res => {
        this.ticketList = res.content
        this.pageInfo.total = res.totalElements
        this.loading = false;
      })
    },
    handleSearch(val) {
      for (var k in val) {
        this.listQuery[k] = val[k]
      }
      // if (this.listQuery && this.listQuery.classify == 23) {
      //   this.isExport = true;
      // } else {
      //   this.isExport = false;
      // }
      console.log(this.listQuery.classify);
      this.getList()
    },
    // 查看工单
    handleView(row) {
      this.$router.push({ name: 'ProcessListHandle', query: { workOrderId: row.id, processId: row.process.id }})
    },
    // 删除工单
    handleDelete(row) {
      this.$confirm('此操作将删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delWorkOrderInfo([row.id]).then(res => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    // 结束工单
    handleUnity(row) {
      this.$confirm('此操作将会结束该工单, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        endWorkOrder({ id: row.id }).then(res => {
          this.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    // 点击转交
    handleInversion(row) {
      this.$refs.forwardOrde.init(row)
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    dowProcess() {
      if (this.multipleSelection.length) {
        const data = {
          fileName: '议程单',
          templateName: 'ycd',
          ids: this.multipleSelection.map((item) => {
            return item.id
          })
        }
        console.log(data);
        exportProcess(data).then((res) => {
          downloadFile(res, '议程单', 'doc');
        })
      } else {
        this.$notify({
          title: '提示',
          message: '请选择你要导出的议题！',
          type: 'warning',
          duration: 2000
        });
      }
    }
  }
}
</script>

<style scoped>

</style>
