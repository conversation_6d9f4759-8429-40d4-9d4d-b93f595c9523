<template>
  <div class="task-dialog">
    <el-dialog
      :before-close="cancelForm"
      :close-on-click-modal="false"
      title="盘库"
      :visible.sync="visible"
      append-to-body
      width="800px"
    >

      <div v-if="showFormData" class="text item task-content">
        <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="130px">
          <el-form-item label="库房名称">
            <span>{{ displayBasicData && displayBasicData.depot && displayBasicData.depot.title }}</span>
          </el-form-item>
          <el-form-item label="项目名称">
            <span>{{ displayBasicData && displayBasicData.pm && displayBasicData.pm.name }}</span>
          </el-form-item>
          <el-form-item label="设备">
            <span>{{ displayBasicData && displayBasicData.device && displayBasicData.device.name }}</span>
          </el-form-item>
          <el-form-item label="品牌">
            <span>{{ displayBasicData && displayBasicData.brand && displayBasicData.brand.name }}</span>
          </el-form-item>
          <el-form-item label="型号">
            <span>{{ displayBasicData && displayBasicData.model && displayBasicData.model.name }}</span>
          </el-form-item>
          <el-form-item label="库存数量">
            <span>{{ ruleForm && ruleForm.stockAmount }}</span>
          </el-form-item>
          <!-- <el-form-item label="盘库数量">
            <span>{{ displayBasicData && displayBasicData.stockCount }}</span>
          </el-form-item> -->
          <!--表单项-->
          <el-form-item label="盘库数量" prop="stockCount">
            <el-input-number
              v-model="ruleForm.stockCount"
              :min="0"
              :precision="0"
              :step="1"
              controls-position="right"
              placeholder="请输入盘库数量"
              style="width: 220px"
            />
          </el-form-item>
        </el-form>
        <!-- <fm-generate-form
          :ref="'generateForm'"
          :data="formStruct"
          :preview="viewOrEdit"
          :remote="remoteFunc"
          :value="formData"
        /> -->
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="cancelForm">取消</el-button>
        <el-button
          :disabled="submitDisabled"
          type="primary"
          @click="submitAction"
        >
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth';
// import extendBindTpl from '@/api/system/extendBindTpl';
import { mapGetters } from 'vuex';
import amStock from '@/api/property/amStock'
export default {
  components: {},
  data() {
    return {
      visible: false,
      ruleForm: {
        basicData: {},
        enabled: 1,
        stockCount: 0,
        id: null
      },
      displayBasicData: {},
      rules: {
        stockCount: [
          { required: true, message: '请输入盘库数量', trigger: 'blur' }
        ]
      },
      formStruct: {},
      formData: {},
      jsonData: {},
      submitDisabled: false,
      viewOrEdit: false,
      showFormData: false,
      remoteFunc: {
        funcGetToken(resolve) {
          resolve(getToken())
        }
      }
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  methods: {
    init(info) {
      this.visible = true;
      const { id } = info;
      this.bindId = this.$config.stocktaking_key.bindId
      // this.ruleForm.basicData = { basicNo: basicNo }
      // this.ruleForm.id = id
      this.getBasicData(id)
      // this.getProcessNodeList(this.bindId);
    },
    async getBasicData(id) {
      await amStock.get({ id, enabled: 1 }).then(res => {
        if (res && res.content) {
          const jsonData = res.content[0];
          this.ruleForm = {
            basicData: jsonData.basicData,
            id: jsonData.id,
            enabled: jsonData.enabled,
            stockInAmount: jsonData.stockInAmount,
            stockAmount: jsonData.stockAmount,
            stockOutAmount: jsonData.stockOutAmount,
            stockCount: 0
          }
          this.displayBasicData = {
            ...jsonData.basicData
          }
          this.showFormData = true;
        }
      })
    },
    // getProcessNodeList(id) {
    //   this.loading = true;
    //   const data = { id, enabled: 1 }
    //   extendBindTpl.get(data).then(res => {
    //     if (res && res.content && res.content.length) {
    //       this.processStructureValue = res.content[0];
    //       this.formStruct = JSON.parse(this.processStructureValue.extendTpl.formStruct);
    //     }
    //     this.showFormData = true;
    //   }).finally(() => {
    //     this.loading = false;
    //   });
    // },
    submitAction() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          const subData = { ...this.ruleForm, bindId: this.bindId };
          const request = amStock.stockCount;
          this.submitDisabled = true
          request(subData).then(response => {
            this.$notify({
              title: `盘库成功`,
              type: 'success',
              duration: 2500
            })
            this.cancelForm();
            this.$emit('successAction')
          }).catch((e) => {
            console.log(e);
          }).finally(() => {
            this.submitDisabled = false
          })
        } else {
          this.submitDisabled = false
          return false
        }
      });
    },
    // submitAction2() {
    //   this.$refs['ruleForm'].validate((valid) => {
    //     if (valid) {
    //       this.checkModule().then(res => {
    //         if (!res.flag) {
    //           return false;
    //         } else {
    //           const subData = { ...this.ruleForm, ...res.subData };
    //           const request = amStock.stockCount;
    //           this.submitDisabled = true
    //           request(subData).then(response => {
    //             this.$notify({
    //               title: `盘库成功`,
    //               type: 'success',
    //               duration: 2500
    //             })
    //             this.cancelForm();
    //             this.$emit('successAction')
    //           }).catch((e) => {
    //             console.log(e);
    //           }).finally(() => {
    //             this.submitDisabled = false
    //           })
    //         }
    //       })
    //     } else {
    //       this.submitDisabled = false
    //       return false
    //     }
    //   });
    // },

    // async checkModule() {
    //   const subData = {
    //     bindId: this.bindId,
    //     formData: null,
    //     formStruct: null,
    //     formBindToVar: false,
    //     relation: this.processStructureValue.relation
    //   };
    //   if (this.showFormData) {
    //     return await this.$refs['generateForm'].getData().then(values => {
    //       console.log(values, '<===>', 'values')
    //       subData.formData = JSON.stringify(values);
    //       subData.formStruct = JSON.stringify(this.formStruct);
    //       return {
    //         flag: true,
    //         subData
    //       };
    //     }).catch(() => {
    //       return {
    //         flag: false
    //       };
    //     })
    //   } else {
    //     return Promise.resolve({
    //       subData,
    //       flag: true
    //     });
    //   }
    // },
    cancelForm() {
      this.visible = false;
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.el-picker-panel__footer .el-button--text.el-picker-panel__link-btn {
  display: none;
}
</style>

<style lang="scss" rel="stylesheet/scss">

.no-atTheMoment {
  .el-picker-panel__footer .el-button--text.el-picker-panel__link-btn {
    display: none;
  }
}
</style>
