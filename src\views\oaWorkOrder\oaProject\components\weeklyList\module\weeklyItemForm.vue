<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :before-close="concelForm"
      :visible.sync="visible"
      :title="title"
      width="800px"
    >
      <div v-if="showFormData" class="text item">
        <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="110px">
          <fm-generate-form
            :ref="'generateForm'"
            :data="formStruct"
            :remote="remoteFunc"
            :value="formData"
            :preview="viewOrEdit"
          />
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="concelForm(false)">取消</el-button>
        <el-button :disabled="submitDisabled" type="primary" @click="submitAction">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth';
import Vue from 'vue'
import { GenerateForm } from '@/components/VueFormMaking'
import 'form-making/dist/FormMaking.css'
Vue.component(GenerateForm.name, GenerateForm)
// import { get, add, edit } from '@/api/oaWorkOrder/oaPmWeeklyReport'
import crudDictDetail from '@/api/system/dictDetail'
import extendBindTpl from '@/api/system/extendBindTpl';

export default {
  data() {
    return {
      visible: false,
      ruleForm: {
      },
      rules: {
      },
      title: '填写周报',
      formStruct: {},
      formData: {},
      jsonData: {},
      submitDisabled: false,
      viewOrEdit: false,
      showFormData: false,
      bindId: '',
      weekDate: [],
      relation: '',
      remoteFunc: {
        funcGetToken(resolve) {
          resolve(getToken())
        },
        getDictDetail(resolve, dictName) {
          crudDictDetail.get(dictName).then(response => {
            const options = response.content
            resolve(options)
          })
        },
        getWeeklyWorkClassify(resolve) {
          this.getDictDetail(resolve, 'weekly_work_classify');
        },
        getWeeklyCompleteStatus(resolve) {
          this.getDictDetail(resolve, 'weekly_complete_status');
        }
      }
    }
  },
  created() {
  },
  methods: {
    async init(info) {
      this.bindId = this.$config['pm_weekly']?.bindId;
      this.visible = true;

      if (info && info.fid) {
        this.getContent(info);
      } else {
        this.getProcessNodeList(this.bindId);
      }
    },
    getContent(info) {
      const jsonData = info;
      this.jsonData = jsonData;
      this.formStruct = JSON.parse(jsonData.formStruct);
      this.formData = JSON.parse(jsonData.formData);
      this.relation = jsonData.relation;
      this.showFormData = true;
    },
    getProcessNodeList(id) {
      const data = { id, enabled: 1 }
      extendBindTpl.get(data).then(res => {
        if (res && res.content && res.content.length) {
          this.processStructureValue = res.content[0];
          const FormStruct = JSON.parse(this.processStructureValue.extendTpl.formStruct);
          this.relation = this.processStructureValue.relation;
          this.formStruct = FormStruct;
        }
        this.showFormData = true;
      });
    },
    async submitAction() {
      // 验证表单
      const valid = await new Promise(resolve => {
        this.$refs['ruleForm'].validate(resolve);
      });

      // 如果验证不通过，直接通知用户并返回
      if (!valid) {
        this.submitDisabled = false;
        this.$notify({
          title: '请根据提示填写表单信息',
          type: 'info',
          duration: 2500
        });
        return;
      }

      this.submitDisabled = true;

      // 准备提交的数据
      const subData = {
        id: this.jsonData?.id,
        fv1: this.$route.query.name, // 项目名称
        pmId: this.$route.query.projectId, // 项目id
        bindId: this.bindId,
        enabled: 1,
        formData: '',
        formStruct: JSON.stringify(this.formStruct),
        formBindToVar: true,
        relation: this.relation,
        fid: this.jsonData && this.jsonData.fid ? this.jsonData.fid : undefined
      };

      try {
        // 获取表单数据
        const values = await this.$refs['generateForm'].getData();
        subData.formData = JSON.stringify(values);

        // 根据条件执行相应操作
        this.concelForm(this.jsonData && this.jsonData.fid ? 'edit' : 'add', subData);
      } catch (error) {
        // 处理获取表单数据时的错误
        this.submitDisabled = false;
        console.error('Error in getting form data:', error);
        // 可以添加用户通知代码
      }
    },
    concelForm(action, data) {
      this.visible = false;
      this.processStructureValue = {};
      this.formStruct = {};
      this.formData = {};
      this.showFormData = false;
      this.submitDisabled = false;
      this.jsonData = {};
      this.$emit('succeSubmit', action, data);
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
::v-deep .el-input-number .el-input__inner {
  text-align: left;
}
</style>
