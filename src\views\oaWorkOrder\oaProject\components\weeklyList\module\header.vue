<template>
  <div
    v-if="crud.props.searchToggle"
  >
    <el-select
      v-model="query.fv6"
      class="filter-item"
      clearable
      placeholder="请选择周报分类"
      size="small"
      style="width: 230px"
    >
      <el-option
        v-for="item in dict.weekly_type"
        :key="item.id"
        :label="item.label"
        :value="item.label"
      />
    </el-select>
    <el-select
      v-model="query.fv7"
      class="filter-item"
      clearable
      placeholder="请选择内容分类"
      size="small"
      style="width: 230px"
    >
      <el-option
        v-for="item in dict.weekly_work_classify"
        :key="item.id"
        :label="item.label"
        :value="item.label"
      />
    </el-select>
    <rrOperation />
  </div>
</template>

<script>
import { header } from '@crud/crud';
import rrOperation from '@crud/RR.operation.vue';

export default {
  components: { rrOperation },
  dicts: ['weekly_work_classify', 'weekly_type'],
  mixins: [header()],
  props: {
    permission: {
      type: Object,
      required: true
    }
  }
}
</script>
