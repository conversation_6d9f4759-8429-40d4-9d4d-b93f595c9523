import { tableHeader } from './field'
import { isArray } from '@/utils/is'

/**
 * 格式化表格数据
 * @param {Array} val - 表格数据数组
 */
export function formatterTableData(val) {
  if (val && val.length) {
    let tableData = [];
    tableData = val.map(item => {
      const json = item.extend.data || {};

      return {
        ...json,
        ...item,
        ft1File: formateFiles(item, 'ft1'),
        ft2File: formateFiles(item, 'ft2')
      };
    });
    return tableData;
  } else {
    return [];
  }
}

export function formateFiles(item, type) {
  const obj = {
    thurl: '',
    url: []
  }
  let initData = ''
  try {
    initData = JSON.parse(item[type]);
  } catch (error) {
    initData = item[type];
  }
  if (isArray(initData) && initData.length > 0) {
    obj.thurl = initData[0].thUrl
    obj.url = initData.map(item => item.url)
  }
  return obj
}

/**
 * 格式化表头
 * @param {Array} val - 表格数据数组
 */
export function formatterTableHeader(val) {
  const otherHeader = [];
  return [...tableHeader, ...otherHeader];
}

/**
 * 点击是否显示操作列
 * @param vueInstance
 * @returns {Promise<void>}
 */
export async function toggleSideBarFun(vueInstance) {
  vueInstance.operateShow = !vueInstance.operateShow;
  await vueInstance.$nextTick()
  vueInstance.$refs.table.doLayout()
}

export function mergeCell({ row, column, rowIndex, columnIndex, tableData }) {
  if (columnIndex === 1 || columnIndex === 2 || columnIndex === 3) {
    const arr = getSpanArray(tableData, 'fv4');
    return getSpan(arr, rowIndex);
  } else if (columnIndex === 4 || columnIndex === 5) {
    const arr = getSpanArray(tableData, 'fv4', 'fv5');
    return getSpan(arr, rowIndex);
  }
  return { rowspan: 1, colspan: 1 };
}

function getSpanArray(list, key1, key2) {
  const spanArray = [];
  for (let i = 0; i < list.length; i++) {
    if (i === 0) {
      spanArray.push({ row: 1, col: 1 });
    } else {
      const isSame = key2
        ? list[i][key1] === list[i - 1][key1] && list[i][key2] === list[i - 1][key2]
        : list[i][key1] === list[i - 1][key1];
      if (isSame) {
        spanArray.push({ row: 0, col: 0 });
        const index = spanArray.findIndex((_, idx) => list[idx][key1] === list[i][key1] && (!key2 || list[idx][key2] === list[i][key2]));
        spanArray[index].row++;
      } else {
        spanArray.push({ row: 1, col: 1 });
      }
    }
  }
  return spanArray;
}

function getSpan(arr, rowIndex) {
  return {
    rowspan: arr[rowIndex].row,
    colspan: arr[rowIndex].col
  };
}
