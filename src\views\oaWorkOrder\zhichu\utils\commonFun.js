import numeric from 'numeric';
import { tableHeader } from './field'
import crudCategory from '@/api/system/category';

/**
 * 项目列表表头自定义颜色
 * @param row
 * @param column
 * @param rowIndex
 * @param columnIndex
 * @returns {string} 最终颜色
 */
export function tableRowClassName({ row, column, rowIndex, columnIndex }) {
  const { property } = column
  const ruleMap = {
    baseKey: ['fv2', 'createBy', 'cateNamesOne', 'cateNamesTwo', 'fv3', 'fv4'],
    dateFormatKey: ['createTime', 'fv15', 'fv16', 'fv17', 'fv20'],
    incomeKey: ['fv7', 'fv8', 'fv9', 'fv14'],
    payKey: ['fv11', 'fv12', 'fv21', 'fv13', 'fv23', 'fv24', 'fv25']
  }

  // 对应的背景色
  const bgColor = {
    baseKey: '#FFC125;', // 第一个颜色 基础
    incomeKey: '#66ccff;', // 第二个颜色 收入
    payKey: '#13CE66;', // 第三个颜色 支出
    dateFormatKey: '#836FFF;' // 第四个颜色 时间
  }
  const getBgColor = (property) => {
    for (const key in ruleMap) {
      if (ruleMap[key].includes(property)) {
        return `background:${bgColor[key]}color:white`;
      }
    }
  }
  return getBgColor(property)
}

/**
 * 格式化表格数据
 * @param {Array} val - 表格数据数组
 */
export function formatterTableData(val) {
  if (val && val.length) {
    let tableData = [];
    tableData = val.map(item => {
      return {
        ...item
      };
    });
    return tableData;
  } else {
    return [];
  }
}

/**
 * 格式化表头
 * @param {Array} val - 表格数据数组
 */
export function formatterTableHeader(val) {
  const otherHeader = [];
  return [...tableHeader, ...otherHeader];
}

/**
 * 将数字转化为千分位
 * @param value
 * @returns {string}
 */
export function handleMoney(value) {
  return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

/**
 * 点击项目列表批注
 * <AUTHOR>
 * @param data 当前行数据
 * @param vueInstance 当前实例
 * @returns {Promise<void>}
 */
export async function operateRemarks(data, vueInstance) {
  vueInstance.remarksShow = true;
  const { ft1, id, ft2, name } = data;
  await vueInstance.$nextTick()
  vueInstance.$refs.remarksRef.initData({ id, ft1, ft2, name })
}

/**
 * 点击项目列表日志
 * @param data 当前行数据
 * @param vueInstance 当前实例
 * @returns {Promise<void>}
 */
export async function operateLog(data, vueInstance) {
  vueInstance.projectLogShow = true;
  const { ft3, id, name } = data;
  await vueInstance.$nextTick()
  vueInstance.$refs.logRef.initData({ id, ft3, name })
}

export async function toggleSideBarFun(vueInstance) {
  vueInstance.operateShow = !vueInstance.operateShow;
  await vueInstance.$nextTick()
  vueInstance.$refs.table.doLayout()
}

/**
 * 点击添加项目
 * @param data
 * @param vueInstance
 */
export function addProjectFun(data, vueInstance) {
  const { row, command } = data;
  vueInstance.$router.push({
    name: 'AddOaPmForm',
    query: { fv18: row.name, bindId: vueInstance.bindId, pid: row.id, command, fv1: '子项目' }
  })
}

/**
 * 确认选择 项目成员和目录
 * @param info
 * @param vueInstance
 */
export function sureSelectFun(info, vueInstance) {
  const { type } = info
  const typeStatus = {
    'user': 'hasSelectUser',
    'file': 'hasSelectFile'
  };
  vueInstance[typeStatus[type]] = false;
  vueInstance.crud.refresh();
}

export function onDispatchFun(data, vueInstance) {
  const { row } = data;
  const { categoryId } = vueInstance.$config.process_keys
  vueInstance.$refs.dispatch.init({ id: row.id, categoryId })
}

/**
 * 处理项目表格底部金额合计
 * @param param
 * @returns {string[]}
 */
export function handleSummaries(param) {
  const { firstValue, columns, data, targetProperties } = param;
  const initData = JSON.parse(JSON.stringify(data));
  const sums = []
  // const length = initData.length;
  const obj = {};
  initData.forEach(item => {
    targetProperties.forEach(property => {
      if (item.hasOwnProperty(property)) {
        if (property === 'fv14' && item[property].includes('%')) {
          item[property] = item[property].replace('%', '');
        }
        if (obj.hasOwnProperty(property)) {
          obj[property] = numAddtract(obj[property], item[property])
        } else {
          obj[property] = numAddtract(item[property])
        }
      }
    });
  });
  for (const prop in obj) {
    let processedValue = obj[prop];

    if (prop !== 'fv14') {
      processedValue = numformatMillennials(processedValue);
    } else {
      const num = grossFv14Format(obj);
      const formattedNum = numformatMillennials(num);
      processedValue = isNumber(formattedNum) ? `${formattedNum}%` : '0.00%';
    }
    obj[prop] = processedValue;
  }
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = firstValue;
      return;
    }
    sums[index] = obj[column.property]
  })
  return sums
}

/**
 * 判断当前用户是项目负责人并且是项目负责人则显示项目成员和项目权限
 * @param user 当前用户
 * @param projectRoles 项目角色
 * @param row 当前行数据
 * @returns {boolean|boolean}
 */
export function projectLeaderOrRoles(user, projectRoles, row) {
  // 获取当前用户的用户名和项目负责人的用户名
  // const userName = user.username;
  // const projectLeaderName = row['3'];

  // 判断当前用户是否是项目负责人
  // const isProjectLeader = userName === projectLeaderName;

  // 获取项目的所有角色
  const projectAllRoles = projectRoles.otherInfo.split(',').map(Number);

  // 获取当前用户拥有的角色
  const userRoles = user.roles.map(role => role.id);

  // 判断当前用户是否拥有项目的任意一个角色
  const isProjectRoles = userRoles.some(role => projectAllRoles.includes(role));

  // 如果当前用户是项目负责人或拥有项目的任意一个角色，则返回 true，否则返回 false
  // return isProjectLeader || isProjectRoles;
  console.log(isProjectRoles, '<===>', 'isProjectRoles')
  return isProjectRoles;
}

export function numAddtract(a, b) {
  const num1 = parseFloat(a) || 0.00;
  const num2 = parseFloat(b) || 0.00;
  const num = numeric.add(num1, num2)
  return numformat(num);
}

export function numformat(num) {
  const str = num.toFixed(2).toString()
  // return str.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return str
}

export function numDivtract(a, b) {
  const num1 = parseFloat(a) || 0.00;
  const num2 = parseFloat(b) || 0.00;
  const num = numeric.div(num1, num2)
  return numformat(num);
}

export function grossFv14Format(obj) {
  const newObj = JSON.parse(JSON.stringify(obj))
  const num1 = parseFloat(newObj.fv7) || 0.00;
  const num2 = parseFloat(newObj.fv11) || 0.00;
  const num = numeric.sub(num1, num2)
  const gross = numeric.mul(numeric.div(num, num1), 100);
  return numformat(gross)
}

export function numformatMillennials(str) {
  return str.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

/**
 * 当内容过长被隐藏时显示 tooltip 判断是否显示
 * @param header
 * @returns {[]|string|boolean|*}
 */
export function getShowTip(header) {
  const flag = header.showTip !== undefined ? header.showTip : true;
  return flag
}

/**
 * 获取项目级联分类数据
 */
export function getCategoryList() {
  const categoryId = 74;
  return crudCategory.getChildren([categoryId]).then((res) => {
    const data = res.content;
    if (data && data[0]) {
      const childrenData = data[0].children;
      if (childrenData.length > 0) {
        childrenData.map((item) => {
          item.value = item.id;
          if (item.hasChildren) {
            handleData(item.children);
          }
        });
        return childrenData;
      }
    }
  })
}

function handleData(option) {
  option && option.map((item) => {
    item.value = item.id;
    if (item.hasChildren) {
      handleData(item.children);
    }
  });
}

function isNumber(value) {
  return !isNaN(parseFloat(value)) && isFinite(value);
}
