<template>
  <el-dialog
    v-dialog-drag
    :before-close="concelForm"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :title="title"
    :visible.sync="visible"
    append-to-body
    width="800px"
  >

    <div v-loading="loading" class="text item">
      <el-form ref="elForm" :model="elForm" :rules="elRule" label-width="130px">
        <el-form-item v-for="item in baseProjectFiled.fields" :key="item.id" :label="`${item.label}:`" :prop="item.value">
          <el-select v-if="item.value=='fv4'" v-model="elForm[item.value]" :placeholder="`请选择${item.label}`">
            <el-option
              v-for="item in field"
              :key="item.type"
              :label="item.type"
              :value="item.type"
            />
          </el-select>
          <el-input v-else v-model="elForm[item.value]" :disabled="item.value == 'fv1'" style="width: 100%;" />
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="text" @click="concelForm">取消</el-button>
      <el-button
        :disabled="submitDisabled"
        type="primary"
        @click="submitAction"
      >
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import spiderArticleApi from '@/api/spider/spiderArticle';
import { baseProjectFiled, field } from '@/views/spider/utils/field';

export default {
  name: 'BaseForm',
  data() {
    return {
      title: '更正基础信息',
      visible: false,
      submitDisabled: false,
      elForm: {},
      elRule: {},
      loading: false,
      baseProjectFiled,
      field
    }
  },

  methods: {
    initData(data) {
      this.visible = true
      this.elForm = JSON.parse(JSON.stringify(data));
    },
    concelForm() {
      this.visible = false;
      this.$emit('success');
    },
    submitAction() {
      this.$refs['elForm'].validate((valid) => {
        if (!valid) {
          return false;
        } else {
          spiderArticleApi.editSpiderArticleBase(this.elForm).then(response => {
            this.concelForm();
            this.$notify({
              title: '操作成功',
              type: 'success',
              duration: 2500
            })
          }).catch((e) => {
            console.log(e);
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
