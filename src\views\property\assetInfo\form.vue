<template>
  <div class="app-container">
    <el-card class="box-card" style="margin-top: 10px">
      <div slot="header" class="clearfix">
        <span>资产信息</span>
      </div>
      <el-form v-if="showBaseForm" ref="elFormRef" :model="elForm" :rules="elRules" label-width="130px" size="small">
        <!--额外的表单-->
        <el-form-item label="项目" prop="pm.id">
          <el-select
            ref="elselect"
            v-model="elForm.pm.id"
            :collapse-tags="true"
            :loading="selectLoading"
            :remote-method="remoteSelectProject"
            class="filter-item"
            clearable
            debounce="500"
            filterable
            :multiple="false"
            placeholder="请输入项目名称"
            remote
            reserve-keyword
            size="small"
            style="width: 550px"
            @keyup.enter.native="handleEnter"
          >

            <el-option v-for="item in projectList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="资产类别" prop="categoryValue">
          <el-cascader
            v-model="elForm.categoryValue"
            :options="Devices"
            :props="cascaderProps"
            :show-all-levels="true"
            clearable
            placeholder="选择资产类别"
            style="width: 550px"
            @change="handleCategoryChange"
          />
        </el-form-item>
        <el-form-item label="仓库" prop="depot.id">
          <el-select v-model="elForm.depot.id" class="filter-item" clearable placeholder="请选择仓库" style="width: 550px;">
            <el-option v-for="item in storeroomList" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <div v-if="showFormData" class="text item">
        <fm-generate-form
          :ref="'generateForm'"
          :data="formStruct"
          :preview="viewOrEdit"
          :remote="remoteFunc"
          :value="formData"
        />
      </div>
      <hr style="background-color: #d9d9d9; border:0; height:1px;">
      <div class="text item" style="text-align: center;margin-top:18px">
        <el-button v-if="!viewOrEdit" :disabled="submitDisabled" type="primary" @click="submitAction">提交</el-button>
        <el-button @click="cancelSubmit">{{ viewOrEdit == false ? '取 消' : '返回' }}</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import extendBindTpl from '@/api/system/extendBindTpl';
import { getToken } from '@/utils/auth';
import crudDictDetail from '@/api/system/dictDetail';
import crudTable from '@/api/property/amBasicData';
//
import { getPmTreeSmall } from '@/api/oaWorkOrder/oaPmTree'
import { getByName } from '@/api/system/user';
import crudAmCategory from '@/api/property/amCategory'
import amDepots from '@/api/property/amDepot'

export default {
  name: 'AddAssetInfoForm',
  components: {},
  data() {
    return {
      elForm: {
        categoryValue: [], // 级联选择器的值
        enabled: 1,
        pm: {
          id: null,
          name: ''
        },
        depot: {
          id: null
        },
        device: {
          subCount: 0,
          id: null // 设备
        },
        brand: {
          subCount: 0,
          id: null // 品牌
        },
        model: {
          subCount: 0,
          id: null // 型号
        }
      },
      elRules: {
        'pm.id': [
          { required: true, message: '请选择项目', trigger: 'change' }
        ],
        categoryValue: [
          { required: true, message: '请选择资产类别', trigger: 'change' }
        ],
        'depot.id': [
          { required: true, message: '请选择仓库', trigger: 'change' }
        ]
      },
      formStruct: {},
      formData: {},
      remoteFunc: {
        funcGetToken(resolve) {
          resolve(getToken())
        },
        getDictDetail(resolve, dictName) {
          crudDictDetail.get(dictName).then(res => {
            const options = res.content
            resolve(options)
          })
        },
        getPartition(resolve) {
          // 施工分区
          this.getDictDetail(resolve, 'partition_type');
        },
        getXyTown(resolve) {
          // 区域
          this.getDictDetail(resolve, 'xyTown_type');
        },
        getAutoDiveStaus(resolve) {
          // 盘点情况
          this.getDictDetail(resolve, 'auto_drive_status');
        }
      },
      submitDisabled: false,
      viewOrEdit: false,
      showFormData: false,
      pointList: [], // 点位列表
      positionList: [], // 位置列表
      // /
      selectLoading: false,
      projectList: [],
      Devices: [],
      cascaderProps: {
        lazy: true, // 开启懒加载
        value: 'id', // 将每个选项的值设置为 'id' 属性
        label: 'label', // 用于显示选项名称的属性
        children: 'children', // 包含子选项的属性
        isLeaf: 'isLeaf', // 指示选项是否为叶子节点的属性
        lazyLoad: this.lazyLoad
      },
      storeroomList: [],
      showBaseForm: false
    }
  },
  created() {
    this.initStoreroomList()
    this.init()
  },
  methods: {
    init() {
      const { id, type } = this.$route.query
      this.viewOrEdit = type
      if (id) {
        // 编辑或者查看
        this.getContent(id)
      } else {
        // 添加
        this.showBaseForm = true;
        this.getProcessNodeList()
      }
    },
    async getContent(id) {
      const res = await crudTable.get({ id, enabled: 1 });
      if (res && res.content && res.content.length) {
        const jsonData = res.content[0];
        this.processStructureValue = jsonData;
        this.formStruct = JSON.parse(jsonData.formStruct);
        this.formData = JSON.parse(jsonData.formData);
        this.jsonData = jsonData;
        this.showFormData = true;
        this.elForm = jsonData;

        await this.remoteSelectProject(jsonData.pm.name)
        this.elForm.pm.name = jsonData.pm.name;
        this.elForm.categoryValue = await this.getCategoryValue(jsonData);
        await this.initCategoryValue();
        this.showBaseForm = true;
      }
    },
    getProcessNodeList() {
      const data = { id: this.$config.assetInfo_key.bindId, enabled: 1 }
      extendBindTpl.get(data).then(res => {
        if (res && res.content && res.content.length) {
          this.processStructureValue = res.content[0];
          const FormStruct = JSON.parse(this.processStructureValue.extendTpl.formStruct);
          this.formStruct = FormStruct;
        }
        this.showFormData = true;
      });
    },
    submitAction() {
      this.$refs['elFormRef'].validate((valid) => {
        if (!valid) {
          return false;
        } else {
          this.checkModule().then(res => {
            if (!res.flag) {
              return false;
            } else {
              const subData = { ...this.elForm, ...res.subData };
              delete subData.categoryValue; // 删除分类值
              let request = crudTable.add;
              let title = '添加'
              if (subData.id) {
                request = crudTable.edit;
                title = '编辑'
              }
              console.log(subData, '<===>', 'subData')
              this.submitDisabled = true;
              request(subData).then(response => {
                this.$notify({
                  title: `${title}成功`,
                  type: 'success',
                  duration: 2500
                })
                this.cancelSubmit();
              }).catch((e) => {
                console.log(e);
              }).finally(() => {
                this.submitDisabled = false;
              })
            }
          })
        }
      })
    },
    async checkModule() {
      const subData = {
        bindId: this.$config.assetInfo_key.bindId,
        // categoryId: this.$config.assetInfo_key.categoryId,
        formData: null,
        formStruct: null,
        formBindToVar: true,
        relation: this.processStructureValue.relation
      };
      if (this.showFormData) {
        const p = await this.$refs['generateForm'].getData().then(values => {
          subData.formData = JSON.stringify(values);
          subData.formStruct = JSON.stringify(this.formStruct);
          return {
            flag: true,
            subData
          };
        }).catch(() => {
          return {
            flag: false
          };
        })
        return p;
      } else {
        return Promise.resolve({
          subData,
          flag: true
        });
      }
    },
    cancelSubmit() {
      const query = {
        bindId: this.$config.assetInfo_key.bindId
        // categoryId: this.$config.assetInfo_key.categoryId
      }
      this.$router.push({ name: 'AssetInfo', query });
    },
    // 表单逻辑

    async remoteSelectProject(query, type) {
      console.log(query, '<===>', 'query')
      const { bindId } = this.$config.projects_keys;
      const parameters = {
        enabled: 1,
        name: query,
        size: 20,
        bindId,
        fv1: '项目'
      }
      const result = await this.publicRemote(query, 'projectType', parameters)
      const content = result.content || [];
      if (type) {
        const obj = content.find(item => item.name === query)
        this.query.pmId = [obj.id];
      }
      this.projectList = content || [];
    },
    async publicRemote(query, type, parameters) {
      const QueryTypes = {
        'projectType': { loadingProp: 'selectLoading', remoteMethod: getPmTreeSmall },
        'userType': { loadingProp: 'selectLoading', remoteMethod: getByName }
      }
      const typeMap = QueryTypes[type];
      if (query !== '') {
        this[typeMap.loadingProp] = true;
        try {
          const res = await typeMap.remoteMethod(parameters);
          return res || [];
        } catch (error) {
          return [];
        } finally {
          this[typeMap.loadingProp] = false;
        }
      } else {
        return [];
      }
    },
    handleEnter(val) {
      this.crud.query.pmId = this.projectList.map(item => item.id);
    },
    // 懒加载数据
    lazyLoad(node, resolve) {
      if (node.isLeaf) {
        // 如果是叶子节点，不需要加载数据
        resolve([]);
      } else {
        const parentId = node.value; // 假设当前节点的 'id' 是父节点的 ID
        crudAmCategory.getAmCategory({ enabled: '1', pid: parentId || null }).then((children) => {
          resolve(children.content);
        });
      }
    },
    // 初始化数据
    initDevices() {
      crudAmCategory.getAmCategory({ enabled: '1', pid: null }).then(res => {
        this.Devices = res.content.map(item => ({
          ...item,
          leaf: !item.hasChildren
        }))
        console.log(this.Devices, '<===>', 'Devices')
      })
    },
    handleCategoryChange(value) {
      // 初始化空对象
      this.elForm.device = this.elForm.device || { subCount: 0, id: null }
      this.elForm.brand = this.elForm.brand || { subCount: 0, id: null }
      this.elForm.model = this.elForm.model || { subCount: 0, id: null }

      // 根据选择层级动态赋值
      if (Array.isArray(value)) {
        // 重置所有值
        this.elForm.device.id = null
        this.elForm.brand.id = null
        this.elForm.model.id = null

        // 按层级赋值
        if (value.length > 0) {
          this.elForm.device.id = value[0]
        }
        if (value.length > 1) {
          this.elForm.brand.id = value[1]
        }
        if (value.length > 2) {
          this.elForm.model.id = value[2]
        }
      }
    },
    async initCategoryValue() {
      const { device, brand, model } = this.elForm
      const categoryValue = []

      // 确保对象存在
      if (device?.id) {
        categoryValue.push(device.id)
        await new Promise(resolve => this.lazyLoad({ value: device.id }, resolve))
      }

      if (brand?.id) {
        categoryValue.push(brand.id)
        await new Promise(resolve => this.lazyLoad({ value: brand.id }, resolve))
      }

      if (model?.id) {
        categoryValue.push(model.id)
      }

      // 只有在有值时才设置
      if (categoryValue.length > 0) {
        this.$set(this.elForm, 'categoryValue', categoryValue)
      }
    },
    getCategoryValue(jsonData) {
      const categoryValue = []
      if (jsonData.device?.id) {
        categoryValue.push(jsonData.device.id)
      }
      if (jsonData.brand?.id) {
        categoryValue.push(jsonData.brand.id)
      }
      if (jsonData.model?.id) {
        categoryValue.push(jsonData.model.id)
      }
      return categoryValue
    },
    // 获取仓库列表
    async initStoreroomList() {
      try {
        const json = {
          enabled: 1,
          page: 0,
          size: 99999,
          sort: 'createTime,desc',
          bindId: this.$config.storeroom_key.bindId
        }
        const res = await amDepots.get(json);
        if (res && res.content) {
          this.storeroomList = res.content;
        }
      } catch (error) {
        console.error('获取仓库列表失败:', error)
      }
    }

  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped></style>
