import Vue from 'vue'

const config = Vue.prototype.$config
let currentConfig = {}
import crudCategory from '@/api/system/category';
import { isArray } from '@/utils/is';

/**
 * 设置所有页面需要的配置
 */
export function setConfig() {
  currentConfig = {
    Junction_list: {
      ...config.Junction_list,
      inspectsBindId: config.inspects_list.bindId,
      name: 'JunctionCreate', // 部件详情页路由名称
      inspectsName: 'Inspects'// 巡检路由名称
    }, // 路口配置
    Junction_list2: {
      ...config.Junction_list2,
      name: 'JunctionCreate2'// 部件详情页路由名称
    }, // 路口2.0配置
    inspects_list: {
      ...config.inspects_list
    }, // 路口巡检配置
    rod_list: {
      ...config.rod_list,
      inspectsBindId: config.rod_inspects_list.bindId,
      name: '<PERSON><PERSON><PERSON>', // 部件路由名称
      inspectsName: 'RodInspects'// 巡检路由名称
    }, // 监控杆配置
    rod_inspects_list: {
      ...config.rod_inspects_list
    }, // 监控杆巡检
    motorroom_list: {
      ...config.motorroom_list,
      name: 'qwe', // 部件路由名称
      inspectsName: 'qwe'// 巡检路由名称
    }, // 机房配置
    motorroom_inspects_list: {
      ...config.motorroom_inspects_list
    }, // 机房巡检 配置中没有绑定ID
    xl_asset_key: {
      ...config.xl_asset_key,
      name: 'XlAsset', // 部件路由名称
      inspectsName: 'XlInspectsForm'// 巡检路由名称
    }, // 雪亮配置
    auto_pilot3_key: {
      ...config.auto_pilot3_key,
      name: 'AutoPilot3', // 部件路由名称
      inspectsName: 'XlInspectsForm'// 巡检路由名称
    }, // 自动驾驶3.0
    Asset_class: {
      ...config.Asset_class
    } // 资产(分类)配置
  }
  return currentConfig
}

/**
 * marker点位的icon
 * @param state
 * @param type
 * @param zoom
 * @param level
 * @param vueInstance
 * @returns {string}
 */
export function styleIconFun(state, type, zoom, level) {
  const zoomStr = zoom.toString(); // 将 zoom 转换为字符串，以便拼接
  // 定义状态的映射
  const stateMapping = {
    [currentConfig.Junction_list.categoryId]: {
      '正常': 'iconGreen',
      '故障': styleIddangFun(level, type),
      '维修': 'iconYellow',
      '默认': 'iconGreen'
    },
    [currentConfig.rod_list.categoryId]: {
      '正常': 'cameraB',
      '故障': styleIddangFun(level, type),
      '维修': 'cameraH',
      '默认': 'cameraB'
    },
    [currentConfig.motorroom_list.categoryId]: {
      '正常': 'booksB',
      '故障': styleIddangFun(level, type),
      '维修': 'booksH',
      '默认': 'booksB'
    },
    [currentConfig.Junction_list2.categoryId]: {
      '正常': 'iconGreen',
      '默认': 'iconGreen'
    },
    [currentConfig.xl_asset_key.categoryId]: {
      '正常': 'xlAssetSuccess',
      '异常': 'xlAssetError',
      '默认': 'xlAssetSuccess'
    },
    [currentConfig.auto_pilot3_key.categoryId]: {
      '数据回传': 'autoPilot3Red',
      '熔纤完成': 'autoPilot3Green',
      '穿线完成': 'autoPilot3Purple',
      '熔纤、穿线完成': 'autoPilot3Yellow',
      '未开始': 'autoPilot3Grey',
      '默认': 'autoPilot3Grey'
    }
  };
  // 获取当前类型的状态映射或返回一个空对象
  const typeStates = stateMapping[type] || {};
  // 根据状态获取相应的字符串，没有状态则使用默认值
  const stateStr = typeStates[state] || typeStates['默认'];
  // console.log(stateStr, 898)
  return stateStr + zoomStr;
}

/** 故障等级的图标id */
export function styleIddangFun(level, type, vueInstance) {
  const typeMapping = {
    [currentConfig.Junction_list.categoryId]: {
      '极高': 'errorjigao',
      '高': 'errorgao',
      '中': 'errorzhong',
      '默认': 'iconRed'
    },
    [currentConfig.rod_list.categoryId]: {
      '极高': 'cameraRiga',
      '高': 'cameraGao',
      '中': 'cameraRhona',
      '默认': 'cameraR'
    },
    [currentConfig.motorroom_list.categoryId]: {
      '极高': 'booksJIgao',
      '高': 'booksGao',
      '中': 'booksZhong',
      '默认': 'booksR'
    }
  };
  const levels = typeMapping[type] || {};
  return levels[level] || levels['默认'];
}

/**
 * 雪亮点击marker弹框
 * @param info
 * @returns {string}
 */
export function xlAssetContent(info) {
  return `
        <div class="infowindowBtn btn xlAssetBox">
            <div class="xlasset-box-info">
                <div class="xlAsset-info-item">
                    <span>点位编号：</span>
                    <span>${info.properties.fv4}</span>
                </div>
                <div class="xlAsset-info-item">
                    <span>路口名称：</span>
                    <span>${info.properties.title}</span>
                </div>
                <div class="xlAsset-info-item">
                    <span>上次巡检时间：</span>
                    <span>${info.properties.fv19}</span>
                </div>
            </div>
            <div class="xlAsset-box-oper">
                <button class="xlAsset-box-btn" onclick="infoClick('xlAsset')">查看详情</button>
                <button class="xlAsset-box-btn" onclick="infoClick('xlAssetInspect')">巡检记录</button>
            </div>
        </div>
    `;
}

/**
 * 自动驾驶点击marker弹框
 * @param info
 * @returns {string}
 */
export function autoPilotsContent(info) {
  return `
        <div class="infowindowBtn btn xlAssetBox">
            <div class="xlasset-box-info">
                <div class="xlAsset-info-item">
                    <span>路口编号：</span>
                    <span>${info.properties.fv4}</span>
                </div>
                <div class="xlAsset-info-item">
                    <span>路口位置：</span>
                    <span>${info.properties.title}</span>
                </div>
            </div>
            <div class="xlAsset-box-oper">
                <!--<button class="xlAsset-box-btn" onclick="infoClick('autoPilots')">查看详情</button>-->
            </div>
        </div>
    `;
}

/**
 * 默认点击marker的弹框
 * @param info
 * @param list
 * @returns {string}
 */
export function defaultContent(info, list) {
  return `
        <div>
            <span>${info.properties.title}</span>
            <p>${info.properties.code || ''}</p>
        </div>
        <div class="infowindowBtn btn">
          	<button class="edit" onclick="infoClick('edit')">修改位置</button>
            <button class="xunjian" onclick="infoClick('xunjian')">巡检记录</button>
            <button class="details" onclick="infoClick('details')">${formatCategory(info.properties.type, list)}详情</button>
        </div>
    `;
}

/**
 * 获取路口code码
 * @param item
 * @returns {string}
 */
export function getCode(item) {
  let code = '';
  const itemData = item?.item;
  if (itemData && Array.isArray(itemData) && itemData.length > 0) {
    const codeData = itemData[0];
    code = codeData.extend.data[2];
  }
  return code;
}

/**
 *
 * @param id
 * @param list
 * @returns {*|string|string}
 */
export function getPosition(item, type) {
  let fv2 = '';
  let fv3 = '';
  if (type) {
    item = JSON.parse(item.location);
  }
  if (item.fv2 && item.fv2 !== 'null' && item.fv2 !== 'undefined') {
    fv2 = item.fv2;
  }
  if (item.fv3 && item.fv3 !== 'null' && item.fv3 !== 'undefined') {
    fv3 = item.fv3;
  }
  // eslint-disable-next-line no-undef
  const position = new TMap.LatLng(fv3, fv2);
  return position;
}

function formatCategory(id, list) {
  if (!id) {
    return '';
  }
  const foundItem = list.find(item => item.id == id);
  return foundItem ? foundItem.label : '';
}

/**
 * 查询条件配置
 * @param value
 * @returns {*|{placeholder: string, searchTitle: string}}
 */
export function searchConfig(value) {
  const categoryConfig = {
    [currentConfig.Junction_list.categoryId]: {
      placeholder: '请输入道路名称',
      searchTitle: 'title'
    },
    [currentConfig.xl_asset_key.categoryId]: {
      placeholder: '请输入点位编号或立杆位置',
      searchTitle: 'fv4OrTitle'
    },
    [currentConfig.auto_pilot3_key.categoryId]: {
      placeholder: '请输入路口名称或编号',
      searchTitle: 'fv4OrTitle'
    },
    '默认': {
      placeholder: '请输入路口名称',
      searchTitle: 'title'
    }
  };
  return categoryConfig[value] || categoryConfig['默认']
}

/**
 * 获取级联分类数据
 */
export async function getCategoryList(categoryId, bindId) {
  const fetchChildren = async(categoryId) => {
    const json = {
      page: 0,
      size: 9999999999,
      sort: 'id, desc',
      pidIsNull: true,
      enabled: 1,
      bindId: bindId,
      pid: categoryId

    }
    const res = await crudCategory.getCategory(json);
    const data = res.content;

    if (!data || data.length === 0) {
      return [];
    }

    const children = await Promise.all(data.map(async(item) => {
      item.value = item.id;
      if (item.hasChildren) {
        item.children = await fetchChildren(item.id);
      }
      return item;
    }));

    return children;
  };

  try {
    return await fetchChildren(categoryId);
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

/**
 * 格式化标签请求参数
 */
export function formatRequestJson(selectedCategories, selectObject) {
  const json = {
    dateFormat: 'yyyy-MM-d',
    indexName: 'fv2',
    type: { values: [], fieldName: 'fv1' },
    tag: groupByKey(selectedCategories, 'fv10', 'fv11')
  };
  json.type.values = Object.keys(selectObject)

  return json;
}

function groupByKey(arr, initKey, valKey) {
  const obj = arr.reduce((acc, item) => {
    const key = item[initKey]; // 使用fv10作为键
    if (!acc[key]) {
      acc[key] = []; // 如果这个键不存在，则初始化一个空数组
    }
    acc[key].push(item[valKey]); // 将fv11的值添加到对应的数组中
    return acc;
  }, {});
  return obj
}

/** 车网&京智网 确定颜色 */
export function findMaxSortColor(baseStatus, statusValue = '完成') {
  // 判断是否有机场状态
  const result = hasAirportStatus(baseStatus);
  const color = 'autoPilot3Grey'
  const airPortColor = 'autoPilot3Success'
  if (result) {
    return airPortColor
  } else {
    // 筛选出 status 为指定值的项
    const completedItems = baseStatus.filter(item => item.status === statusValue);
    // 找到 sort 最大的项
    const minSortItem = completedItems.reduce((minItem, currentItem) => {
      return parseInt(currentItem.sort) < parseInt(minItem.sort) ? currentItem : minItem;
    }, completedItems[0]);

    // 返回该项的 color
    if (!minSortItem) {
      return color
    }
    return minSortItem.color;
  }
}

export function updateStatus(baseStatus, statuses) {
  return baseStatus.map(item => {
    if (!item.key) {
      return item;
    }
    const chineseKey = Object.keys(statuses).find(key => key.includes(item.key) || item.key.includes(key));
    console.log(chineseKey, '<===>', 'chineseKey')
    if (chineseKey) {
      item.finalStatus = statuses[chineseKey];
    }
    return item;
  });
}

export function hasAirportStatus(items) {
  // 遍历数组中的每个项
  for (const item of items) {
    // 检查 status 是否包含 "机场" 关键字
    if (item.status.includes('机场')) {
      return true; // 如果找到，则返回 true
    }
  }
  return false; // 如果没有找到，则返回 false
}

export function getFinalColor(hasStatus) {
  let color = ''

  const statusNormalTag = hasStatus.filter(item => item.key)
  // 先判断正常的
  const completedItems = statusNormalTag.filter(item => item.status.includes(item.finalStatus));
  const minSortItem = completedItems.reduce((minItem, currentItem) => {
    return parseInt(currentItem.sort) < parseInt(minItem.sort) ? currentItem : minItem;
  }, completedItems[0])
  if (minSortItem && !isArray(minSortItem)) {
    color = minSortItem.color;
  } else {
    const statusSpecialTag = hasStatus.find(item => !item.key)
    const specialResult = statusSpecial(hasStatus, statusSpecialTag)
    if (specialResult) {
      color = statusSpecialTag.color
    }
  }
  return color;
}

export function statusSpecial(hasStatus, statusSpecialTag) {
  const specialResult = hasStatus.some(item => {
    return statusSpecialTag.status.includes(item.finalStatus);
  });
  return specialResult
}

