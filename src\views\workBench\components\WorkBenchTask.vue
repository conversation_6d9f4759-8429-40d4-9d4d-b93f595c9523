<template>
  <div class="WorkBenchTask">
    <el-table
      ref="table"
      v-loading="crud.loading"
      :data="tableData"
      row-key="id"
      style="width: 100%;"
      @select="crud.selectChange"
      @select-all="crud.selectAllChange"
      @selection-change="crud.selectionChangeHandler"
    >
      <el-table-column
        v-for="item in tableHeader"
        :key="item.prop"
        :align="item.align || 'center'"
        :fixed="item.fixed || false"
        :label="item.label"
        :prop="item.prop"
        :show-overflow-tooltip="true"
        :width="item.width"
      >
        <template slot-scope="scope">
          <template v-if="item.label == '预览图'">
            <el-image
              :key="item.id"
              :preview-src-list="[scope.row.ft1[0].url]"
              :src="scope.row.ft1[0].url"
              class="el-avatar"
              fit="contain"
              lazy
            >
              <div slot="error">
                <i class="el-icon-document" />
              </div>
            </el-image>
          </template>
          <template v-if="item.prop === 'currentStatusVal'">
            <el-tag v-if="scope.row.currentStatusVal" size="mini" type="success">是</el-tag>
            <el-tag v-else size="mini" type="danger">否</el-tag>
          </template>
          <span v-else>{{ scope.row[item.prop] }}</span>
        </template>
      </el-table-column>
      <!--   编辑与删除   -->
      <el-table-column v-if="operateColumn.isShow" v-bind="operateColumn">
        <template slot-scope="scope">
          <slot :data="scope.row" name="workTaskRef" />
        </template>

      </el-table-column>

    </el-table>
    <!--分页组件-->
    <pagination />
  </div>
</template>

<script>
import CRUD, { crud, presenter } from '@crud/crud';
import pagination from '@crud/Pagination.vue';

export default {
  name: 'WorkBenchTask',
  components: {
    pagination
  },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: false,
      title: '任务',
      url: 'api/oaPmTaskTodo',
      sort: ['createTime,desc'],
      query: { enabled: 1 },
      optShow: {
        add: false,
        edit: false,
        del: false,
        download: false,
        reset: false,
        rightGroup: false
      }
    })
  },
  mixins: [presenter(), crud()],
  props: {
    customCrudOption: {
      type: Object,
      required: true
    },
    tableHeader: {
      type: Array,
      default() {
        return []
      }
    },
    tableData: {
      type: Array,
      default() {
        return []
      }
    },
    operateColumn: {
      type: Object,
      default() {
        return {
          isShow: true,
          fixed: 'right',
          width: '240',
          label: '操作',
          align: 'center'
        }
      }
    }
  },
  data() {
    return {}
  },
  watch: {
    customCrudOption: {
      handler(val) {
        this.crud.updateUrl(val.url)
      },
      deep: true,
      immediate: true
    }
  },
  methods: {}
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
