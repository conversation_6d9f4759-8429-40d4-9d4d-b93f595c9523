import Vue from 'vue'
import Router from 'vue-router'
import Layout from '../layout/index'

Vue.use(Router)

export const constantRouterMap = [
  {
    path: '/login',
    meta: { title: '登录', noCache: true },
    component: (resolve) => require(['@/views/login/loginWx.vue'], resolve),
    hidden: true
  },
  {
    path: '/404',
    component: (resolve) => require(['@/views/features/404'], resolve),
    hidden: true
  },
  {
    path: '/401',
    component: (resolve) => require(['@/views/features/401'], resolve),
    hidden: true
  },
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path*',
        component: (resolve) => require(['@/views/features/redirect'], resolve)
      }
    ]
  },
  // {
  //   path: '/',
  //   component: Layout,
  //   redirect: '/dashboard',
  //   children: [
  //     {
  //       path: 'dashboard',
  //       component: (resolve) => require(['@/views/map/index'], resolve),
  //       name: 'Dashboard',
  //       meta: { title: '地图总览', icon: 'index', affix: true, noCache: true }
  //     }
  //   ]
  // },
  // {
  //   path: '/',
  //   component: Layout,
  //   redirect: '/process/upcoming',
  //   children: [
  //     {
  //       path: 'dashboard',
  //       component: (resolve) => require(['@/views/process/list/upcoming'], resolve),
  //       name: 'Dashboard',
  //       meta: { title: '首页', icon: 'index', affix: true, noCache: true }
  //     }
  //   ]
  // },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'center',
        component: (resolve) => require(['@/views/system/user/center'], resolve),
        name: '个人中心',
        meta: { title: '个人中心' }
      }
      // {
      //  path: 'projectProfit',
      //  component: (resolve) => require(['@/views/oaWorkOrder/projectProfit'], resolve),
      //  name: '项目利润预算',
      //  meta: { title: '项目利润预算' }
      // },
      // {
      //  path: 'zhichu',
      //  component: (resolve) => require(['@/views/oaWorkOrder/zhichu'], resolve),
      //  name: '项目利润预算支出',
      //  meta: { title: '项目利润预算支出' }
      // }
    ]
  }
]

export default new Router({
  mode: 'hash',
  // mode: 'history',
  // base: '/admin',
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRouterMap
})

export function updateRouterMap(routerMap) {

}
