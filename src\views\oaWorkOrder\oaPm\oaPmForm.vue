<template>
  <div class="app-container">
    <el-card class="box-card" style="margin-top: 10px">
      <div slot="header" class="clearfix">
        <span>{{ projectForm.fv1 }}信息</span>
      </div>
      <el-form ref="ruleForm" :model="projectForm" :rules="rules" label-width="120px" size="small">
        <el-form-item :label="`${projectForm.fv1 || ''}名称:`" prop="name">
          <el-input v-model="projectForm.name" style="width: 550px;" />
        </el-form-item>
        <!--<el-form-item :label="`${projectForm.fv1 || ''}排序:`" prop="sort">-->
        <!--  <el-input-number-->
        <!--    v-model.number="projectForm.sort"-->
        <!--    :max="999"-->
        <!--    :min="0"-->
        <!--    controls-position="right"-->
        <!--    style="width: 550px;"-->
        <!--  />-->
        <!--</el-form-item>-->
        <el-form-item v-if="isShowTree" label="顶级:" prop="isTop">
          <el-radio-group v-model="projectForm.isTop" style="width: 220px">
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="状态:" prop="enabled">
          <el-radio
            v-for="item in dict.project_status"
            :key="item.id"
            v-model="projectForm.enabled"
            :label="item.value"
          >
            {{ item.label }}
          </el-radio>
        </el-form-item>
        <el-form-item label="分类:" prop="categoryId">
          <el-radio
            v-for="item in weeklyCategorizes"
            :key="item.id"
            v-model="projectForm.categoryId"
            :label="item.id"
          >
            {{ item.label }}
          </el-radio>
        </el-form-item>
        <el-form-item v-if="projectForm.isTop === '0' && isShowTree" label="上级" prop="pid">
          <treeselect
            v-model="projectForm.pid"
            :load-options="params => lazyLoadTrees(params)"
            :options="allprojectList"
            placeholder="选择上级类目"
            style="width: 550px;"
            @select="(node)=>handleTreeSelect(node)"
          />
        </el-form-item>
      </el-form>
      <div v-if="showFormData && (projectForm.fv1=='项目' || projectForm.fv1=='子项目') " class="text item">
        <fm-generate-form
          :ref="'generateForm'"
          :data="formStruct"
          :preview="viewOrEdit"
          :remote="remoteFunc"
          :value="formData"
          @on-change="changeFormData"
        />
      </div>
      <hr style="background-color: #d9d9d9; border:0; height:1px;">
      <div class="text item" style="text-align: center;margin-top:18px">
        <el-button v-if="!viewOrEdit" :disabled="submitDisabled" type="primary" @click="submitAction">提交</el-button>
        <el-button @click="concelForm">{{ viewOrEdit == false ? '取 消' : '返回' }}</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import Vue from 'vue'
import { GenerateForm } from '@/components/VueFormMaking'
import 'form-making/dist/FormMaking.css'
import { getTreeLists, lazyTrees } from '@/utils/getTrees'

Vue.component(GenerateForm.name, GenerateForm)
import extendBindTpl from '@/api/system/extendBindTpl'
import { getToken } from '@/utils/auth'
import crudDictDetail from '@/api/system/dictDetail'
import crudCategory from '@/api/system/category';
import Treeselect from '@riophae/vue-treeselect';
import oaPmTree from '@/api/oaWorkOrder/oaPmTree';
import { mapGetters } from 'vuex';

export default {
  name: 'OaPmForm',
  components: { Treeselect },
  dicts: ['project_status'],
  data() {
    const that = this
    return {
      weeklyCategorizes: [],
      isShowTree: true,
      projectForm: {
        id: null,
        name: null,
        isTop: '1',
        subCount: 0,
        pid: null,
        sort: 999,
        enabled: '1',
        fv1: '项目',
        categoryId: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ]
      },
      fv1Option: ['项目', '目录'],
      allprojectList: [],
      viewOrEdit: false, // 默认是编辑
      submitDisabled: false,
      processStructureValue: {},
      ruleForm: {},
      jsonData: {},
      formStruct: {},
      formData: {},
      showFormData: false,
      remoteFunc: {
        funcGetToken(resolve) {
          resolve(getToken())
        },
        getDictDetail(resolve, dictName) {
          crudDictDetail.get(dictName).then(response => {
            const options = response.content
            resolve(options)
          })
        },
        isStageInit(resolve) {
          // 是否立项
          this.getDictDetail(resolve, 'is_stage_init');
        },
        projectTypeList(resolve) {
          // 项目基本信息项目类型
          this.getDictDetail(resolve, 'project_info_type');
        },
        projectStageList(resolve) {
          // 项目基本信息项目阶段
          this.getDictDetail(resolve, 'project_info_stage');
        },
        projectStatusList(resolve) {
          // 项目基本信息项目状态
          this.getDictDetail(resolve, 'project_info_status');
        },
        getDepartment(resolve) {
          // 所属部门
          this.getDictDetail(resolve, 'department');
        },
        projectCategoryList(resolve) {
          const { categoryId } = that.$config.projects_categorize;
          // 项目分类
          this.cascaderGet(categoryId).then((value) => {
            resolve(value);
          });
        },

        cascaderGet(id) {
          return crudCategory.getChildren([id]).then((res) => {
            const data = res.content;
            if (data && data[0]) {
              const childrenData = data[0].children;
              if (childrenData.length > 0) {
                childrenData.map((item) => {
                  item.value = item.id;
                  if (item.hasChildren) {
                    this.handleData(item.children);
                  }
                });
                const options = childrenData;
                return options;
              }
            }
          });
        },
        // 对级联选择器数据进行处理
        handleData(option) {
          option && option.map((item) => {
            item.value = item.id;
            if (item.hasChildren) {
              this.handleData(item.children);
            }
          });
        }
      }
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  created() {
    this.getInitData();
  },
  methods: {
    handleTreeSelect(node) {
      this.projectForm.fv18 = node.name
    },
    getInitData() {
      const { id, pid, fv1, command, bindId, fv18 } = this.$route.query;
      this.$set(this.projectForm, 'fv1', this.fv1Option[command - 1]);
      if (id) {
        this.getContent(id);
        if (fv1 == '项目' || fv1 == '子项目') {
          const queryParams = {
            enabled: '1',
            fv1: ['项目', '子项目'],
            ids: [id],
            bindId,
            size: 99999
          }
          getTreeLists({ apiMethod: oaPmTree.getPmTreeSuperior, queryParams }).then(res => {
            this.allprojectList = res || []
          })
        }
        this.viewOrEdit = this.$route.query.type == 'see';
      } else {
        // 创建的时候
        const queryParams = {
          enabled: '1',
          fv1,
          bindId,
          size: 99999
        }
        if (pid) {
          this.isShowTree = false;
          queryParams.ids = [pid];
          this.$set(this.projectForm, 'isTop', '0');
          this.$set(this.projectForm, 'pid', pid);
          this.$set(this.projectForm, 'fv1', fv1);
          fv1 == '子项目' && this.$set(this.projectForm, 'fv18', fv18);
          if (command == 2) {
            this.showFormData = false;
          } else {
            this.getProcessNodeList();
          }
        } else {
          getTreeLists({ apiMethod: oaPmTree.getPmTree, queryParams }).then(res => {
            this.allprojectList = res || []
          })
          this.getProcessNodeList();
        }
      }
      this.getWeeklyCategory(id)
    },
    // 获取是项目还是周报分类
    getWeeklyCategory(type) {
      const { categoryId } = this.$config.weekly_categorize
      crudCategory.getChildren([categoryId]).then((res) => {
        const [firstItem = []] = res.content;
        this.weeklyCategorizes = firstItem.children || [];
        if (!type) {
          this.projectForm.categoryId = this.weeklyCategorizes[0].id;
        }
      })
    },
    lazyLoadTrees(params) {
      const otherParams = {
        fv1: ['项目', '子项目']
      }
      lazyTrees({ ...params, apiMethod: oaPmTree.getPmTree, otherParams })
    },
    getContent(id) {
      oaPmTree.getPmTree({ id, enabled: 1 }).then(res => {
        if (res && res.content && res.content.length) {
          const jsonData = res.content[0];
          this.processStructureValue = jsonData;
          this.formStruct = JSON.parse(jsonData.formStruct);
          this.formData = JSON.parse(jsonData.formData);
          this.jsonData = jsonData;
          this.showFormData = true;
          if (jsonData.enabled === true || jsonData.enabled == '1') {
            jsonData.enabled = '1';
          }
          if (jsonData.enabled === false || jsonData.enabled == '0') {
            jsonData.enabled = '0';
          }
          this.projectForm = jsonData
          if (!jsonData.pid) {
            this.$set(this.projectForm, 'isTop', '1');
          } else {
            this.$set(this.projectForm, 'isTop', '0');
          }
        }
      })
    },
    getProcessNodeList() {
      const data = { id: this.$route.query.bindId, enabled: 1 }
      extendBindTpl.get(data).then(res => {
        if (res && res.content && res.content.length) {
          this.processStructureValue = res.content[0];
          const FormStruct = JSON.parse(this.processStructureValue.extendTpl.formStruct);
          this.formStruct = FormStruct;
        }
        this.showFormData = true;
      });
    },
    submitAction() {
      this.$refs['ruleForm'].validate((valid) => {
        if (!valid) {
          return false;
        } else {
          if (this.projectForm.pid !== null && this.projectForm.pid === this.projectForm.id) {
            this.$message({
              message: '上级项目不能为当前节点',
              type: 'warning'
            })
            return false
          }
          if (this.projectForm.isTop == '1') {
            this.projectForm.pid = null
            this.projectForm.fv18 = null
            this.projectForm.fv1 = '项目'
          } else {
            // 不是顶级 没有选则pid不能执行
            if (!this.projectForm.pid) {
              this.$message({
                message: '上级项目不能为空',
                type: 'warning'
              })
              return false
            }
            this.projectForm.fv1 = '子项目'
          }
          this.checkModule().then(res => {
            if (!res.flag) {
              return false;
            } else {
              const subData = { ...this.projectForm, ...res.subData };
              let request = oaPmTree.add;
              if (subData.id) {
                request = oaPmTree.edit;
              } else {
                const items = [
                  {
                    user: { id: this.user.id, enabled: 1 },
                    enabled: 1
                  }
                ];
                subData['items'] = items
              }
              request(subData).then(response => {
                this.$notify({
                  title: '操作成功',
                  type: 'success',
                  duration: 2500
                })
                this.concelForm();
              }).catch((e) => {
                console.log(e);
              })
            }
          })
        }
      })
    },
    async checkModule() {
      const subData = {
        bindId: this.$route.query.bindId,
        formData: null,
        formStruct: null,
        formBindToVar: true,
        relation: this.processStructureValue.relation
      };
      if (this.showFormData) {
        const p = await this.$refs['generateForm'].getData().then(values => {
          console.log(values, '<===>', 'values')
          values['7'] = this.projectForm.name
          subData.formData = JSON.stringify(values);
          subData.formStruct = JSON.stringify(this.formStruct);
          return {
            flag: true,
            subData
          };
        }).catch(() => {
          return {
            flag: false
          };
        })
        return p;
      } else {
        return Promise.resolve({
          subData,
          flag: true
        });
      }
    },
    concelForm() {
      this.$router.push({ name: 'OaPm', query: { id: this.$route.query.id }});
    },
    changeFormData(field, value, models) {

    }
  }
};
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
::v-deep .vue-treeselect__control, ::v-deep .vue-treeselect__placeholder, ::v-deep .vue-treeselect__single-value {
	height: 30px;
	line-height: 30px;
}
</style>
