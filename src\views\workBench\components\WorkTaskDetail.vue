<template>
  <el-form class="task-detail" label-width="120px">
    <el-form-item label="任务标题：">
      {{ projectDetail.name }}
    </el-form-item>
    <el-form-item label="是否结束：">
      <el-tag v-if="projectDetail.currentStatusVal" size="mini" type="success">是</el-tag>
      <el-tag v-else size="mini" type="danger">否</el-tag>
    </el-form-item>
    <el-form-item label="任务状态：">
      {{ projectDetail.fv12 }}
    </el-form-item>
    <el-form-item label="任务详情：">

      {{ projectDetail.ft1 }}
    </el-form-item>
    <!--<el-form-item label="里程碑：">-->
    <!--  {{ projectDetail.fv7 }}-->
    <!--</el-form-item>-->
    <!--<el-form-item label="优先级：">-->
    <!--  {{ projectDetail.fv8 }}-->
    <!--</el-form-item>-->
    <el-form-item label="处理人：">
      {{ projectDetail.fv9 }}
    </el-form-item>
    <el-form-item label="开始时间：">
      {{ projectDetail.fv13 }}
    </el-form-item>
    <el-form-item label="预计结束时间：">
      {{ projectDetail.fv11 }}
    </el-form-item>
    <el-form-item v-if="projectDetail.attachment && projectDetail.attachment.length" label="附件：">
      <div
        v-for="(item,index) in projectDetail.attachment"
        :key="item.id"
        class="link-item"
      >
        <span
          @click="downloadUrl(item.response.url, item.response.originalFilename)"
        >
          {{ item.response.createBy }}---{{ item.response.originalFilename }}
        </span>
        <el-button
          circle
          class="del-button"
          icon="el-icon-delete"
          size="mini"
          type="danger"
          @click="delAttachment(index)"
        />
      </div>
    </el-form-item>
  </el-form>
</template>

<script>
import { mapGetters } from 'vuex';
import { downloadUrl } from '@/utils/index'

export default {
  name: 'WorkDetail',
  props: {
    projectDetail: {
      type: Object,
      default() {
        return {}
      }
    },
    type: {
      type: Number,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      downloadUrl
    }
  },
  computed: {
    ...mapGetters([
      'baseApi',
      'fileUploadApi'
    ])
  },
  watch: {},
  created() {
  },
  methods: {
    /**
		 * 删除附件
		 * @param index
		 */
    delAttachment(index) {
      if (this.type == 1) {
        this.$emit('delAttachment', index)
      }
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.task-detail {
	::v-deep .el-form-item__content {
		/* 允许文本溢出容器 */
		overflow: visible;
		/* 让溢出部分换行显示 */
		word-wrap: break-word;
		/* 或者使用 overflow-wrap */
		overflow-wrap: break-word;
		/* 或者强制在单词内部断行 */
		word-break: break-all;
	}

	.link-item {
		cursor: pointer;
		word-break: keep-all;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		color: #1890ff;
		font-size: 13px;
		margin-bottom: 8px;

		.del-button {
			margin-left: 5px;
		}

	}
}
</style>
