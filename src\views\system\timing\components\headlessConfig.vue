<template>
  <div class="app-container">
    <el-form ref="form" :model="formData" label-width="250px">
      <el-form-item label="启用无头混合爬取（实验性功能）">
        <el-radio-group v-model="formData.headless">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="使用本地已安装的Chrome浏览器而不是katana安装的浏览器">
        <el-radio-group v-model="formData.use_installed_chrome">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="在屏幕上显示浏览器（无头模式）">
        <el-radio-group v-model="formData.show_browser">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <add-input :arr-value="formData['headless-options']" label-info="使用附加选项启动无头Chrome" tip-info="" />
      <el-form-item label="no-sandbox这段代码是一个命令行工具的参数配置部分，用于解析用户在命令行中输入的参数并设置对应的选项值">
        <el-radio-group v-model="formData.headless_no_sandbox">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import addInput from '@/views/system/timing/common/addInput'
import { delArrInvalid } from '@/views/system/timing/utils/formate'
export default {
  name: 'ScopeConfig',
  components: { addInput },
  data() {
    return {
      formData: {
        'headless': null,
        'use_installed_chrome': null,
        'show_browser': null,
        'headless_optional_arguments': [],
        'headless_no_sandbox': null
        // 'system_chrome_path': null,
        // 'chrome_ws_url': null,
        // 'xhr_extraction': false
      }
    };
  },

  mounted() {

  },

  methods: {
    getFormData() {
      return {
        headless: delArrInvalid(this.formData)
      };
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
