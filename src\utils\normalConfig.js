import { getConfig } from '@/utils/getConfigData';
import Vue from 'vue';

export const setNormalConfig = async(query) => {
  try {
    const res = await getConfig({ ...query });
    const configData = res.extend.data;
    const { configInfo } = configData;
    const configItems = {}
    configInfo.map(item => {
      configItems[item.key] = item
    })
    Vue.prototype.$config = configItems
    Vue.prototype.$configData = configData
  } catch (error) {
    console.error('Error while fetching config:', error);
  }
}
