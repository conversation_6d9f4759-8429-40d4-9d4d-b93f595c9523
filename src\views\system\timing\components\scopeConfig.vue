<template>
  <div class="app-container">
    <el-form ref="form" :model="formData" label-width="250px">
      <add-input :arr-value="formData.scope" label-info="由爬虫跟随的范围URL正则表达式" tip-info="" />
      <add-input :arr-value="formData.out_of_scope" label-info="要排除的范围之外的URL正则表达式" tip-info="" />
      <el-form-item label="预定义的范围字段 (dn, rdn, fqdn)">
        <el-input v-model="formData.field_scope" placeholder="预定义的范围字段 (dn, rdn, fqdn)" style="width:60%" />
      </el-form-item>
      <el-form-item label="禁用基于主机的默认范围">
        <el-radio-group v-model="formData.no_scope">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="显示来自范围爬行的外部端点">
        <el-radio-group v-model="formData.display_out_scope">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import addInput from '@/views/system/timing/common/addInput'
import { delArrInvalid } from '@/views/system/timing/utils/formate'
export default {
  name: 'ScopeConfig',
  components: { addInput },
  data() {
    return {
      formData: {
        'scope': [],
        'out_of_scope': [],
        'field_scope': null,
        'no_scope': null,
        'display_out_scope': null
      }
    };
  },

  mounted() {

  },

  methods: {
    getFormData() {
      return {
        scope: delArrInvalid(this.formData)
      };
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
