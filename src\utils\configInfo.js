import globalConfig from '@/api/system/globalConfig'

function getConfig({ enabled = 1, type = 'NORMAL', key }) {
  return new Promise(resolve => {
    globalConfig.get({ enabled, type, key }).then((res) => {
      if (res?.content?.length) {
        const data = res.content[0]
        resolve(data)
      }
    })
  })
}

/**
 *  公共配置函数
 * @param {唯一标识} key
 * @param {配置类型} type
 */
async function getConfigFun(key, type) {
  const data = { key, type }
  return await getConfig(data)
}

export async function getConfigInfo(configDefinitions) {
  const configPromises = configDefinitions.map(({ key, type }) => getConfigFun(key, type))
  const configs = await Promise.all(configPromises)
  configs.forEach((config, i) => {
    const { prop } = configDefinitions[i]
    this[prop] = config
  })
}
