<template>
  <div
    v-loading="pageLoading"
    class="app-container oa-pm"
    element-loading-spinner="el-icon-loading"
    element-loading-text="更新中"
  >
    <!--工具栏-->
    <div class="head-container">
      <search-header :dict="dict" :permission="permission" />
      <crudOperation :permission="permission" :show-fixed="true">
        <!--一系列更新按钮-->
        <custom-action-button
          slot="right"
          :bind-id="bindId"
          :current-crud="crud"
          :permission="permission"
          @importProject="importProject"
          @successUpdateInfo="successUpdateInfo"
        />
        <!--<fixed-button slot="fixed" @openTagHistory="openTagHistory" />-->
      </crudOperation>
    </div>
    <!--表格渲染-->
    <div class="project-table-content">
      <Hamburger
        :is-active="operateShow"
        class="hamburger-container"
        @toggleClick="toggleSideBar"
      />
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="tableData"
        :header-cell-style="(params)=>tableRowClassName({column: params.column, query: crud.query})"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        class="table-hover"
        lazy
        row-key="id"
        @select="crud.selectChange"
        @select-all="crud.selectAllChange"
        @selection-change="crud.selectionChangeHandler"
      >
        <template v-for="(header, initIndex) in tableHeaders">
          <el-table-column
            :key="header.label + initIndex"
            :align="header.align"
            :fixed="header.fixed"
            :label="header.label"
            :prop="header.prop"
            :show-overflow-tooltip="true"
            :sortable="header.sortable || false"
            :width="header.width || 120"
          >
            <template v-slot:header>
              <div v-if="isDynamicHeader(header.prop)" class="custom-table-header" @dblclick="onClickTag(header.prop)">
                <template v-if="header.prop == currentEditHeader">
                  <edit-header
                    :header="header"
                  />
                </template>
                <template v-else>
                  <span>{{ header.prop }}</span>
                  <!--<el-popconfirm-->
                  <!--  ref="popconfirm"-->
                  <!--  title="确定删除该列吗？"-->
                  <!--&gt;-->
                  <!--  <i slot="reference" class="el-icon-circle-close del-icon" />-->
                  <!--</el-popconfirm>-->
                </template>

              </div>
              <div v-else>
                {{ header.label }}
              </div>
            </template>
            <template slot-scope="scope">
              <see-cell
                :crud="crud"
                :current-scope="scope"
                :dict="dict"
                :header="header"
                :is-show-tag="isShowTag"
                :permission="permission"
              />
            </template>
          </el-table-column>
        </template>

        <transition name="fade">

          <el-table-column
            v-if="operateShow"
            fixed="right"
            label="操作"
            width="180"
          >
            <template slot-scope="scope">
              <!--<el-button size="mini" type="success" @click="toInspects(scope.row)">巡检记录</el-button>-->
              <udOperation
                :data="scope.row"
                :permission="permission"
                msg="确定删除吗,此操作不能撤销！"
              />
            </template>
          </el-table-column>
        </transition>
      </el-table>

      <!--分页组件-->
      <pagination />
    </div>
    <!--导入项目-->
    <upload-excel ref="uploadExcel" @getlist="crud.toQuery()" />

    <!--标签历史数据-->
    <tag-history
      ref="tagHistory"
      :drawer-options="tagDrawerOptions"
      :select-query="tagSelectOptions"
    />
  </div>
</template>

<script>
import crudTable from '@/api/parts/assets'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination';
import UploadExcel from '@/views/junction/autoPilot4/components/UploadExcel'
import SeeCell from '@/views/junction/autoPilot4/components/SeeCell';
import CustomActionButton from '@/views/junction/autoPilot4/components/CustomActionButton';
// import FixedButton from '@/views/junction/autoPilot4/components/FixedButton';
import SearchHeader from '@/views/junction/autoPilot4/components/SearchHeader';
import EditHeader from '@/views/junction/autoPilot4/components/EditHeader';
import TagHistory from '@/views/components/UploadTagHistory/index';
import { mapGetters } from 'vuex'
import Hamburger from '@/components/Hamburger'
import { tableRowClassName } from '@/utils/setTableByLabel';
import {
  formatterTableData,
  formatterTableHeader,
  toggleSideBarFun
} from '@/views/junction/autoPilot4/utils/commonFun'
import {
  permission,
  tagDrawerOptions,
  tagSelectOptions
} from '@/views/junction/autoPilot4/utils/field'
// crud交由presenter持有
const defaultForm = {
  id: null
}
// const getTagApi = 'api/omAsset/findAssetWithTagAndTop';
const getSmallApi = 'api/omAsset/autoPilot';
export default {
  name: 'AutoPilot4',
  components: {
    UploadExcel,
    SearchHeader, crudOperation, pagination,
    CustomActionButton,
    // FixedButton,
    SeeCell,
    udOperation,
    Hamburger,
    EditHeader,
    TagHistory
  },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: false,
      title: '自动驾驶4.0项目',
      url: getSmallApi,
      sort: [],
      query: {
        enabled: 1, fv6: [], fv7: [], fv4OrTitle: '', 'type.fieldName': 'fv1',
        'type.values': '路口'
      },
      crudMethod: { ...crudTable },
      optShow: {
        add: true,
        edit: false,
        del: false,
        download: false,
        reset: true,
        rightGroup: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: [],
  data() {
    return {
      isShowTag: false, // 控制tag
      pageLoading: false,
      operateShow: false, // 操作列是否显示
      tableData: [],
      permission,
      bindId: '',
      categoryId: '',
      tableHeaders: [],
      dynamicHeader: [],
      currentEditHeader: '',
      tagDrawerOptions,
      tagSelectOptions
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ]),
    isDynamicHeader({ dynamicHeader },) {
      return (prop) => {
        if (dynamicHeader && dynamicHeader.length) {
          return dynamicHeader.some(element => element.prop === prop);
        }
        return false;
      }
    }
  },
  watch: {
    'crud.data': {
      async handler(val, oldVal) {
        const dynamicHeader = this.crud?.metaData?.tableHeader;
        this.dynamicHeader = dynamicHeader || [];
        this.tableHeaders = formatterTableHeader(val, dynamicHeader, this.crud.query)
        this.tableData = formatterTableData(val)
        await this.$nextTick()
        this.$refs.table.doLayout()
      },
      deep: true
    }
    // 'crud.query': {
    //   async handler(val) {
    //     console.log(val)
    //     if (val.tag && val.tag[0] && val.top && val.top[0]) { // 代表有标签，则去替换到请求url
    //       this.crud.url = getTagApi;
    //     } else {
    //       this.crud.url = getSmallApi;
    //     }
    //   },
    //   deep: true
    // }
  },
  async created() {
    this.crud.operToggle = false;
    const { bindId, categoryId } = this.$config.auto_pilot4_key
    const { fv4 } = this.$route.query
    if (fv4) {
      this.crud.query.fv4OrTitle = fv4
    }
    this.bindId = bindId;
    this.categoryId = categoryId
    this.setAssetConfig();
    this.crud.toQuery();
  },
  methods: {
    tableRowClassName,
    onClickTag(prop) {
      return
      // this.currentEditHeader = prop;
    },
    // 成功更新之后的操作
    successUpdateInfo(type) {
      if (type == 1) {
        this.pageLoading = true
      } else if (type == 2) {
        this.pageLoading = false
      } else {
        this.pageLoading = false
        this.crud.toQuery();
      }
    },
    // 导入雪亮资产
    importProject() {
      const { bindId, categoryId } = this.$config.auto_pilot4_key
      this.$refs.uploadExcel.init({ bindId, categoryId });
    },
    // 去雪亮巡检页面
    toInspects(row) {
      this.$router.push({
        name: 'XlInspects',
        query: {
          fv4: row.fv4,
          omAssetID: row.id,
          omAssetTitle: row.title
        }
      })
    },

    [CRUD.HOOK.beforeRefresh]() {
      this.setAssetConfig()
    },
    [CRUD.HOOK.beforeToAdd]() {
      const query = {
        name: 'AutoPilot4Form'
      }
      this.$router.push(query)
    },
    [CRUD.HOOK.beforeToEdit](crud, form) {
      const { id } = form
      this.$router.push({ name: 'AutoPilot4Form', query: { id }})
    },
    // 点击操作列头
    async toggleSideBar() {
      await toggleSideBarFun(this)
    },
    //   设置配置项
    setAssetConfig() {
      const { bindId, categoryId } = this.$config.auto_pilot4_key
      this.crud.query.bindId = bindId
      this.crud.query.categoryId = categoryId;
    },
    openTagHistory() {
      this.$refs.tagHistory.initData();
    }
  }
}
</script>

<style>
.table-hover .el-table__body tr.hover-row.current-row > td.el-table__cell, .el-table__body tr.hover-row.el-table__row--striped.current-row > td.el-table__cell, .el-table__body tr.hover-row.el-table__row--striped > td.el-table__cell, .el-table__body tr.hover-row > td.el-table__cell {
	background-color: transparent !important;
}
</style>
<style lang="scss" rel="stylesheet/scss" scoped>

//操作按钮相关
.operate-button {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.project-table-content {
	position: relative;

	.hamburger-container {
		position: absolute;
		right: 0;
		top: 0;
		z-index: 8;
		line-height: 40px;
		cursor: pointer;
		transition: background .3s;
		-webkit-tap-highlight-color: transparent;
		background: rgba(0, 0, 0, .09);

		&:hover {
			background: rgba(0, 0, 0, .19)
		}
	}
}

::v-deep .el-input-number .el-input__inner {
	text-align: left;
}

::v-deep .vue-treeselect__control, ::v-deep .vue-treeselect__placeholder, ::v-deep .vue-treeselect__single-value {
	height: 30px;
	line-height: 30px;
}

.table-fixed {
	//max-height: 760px;
	// 表格合计样式
}

.custom-table-header {
	display: flex;
	align-items: center;
	justify-content: center;

	&:hover {
		.del-icon {
			opacity: 1;
		}
	}

	.del-icon {
		font-size: 14px;
		line-height: 18px;
		color: #f56c6c;
		opacity: 0;
		cursor: pointer;
		margin-left: 5px;
	}
}

</style>
