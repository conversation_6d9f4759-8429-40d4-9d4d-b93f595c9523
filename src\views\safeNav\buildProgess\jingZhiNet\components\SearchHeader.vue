<template>
  <div v-if="crud.props.searchToggle" class="">
    <el-input
      v-model="query.fv4OrTitle"
      class="filter-item"
      clearable
      placeholder="请输入路口名称搜索"
      size="small"
      style="width: 180px;"
      @keyup.enter.native="crud.toQuery"
    />
    <rrOperation />
  </div>
</template>

<script>
import { header } from '@crud/crud';
import rrOperation from '@crud/RR.operation';

export default {
  components: { rrOperation },
  mixins: [header()],
  props: {
    permission: {
      type: Object,
      required: true
    },
    dict: {
      type: Object,
      required: true
    }
  },
  data() {
    return {}
  },

  computed: {},
  watch: {},
  created() {
  },
  methods: {}
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
