<template>
  <div class="app-container oa-pm" element-loading-spinner="el-icon-loading" element-loading-text="更新中">
    <!--工具栏-->
    <div class="head-container">
      <search-header :category-list="categoryList" :dict="dict" :permission="permission" />
      <crudOperation :permission="permission">
        <!--一系列更新按钮-->
        <custom-action-button
          slot="right"
          :bind-id="bindId"
          :current-crud="crud"
          :permission="permission"
          @importProject="importProject"
          @exportOutBoundData="exportOutBoundData"
        />
      </crudOperation>
    </div>
    <!--表格渲染-->
    <div ref="projectBody" class="project-table-content">
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="tableData"
        row-key="id"
        @select-all="crud.selectAllChange"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column
          v-for="(header, initIndex) in tableHeaders"
          :key="header.label + initIndex"
          v-bind="getColumnProps(header)"
        >
          <template slot-scope="scope">
            <see-cell :current-cell="scope" :header="header" :permission="permission" />
          </template>
        </el-table-column>
        <transition name="fade">
          <el-table-column fixed="right" label="操作" width="270">
            <template slot-scope="scope">
              <el-button type="success" size="mini" @click="viewDetails(scope.row)">查看</el-button>
              <el-button style="margin-left:0" type="warning" size="mini" @click="goStock(scope.row)">库存</el-button>
              <udOperation :data="scope.row" :msg="`确定删除这个库存吗？此操作不能撤销！`" :permission="permission" />
            </template>
          </el-table-column>
        </transition>
      </el-table>

      <!--分页组件-->
      <pagination />
    </div>
    <eForm v-if="eFormShow" ref="eForm" @successAction="eFormSuccessAction" />
    <!--导入项目-->
    <upload-file ref="refUploadFile" @getlist="crud.toQuery()" />

    <!-- 导出出库数据模态框 -->
    <el-dialog title="导出出库数据" :visible.sync="exportDialogVisible" width="600px" @close="resetExportForm">
      <el-form ref="exportForm" :model="exportForm" :rules="exportRules" label-width="100px">
        <el-form-item label="出库类型" prop="orderType">
          <el-select v-model="exportForm.orderType" placeholder="请选择出库类型" style="width: 350px;">
            <el-option label="新设备" value="1" />
            <el-option label="拆回" value="2" />
            <el-option label="维修返回" value="3" />
            <el-option label="后期接收" value="4" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="exportDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmExport">确 定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import amStockOut from '@/api/property/amStockOut'
import { getToken } from '@/utils/auth'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination';
import UploadFile from './components/uploadFile.vue'
import SeeCell from './components/seeCell.vue';
import CustomActionButton from './components/customActionButton.vue';
import SearchHeader from './components/searchHeader.vue'
import eForm from './components/eForm.vue';
import { mapGetters } from 'vuex'
import {
  formatterTableData,
  formatterTableHeader
} from './utils/commonFun'
import {
  permission
} from './utils/field'
// crud交由presenter持有
const defaultForm = {
  id: null
}
export default {
  name: 'Stock',
  components: {
    eForm,
    UploadFile,
    SearchHeader,
    crudOperation,
    udOperation,
    pagination,
    CustomActionButton,
    SeeCell
  },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: false,
      title: '订单',
      url: 'api/amStockOut/small',
      sort: [],
      query: {
        basicNo: '',
        enabled: 1,
        title: '',
        fv4: '',
        sort: ['createTime,desc']
      },
      crudMethod: { ...amStockOut },
      optShow: {
        add: false,
        edit: false,
        del: false,
        download: false,
        reset: true,
        rightGroup: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: [],
  data() {
    return {
      eFormShow: false,
      tableData: [],
      permission,
      toolbarID: 'toolbarID',
      bindId: '',
      tableHeaders: [],
      categoryList: [],
      selectOption: {
        fv2: []
      },
      filterValues: {},
      filteredData: [],
      // 导出相关数据
      exportDialogVisible: false,
      exportForm: {
        orderType: ''
      },
      exportRules: {
        orderType: [
          { required: true, message: '请选择出库类型', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  watch: {
    'crud.data': {
      async handler(val, oldVal) {
        // 处理表头和筛选选项
        this.tableHeaders = formatterTableHeader(val)
        this.tableData = formatterTableData(val)
      },
      deep: true
    }
  },
  async created() {
    this.crud.operToggle = true
    const { bindId } = this.$config.outBound_key
    this.bindId = bindId;
    this.crud.query.bindId = bindId
    const { basicNo } = this.$route.query;
    if (basicNo) {
      this.crud.query.basicNo = basicNo
    }
    this.crud.toQuery();
  },
  methods: {
    async [CRUD.HOOK.beforeToEdit](crud, form) {
      const { id } = form
      this.eFormShow = true
      await this.$nextTick()
      const data = {
        id
      }
      this.$refs.eForm.init(data)
    },
    async viewDetails(row) {
      const { id } = row
      this.eFormShow = true
      await this.$nextTick()
      const data = {
        type: 2,
        id
      }
      this.$refs.eForm.init(data)
    },
    goStock(row) {
      this.$router.push({ name: 'Stock', query: { basicNo: row.basicData?.basicNo }})
    },
    // 表格属性
    getColumnProps(header) {
      return {
        'header-align': header.align ? header.align : 'left',
        align: header.align,
        fixed: header.fixed,
        label: header.label,
        prop: header.prop,
        'show-overflow-tooltip': true,
        sortable: header.sortable || false,
        width: header.width
      };
    },
    // 导入项目
    importProject() {
      const { bindId, categoryId } = this.$config.order_key
      this.$refs.refUploadFile.init({ bindId, categoryId });
    },
    [CRUD.HOOK.beforeRefresh]() {
      this.crud.query.bindId = this.bindId
    },
    [CRUD.HOOK.beforeResetQuery]() {
      this.$refs.table.clearSort()
    },
    handleMany(data) {
      const { item } = data
      typeof this[item.fun] === 'function' && this[item.fun](data)
    },
    // 修改出库成功成功
    eFormSuccessAction() {
      this.crud.refresh()
      this.eFormShow = false
    },
    // 显示导出对话框
    showExportDialog() {
      this.exportDialogVisible = true
    },
    // 重置导出表单
    resetExportForm() {
      this.exportForm.orderType = ''
      this.$refs.exportForm && this.$refs.exportForm.resetFields()
    },
    // 确认导出
    confirmExport() {
      this.$refs.exportForm.validate((valid) => {
        if (valid) {
          this.exportOutBoundData()
        } else {
          return false
        }
      })
    },
    async exportOutBoundData() {
      try {
        // 构建导出URL
        const exportUrl = `api/amStockOut/exportStockOut`

        // 使用fetch获取文本流数据
        const response = await fetch(exportUrl, {
          method: 'GET',
          headers: {
            'Authorization': getToken(),
            'Content-Type': 'application/json'
          }
        })

        if (!response.ok) {
          throw new Error('网络响应异常')
        }

        // 获取响应的blob数据
        const blob = await response.blob()

        // 创建下载链接并触发下载
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        const fileName = `出库数据_${new Date().toISOString().slice(0, 10)}.xlsx`

        link.setAttribute('download', fileName)
        document.body.appendChild(link)
        link.click()

        // 清理
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        this.$message({
          message: '导出成功！',
          type: 'success'
        })
        this.exportDialogVisible = false
        this.resetExportForm()
      } catch (error) {
        console.error('导出失败:', error)
        this.$message({
          message: '导出失败，请重试',
          type: 'error'
        })
      }
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
//操作按钮相关
.operate-button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;

}

.project-table-content {
  position: relative;

  .hamburger-container {
    position: absolute;
    right: 0;
    top: 0;
    z-index: 8;
    line-height: 50px;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color: transparent;
    background: rgba(0, 0, 0, .09);

    &:hover {
      background: rgba(0, 0, 0, .19)
    }
  }
}

::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

::v-deep .vue-treeselect__control,
::v-deep .vue-treeselect__placeholder,
::v-deep .vue-treeselect__single-value {
  height: 30px;
  line-height: 30px;
}

.info-vxe-toolbar {
  padding: 0 !important;
  width: 28px !important;
  height: 28px !important;

  .vxe-button.vxe-tools--operate.type--button {
    min-width: 28px !important;
    height: 28px !important;
    padding: 0px 12px !important;

  }

  .vxe-tools--operate.is--circle {
    border-radius: 0 !important;
  }

}

.custom-filter {
  padding: 8px;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
}

.filter-select-dropdown {
  z-index: 999999 !important;
}

.my-filter {
  padding: 12px;

  .filter-footer {
    margin-top: 12px;
    text-align: right;

    >button {
      margin-left: 8px;
    }
  }
}

.custom-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 4px;

  .el-icon-arrow-down {
    cursor: pointer;
    padding: 0 4px;
    font-size: 16px;
    color: #909399;
    transition: transform .3s;

    &:hover {
      color: #409EFF;
    }

    &.is-active {
      color: #409EFF;
      transform: rotate(180deg);
    }
  }
}

.filter-content {
  padding: 12px;

  .filter-footer {
    margin-top: 12px;
    text-align: right;

    >button {
      margin-left: 8px;
    }
  }
}
</style>

<style lang="scss">
.filter-popover {
  min-width: 240px !important;
}
</style>
