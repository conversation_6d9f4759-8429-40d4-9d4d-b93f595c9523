<template>
  <!-- 绑定规则 -->
  <el-dialog v-if="isShow" v-loading="loading" append-to-body :show-close="false" destroy-on-close :close-on-click-modal="false" :visible="isShow" title="绑定模板" width="500px">
    <el-form ref="form" :rules="formRules" :model="form" size="small" label-width="120px">
      <el-form-item label="权限标识" prop="eventTag">
        <el-input v-model="form.eventTag" clearable style="width: 300px" placeholder="" />
      </el-form-item>
      <el-form-item label="切入点" prop="pointcut">
        <el-select v-model="form.pointcut" clearable style="width: 300px" placeholder="请选择切入点">
          <el-option
            v-for="item in dict.rule_point_cut"
            :key="item.id"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="范围" prop="scope">
        <el-select v-model="form.scope" clearable style="width: 300px" placeholder="请选择范围">
          <el-option
            v-for="item in dict.rule_scope"
            :key="item.id"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="规则链" prop="ruleChain">
        <el-select v-model="form.ruleChain" clearable style="width: 300px" placeholder="请选择规则链">
          <el-option
            v-for="item in ruleList"
            :key="item.id"
            :value="item.key"
            :label="item.description"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="text" @click="bindCancel()">取消</el-button>
      <el-button v-if="checkPer(['admin','extendMenuRule:edit','extendMenuRule:add'])" type="primary" @click="submitted()">确认</el-button>
      <el-button v-permission="['admin','extendMenuRule:del']" type="danger" @click="deleteTpl()">删除</el-button>
    </span>
  </el-dialog>
</template>

<script>
import bindRule from '@/api/system/bindRule'
import configRule from '@/api/system/globalConfig'
// import { getBindUrl } from '@/utils/getBindId.js'
export default {
  data() {
    return {
      form: {
        id: undefined,
        enabled: 1,
        eventTag: '',
        menuId: '',
        pointcut: '',
        ruleChain: '',
        scope: ''
      },
      formRules: {
        'eventTag': { required: true, message: '请填写权限标识', trigger: 'blur' },
        'pointcut': { required: true, message: '请选择切入点', trigger: 'blur' },
        'ruleChain': { required: true, message: '请选择范围', trigger: 'blur' },
        'scope': { required: true, message: '请选择规则链', trigger: 'blur' }
      },
      isShow: false,
      ruleList: [],
      isEdit: false,
      loading: false
    }
  },
  dicts: ['rule_scope', 'rule_point_cut'],
  methods: {
    // 初始化数据
    async initData(data) {
      this.loading = true;
      this.isShow = true;
      this.getConfigRule();
      const getData = await this.getBindRule(data.id);
      this.loading = false;
      if (getData?.content?.length) {
        this.isEdit = true;
        this.form = getData.content[0];
        return
      }
      this.form.eventTag = data.permission;
      this.form.menuId = data.id;
    },

    // 点击取消
    bindCancel() {
      this.isShow = false;
      this.isEdit = false;
      this.loading = false;
      Object.keys(this.form).forEach(key => {
        this.form[key] = ''
      });
      this.form.id = undefined;
      this.form.enabled = 1;
    },
    // 获取所有规则
    getConfigRule() {
      const data = {
        type: 'RULE',
        size: 999,
        enabled: 1,
        sort: 'id,desc'
      };
      configRule.get(data).then(response => {
        this.ruleList = response.content;
      })
    },
    // 获取已经绑定的规则
    async getBindRule(menuId) {
      const data = {
        enabled: 1,
        menuId,
        sort: 'id,desc'
      };
      return await bindRule.get(data);
    },
    // 确定绑定
    submitted() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let title = '绑定成功';
          let api = bindRule.add;
          if (this.isEdit) {
            title = '修改成功';
            api = bindRule.edit;
          }
          api(this.form).then(res => {
            this.$notify({
              title: title,
              type: 'success',
              duration: 2500
            });
            this.bindCancel();
          })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    deleteTpl() {
      if (!this.form.id) {
        this.$notify({
          title: '还没有绑定过规则',
          type: 'info',
          duration: 2500
        });
        return;
      }
      this.$confirm('此操作将永久删除该按钮的模板绑定规则, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        bindRule.del([this.form.id]).then((res) => {
          this.bindCancel();
          this.$notify({
            title: '删除成功',
            type: 'success',
            duration: 2500
          });
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    }

  }
}
</script>
