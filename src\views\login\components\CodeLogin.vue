<template>
  <div class="code-login">
    <!--扫码登录-->
    <!--<wxlogin-->
    <!--  :appid="wxLogin.appid"-->
    <!--  :href="wxLogin.href"-->
    <!--  :redirect_uri="wxLogin.redirect_uri"-->
    <!--  :scope="wxLogin.scope"-->
    <!--/>-->
    <self-wx-login
      :appid="wxLogin.appid"
      :href="wxLogin.href"
      :redirect-uri="wxLogin.redirect_uri"
      :scope="wxLogin.scope"
    />
  </div>
</template>

<script>
import settings from '@/settings';
import SelfWxLogin from '@/components/SelfWxLogin/index.vue';

const WXLOGINHERF = 'aWZyYW1lIHtoZWlnaHQ6IGF1dG8gIWltcG9ydGFudH0KI3RwbF9mb3JfaWZyYW1lIHt3aWR0aDogMjAwcHg7fQoud2ViX3FyY29kZV90eXBlX2lmcmFtZSB7d2lkdGg6IDIwMHB4fQouaW1wb3dlckJveCB7d2lkdGg6IDIwMHB4O30KLmltcG93ZXJCb3ggLnFyY29kZSB7d2lkdGg6IDIwMHB4O21hcmdpbi10b3A6MH0KLmltcG93ZXJCb3ggLnRpdGxlIHtmb250LXNpemU6MTZweDtjb2xvcjogIzcwNzA3MDt9Ci5pbXBvd2VyQm94IC5sb2dpblBhbmVsIC50aXRsZSB7ZGlzcGxheTpub25lO30KLmltcG93ZXJCb3ggLmluZm8ge3dpZHRoOiAyMDBweDt9Ci5zdGF0dXNfaWNvbiB7ZGlzcGxheTogbm9uZSAhaW1wb3J0YW50fQouaW1wb3dlckJveCAuc3RhdHVzIHt0ZXh0LWFsaWduOiBjZW50ZXI7fSAK'
export default {
  name: 'CodeLogin',
  components: {
    SelfWxLogin
  },

  props: {},
  data() {
    return {
      wxLogin: {
        appid: settings.appid,
        scope: 'snsapi_login',
        // redirect_uri: 'http://************:8020',
        redirect_uri: 'http://routine.fatoan.com',
        self_redirect: true,
        href: `data:text/css;base64,${WXLOGINHERF}`
      }
    }
  },
  mounted() {
    this.editIfream();
  },
  methods: {
    editIfream() {
      const iframes = document.getElementsByTagName('iframe');
      console.log(iframes, '<===>', 'iframes')
      if (iframes.length > 0) {
        const firstIframe = iframes[0];
        firstIframe.style.width = '206px';
        // firstIframe.sandbox.add('allow-same-origin');
        // // 移除所有的 sandbox 属性
        // const curSandboxValues = Array.from(firstIframe.sandbox);
        // for (const i in curSandboxValues) {
        //   firstIframe.sandbox.remove(curSandboxValues[i]);
        // }
        //
        // // 重新添加 sandbox 属性，先添加 'allow-same-origin'
        // firstIframe.sandbox.add('allow-same-origin');
        // for (const i in curSandboxValues) {
        //   firstIframe.sandbox.add(curSandboxValues[i]);
        // }
      }
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.code-login {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
