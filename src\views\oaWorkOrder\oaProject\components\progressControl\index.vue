<template>
  <div class="progress-control-container">
    <!-- 左右布局 -->
    <el-row :gutter="20">
      <el-col :span="8">
        <div class="left-panel">
          <!-- 搜索栏 -->
          <div class="search-section">
            <el-input v-model="searchText" placeholder="请输入目录名称" clearable @input="handleSearch">
              <i slot="prefix" class="el-input__icon el-icon-search" />
            </el-input>
          </div>

          <!-- 项目名和新增按钮 -->
          <div class="header-section">
            <span class="project-name">项目目录 （进度：{{ projectProgress }}%）</span>
            <div class="header-buttons">
              <el-button type="success" size="small" @click="showImportDialog">
                导入
              </el-button>
              <el-button type="primary" size="small" @click="showAddDirectoryDialog">
                新增
              </el-button>
            </div>
          </div>

          <!-- 树型控件 -->
          <div class="tree-section">
            <el-tree
              ref="directoryTree"
              :data="treeData"
              :props="treeProps"
              node-key="id"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              :highlight-current="true"
              :expanded-keys="expandedKeys"
              @node-expand="handleNodeExpand"
              @node-collapse="handleNodeCollapse"
              @node-contextmenu="handleNodeContextMenu"
              @node-click="handleNodeClick"
            >
              <span slot-scope="{ node, data }" class="custom-tree-node">
                <span class="node-label">
                  <i v-if="data.type === 'catalog'" class="el-icon-folder node-icon" />
                  <i v-else-if="data.type === 'point'" class="el-icon-location node-icon" />
                  <span>{{ node.label }}</span>
                </span>
              </span>
            </el-tree>
          </div>
        </div>
      </el-col>

      <el-col :span="16">
        <div class="right-panel">
          <!-- 未选择点位时的提示 -->
          <div v-if="!selectedPoint" class="no-selection">
            <i class="el-icon-info" />
            <p>请在左侧选择一个点位查看里程碑列表</p>
          </div>

          <!-- 选择点位后的里程碑管理 -->
          <div v-else class="milestone-management">
            <!-- 搜索区域 -->
            <div class="search-area">
              <el-form :inline="true" :model="milestoneSearchForm" class="search-form">
                <el-form-item label="名称：">
                  <el-input v-model="milestoneSearchForm.name" placeholder="请输入里程碑名称" clearable style="width: 200px" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="searchMilestones">查询</el-button>
                  <el-button @click="resetMilestoneSearch">重置</el-button>
                  <el-button type="success" @click="showAddMilestoneDialog">添加里程碑</el-button>
                  <el-button type="danger" :disabled="selectedMilestones.length === 0" @click="batchDeleteMilestones">
                    批量删除
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- 里程碑列表标题 -->
            <div class="milestone-title">
              <h3>里程碑列表【{{ selectedPoint.originalName || selectedPoint.name }}】</h3>
            </div>

            <!-- 里程碑列表 -->
            <div class="milestone-table">
              <el-table
                :data="filteredMilestones"
                style="width: 100%"
                @selection-change="handleMilestoneSelectionChange"
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="name" label="里程碑名称" min-width="120" />
                <el-table-column label="计划日期" min-width="180">
                  <template slot-scope="scope">
                    {{ formatDateRange(scope.row.planStartDate, scope.row.planEndDate) }}
                  </template>
                </el-table-column>
                <el-table-column label="实际日期" min-width="180">
                  <template slot-scope="scope">
                    {{ formatDateRange(scope.row.realStartDate, scope.row.realEndDate) }}
                  </template>
                </el-table-column>
                <el-table-column prop="weight" label="权重" width="80">
                  <template slot-scope="scope">
                    <span>{{ !scope.row.weight.toString().includes('%') ? scope.row.weight + '%' :
                      scope.row.weight }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="进度" width="180">
                  <template slot-scope="scope">
                    <el-progress :percentage="scope.row.progress" :show-text="true" :stroke-width="8" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="260" fixed="right">
                  <template slot-scope="scope">
                    <el-button size="mini" @click="openControlPointDrawer(scope.row)">
                      管控点管理
                    </el-button>
                    <el-button size="mini" type="primary" @click="editMilestone(scope.row)">
                      修改
                    </el-button>
                    <el-button size="mini" type="danger" @click="deleteMilestone(scope.row)">
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 里程碑分页器 -->
            <div class="milestone-pagination">
              <el-pagination
                :current-page="milestonePage.page"
                :page-size="milestonePage.size"
                :page-sizes="milestonePage.sizes"
                :total="milestonePage.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleMilestonePagination({ page: milestonePage.page, limit: $event })"
                @current-change="handleMilestonePagination({ page: $event, limit: milestonePage.size })"
              />
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 右键菜单 -->
    <ul v-show="contextMenuVisible" :style="contextMenuStyle" class="context-menu">
      <li v-if="contextMenuType === 'catalog'" @click="handleAddSubDirectory">
        <i class="el-icon-folder-add" />
        添加子目录
      </li>
      <li v-if="contextMenuType === 'catalog'" @click="handleAddPoint">
        <i class="el-icon-location" />
        添加点位
      </li>
      <li @click="handleEdit">
        <i class="el-icon-edit" />
        修改
      </li>
      <li @click="handleDelete">
        <i class="el-icon-delete" />
        删除
      </li>
    </ul>

    <!-- 添加目录模态框 -->
    <el-dialog :title="dialogTitle" :visible.sync="directoryDialogVisible" width="800px" @close="resetDirectoryForm">
      <el-form ref="directoryForm" :model="directoryForm" :rules="directoryRules" label-width="220px">
        <el-form-item v-if="showParentSelect" label="上级目录" prop="parentId">
          <el-select v-model="directoryForm.parentId" placeholder="请选择上级目录" style="width: 100%">
            <el-option v-for="item in parentOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="directoryForm.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="目录排序 （值越小越靠前！）" prop="sort">
          <el-input-number v-model="directoryForm.sort" :min="0" style="width: 100%" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="directoryDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleDirectorySubmit">确定</el-button>
      </div>
    </el-dialog>

    <!-- 添加点位模态框 -->
    <el-dialog :title="pointDialogTitle" :visible.sync="pointDialogVisible" width="800px" @close="resetPointForm">
      <el-form ref="pointForm" :model="pointForm" :rules="pointRules" label-width="220px">
        <el-form-item label="上级目录" prop="parentId">
          <el-select v-model="pointForm.parentId" placeholder="请选择上级目录" style="width: 100%">
            <el-option v-for="item in parentOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="点位" prop="name">
          <el-input v-model="pointForm.name" placeholder="请输入点位名称" />
        </el-form-item>
        <el-form-item label="经度" prop="lon">
          <el-input v-model="pointForm.lon" placeholder="请输入经度" />
        </el-form-item>
        <el-form-item label="纬度" prop="lat">
          <el-input v-model="pointForm.lat" placeholder="请输入纬度" />
        </el-form-item>
        <el-form-item label="目录排序 （值越小越靠前！）" prop="sort">
          <el-input-number v-model="pointForm.sort" :min="0" style="width: 100%" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="pointDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handlePointSubmit">确定</el-button>
      </div>
    </el-dialog>

    <!-- 添加/修改里程碑模态框 -->
    <el-dialog
      :title="milestoneDialogTitle"
      :visible.sync="milestoneDialogVisible"
      width="800px"
      @close="resetMilestoneForm"
    >
      <el-form ref="milestoneForm" :model="milestoneForm" :rules="milestoneRules" label-width="220px">
        <el-form-item label="所属点位" prop="pointName">
          <el-input v-model="milestoneForm.pointName" disabled />
        </el-form-item>
        <el-form-item label="里程碑名称" prop="name">
          <el-input v-model="milestoneForm.name" placeholder="请输入里程碑名称" />
        </el-form-item>
        <el-form-item label="计划日期" prop="planDateRange">
          <el-date-picker
            v-model="milestoneForm.planDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="实际开始日期" prop="realStartDate">
          <el-date-picker
            v-model="milestoneForm.realStartDate"
            type="date"
            placeholder="选择实际开始日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="实际结束日期" prop="realEndDate">
          <el-date-picker
            v-model="milestoneForm.realEndDate"
            type="date"
            placeholder="选择实际结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="width: 100%"
          />
        </el-form-item>
        <!-- <el-form-item label="状态" prop="status">
          <el-radio-group v-model="milestoneForm.status">
            <el-radio :label="1">未开始</el-radio>
            <el-radio :label="2">进行中</el-radio>
            <el-radio :label="3">已完成</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item label="里程碑要求" prop="remark">
          <el-input v-model="milestoneForm.remark" type="textarea" :rows="3" placeholder="请输入里程碑要求" />
        </el-form-item>
        <el-form-item label="里程碑权重" prop="weight">
          <el-input-number v-model="milestoneForm.weight" :min="0" style="width: 100%" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="milestoneDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleMilestoneSubmit">确定</el-button>
      </div>
    </el-dialog>

    <!-- 管控点管理抽屉 -->
    <el-drawer
      :visible.sync="controlPointDrawerVisible"
      direction="rtl"
      size="50%"
      :show-close="false"
      :with-header="false"
      @close="closeDrawer"
    >
      <div class="drawer-content">
        <!-- 抽屉头部 -->
        <div class="drawer-header">
          <span class="drawer-title">管控点管理</span>
          <div class="drawer-actions">
            <el-button size="small" @click="controlPointDrawerVisible = false">
              关闭
            </el-button>
            <el-button type="primary" size="small" @click="showAddControlPointDialog">
              新增管控点
            </el-button>
          </div>
        </div>

        <!-- 管控点列表 -->
        <div class="control-point-list">
          <el-table :data="currentMilestoneControlPoints" style="width: 100%" stripe>
            <el-table-column prop="name" label="管控点名称" min-width="150" />
            <el-table-column label="状态" width="100" align="center">
              <template slot-scope="scope">
                <el-tag :type="getControlPointStatusType(scope.row.status)">
                  {{ getControlPointStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="完成情况" width="150" />
            <el-table-column prop="weight" label="权重" width="80">
              <template slot-scope="scope">
                <span>{{ !scope.row.weight.toString().includes('%') ? scope.row.weight + '%' :
                  scope.row.weight }}</span>
              </template>
            </el-table-column>
            <el-table-column label="附件" width="80" align="center">
              <template slot-scope="scope">
                <a v-if="scope.row.attachmentUrl" style="color: #409eff;" :href="scope.row.attachmentUrl">附件下载</a>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right" align="center">
              <template slot-scope="scope">
                <el-button size="mini" type="primary" @click="editControlPoint(scope.row)">
                  修改
                </el-button>
                <el-button size="mini" type="danger" @click="deleteControlPoint(scope.row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-drawer>

    <!-- 添加/修改管控点模态框 -->
    <el-dialog
      :title="controlPointDialogTitle"
      :visible.sync="controlPointDialogVisible"
      width="800px"
      @close="resetControlPointForm"
    >
      <el-form ref="controlPointForm" :model="controlPointForm" :rules="dynamicControlPointRules" label-width="220px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="controlPointForm.name" placeholder="请输入管控点名称" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="controlPointForm.status" @change="handleStatusChange">
            <el-radio :label="1">未开始</el-radio>
            <el-radio :label="2">进行中</el-radio>
            <el-radio :label="3">已完成</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="管控点完成情况" prop="remark">
          <el-input v-model="controlPointForm.remark" type="textarea" :rows="3" placeholder="请输入管控点完成情况" />
        </el-form-item>
        <el-form-item label="管控点权重" prop="weight">
          <el-input-number v-model="controlPointForm.weight" :min="0" style="width: 100%" />
        </el-form-item>
        <el-form-item label="附件上传" prop="attachment_url">
          <el-upload
            ref="attachmentUpload"
            :action="attachmentUploadUrl"
            :headers="uploadHeaders"
            :file-list="fileList"
            :on-success="handleAttachmentSuccess"
            :on-remove="handleAttachmentRemove"
            :limit="1"
            :on-exceed="handleAttachmentExceed"
          >
            <el-button size="small" type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="controlPointDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleControlPointSubmit">确定</el-button>
      </div>
    </el-dialog>

    <!-- 导入进度管控文件（隐藏的文件选择器） -->
    <input ref="importFileInput" type="file" accept=".xlsx,.xls" style="display: none" @change="handleFileSelect">
  </div>
</template>

<script>
import {
  addCatalog,
  delCatalog,
  editCatalog,
  addMilestone,
  delMilestone,
  editMilestone,
  getMilestoneList,
  addPoint,
  delPoint,
  editPoint,
  addStep,
  delStep,
  editStep,
  getStepList,
  getCatalogTree,
  importPmProgress
} from '@/api/oaWorkOrder/oaPmProgress'
import { getToken } from '@/utils/auth'

export default {
  name: 'ProgressControl',
  data() {
    return {
      // 搜索文本
      searchText: '',

      // 树形数据
      treeData: [],

      // 树形配置
      treeProps: {
        children: 'children',
        label: 'displayName'
      },

      // 展开的节点keys
      expandedKeys: [],

      // 右键菜单相关
      contextMenuVisible: false,
      contextMenuStyle: {
        position: 'fixed',
        top: '0px',
        left: '0px',
        zIndex: 9999
      },
      contextMenuType: 'catalog', // catalog 或 point
      currentNode: null,

      // 目录对话框相关
      directoryDialogVisible: false,
      dialogTitle: '添加目录',
      showParentSelect: false,
      directoryForm: {
        name: '',
        sort: 0,
        parentId: undefined
      },
      directoryRules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ]
      },

      // 点位对话框相关
      pointDialogVisible: false,
      pointDialogTitle: '添加点位',
      pointForm: {
        name: '',
        lon: '',
        lat: '',
        sort: 0,
        parentId: undefined
      },
      pointRules: {
        name: [
          { required: true, message: '请输入点位名称', trigger: 'blur' }
        ],
        parentId: [
          { required: true, message: '请选择上级目录', trigger: 'change' }
        ],
        lon: [
          { required: true, message: '请输入经度', trigger: 'blur' }
        ],
        lat: [
          { required: true, message: '请输入纬度', trigger: 'blur' }
        ]
      },

      // 编辑模式
      isEditMode: false,
      editingId: null,

      // ID生成器
      nextId: 1000,

      // 选中的点位
      selectedPoint: null,

      // 里程碑搜索表单
      milestoneSearchForm: {
        name: ''
      },

      // 里程碑列表数据
      milestones: [],

      // 里程碑分页数据
      milestonePage: {
        page: 1,
        size: 10,
        total: 0,
        sizes: [10, 20, 30, 50]
      },

      // 选中的里程碑
      selectedMilestones: [],

      // 里程碑对话框相关
      milestoneDialogVisible: false,
      milestoneDialogTitle: '添加里程碑',
      milestoneForm: {
        pointName: '',
        pointId: null,
        name: '',
        planDateRange: [],
        realStartDate: '',
        realEndDate: '',
        status: 1,
        remark: '',
        weight: 0
      },
      milestoneRules: {
        name: [
          { required: true, message: '请输入里程碑名称', trigger: 'blur' }
        ]
      },

      // 管控点抽屉相关
      controlPointDrawerVisible: false,
      currentMilestone: null,

      // 管控点数据
      controlPoints: [],

      // 管控点对话框相关
      controlPointDialogVisible: false,
      controlPointDialogTitle: '添加管控点',
      controlPointForm: {
        name: '',
        status: 1, // 默认未开始
        remark: '',
        weight: 0,
        attachment_url: '' // 附件URL
      },
      // 文件列表，用于el-upload组件
      fileList: [],
      controlPointRules: {
        name: [
          { required: true, message: '请输入管控点名称', trigger: 'blur' }
        ]
      },

      // 图片预览相关
      dialogVisible: false,
      dialogImageUrl: '',
      uploadAction: '/api/upload', // 上传接口地址

      // 编辑相关
      isMilestoneEditMode: false,
      editingMilestoneId: null,
      isControlPointEditMode: false,
      editingControlPointId: null,
      projectProgress: 0

      // 导入相关（保留空对象以防需要）
    }
  },
  computed: {
    // 获取所有目录选项（用于上级目录选择）
    parentOptions() {
      const options = []
      this.getDirectoryOptions(this.treeData, options)
      return options
    },

    // 附件上传URL
    attachmentUploadUrl() {
      return this.$store.getters.cloudfileUploadApi + '?name=管控点附件&platform=ALIYUN-oss-1'
    },

    // 上传请求头
    uploadHeaders() {
      return {
        'Authorization': getToken()
      }
    },

    // 过滤后的里程碑列表
    filteredMilestones() {
      if (!this.selectedPoint) return []
      // 现在分页和搜索都在后端处理，直接返回里程碑列表
      return this.milestones
    },

    // 当前里程碑的管控点列表
    currentMilestoneControlPoints() {
      if (!this.currentMilestone) return []

      return this.controlPoints
        .filter(cp => cp.milestoneId === this.currentMilestone.id)
        .sort((a, b) => a.weight - b.weight)
    },

    // 动态管控点验证规则
    dynamicControlPointRules() {
      var rules = {
        name: [
          { required: true, message: '请输入管控点名称', trigger: 'blur' }
        ]
      }

      // 当状态为已完成时，附件为必填
      if (this.controlPointForm.status === 3) {
        rules.attachment_url = [
          {
            required: true,
            validator: (_, value, callback) => {
              if (!value || value.trim() === '') {
                callback(new Error('已完成状态下附件为必传'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ]
      } else {
        rules = {
          name: [
            { required: true, message: '请输入管控点名称', trigger: 'blur' }
          ]
        }
      }

      return rules
    }
  },
  mounted() {
    // 点击其他地方隐藏右键菜单
    document.addEventListener('click', this.hideContextMenu)
    // 初始化加载数据
    this.initData()
  },
  beforeDestroy() {
    document.removeEventListener('click', this.hideContextMenu)
  },
  methods: {
    async closeDrawer() {
      // this.controlPointDrawerVisible = false
      // const projectId = this.$route.query.projectId || 1
      // await this.loadCatalogTree(projectId)
    },
    handleStatusChange() {
      // 当状态改变时，重新验证附件字段
      this.$nextTick(() => {
        setTimeout(() => {
          if (this.$refs.controlPointForm) {
            this.$refs.controlPointForm.validateField('attachment_url')
          }
        }, 1000);
      })
    },
    // 初始化数据
    async initData() {
      // 这里需要传入项目ID，暂时使用固定值，实际应该从路由参数或props获取
      const projectId = this.$route.query.projectId || 1
      await this.loadCatalogTree(projectId)
    },

    // 加载目录树
    async loadCatalogTree(projectId) {
      try {
        const response = await getCatalogTree(projectId)
        // 处理数据，为目录添加节点数量显示，为点位添加进度显示
        this.treeData = this.processTreeData(response.treeList || [])
        this.projectProgress = response.progress || 0
      } catch (error) {
        console.error('加载目录树失败:', error)
      }
    },

    // 处理树形数据，为目录添加节点数量显示，为点位添加进度显示
    processTreeData(treeData) {
      const processNode = (node) => {
        // 创建节点副本，避免修改原始数据
        const processedNode = { ...node }

        // 保留原始名称，用于编辑时使用
        processedNode.originalName = processedNode.name

        if (processedNode.children && Array.isArray(processedNode.children)) {
          // 递归处理子节点
          processedNode.children = processedNode.children.map(child => processNode(child))

          // 如果当前节点是目录类型，统计其下的节点数量
          if (processedNode.type === 'catalog') {
            const nodeCount = this.countNodesInCatalog(processedNode)
            if (nodeCount > 0) {
              // 在显示名称后添加节点数量显示，保持原始名称不变
              processedNode.displayName = `${processedNode.name} (${nodeCount})`
            } else {
              processedNode.displayName = processedNode.name
            }
          }
        }

        // 如果当前节点是点位类型，添加进度显示
        if (processedNode.type === 'point') {
          const progress = processedNode.progress || 0
          // 确保进度值是数字并格式化为百分比
          const progressPercent = typeof progress === 'number' ? progress : parseFloat(progress) || 0
          // 使用新的displayName字段显示进度，保持原始name不变
          processedNode.displayName = `${processedNode.name} （进度：${progressPercent}%）`
        }

        // 如果没有设置displayName，则使用原始name
        if (!processedNode.displayName) {
          processedNode.displayName = processedNode.name
        }

        return processedNode
      }

      return treeData.map(node => processNode(node))
    },

    // 统计目录直接子级中的节点数量（不包括更深层级）
    countNodesInCatalog(catalogNode) {
      if (!catalogNode.children || !Array.isArray(catalogNode.children)) {
        return 0
      }

      // 只统计直接子级中的节点数量
      let count = 0
      catalogNode.children.forEach(child => {
        // 统计节点类型的数量（point、milestone等非catalog类型）
        if (child.type && child.type !== 'catalog') {
          count++
        }
      })

      return count
    },

    // 加载里程碑列表
    async loadMilestones() {
      if (!this.selectedPoint) return

      try {
        const params = {
          pointId: this.selectedPoint.id,
          name: this.milestoneSearchForm.name,
          page: this.milestonePage.page - 1, // 后端页码从0开始
          size: this.milestonePage.size
        }

        const response = await getMilestoneList(params)
        this.milestones = response.content || []
        this.milestonePage.total = response.totalElements || 0
      } catch (error) {
        console.error('加载里程碑列表失败:', error)
      }
    },

    // 加载管控点列表
    async loadControlPoints() {
      if (!this.currentMilestone) return

      try {
        const params = {
          milestoneId: this.currentMilestone.id
        }

        const response = await getStepList(params)
        this.controlPoints = response || []
      } catch (error) {
        console.error('加载管控点列表失败:', error)
      }
    },
    // 搜索处理
    handleSearch() {
      this.$refs.directoryTree.filter(this.searchText)
    },

    // 树形过滤方法
    filterNode(value, data) {
      if (!value) return true
      // 使用原始名称进行搜索，避免搜索到进度信息
      const searchName = data.originalName || data.name
      return searchName.indexOf(value) !== -1
    },

    // 处理节点展开
    handleNodeExpand(data) {
      if (!this.expandedKeys.includes(data.id)) {
        this.expandedKeys.push(data.id)
      }
    },

    // 处理节点折叠
    handleNodeCollapse(data) {
      const index = this.expandedKeys.indexOf(data.id)
      if (index > -1) {
        this.expandedKeys.splice(index, 1)
      }
    },

    // 递归查找节点
    findNodeById(id, nodes) {
      for (const node of nodes) {
        if (node.id === id) {
          return node
        }
        if (node.children && node.children.length > 0) {
          const found = this.findNodeById(id, node.children)
          if (found) return found
        }
      }
      return null
    },

    // 显示添加目录对话框
    showAddDirectoryDialog() {
      this.dialogTitle = '添加目录'
      this.showParentSelect = false
      this.isEditMode = false
      this.editingId = null
      this.directoryDialogVisible = true
    },

    // 处理节点右键菜单
    handleNodeContextMenu(event, data, node) {
      event.preventDefault()
      this.currentNode = { data, node }
      this.contextMenuType = data.type
      this.contextMenuStyle.left = event.clientX + 'px'
      this.contextMenuStyle.top = event.clientY + 'px'
      this.contextMenuVisible = true
    },

    // 隐藏右键菜单
    hideContextMenu() {
      this.contextMenuVisible = false
    },

    // 获取目录选项（递归）
    getDirectoryOptions(nodes, options, level = 0) {
      nodes.forEach(node => {
        if (node.type === 'catalog') {
          options.push({
            id: node.id,
            name: '  '.repeat(level) + (node.originalName || node.name) // 使用原始名称，避免包含节点数量信息
          })
          if (node.children && node.children.length > 0) {
            this.getDirectoryOptions(node.children.filter(child => child.type === 'catalog'), options, level + 1)
          }
        }
      })
    },

    // 添加子目录
    handleAddSubDirectory() {
      this.dialogTitle = '添加子目录'
      this.showParentSelect = true
      this.isEditMode = false
      this.editingId = null
      this.directoryForm.parentId = this.currentNode.data.id
      this.directoryDialogVisible = true
      this.hideContextMenu()
    },

    // 添加点位
    handleAddPoint() {
      this.pointDialogTitle = '添加点位'
      this.isEditMode = false
      this.editingId = null
      this.pointForm.parentId = this.currentNode.data.id
      this.pointDialogVisible = true
      this.hideContextMenu()
    },

    // 编辑
    handleEdit() {
      const { data } = this.currentNode
      this.isEditMode = true
      this.editingId = data.id

      if (data.type === 'catalog') {
        this.dialogTitle = '修改目录'
        this.showParentSelect = !!data.parentId
        this.directoryForm = {
          name: data.originalName || data.name, // 使用原始名称，避免包含节点数量信息
          sort: data.sort,
          parentId: data.parentId || undefined
        }
        this.directoryDialogVisible = true
      } else {
        this.pointDialogTitle = '修改点位'
        this.pointForm = {
          name: data.originalName || data.name, // 使用原始名称，避免包含进度信息
          lon: data.lon,
          lat: data.lat,
          sort: data.sort,
          parentId: data.parentId || undefined
        }
        this.pointDialogVisible = true
      }
      this.hideContextMenu()
    },

    // 删除
    handleDelete() {
      const { data } = this.currentNode
      const itemType = data.type === 'catalog' ? '目录' : '点位'

      this.$confirm(`确定要删除该${itemType}吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          // 检查是否删除的是当前选中的点位
          const isCurrentSelectedPoint = data.type === 'point' && this.selectedPoint && this.selectedPoint.id === data.id

          if (data.type === 'catalog') {
            await delCatalog([data.id])
          } else {
            await delPoint([data.id])
          }

          // 如果删除的是当前选中的点位，需要重置右侧里程碑相关数据
          if (isCurrentSelectedPoint) {
            this.selectedPoint = null
            this.milestones = []
            this.selectedMilestones = []
            this.milestoneSearchForm.name = ''
            // 如果管控点抽屉是打开的，也需要关闭
            this.controlPointDrawerVisible = false
            this.currentMilestone = null
            this.controlPoints = []
          }

          // 重新加载目录树
          const projectId = this.$route.query.projectId || 1
          await this.loadCatalogTree(projectId)
          this.$message.success('删除成功')
        } catch (error) {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        }
      }).catch(() => {
        // 取消删除
      })
      this.hideContextMenu()
    },

    // 递归删除节点
    deleteNode(id, nodes = this.treeData) {
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].id === id) {
          nodes.splice(i, 1)
          return true
        }
        if (nodes[i].children && nodes[i].children.length > 0) {
          if (this.deleteNode(id, nodes[i].children)) {
            return true
          }
        }
      }
      return false
    },

    // 目录表单提交
    handleDirectorySubmit() {
      this.$refs.directoryForm.validate((valid) => {
        if (valid) {
          if (this.isEditMode) {
            this.updateDirectory()
          } else {
            this.addDirectory()
          }
        }
      })
    },

    // 添加目录
    async addDirectory() {
      try {
        const params = {
          name: this.directoryForm.name,
          sort: this.directoryForm.sort,
          parentId: this.directoryForm.parentId || undefined,
          pmId: this.$route.query.projectId
        }

        await addCatalog(params)
        const projectId = this.$route.query.projectId || 1
        await this.loadCatalogTree(projectId)
        this.directoryDialogVisible = false
        this.$message.success('添加成功')
      } catch (error) {
        console.error('添加目录失败:', error)
        this.$message.error('添加失败')
      }
    },

    // 更新目录
    async updateDirectory() {
      try {
        const params = {
          id: this.editingId,
          name: this.directoryForm.name,
          sort: this.directoryForm.sort,
          parentId: this.directoryForm.parentId,
          pmId: this.$route.query.projectId
        }

        await editCatalog(params)
        const projectId = this.$route.query.projectId || 1
        await this.loadCatalogTree(projectId)
        this.directoryDialogVisible = false
        this.$message.success('修改成功')
      } catch (error) {
        console.error('修改目录失败:', error)
        this.$message.error('修改失败')
      }
    },

    // 点位表单提交
    handlePointSubmit() {
      this.$refs.pointForm.validate((valid) => {
        if (valid) {
          if (this.isEditMode) {
            this.updatePoint()
          } else {
            this.addPoint()
          }
        }
      })
    },

    // 添加点位
    async addPoint() {
      try {
        const params = {
          name: this.pointForm.name,
          sort: this.pointForm.sort,
          catalogId: this.pointForm.parentId,
          pmId: this.$route.query.projectId,
          lon: this.pointForm.lon,
          lat: this.pointForm.lat
        }

        await addPoint(params)
        const projectId = this.$route.query.projectId || 1
        await this.loadCatalogTree(projectId)
        this.pointDialogVisible = false
        this.$message.success('添加成功')
      } catch (error) {
        console.error('添加点位失败:', error)
        this.$message.error('添加失败')
      }
    },

    // 更新点位
    async updatePoint() {
      try {
        const params = {
          id: this.editingId,
          name: this.pointForm.name,
          lon: this.pointForm.lon,
          lat: this.pointForm.lat,
          sort: this.pointForm.sort,
          catalogId: this.pointForm.parentId,
          pmId: this.$route.query.projectId
        }

        await editPoint(params)

        // 重新加载目录树
        const projectId = this.$route.query.projectId || 1
        await this.loadCatalogTree(projectId)
        this.pointDialogVisible = false
        this.$message.success('修改成功')
      } catch (error) {
        console.error('修改点位失败:', error)
        this.$message.error('修改失败')
      }
    },

    // 添加到父节点
    addToParent(newNode, parentId, nodes = this.treeData) {
      for (const node of nodes) {
        if (node.id === parentId) {
          if (!node.children) {
            node.children = []
          }
          node.children.push(newNode)
          return true
        }
        if (node.children && node.children.length > 0) {
          if (this.addToParent(newNode, parentId, node.children)) {
            return true
          }
        }
      }
      return false
    },

    // 更新节点
    updateNode(id, updates, nodes = this.treeData) {
      for (const node of nodes) {
        if (node.id === id) {
          Object.assign(node, updates)
          return true
        }
        if (node.children && node.children.length > 0) {
          if (this.updateNode(id, updates, node.children)) {
            return true
          }
        }
      }
      return false
    },

    // 排序节点
    sortNodes(nodes = this.treeData) {
      nodes.sort((a, b) => a.sort - b.sort)
      nodes.forEach(node => {
        if (node.children && node.children.length > 0) {
          this.sortNodes(node.children)
        }
      })
    },

    // 重置目录表单
    resetDirectoryForm() {
      this.directoryForm = {
        name: '',
        sort: 0,
        parentId: undefined
      }
      this.isEditMode = false
      this.editingId = null
      this.dialogTitle = '添加目录'
      this.showParentSelect = false
      this.$refs.directoryForm && this.$refs.directoryForm.resetFields()
    },

    // 重置点位表单
    resetPointForm() {
      this.pointForm = {
        name: '',
        lon: '',
        lat: '',
        sort: 0,
        parentId: undefined
      }
      this.isEditMode = false
      this.editingId = null
      this.pointDialogTitle = '添加点位'
      this.$refs.pointForm && this.$refs.pointForm.resetFields()
    },

    // 处理节点点击（选择点位）
    async handleNodeClick(data) {
      if (data.type === 'point') {
        this.selectedPoint = data
        // 清空之前的选择
        this.selectedMilestones = []
        // 重置分页到第一页
        this.milestonePage.page = 1
        // 加载该点位的里程碑列表
        await this.loadMilestones()
      }
    },

    // 里程碑搜索
    async searchMilestones() {
      if (!this.selectedPoint) {
        this.$message.warning('请先选择一个点位')
        return
      }
      // 搜索时重置到第一页
      this.milestonePage.page = 1
      await this.loadMilestones()
    },

    // 重置里程碑搜索
    async resetMilestoneSearch() {
      this.milestoneSearchForm.name = ''
      // 重置时也重置到第一页
      this.milestonePage.page = 1
      await this.loadMilestones()
    },

    // 里程碑分页处理
    handleMilestonePagination(pagination) {
      this.milestonePage.page = pagination.page
      this.milestonePage.size = pagination.limit
      this.loadMilestones()
    },

    // 显示添加里程碑对话框
    showAddMilestoneDialog() {
      if (!this.selectedPoint) {
        this.$message.warning('请先选择一个点位')
        return
      }

      this.milestoneDialogTitle = '添加里程碑'
      this.isMilestoneEditMode = false

      // 重置表单并设置点位信息
      this.resetMilestoneForm()
      this.milestoneForm.pointName = this.selectedPoint.originalName || this.selectedPoint.name
      this.milestoneForm.pointId = this.selectedPoint.id

      this.milestoneDialogVisible = true
    },

    // 里程碑选择变化
    handleMilestoneSelectionChange(selection) {
      this.selectedMilestones = selection
    },

    // 批量删除里程碑
    batchDeleteMilestones() {
      if (this.selectedMilestones.length === 0) {
        this.$message.warning('请选择要删除的里程碑')
        return
      }

      this.$confirm(`确定要删除选中的 ${this.selectedMilestones.length} 个里程碑吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          const list = []
          this.selectedMilestones.forEach(item => {
            list.push(item.id)
          })
          await delMilestone(list)
          // 重新加载里程碑列表
          await this.loadMilestones()
          this.selectedMilestones = []
          this.$message.success('删除成功')
        } catch (error) {
          console.error('批量删除里程碑失败:', error)
          this.$message.error('删除失败')
        }
      }).catch(() => {
        // 取消删除
      })
    },

    // 格式化日期范围
    formatDateRange(startDate, endDate) {
      // 格式化单个日期，去掉时分秒
      const formatDate = (date) => {
        if (!date) return null
        // 如果日期包含时分秒，只取日期部分
        if (typeof date === 'string' && date.includes(' ')) {
          return date.split(' ')[0]
        }
        return date
      }

      const formattedStartDate = formatDate(startDate)
      const formattedEndDate = formatDate(endDate)

      if (!formattedStartDate && !formattedEndDate) return '-'
      if (!formattedStartDate) return `- ~ ${formattedEndDate}`
      if (!formattedEndDate) return `${formattedStartDate} ~ -`
      return `${formattedStartDate} ~ ${formattedEndDate}`
    },

    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        1: 'info',
        2: 'warning',
        3: 'success'
      }
      return statusMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        1: '未开始',
        2: '进行中',
        3: '已完成'
      }
      return statusMap[status] || '未知'
    },

    // 获取管控点状态类型
    getControlPointStatusType(status) {
      const statusMap = {
        1: 'info',
        2: 'warning',
        3: 'success'
      }
      return statusMap[status] || 'info'
    },

    // 获取管控点状态文本
    getControlPointStatusText(status) {
      const statusMap = {
        1: '未开始',
        2: '进行中',
        3: '已完成'
      }
      return statusMap[status] || '未知'
    },

    // 编辑里程碑
    editMilestone(milestone) {
      this.milestoneDialogTitle = '修改里程碑'
      this.isMilestoneEditMode = true
      this.editingMilestoneId = milestone.id

      // 处理日期范围，确保不会有undefined值
      const planStartDate = milestone.planStartDate || null
      const planEndDate = milestone.planEndDate || null
      const planDateRange = (planStartDate && planEndDate) ? [planStartDate, planEndDate] : []

      this.milestoneForm = {
        pointName: this.selectedPoint.originalName || this.selectedPoint.name,
        pointId: this.selectedPoint.id,
        name: milestone.name,
        planDateRange: planDateRange,
        realStartDate: milestone.realStartDate || '',
        realEndDate: milestone.realEndDate || '',
        status: milestone.status || 1,
        remark: milestone.remark || '',
        weight: milestone.weight || 0
      }

      this.milestoneDialogVisible = true
    },

    // 删除里程碑
    deleteMilestone(milestone) {
      this.$confirm('确定要删除该里程碑吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          await delMilestone({ id: milestone.id })

          // 重新加载里程碑列表
          await this.loadMilestones()

          const projectId = this.$route.query.projectId || 1
          await this.loadCatalogTree(projectId)
          this.$message.success('删除成功')
        } catch (error) {
          console.error('删除里程碑失败:', error)
          this.$message.error('删除失败')
        }
      }).catch(() => {
        // 取消删除
      })
    },

    // 里程碑表单提交
    handleMilestoneSubmit() {
      this.$refs.milestoneForm.validate((valid) => {
        if (valid) {
          if (this.isMilestoneEditMode) {
            this.updateMilestone()
          } else {
            this.addMilestone()
          }
        }
      })
    },

    // 添加里程碑
    async addMilestone() {
      try {
        const params = {
          pointId: this.milestoneForm.pointId,
          catalogId: this.selectedPoint.parentId,
          name: this.milestoneForm.name,
          planStartDate: (this.milestoneForm.planDateRange && this.milestoneForm.planDateRange[0]) || '',
          planEndDate: (this.milestoneForm.planDateRange && this.milestoneForm.planDateRange[1]) || '',
          realStartDate: this.milestoneForm.realStartDate || '',
          realEndDate: this.milestoneForm.realEndDate || '',
          status: this.milestoneForm.status || 1,
          remark: this.milestoneForm.remark || '',
          weight: this.milestoneForm.weight || 0,
          pmId: this.$route.query.projectId
        }

        await addMilestone(params)
        // 重新加载里程碑列表
        await this.loadMilestones()
        const projectId = this.$route.query.projectId || 1
        await this.loadCatalogTree(projectId)
        this.milestoneDialogVisible = false
        this.$message.success('添加成功')
      } catch (error) {
        console.error('添加里程碑失败:', error)
        this.$message.error('添加失败')
      }
    },

    // 更新里程碑
    async updateMilestone() {
      try {
        const params = {
          id: this.editingMilestoneId,
          catalogId: this.selectedPoint.parentId,
          pointId: this.milestoneForm.pointId,
          name: this.milestoneForm.name,
          planStartDate: (this.milestoneForm.planDateRange && this.milestoneForm.planDateRange[0]) || '',
          planEndDate: (this.milestoneForm.planDateRange && this.milestoneForm.planDateRange[1]) || '',
          realStartDate: this.milestoneForm.realStartDate || '',
          realEndDate: this.milestoneForm.realEndDate || '',
          status: this.milestoneForm.status || 1,
          remark: this.milestoneForm.remark || '',
          weight: this.milestoneForm.weight || 0,
          pmId: this.$route.query.projectId
        }

        await editMilestone(params)

        // 重新加载里程碑列表
        await this.loadMilestones()
        const projectId = this.$route.query.projectId || 1
        await this.loadCatalogTree(projectId)
        this.milestoneDialogVisible = false
        this.$message.success('修改成功')
      } catch (error) {
        console.error('修改里程碑失败:', error)
        this.$message.error('修改失败')
      }
    },

    // 计算进度
    calculateProgress(status) {
      const progressMap = {
        1: 0,
        2: 50,
        3: 100
      }
      return progressMap[status] || 0
    },

    // 重置里程碑表单
    resetMilestoneForm() {
      this.milestoneForm = {
        pointName: '',
        pointId: null,
        name: '',
        planDateRange: [],
        realStartDate: '',
        realEndDate: '',
        status: 1,
        remark: '',
        weight: 0
      }
      this.$refs.milestoneForm && this.$refs.milestoneForm.resetFields()
    },

    // 打开管控点抽屉
    async openControlPointDrawer(milestone) {
      this.currentMilestone = milestone
      this.controlPointDrawerVisible = true
      // 加载该里程碑的管控点列表
      await this.loadControlPoints()
    },

    // 显示添加管控点对话框
    showAddControlPointDialog() {
      this.controlPointDialogTitle = '添加管控点'
      this.isControlPointEditMode = false
      // 重置表单和文件列表
      this.resetControlPointForm()
      this.controlPointDialogVisible = true
    },

    // 编辑管控点
    editControlPoint(controlPoint) {
      this.controlPointDialogTitle = '修改管控点'
      this.isControlPointEditMode = true
      this.editingControlPointId = controlPoint.id

      this.controlPointForm = {
        name: controlPoint.name,
        status: controlPoint.status || 1, // 如果没有状态，默认为未开始
        remark: controlPoint.remark,
        weight: controlPoint.weight,
        attachment_url: controlPoint.attachmentUrl || '' // 加载已有的附件URL
      }

      // 设置文件列表，如果有附件URL则创建文件对象
      if (controlPoint.attachmentUrl) {
        // 从URL中提取文件名
        const fileName = controlPoint.attachmentUrl.split('/').pop() || '附件'
        this.fileList = [{
          name: fileName,
          url: controlPoint.attachmentUrl,
          uid: Date.now(), // 生成唯一ID
          status: 'success'
        }]
      } else {
        this.fileList = []
      }

      console.log(this.controlPointForm);

      this.controlPointDialogVisible = true
    },

    // 删除管控点
    deleteControlPoint(controlPoint) {
      this.$confirm('确定要删除该管控点吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          await delStep({ id: controlPoint.id })
          await this.loadControlPoints()
          await this.loadMilestones()
          this.$message.success('删除成功')
        } catch (error) {
          console.error('删除管控点失败:', error)
          this.$message.error('删除失败')
        }
      }).catch(() => {
        // 取消删除
      })
    },

    // 管控点表单提交
    handleControlPointSubmit() {
      this.$refs.controlPointForm.validate((valid) => {
        if (valid) {
          if (this.isControlPointEditMode) {
            this.updateControlPoint()
          } else {
            this.addControlPoint()
          }
        }
      })
    },

    // 添加管控点
    async addControlPoint() {
      try {
        const params = {
          milestoneId: this.currentMilestone.id,
          catalogId: this.selectedPoint.parentId,
          pointId: this.selectedPoint.id,
          name: this.controlPointForm.name,
          status: this.controlPointForm.status,
          remark: this.controlPointForm.remark,
          weight: this.controlPointForm.weight,
          pmId: this.$route.query.projectId,
          attachment_url: this.controlPointForm.attachment_url // 将附件列表转为JSON字符串
        }

        await addStep(params)

        // 重新加载管控点列表
        await this.loadControlPoints()
        await this.loadMilestones()
        this.controlPointDialogVisible = false
        this.$message.success('添加成功')
      } catch (error) {
        console.error('添加管控点失败:', error)
        this.$message.error('添加失败')
      }
    },

    // 更新管控点
    async updateControlPoint() {
      try {
        const params = {
          id: this.editingControlPointId,
          milestoneId: this.currentMilestone.id,
          catalogId: this.selectedPoint.parentId,
          pointId: this.selectedPoint.id,
          name: this.controlPointForm.name,
          status: this.controlPointForm.status,
          remark: this.controlPointForm.remark,
          weight: this.controlPointForm.weight,
          pmId: this.$route.query.projectId,
          attachment_url: this.controlPointForm.attachment_url// 将附件列表转为JSON字符串
        }

        await editStep(params)

        // 重新加载管控点列表
        await this.loadControlPoints()
        await this.loadMilestones()
        this.controlPointDialogVisible = false
        this.$message.success('修改成功')
      } catch (error) {
        console.error('修改管控点失败:', error)
        this.$message.error('修改失败')
      }
    },

    // 重置管控点表单
    resetControlPointForm() {
      this.controlPointForm = {
        name: '',
        status: 1, // 默认未开始
        remark: '',
        weight: 0,
        attachment_url: ''
      }
      // 重置文件列表
      this.fileList = []
      this.$refs.controlPointForm && this.$refs.controlPointForm.resetFields()
      // 清空附件上传组件
      this.$refs.attachmentUpload && this.$refs.attachmentUpload.clearFiles()
    },

    // 附件上传成功回调
    handleAttachmentSuccess(response, file) {
      if (response && response.url) {
        // 更新表单中的附件URL
        this.controlPointForm.attachment_url = response.url;

        // 更新文件列表
        const updatedFile = {
          name: file.name,
          url: response.url,
          uid: file.uid,
          status: 'success'
        }

        // 更新fileList中对应的文件
        const index = this.fileList.findIndex(item => item.uid === file.uid)
        if (index !== -1) {
          this.$set(this.fileList, index, updatedFile)
        } else {
          this.fileList.push(updatedFile)
        }

        this.$message.success('附件上传成功')
      } else {
        this.$message.error('附件上传失败')
      }
    },

    // 附件移除回调
    handleAttachmentRemove(file) {
      // 清空表单中的附件URL
      this.controlPointForm.attachment_url = ''

      // 从文件列表中移除对应的附件
      const index = this.fileList.findIndex(item => item.uid === file.uid)
      if (index !== -1) {
        this.fileList.splice(index, 1)
      }
    },

    // 附件数量超出限制回调
    handleAttachmentExceed() {
      this.$message.warning('最多只能上传1个附件')
    },

    // 获取管控点的附件数量
    getAttachmentCount(controlPoint) {
      if (!controlPoint.attachments) return 0

      try {
        // 如果attachments是字符串，尝试解析为JSON
        if (typeof controlPoint.attachments === 'string') {
          const attachments = JSON.parse(controlPoint.attachments)
          return Array.isArray(attachments) ? attachments.length : 0
        }
        // 如果attachments已经是数组
        if (Array.isArray(controlPoint.attachments)) {
          return controlPoint.attachments.length
        }
        return 0
      } catch (error) {
        console.error('解析附件数据失败:', error)
        return 0
      }
    },

    // 图片预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },

    // 移除图片
    handleRemove(file, fileList) {
      console.log('移除图片:', file, fileList)
    },

    // 上传前检查
    beforeUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG/PNG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
      }
      return isJPG && isLt2M
    },

    // 显示导入对话框（直接触发文件选择）
    showImportDialog() {
      this.$refs.importFileInput.click()
    },

    // 处理文件选择
    async handleFileSelect(event) {
      const file = event.target.files[0]
      if (!file) return

      // 验证文件类型和大小
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.type === 'application/vnd.ms-excel'
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isExcel) {
        this.$message.error('上传文件只能是 Excel 格式!')
        this.$refs.importFileInput.value = '' // 清空选择
        return
      }
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!')
        this.$refs.importFileInput.value = '' // 清空选择
        return
      }

      // 创建FormData
      const formData = new FormData()
      formData.append('file', file)

      // 添加项目ID
      const pmId = this.$route.query.projectId || 1
      formData.append('pmId', pmId)

      // 显示加载中
      const loading = this.$loading({
        lock: true,
        text: '正在导入...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      try {
        await importPmProgress(formData)

        loading.close()

        this.$message.success('导入成功')
        // 重新加载数据
        await this.loadCatalogTree(pmId)
      } catch (error) {
        loading.close()
        console.error('导入失败:', error)
        this.$message.error('导入失败')
      }

      // 清空文件选择，以便下次选择同一文件时也能触发change事件
      this.$refs.importFileInput.value = ''
    }

  }
}
</script>

<style lang="scss" scoped>
.progress-control-container {
  padding: 20px;
  height: calc(100vh - 200px);

  .left-panel {
    height: 100%;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 16px;
    background: #fff;

    .search-section {
      margin-bottom: 16px;
    }

    .header-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #e4e7ed;

      .project-name {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }

      .header-buttons {
        display: flex;
        gap: 8px;
      }
    }

    .tree-section {
      height: calc(100% - 120px);
      overflow-y: auto;

      .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        padding-right: 8px;

        .node-label {
          display: flex;
          align-items: center;

          .node-icon {
            margin-right: 6px;
            color: #606266;
          }
        }
      }
    }
  }

  .right-panel {
    height: 100%;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 16px;
    background: #fff;

    .no-selection {
      text-align: center;
      padding: 60px 20px;
      color: #666;

      i {
        font-size: 48px;
        color: #c0c4cc;
        margin-bottom: 16px;
      }

      p {
        font-size: 14px;
        color: #999;
      }
    }

    .milestone-management {
      height: 100%;
      display: flex;
      flex-direction: column;

      .search-area {
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid #e4e7ed;

        .search-form {
          margin: 0;

          .el-form-item {
            margin-bottom: 0;
            margin-right: 16px;
          }
        }
      }

      .milestone-title {
        margin-bottom: 16px;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 500;
          color: #303133;
        }
      }

      .milestone-table {
        flex: 1;
        overflow: hidden;

        .el-table {
          height: 100%;
        }
      }
    }
  }
}

// 右键菜单样式
.context-menu {
  position: fixed;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 4px 0;
  margin: 0;
  list-style: none;
  z-index: 9999;
  min-width: 120px;

  li {
    padding: 8px 16px;
    font-size: 14px;
    color: #606266;
    cursor: pointer;
    display: flex;
    align-items: center;

    i {
      margin-right: 8px;
      font-size: 16px;
    }

    &:hover {
      background-color: #f5f7fa;
      color: #409eff;
    }
  }
}

// 表单提示样式
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

// 对话框样式调整
::v-deep .el-dialog__body {
  padding: 20px 20px 10px 20px;
}

::v-deep .el-form-item__label {
  font-weight: 500;
}

// 树形控件样式调整
::v-deep .el-tree-node__content {
  height: 32px;

  &:hover {
    background-color: #f5f7fa;
  }
}

::v-deep .el-tree-node__expand-icon.expanded {
  transform: rotate(90deg);
}

// 抽屉样式
.drawer-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;

  .drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e4e7ed;

    .drawer-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }

  .control-point-list {
    flex: 1;
    overflow: hidden;

    .el-table {
      height: 100%;
    }
  }

  .drawer-actions {
    .el-button {
      margin-left: 8px;
    }
  }

  .photo-upload-section {
    padding: 16px 0;

    .el-upload--picture-card {
      width: 80px;
      height: 80px;
      line-height: 80px;
    }

    .el-upload-list--picture-card .el-upload-list__item {
      width: 80px;
      height: 80px;
    }
  }
}

// 抽屉覆盖样式
::v-deep .el-drawer__body {
  padding: 0;
}

// 表格样式调整
::v-deep .el-table th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 500;
}

::v-deep .el-table .el-table__row:hover>td {
  background-color: #f5f7fa;
}

// 进度条样式调整
::v-deep .el-progress-bar__outer {
  border-radius: 4px;
}

::v-deep .el-progress-bar__inner {
  border-radius: 4px;
}

// 标签样式调整
.el-tag {
  border-radius: 4px;
}

// 上传提示样式
.upload-tip {
  margin-top: 8px;
  font-size: 12px;
}

// 里程碑分页器样式
.milestone-pagination {
  margin-top: 20px;
  text-align: right;
}

::v-deep .milestone-pagination .el-pagination {
  display: inline-block;
}
</style>
