<template>
  <div>
    <el-dialog append-to-body :close-on-click-modal="false" :before-close="handleClose" :visible.sync="matchVisible" title="匹配" width="800px">
      <el-form size="small" label-width="90px">
        <el-table :data="tableData" size="small" style="width: 100%;margin-bottom: 15px">
          <el-table-column prop="title" label="未匹配名称" align="center" />
          <el-table-column label="匹配系统名称" align="center">
            <template slot-scope="scope">
              <el-select v-model="tableData[scope.$index].newTitle" style="width: 200px" filterable class="edit-input" clearable size="mini" placeholder="请选择" @change="fieldChange(scope.$index)">
                <el-option
                  v-for="item in aliasList"
                  :key="item.id"
                  :label="item.title"
                  :value="item.title+'_'+item.assetId"
                />
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="handleClose">取消</el-button>
        <el-button :loading="loading" type="primary" @click="matchSubmit">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import alias from '@/api/parts/alias'
import { updateAssetManualId } from '@/api/parts/fault'
import { getConfig } from '@/utils/getConfigData';
export default {
  name: 'Match',
  data() {
    return {
      matchVisible: false,
      tableData: [],
      aliasList: [],
      loading: false,
      aliasBindId: '',
      page: 0
    }
  },
  created() {
    this.getAliasList();
    this.getConfigInfo();
  },
  methods: {
    init(data) {
      this.matchVisible = true;
      this.tableData = data.map(item => {
        item.newTitle = '';
        return item;
      })
    },
    handleClose() {
      this.matchVisible = false;
    },
    getAliasList() {
      const page = this.page++; const size = 200;
      alias.get({ status: '已匹配', enabled: 1, size, page }).then(res => {
        this.aliasList = this.aliasList.concat(res.content);
        if (res.totalElements == this.aliasList.length) {
          return
        } else {
          this.getAliasList();
        }
      });
    },
    getConfigInfo() {
      getConfig({ key: 'alias_list' }).then(res => {
        this.aliasBindId = res.extend.data.bindId;// 别名列表绑定id
      });
    },
    fieldChange(index) {
      this.$set(this.tableData, index, this.tableData[index]);
    },
    matchSubmit() {
      const arr = this.tableData.map(item => {
        return {
          asset: {
            id: item.newTitle.split('_')[1]
          },
          title: item.newTitle.split('_')[0],
          description: item.title,
          id: item.id,
          bindId: this.aliasBindId
        }
      });
      updateAssetManualId(arr).then(res => {
        this.$notify({
          title: '匹配成功',
          type: 'success',
          duration: 2500
        });
        setTimeout(() => {
          this.$emit('getlist');
          this.handleClose();
        });
      });
    }
  }
}
</script>

<style>

</style>
