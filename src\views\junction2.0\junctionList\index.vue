<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input
          v-model="query.blur"
          class="filter-item"
          clearable
          placeholder="输入道路名称或者编号搜索"
          size="small"
          style="width: 300px;"
        />
        <!-- <el-select
v-model="junctionNum"
clearable
size="small"
placeholder="请选择查询条件"
class="filter-item"
style="width: 200px"
>
<el-option
v-for="item in dict.junction_num"
:key="item.id"
:label="item.label"
:value="item.value"
/>
</el-select>
<el-input v-model="query[junctionNum]" clearable size="small" placeholder="输入编号搜索" style="width: 200px;" class="filter-item" /> -->
        <!-- <date-range-picker v-model="query.createTime" class="date-item" /> -->
        <rrOperation>
          <!-- <el-button
slot="right"
class="filter-item"
type="primary"
icon="el-icon-eleme"
size="mini"
@click="preView"
>
预览
</el-button>
<el-button
slot="right"
class="filter-item"
type="primary"
icon="el-icon-download"
size="mini"
@click="downLoad"
>
导出
</el-button> -->
        </rrOperation>
      </div>
      <crudOperation :permission="permission">
        <el-button
          slot="left"
          v-permission="permission.add"
          class="filter-item"
          icon="el-icon-plus"
          size="mini"
          type="primary"
          @click="addTable"
        >
          新建
        </el-button>
        <el-button
          slot="left"
          v-permission="permission.upload"
          class="filter-item"
          icon="el-icon-plus"
          size="mini"
          type="success"
          @click="addFile"
        >上传
        </el-button>
        <update-button
          v-if="bindId"
          slot="left"
          :bind-id="bindId"
          :enabled="[1]"
          :permission="permission"
        />
      </crudOperation>
    </div>
    <el-table
      ref="table"
      v-loading="crud.loading"
      :data="tableData"
      row-key="id"
      @select="crud.selectChange"
      @select-all="crud.selectAllChange"
      @selection-change="crud.selectionChangeHandler"
    >
      <el-table-column
        v-for="item in tableHeader"
        :key="item.prop"
        :align="item.align || 'center'"
        :fixed="item.fixed || false"
        :label="item.label"
        :prop="item.prop"
        :show-overflow-tooltip="true"
        :width="item.width || '180'"
      >
        <template slot-scope="scope">
          <template v-if="item.prop.indexOf('img') !='-1'">
            <template v-for="(item,index) in scope.row[item.prop]">
              <el-image
                v-if="index == 0"
                :key="item.id"
                :preview-src-list="[item.url]"
                :src="item.url"
                class="el-avatar"
                fit="contain"
                lazy
              >
                <div slot="error">
                  <i class="el-icon-document" />
                </div>
              </el-image>
            </template>

          </template>
          <template v-else-if="item.label =='项目信息'">
            <el-tag
              v-for="(project,i) in scope.row[item.prop]"
              :key="i"
              class="custom-tag"
              style="margin-right:8px;width: auto;cursor:pointer;"
              @click="goOtherViews(project,scope.row)"
            >
              {{ computedLable(project) }}<br>{{ project.code ? project.code : ' ' }}
            </el-tag>
          </template>
          <span v-else>{{ scope.row[item.prop] }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="180">
        <template slot-scope="scope">
          <!-- <el-button type="success" size="mini" @click="toInspects(scope.row)">巡检记录</el-button> -->
          <el-button
            v-permission="permission.edit"
            size="mini"
            style="margin-left:0;"
            type="primary"
            @click="editItem(scope.row)"
          >编辑
          </el-button>
          <el-popconfirm
            :hide-icon="true"
            cancel-button-text="取消"
            confirm-button-text="确认"
            icon-color="red"
            title="确认要删除该条数据？"
            @confirm="deleteItem(scope.row)"
          >
            <el-button slot="reference" v-permission="permission.del" size="mini" type="danger">删除</el-button>
          </el-popconfirm>
          <!-- <el-button type="success" size="mini" @click="detail(scope.row)">查看</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination />
    <uploadExcel ref="uploadExcel" @getlist="crud.refresh()" />
  </div>
</template>

<script>
import uploadExcel from './components/uploadExcel'
import crudTable from '@/api/parts/assets'
import CRUD, { presenter, header, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
// import { getConfig } from '@/utils/getConfigData.js'
// import DateRangePicker from '@/components/DateRangePicker'
import { downloadUrl } from '@/utils/index'
import updateButton from '@/components/UpdateButton/index'
import { mapGetters } from 'vuex'

export default {
  name: 'JunctionList2',
  components: { crudOperation, rrOperation, pagination, uploadExcel, updateButton },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: false,
      title: '资产管理',
      url: 'api/omAsset/small',
      query: { enabled: 1 },
      crudMethod: { ...crudTable },
      optShow: {
        add: false, edit: false, del: false, download: false, reset: true
      }
    })
  },
  mixins: [presenter(), header(), crud()],
  dicts: ['junction_num'],
  data() {
    return {
      nofistLoad: false,
      tableData: [],
      tableHeader: [],
      permission: {
        add: ['admin', 'omAsset:add'],
        edit: ['admin', 'omAsset:edit'],
        del: ['admin', 'omAsset:del'],
        upload: ['admin', 'omAsset:importXlsWithRule'],
        updateT: ['admin', 'omAsset:updateFormStruct'],
        updateR: ['admin', 'omAsset:updateRelation']
        // updateG: ['admin', 'omAsset:toRedisGeoIndex'],

      },
      props: {
        value: 'id',
        label: 'label',
        emitPath: false
      },
      bindId: '',
      inspectBindID: ''
    }
  },
  computed: {
    ...mapGetters([
      'baseApi',
      'omAssetAPi'
    ]),
    computedLable() {
      return (data) => {
        const str = '自动驾驶'
        if (data.label == str) {
          return data.name
        }
        return data.label
      }
    }
  },
  watch: {
    // junctionNum(newVal, oldVal) {
    // // 当 junctionNum 改变时，重置对应的 query 属性
    //   if (oldVal !== null && this.query[oldVal] !== undefined) {
    //     this.$set(this.query, oldVal, ''); // 清除旧属性值
    //   }
    //   if (newVal !== null && this.query[newVal] === undefined) {
    //     this.$set(this.query, newVal, ''); // 初始化新属性值
    //   }
    // },
    'crud.data': {
      handler(val, oldVal) {
        if (val && val.length && val.length > 0) {
          const newVal = val;
          const publicfilters = ['text_', 'divider_', 'editor_', 'subform'];
          const uniqueFilters = ['file_'];
          const publicLable = ['栅格布局']
          const uniqueLable = ['序号']
          const filterLable = [...publicLable, ...uniqueLable]
          const filterTableHeader = [...publicfilters, ...uniqueFilters]
          const tableHeader = newVal[0].extend.tableHeader.filter((item) => {
            return filterLable.every(subItem => item.label !== subItem) && filterTableHeader.every(subItem => item.prop.indexOf(subItem) == '-1')
          });
          // 定义表头属性的映射关系
          const headerProperties = {
            '道路名称': { width: '250', fixed: 'left' }
          };
          // 更新 tableHeader 中的属性
          tableHeader.forEach(item => {
            if (item.label in headerProperties) {
              Object.entries(headerProperties[item.label]).forEach(([property, value]) => {
                item[property] = value;
              });
            }
          });

          const additionalHeaders = [
            { prop: 'project', label: '项目信息', width: '350' },
            { prop: 'ftCode', label: '福通编号' }
          ];
          // this.tableHeader = [...tableHeader, ...additionalHeaders];

          tableHeader.splice(1, 0, ...additionalHeaders);// 将项目信息这一项放在第二位
          // 设置最终的 tableHeader
          this.tableHeader = tableHeader;

          this.tableData = newVal.map(item => {
            const projectData = item.item?.[0]?.extend?.data;
            const projectHeader = item.item?.[0]?.extend?.tableHeader;

            const fieldsToExtract = ['4', '5', '6', '7', '8'];
            const project = Array.isArray(projectHeader)
              ? projectHeader
                .filter(h => fieldsToExtract.includes(h.prop.toString()) && h.prop in projectData)
                .map(h => {
                  // 创建返回对象
                  const returnObj = {
                    label: h.label,
                    value: projectData[h.prop]
                  };

                  // 根据 label 找到对应的编号并添加到 code 属性中
                  const codeHeader = projectHeader.find(header =>
                    (header.prop.toString() === '2' && h.prop.toString() === '6') ||
													(header.prop.toString() === '3' && h.prop.toString() === '5') || (h.prop.toString() === '7' && h.prop.toString() === '8')
                  );

                  if (codeHeader && codeHeader.prop in projectData) {
                    returnObj.code = projectData[codeHeader.prop];
                    returnObj.name = projectData['7'];
                    returnObj.projectId = projectData['8'];
                  }
                  return returnObj;
                })
              : [];

            return {
              ...item.extend.data,
              project: project,
              ftCode: (typeof projectData === 'object' && projectData !== null && '1' in projectData) ? projectData['1'] : '',
              id: item.id,
              createBy: item.createBy || '',
              createTime: item.createTime || '',
              title: item.title || ''
            };
          });
        } else {
          this.tableHeader = [];
          this.tableData = [];
        }
      },
      deep: true
    }
  },
  created() {
    // this.getConfigData();
  },
  activated() {
    this.crud.toQuery();
    // if (this.nofistLoad) {
    //   this.crud.toQuery();
    // }
    // this.nofistLoad = false
  },
  methods: {
    [CRUD.HOOK.beforeRefresh]() {
      this.crud.query.bindId = this.$route.query.id;
      this.crud.query.categoryId = this.$route.query.category;
      if (this.$route.query.formData) {
        this.$set(this.crud.query, 'blur', this.$route.query.formData);
      }

      this.bindId = this.$route.query.id;
    },
    goOtherViews(data, row) {
      const { name, projectId, code, label } = data;
      const projectsKeys = this.$config.projects_keys;
      const junctionList = this.$config.Junction_list;
      const config = {
        '自动驾驶': {
          routerName: 'OaPproject',
          query: {
            projectId,
            name,
            code,
            docId: projectsKeys.otherInfo,
            id: String(projectsKeys.bindId)
          }
        },
        '信号灯': {
          routerName: 'JunctionList',
          query: {
            id: String(junctionList.bindId),
            category: junctionList.categoryId,
            title: row.ftCode
          }
        }
      };
      config[label] && this.$router.push({ name: config[label].routerName, query: config[label].query })
    },
    getTime() {
      if (this.query.createTime) {
        return this.query.createTime
      }
      const end = `${this.$dayJS().format('YYYY-MM-DD')} 12:00:00`
      const start = `${this.$dayJS().subtract(1, 'day').format('YYYY-MM-DD')} 12:00:00`
      return [start, end]
    },
    preView() {
      console.log(this.omAssetAPi);
      // const params = qs.stringify(data, { indices: false })
      // const params = `page=0&size=99999&createTime=${this.getTime()[0]}&createTime=${this.getTime()[1]}&status=${this.query.status || ''}&enabled=1&ft3=${this.query.ft3}&title=${this.query.title}`
      const params = `page=0&size=99999&enabled=1&asset.categoryId=${this.$route.query.category}`
      const url = `${this.omAssetAPi}/asset/html?${params}`;
      window.open(url)
    },
    downLoad() {
      const params = `page=0&size=99999&enabled=1&asset.categoryId=${this.$route.query.category}`
      // const params = `page=0&size=99999&createTime=${this.getTime()[0]}&createTime=${this.getTime()[1]}&status=${this.query.status || ''}&enabled=1&ft3=${this.query.ft3 || ''}&title=${this.query.title || ''}&fileName=路口信息`
      const url = `${this.omAssetAPi}/asset/xls?${params}&fileName=路口基础信息`;
      downloadUrl(url)
    },
    getConfigData() {
      // const data = {
      //   key: 'inspects_list'
      // }
      // getConfig(data).then(res => {
      //   this.inspectBindID = res.extend.data.bindId;
      // })
    },
    // 点击巡检记录
    toInspects(row) {
      this.$router.push({
        name: 'Inspects',
        query: {
          id: this.inspectBindID,
          omAssetID: row.id,
          omAssetTitle: row.title,
          category: this.$route.query.category
        }
      })
    },
    addFile() {
      this.$refs.uploadExcel.init();
    },
    addTable() {
      this.$router.push({
        name: 'JunctionCreate2',
        query: { id: this.$route.query.id, category: this.$route.query.category }
      });
      this.nofistLoad = true;
    },
    editItem(row) {
      this.$router.push({
        name: 'JunctionCreate2',
        query: { id: this.$route.query.id, rowId: row.id, type: 'edit', category: this.$route.query.category }
      });
      this.nofistLoad = true;
    },
    detail(row) {
      this.$router.push({ name: 'JunctionCreate2', query: { id: this.$route.query.id, rowId: row.id, type: 'see' }})
    },
    deleteItem(row) {
      crudTable.del([row.id]).then(response => {
        this.crud.refresh();
        this.$notify({
          title: '数据已删除',
          type: 'success',
          duration: 2500
        })
      })
    },
    getJson(item) {
      const json = {}
      item.forEach(i => {
        for (const name in i) {
          json[name] = i[name];
        }
      })
      return json;
    },
    getArr(item) {
      const arr = [];
      item.forEach(i => {
        i.forEach(j => {
          arr.push(j)
        })
      })
      return arr;
    }
  },
  [CRUD.HOOK.beforeRefresh]() {
    console.log(123);
    // console.log(this.$router.query.id);
    // crud.query.bindId = this.routerId
  }
}
</script>
<style lang="scss" rel="stylesheet/scss" scoped>
.tableImg {
	width: 50px;
	height: 50px;
}

.table-colume-title {
	cursor: pointer;
	color: #2476F8;
}

.custom-tag {
	margin-right: 8px;
	white-space: normal; /* 允许文本折行 */
	word-break: break-all; /* 在任意字符间折行，适用于没有自然折行点的长单词或URL等 */
	/* 或者使用 word-break: break-word; 保持英文单词和中文句子的完整性 */
	//max-width: 70px;
	//height: 44px;
	height: auto;
	vertical-align: top;
}
</style>
