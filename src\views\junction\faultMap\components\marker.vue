<template>
  <div class="search-box">
    <el-input v-model="query.title" clearable size="small" placeholder="输入路口名称" style="width: 200px;" class="filter-item" />
    <el-select
      v-model="query.fv1"
      clearable
      size="small"
      placeholder="请选择大类"
      class="filter-item"
      style="width: 200px"
    >
      <el-option
        v-for="item in dict.fault_category"
        :key="item.id"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <date-range-picker v-model="query.fv5" value-format="yyyy-MM-dd" class="date-item" size="small" style="width: 250px;" />
    <span style="padding-left:10px;">
      <el-button class="filter-item" size="small" type="success" icon="el-icon-search" @click="searchMap">搜索</el-button>
      <el-button class="filter-item" size="small" type="warning" icon="el-icon-refresh-left" @click="resetMap">重置</el-button>
    </span>
    <div style="margin-top: 10px;">
      <el-checkbox-group v-model="query.fv2" size="small" @change="handleCheckedChange">
        <el-checkbox-button v-for="item in dict.fault_origin" :key="item.value" :label="item.value">{{ item.value }}</el-checkbox-button>
      </el-checkbox-group>
    </div>
  </div>
</template>
<script>
/* eslint-disable */
import { getSmall } from '@/api/parts/fault'
import DateRangePicker from '@/components/DateRangePicker'
export default {
  name: 'MarkerList',
  components: { DateRangePicker },
  dicts: ['fault_origin', 'fault_category'],
  props: {
    map: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      markerLayer: null,
      infoWindow: null,
      query: {
        title: '',
        fv5: [],
        fv2: []
      },
      markList: [],
      bindId:""
    };
  },
  watch: {
    map(val, old) {
      // 初始化marker
      this.setMaker();
      //信息弹窗
      this.infoWin();
    }
  },
  created() {
    this.bindId = this.$route.query.id;
  },
  beforeDestroy() {
    this.markList = [];
    this.markerLayer = null;
    this.infoWindow = null;
  },
  methods: {
    async setMaker() {
      await this.getSmallMap();
      // 点标记
      this.markerLayer = new TMap.MultiMarker({
        id: 'marker-layer', // 图层id
        map: this.map,
        styles: { // 点标注的相关样式
          'marker': new TMap.MarkerStyle({
            'width': 5,
            'height': 5,
            'anchor': { x: 16, y: 32 },
            'src': location.origin + '/mapIcon/red1.png'
          })
        },
        geometries: []
      });
      this.markerLayer.setGeometries(this.markList);
      // 监听marker点击事件
      this.markerLayer.on('click', this.clickHandler);
    },
    /** 点击点位打开信息窗 */
    clickHandler(evt) {
      var content = `<div style="padding:3px;">${evt.geometry.properties.title}</div>`;
      this.infoWindow.open(); // 打开信息窗
      this.infoWindow.setPosition(evt.geometry.position); // 设置信息窗位置
      this.infoWindow.setContent(content); // 设置信息窗内容
    },
    /** 创建信息窗口 */
    infoWin() {
      this.infoWindow = new TMap.InfoWindow({
        map: this.map,
        position: new TMap.LatLng(0, 0),
        offset: { x: -12, y: -32 } // 设置信息窗相对position偏移像素，为了使其显示在Marker的上方
      });
      this.infoWindow.close(); // 初始关闭信息窗关闭
    },
    async getSmallMap(resolve) {
      this.markList = [];
      await getSmall({
        status: '已匹配',
        enabled: 1,
        bindId:this.bindId,
        title: this.query.title,
        fv5: this.query.fv5,
        fv2: this.query.fv2,
        fv1: this.query.fv1,
        page:0,
        size:999999
      }).then(res => {
        const arr = res.content;
        arr && (this.markList = arr.map(item => {
          let fv2 = '';
          let fv3 = '';
          if (item.asset.fv2 && item.asset.fv2 !== 'null' && item.asset.fv2 !== 'undefined') {
            fv2 = item.asset.fv2;
          }
          if (item.asset.fv3 && item.asset.fv3 !== 'null' && item.asset.fv3 !== 'undefined') {
            fv3 = item.asset.fv3;
          }
          return {
            'id': item.id,
            'styleId': 'marker',
            'position': new TMap.LatLng(fv3, fv2),
            'properties': {
              'title': item.title
            }
          }
        }));
      }).catch(e => { console.log(e) })
    },
    async searchMap() {
      await this.getSmallMap();
      this.markerLayer.setGeometries(this.markList);
    },
    resetMap() {
      this.query = {
        title: '',
        fv5: [],
        fv2: []
      };
      this.searchMap();
    },
    handleCheckedChange() {
      this.searchMap();
    }
  }
};
</script>
<style lang="scss" scoped>
.search-box{
  position: absolute;
  width:100%;
  left:20px;
  top:20px;
  z-index: 2000;
  padding: 10px 0 0 10px;
}

</style>
