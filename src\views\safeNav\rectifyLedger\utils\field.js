// 项目列表所有权限
export const permission = {
  add: ['admin', 'omAssetAffiliated:add'],
  edit: ['admin', 'omAssetAffiliated:edit'],
  del: ['admin', 'omAssetAffiliated:del'],
  upload: ['admin', 'omAssetAffiliated:importXlsWithRule'],
  updateT: ['admin', 'omAssetAffiliated:updateFormStruct'],
  updateR: ['admin', 'omAssetAffiliated:updateRelation']
  // updateG: ['admin', 'omAssetAffiliated:toRedisGeoIndex']
}

// 项目列表表头
export const tableHeader = [
  { label: '编号', prop: 'fv4', width: 80, align: 'left', fixed: 'left' },
  { label: '路口名称', prop: 'title', width: 180, align: 'left' },
  { label: '检查单位', prop: 'fv1', align: 'left', width: 150 },
  { label: '检查日期', prop: 'fv2', align: 'left', width: 150 },
  { label: '整改时限', prop: 'fv8', align: 'left', width: 150 },
  { label: '区域负责人', prop: 'fv3', width: 120, align: 'left' },
  { label: '分区', prop: 'fv5', width: 120, align: 'left' },
  { label: '检查发现的问题', prop: 'ft3', width: 150 },
  { label: '问题照片', prop: 'ft1File', width: 150 },
  { label: '整改后照片', prop: 'ft2File', width: 150 },
  { label: '整改完成日期', prop: 'fv7', width: 150, align: 'left' },
  { label: '实际整改人员', prop: 'fv6', width: 150 },
  { label: '状态', prop: 'status', width: 150 },
  { label: '备注', prop: 'ft4', width: 150 },
  { label: '创建日期', prop: 'createTime', width: 150 }
]

// 定义按钮组
export const updateButtonsLists = [
  {
    id: '1',
    label: '导入',
    permission: permission.upload,
    fun: 'importProject',
    size: 'mini',
    className: [],
    icon: 'plus',
    type: 'primary'
  },
  {
    id: '2',
    label: '预览',
    permission: permission.upload,
    fun: 'exportProject',
    size: 'mini',
    className: [],
    icon: 'view',
    type: 'success',
    query: {
      fileType: 'html'
    }
  },
  {
    id: '3',
    label: '导出',
    permission: permission.upload,
    fun: 'exportProject',
    size: 'mini',
    className: [],
    icon: 'download',
    type: 'primary',
    query: {
      fileType: 'xls'
    }
  }
]

// 联合key
export const allKey = {
  '1': {
    target: '2',
    comType: 'fv4',
    targetType: 'title',
    list: 'pointList'
  },
  '2': {
    target: '1',
    comType: 'title',
    targetType: 'fv4',
    list: 'positionList'
  }
}
