import { tableHeader } from './field'
import crudCategory from '@/api/system/category';
// import { convertCurrency } from '@/utils'
/**
 * 项目列表表头自定义颜色
 * @param row
 * @param column
 * @param rowIndex
 * @param columnIndex
 * @returns {string} 最终颜色
 */
export function tableRowClassName({ row, column, rowIndex, columnIndex }) {
  const { property } = column
  const ruleMap = {
    baseKey: ['fv2', 'createBy', 'cateNamesOne', 'cateNamesTwo', 'fv3', 'fv4'],
    dateFormatKey: ['createTime', 'fv15', 'fv16', 'fv17', 'fv20'],
    incomeKey: ['fv7', 'fv8', 'fv9', 'fv14'],
    payKey: ['fv11', 'fv12', 'fv21', 'fv13', 'fv23', 'fv24', 'fv25']
  }

  // 对应的背景色
  const bgColor = {
    baseKey: '#FFC125;', // 第一个颜色 基础
    incomeKey: '#66ccff;', // 第二个颜色 收入
    payKey: '#13CE66;', // 第三个颜色 支出
    dateFormatKey: '#836FFF;' // 第四个颜色 时间
  }
  const getBgColor = (property) => {
    for (const key in ruleMap) {
      if (ruleMap[key].includes(property)) {
        return `background:${bgColor[key]}color:white`;
      }
    }
  }
  return getBgColor(property)
}

/**
 * 格式化表格数据
 * @param {Array} val - 表格数据数组
 */
export function formatterTableData(val) {
  if (val && val.length) {
    let tableData = [];
    tableData = val.map(item => {
      return {
        ...item,
        basicNo: item?.basicData?.basicNo,
        depot: item.basicData?.depot?.title,
        pm: item.basicData?.pm?.name,
        device: item.basicData?.device?.name,
        brand: item.basicData?.brand?.name,
        model: item.basicData?.model?.name
      };
    });
    return tableData;
  } else {
    return [];
  }
}

/**
 * 格式化表头
 * @param {Array} val - 表格数据数组
 */
export function formatterTableHeader(val) {
  const otherHeader = [];
  return [...tableHeader, ...otherHeader];
}

export async function toggleSideBarFun(vueInstance) {
  vueInstance.operateShow = !vueInstance.operateShow;
  await vueInstance.$nextTick()
  vueInstance.$refs.table.doLayout()
}

export function numformat(num) {
  const str = num.toFixed(2).toString()
  // return str.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return str
}

/**
 * 获取项目级联分类数据
 */
export function getCategoryList() {
  const categoryId = 74;
  return crudCategory.getChildren([categoryId]).then((res) => {
    const data = res.content;
    if (data && data[0]) {
      const childrenData = data[0].children;
      if (childrenData.length > 0) {
        childrenData.map((item) => {
          item.value = item.id;
          if (item.hasChildren) {
            handleData(item.children);
          }
        });
        return childrenData;
      }
    }
  })
}

function handleData(option) {
  option && option.map((item) => {
    item.value = item.id;
    if (item.hasChildren) {
      handleData(item.children);
    }
  });
}
