<!-- 规则配置 -->
<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input v-model="query.formData" clearable size="small" placeholder="输入关键字搜索" style="width: 200px;" class="filter-item" />
        <rrOperation />
      </div>
      <crudOperation :permission="permission">
        <el-button
          slot="left"
          v-permission="permission.add"
          class="filter-item"
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="addTable"
        >
          新建
        </el-button>
        <update-button
          slot="left"
          :permission="permission"
          :bind-id="bindId"
          :enabled="1"
        />
      </crudOperation>
    </div>
    <el-table
      ref="table"
      v-loading="crud.loading"
      :data="tableData"
      row-key="id"
      @select="crud.selectChange"
      @select-all="crud.selectAllChange"
      @selection-change="crud.selectionChangeHandler"
    >
      <el-table-column
        v-for="item in tableHeader"
        :key="item.prop"
        :show-overflow-tooltip="true"
        :prop="item.prop"
        :label="item.label"
        :width="item.width || ''"
        :align="item.align || 'center'"
      >
        <template slot-scope="scope">
          <template v-if="item.prop.indexOf('img') !='-1'">
            <img v-for="item in scope.row[item.prop]" :key="item.id" class="tableImg" :src="item.url" alt="">
          </template>
          <template v-else-if="item.prop.indexOf('cascader') !='-1'">
            <span>{{ scope.row[item.prop] ? scope.row[item.prop].join('-') : '' }}</span>
          </template>
          <span v-else>{{ scope.row[item.prop] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="250" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button v-permission="permission.edit" type="primary" size="mini" @click="editItem(scope.row)">编辑</el-button>
          <el-popconfirm
            confirm-button-text="确认"
            :hide-icon="true"
            cancel-button-text="取消"
            icon-color="red"
            title="确认要删除该条数据？"
            @confirm="deleteItem(scope.row)"
          >
            <el-button slot="reference" v-permission="permission.del" type="danger" size="mini">删除</el-button>
          </el-popconfirm>
          <el-button type="success" size="mini" @click="detail(scope.row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination />
  </div>
</template>

<script>
import crudTable from '@/api/system/globalConfig'
import CRUD, { presenter, header, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import updateButton from '@/components/UpdateButton/index'
export default {
  name: 'ConfigScope',
  components: { crudOperation, rrOperation, pagination, updateButton },
  cruds() {
    return CRUD({ title: '分类', url: 'api/extendOptionGlobal', query: { enabled: 1, type: '' }, crudMethod: { ...crudTable }})
  },
  mixins: [presenter(), header(), crud()],
  data() {
    return {
      nofistLoad: false,
      tableData: [],
      tableHeader: [],
      permission: {
        add: ['admin', 'extendOptionGlobal:add'],
        edit: ['admin', 'extendOptionGlobal:edit'],
        del: ['admin', 'extendOptionGlobal:del'],
        updateT: ['admin', 'extendOptionGlobal:updateFormStruct'],
        updateR: ['admin', 'extendOptionGlobal:updateRelation']
      },
      props: {
        value: 'id',
        label: 'label',
        emitPath: false
      },
      bindId: ''
    }
  },
  watch: {
    'crud.data': {
      handler(val, oldVal) {
        if (val && val.length && val.length > 0) {
          const newVal = val;
          const publicfilters = ['text_', 'divider_', 'editor_', 'subform'];
          const uniqueFilters = ['file_'];
          const filterTableHeader = [...publicfilters, ...uniqueFilters]
          const tableHeader = newVal[0].extend.tableHeader.filter((item) => {
            return item.label !== '栅格布局' && filterTableHeader.every(subItem => item.prop.indexOf(subItem) == '-1')
          });
          const otherTableHeader = [
            { prop: 'createBy', label: '发布人员' },
            { prop: 'createTime', label: '发布时间' }
          ];
          this.tableHeader = [...tableHeader, ...otherTableHeader];
          let tableData = [];
          tableData = newVal.map(item => {
            const json = item.extend.data;
            json.id = item.id;
            json.createBy = item.createBy;
            json.createTime = item.createTime;
            return json;
          });
          this.tableData = tableData;
        } else {
          this.tableHeader = [];
          this.tableData = [];
        }
      },
      deep: true
    }
  },
  created() {
    this.crud.optShow = { add: false, edit: false, del: false, download: false, reset: true }
  },
  activated() {
    if (this.nofistLoad) {
      this.crud.refresh();
    }
    this.nofistLoad = false
  },
  methods: {
    [CRUD.HOOK.beforeRefresh]() {
      this.crud.query.bindId = this.$route.query.id;
      this.bindId = this.$route.query.id;
    },
    addTable() {
      this.$router.push({ name: 'ConfigScopeCreate', query: { id: this.$route.query.id }});
    },
    editItem(row) {
      this.$router.push({ name: 'ConfigScopeCreate', query: { id: this.$route.query.id, rowId: row.id, type: 'edit' }});
      this.nofistLoad = true;
    },
    detail(row) {
      this.$router.push({ name: 'ConfigScopeCreate', query: { id: this.$route.query.id, rowId: row.id, type: 'see' }})
    },
    deleteItem(row) {
      crudTable.del([row.id]).then(response => {
        this.crud.refresh();
        this.$notify({
          title: '数据已删除',
          type: 'success',
          duration: 2500
        })
      })
    },
    getJson(item) {
      const json = {}
      item.forEach(i => {
        for (const name in i) {
          json[name] = i[name];
        }
      })
      return json;
    },
    getArr(item) {
      const arr = [];
      item.forEach(i => {
        i.forEach(j => {
          arr.push(j)
        })
      })
      return arr;
    }
  }
}
</script>
<style>
.tableImg {
  width: 50px;height: 50px;
}
</style>
