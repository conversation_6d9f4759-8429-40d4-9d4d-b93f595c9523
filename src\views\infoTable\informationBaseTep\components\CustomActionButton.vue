<template>
  <span class="oper-buttons">
    <update-button
      v-if="bindId"
      :bind-id="bindId"
      :enabled="[1]"
      :other-params="otherParams"
      :permission="permission"
    />
    <el-button
      v-for="(item) in updateButtonsLists"
      :key="item.label"
      v-permission="item.permission"
      :icon="`el-icon-${item.icon}`"
      :size="item.size || 'mini'"
      :type="item.type || 'primary'"
      class="filter-item"
      @click="handelClick(item)"
    >
      {{ item.label }}
    </el-button>
    <el-button-group class="filter-item">
      <el-button size="mini" type="primary">标签队列</el-button>
      <el-button :loading="queueSizeLoading" size="mini" type="primary">{{ queueSize }}</el-button>
      <el-button icon="el-icon-refresh" size="mini" type="primary" @click="queueSizeTag()" />
      <el-button icon="el-icon-delete" size="mini" type="danger" @click="queueEmptyTag()" />
    </el-button-group>
  </span>
</template>

<script>
import { crud } from '@crud/crud'
import updateButton from '@/components/UpdateButton/index.vue';
import { getToken } from '@/utils/auth';
import { downloadUrl } from '@/utils';
import { mapGetters } from 'vuex';
import {
  getConfigData,
  dictConfig
} from '../utils/commonFun';
import { getUpdateButtonsLists } from '../utils/field'
import omAssetTag from '@/api/safeNav/omAssetTag.js'

export default {
  name: '',
  components: { updateButton },
  mixins: [crud()],
  props: {
    bindId: {
      type: String,
      default: ''
    },
    permission: {
      type: Object,
      default: () => {
        return {}
      }
    },
    currentCrud: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    return {
      otherParams: {},
      labelConfig: {},
      updateButtonsLists: [],
      queueSize: 0,
      queueSizeLoading: false
    }
  },

  computed: {
    ...mapGetters([
      'omAssetAPi'
    ]),
    dictApiData() {
      return dictConfig(this.labelConfig?.request, this.labelConfig?.key);
    }
  },
  watch: {},
  async created() {
    this.labelConfig = await getConfigData();
    this.updateButtonsLists = getUpdateButtonsLists(this.dictApiData.permission);
  },
  methods: {
    // 获取标签队列大小
    queueEmptyTag() {
      omAssetTag.queueEmpty().then(res => {
        this.$notify({
          title: '清空成功',
          type: 'success',
          duration: 1000
        })
      })
    },
    // 获取标签队列大小
    queueSizeTag() {
      this.queueSizeLoading = true
      omAssetTag.queueSize().then(res => {
        this.queueSize = res || 0
      }).finally(() => {
        this.queueSizeLoading = false
      })
    },
    handelClick(item) {
      this[item.fun](item.query);
    },
    importProject() {
      this.$emit('importProject')
    },
    uploadExcelSuccess() {
      this.$emit('uploadExcelSuccess')
    },
    exportProject({ fileType }) {
      const { query } = this.currentCrud
      const { bindId } = this.$config[this.labelConfig.configKey]
      const { otherInfo } = this.labelConfig
      const details = JSON.parse(otherInfo)
      const fixedParams = {
        page: 0,
        size: 999999,
        fileName: this.labelConfig.topName
      };

      const variableParams = {
        [`${details.baseQueryPre}.enabled`]: 1,
        [`${details.baseQueryPre}.title`]: query.title,
        [`${details.baseQueryPre}.fv4`]: query.fv7,
        [`${details.baseQueryPre}.fv9`]: query.fv6,
        // 'asset.categoryId': categoryId,
        [`${details.baseQueryPre}.bindId`]: bindId
      };
      const filteredVariableParams = this.filterParams(variableParams);

      const params = { ...fixedParams, ...filteredVariableParams };

      this.downloadFile(fileType, params, details?.baseTemp);
    },
    // 导出所有数据
    exportAllProject({ fileType }) {
      const { query } = this.currentCrud
      const { otherInfo } = this.labelConfig
      const { bindId } = this.$config[this.labelConfig.configKey]
      const details = JSON.parse(otherInfo)
      const fixedParams = {
        page: 0,
        size: 999999,
        fileName: this.labelConfig.topName
      };

      const variableParams = {
        'asset.enabled': 1,
        'asset.title': query.title,
        'asset.fv4': query.fv7,
        'asset.fv9': query.fv6,
        // 'asset.categoryId': categoryId,
        'asset.bindId': bindId,
        'asset.type.values': details?.topName,
        'asset.type.fieldName': 'fv1'
      };
      const filteredVariableParams = this.filterParams(variableParams);

      const params = { ...fixedParams, ...filteredVariableParams };

      this.downloadFile(fileType, params, details?.tagTemp);
    },
    // 导出筛选数据
    exportFilterProject({ fileType }) {
      const { query } = this.currentCrud
      const { otherInfo } = this.labelConfig
      const details = JSON.parse(otherInfo)
      const fixedParams = {
        page: 0,
        size: 999999,
        fileName: this.labelConfig.topName
      };
      const filteredVariableParams = this.filterParams(query);

      this.downloadPostFile(fileType, fixedParams, filteredVariableParams, details?.filterTemp);
    },
    downloadPostFile(fileType, params, filteredVariableParams, templateName) {
      console.log(templateName, '<===>', 'templateName')
      const queryString = Object.keys(params)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&');
      const url = `${this.omAssetAPi}/${templateName}/${fileType}?${queryString}`;
      let message = '处理中，'
      if (fileType === 'html') {
        message = '预览中，';
      } else if (fileType === 'xls') {
        message = '下载中，';
      }
      this.$message({
        message: message + `请稍后查看...`,
        type: 'success'
      })
      const data = {
        asset: filteredVariableParams // 将参数转换为 JSON 字符串
      }
      fetch(url, {
        method: 'POST', // 设置请求方法为 POST
        headers: {
          'Content-Type': 'application/json', // 设置请求头
          'Authorization': `${getToken()}`
        },
        body: JSON.stringify(data)
      })
        .then(response => {
          if (!response.ok) {
            throw new Error('Network response was not ok');
          }
          return response.blob();
        })
        .then(blob => {
          const newUrl = window.URL.createObjectURL(blob);

          if (fileType === 'html') {
            window.open(newUrl, '_blank');
          } else if (fileType === 'xls') {
            downloadUrl(newUrl, this.labelConfig.topName);
          }
        })
        .catch(error => {
          console.error('There was a problem with your fetch operation:', error);
        });
    },
    downloadFile(fileType, params, templateName) {
      console.log(templateName, '<===>', 'templateName')
      const queryString = Object.keys(params)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&');
      const url = `${this.omAssetAPi}/${templateName}/${fileType}?${queryString}`;
      let message = '处理中，'
      if (fileType === 'html') {
        message = '预览中，';
      } else if (fileType === 'xls') {
        message = '下载中，';
      }
      this.$message({
        message: message + `请稍后查看...`,
        type: 'success'
      })
      fetch(url, {
        headers: {
          'Authorization': `${getToken()}`
        }
      })
        .then(response => {
          if (!response.ok) {
            throw new Error('Network response was not ok');
          }
          return response.blob();
        })
        .then(blob => {
          const newUrl = window.URL.createObjectURL(blob);

          if (fileType === 'html') {
            window.open(newUrl, '_blank');
          } else if (fileType === 'xls') {
            downloadUrl(newUrl, this.labelConfig.topName);
          }
        })
        .catch(error => {
          console.error('There was a problem with your fetch operation:', error);
        });
    },
    // 专门用于过滤参数
    filterParams(variableParams) {
      return Object.entries(variableParams).reduce((acc, [key, value]) => {
        if (this.isNotEmpty(value)) {
          acc[key] = typeof value === 'string' ? value.trim() : value;
        }
        return acc;
      }, {});
    },
    // 检查给定值是否为空
    isNotEmpty(value) {
      const type = typeof value;
      switch (type) {
        case 'string':
          return value.trim() !== '';
        case 'object':
          return value !== null && (
            Array.isArray(value) ? value.length > 0 : Object.keys(value).length > 0
          );
        default:
          return value !== null && value !== undefined;
      }
    },
    // 清除数据
    clearAllData(query) {
      const { type, message } = query
      this.$confirm(`确认清空所有的${message}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        const { bindId } = this.$config[this.labelConfig.configKey]
        let requestPromise = []
        if (type == 1) {
          requestPromise = [this.dictApiData.crud.empty(bindId)]
        } else {
          requestPromise = [omAssetTag.empty(this.labelConfig.topName)]
        }
        // 只有俩个都删除才算删除成功
        Promise.all(requestPromise)
          .then(([response]) => {
            loading.close();
            this.successFun(`${message}已清空`);
          })
          .catch(() => {
            loading.close();
          });
      }).catch(() => {
      })
    },
    successFun(title = '数据已清空') {
      this.$notify({
        title,
        type: 'success',
        duration: 1000
      })
      setTimeout(() => {
        this.crud.refresh();
      }, 0)
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
