// 项目列表所有权限

export const permissionAsset = {
  list: ['admin', 'omAsset:list'],
  add: ['admin', 'omAsset:add'],
  edit: ['admin', 'omAsset:edit'],
  del: ['admin', 'omAsset:del'],
  upload: ['admin', 'omAsset:importXlsWithRule'],
  updateT: ['admin', 'omAsset:updateFormStruct'],
  updateR: ['admin', 'omAsset:updateRelation'],
  empty: ['admin', 'omAsset:empty', 'omAssetTag:empty']
  // updateG: ['admin', 'omAssetAffiliated:toRedisGeoIndex']
}
export const permissionAffiliated = {
  list: ['admin', 'omAssetAffiliated:list'],
  add: ['admin', 'omAssetAffiliated:add'],
  edit: ['admin', 'omAssetAffiliated:edit'],
  del: ['admin', 'omAssetAffiliated:del'],
  upload: ['admin', 'omAssetAffiliated:importXlsWithRule'],
  updateT: ['admin', 'omAssetAffiliated:updateFormStruct'],
  updateR: ['admin', 'omAssetAffiliated:updateRelation'],
  empty: ['admin', 'omAssetAffiliated:empty', 'omAssetTag:empty']
  // updateG: ['admin', 'omAssetProgress:toRedisGeoIndex']
}
// 杆体设备安装进度、杆体问题数据、测试杆
export const tableHeaderPole = [
  { label: '路口编号', prop: 'fv1', align: 'left', width: 100, fixed: 'left', showOverflow: true },
  { label: '杆体编号', prop: 'fv4', width: 150, fixed: 'left', showOverflow: true },
  { label: '路口名称', prop: 'title', width: 200, align: 'left', fixed: 'left', showOverflow: true }
]
// 信息总表
export const tableHeaderInfoSummary = [
  // { label: '序号', prop: '0', fixed: 'left', align: 'left', width: 80 },
  { label: '唯一编号', prop: 'fv4', align: 'left', width: 100, fixed: 'left', showOverflow: true },
  { label: '路口名称', prop: 'title', width: 200, align: 'left', fixed: 'left', showOverflow: true }
]
// 信息表-3.0项目、路口-信息表-4.0项目、信息表-信号灯项目、信息表-非现场项目、 信息表-智慧交通项目
export const tableHeaderOtherInfo = [
  { label: '项目名称', prop: 'fv1', align: 'left', width: 100, fixed: 'left', showOverflow: true },
  { label: '唯一编号', prop: 'fv4', width: 150, fixed: 'left', showOverflow: true },
  { label: '路口名称', prop: 'title', width: 200, align: 'left', fixed: 'left', showOverflow: true }
]
// 路口施工进度、路口问题数据 、测试路口表头
export const tableHeaderCrossing = [
  // { label: '序号', prop: '0', fixed: 'left', align: 'left', width: 80 },
  { label: '路口编号', prop: 'fv4', align: 'left', width: 100, fixed: 'left', showOverflow: true },
  { label: '路口名称', prop: 'title', width: 200, align: 'left', fixed: 'left', showOverflow: true }
]

export const tableHeaderConfig = {
  'InfrastructurePlanP': {
    tableHeader: tableHeaderCrossing,
    permission: permissionAsset
  }, // 路口施工进度
  'IntersectionProblemData': {
    tableHeader: tableHeaderCrossing,
    permission: permissionAsset
  }, // 路口问题数据
  'IntersectionTest': {
    tableHeader: tableHeaderCrossing,
    permission: permissionAsset
  }, // 测试路口
  'RodInstallProgress': {
    tableHeader: tableHeaderPole,
    permission: permissionAffiliated
  }, // 杆体设备安装进度
  'RodProblemData': {
    tableHeader: tableHeaderPole,
    permission: permissionAffiliated
  }, // 杆体问题数据
  'RodTest': {
    tableHeader: tableHeaderPole,
    permission: permissionAffiliated
  }, // 测试杆
  'InfoSummaryTable': {
    tableHeader: tableHeaderInfoSummary,
    permission: permissionAsset
  }, // 信息总表
  'Project3': {
    tableHeader: tableHeaderOtherInfo,
    permission: permissionAffiliated
  }, // 信息表-3.0项目
  'Project4': {
    tableHeader: tableHeaderOtherInfo,
    permission: permissionAffiliated
  }, // 信息表-4.0项目
  'SignalLight': {
    tableHeader: tableHeaderOtherInfo,
    permission: permissionAffiliated
  }, // 信息表-信号灯项目
  'OffSiteProjects': {
    tableHeader: tableHeaderOtherInfo,
    permission: permissionAffiliated
  }, // 信息表-非现场项目
  'SmartTraffic': {
    tableHeader: tableHeaderOtherInfo,
    permission: permissionAffiliated
  }, // 信息表-智慧交通项目
  'InfrastructurePlanP4': {
    tableHeader: tableHeaderCrossing,
    permission: permissionAsset
  } // 4.0路口施工进度
};

export function getUpdateButtonsLists(permission) {
  const updateButtonsLists = [
    {
      id: '1',
      label: '导入',
      permission: permission.upload,
      fun: 'importProject',
      size: 'mini',
      className: [],
      icon: 'plus',
      type: 'primary'
    },
    {
      id: '2',
      label: '预览基础数据',
      permission: permission.upload,
      fun: 'exportProject',
      size: 'mini',
      className: [],
      icon: 'view',
      type: 'success',
      query: {
        fileType: 'html'
      }
    },
    {
      id: '3',
      label: '导出基础数据',
      permission: permission.upload,
      fun: 'exportProject',
      size: 'mini',
      className: [],
      icon: 'download',
      type: 'primary',
      query: {
        fileType: 'xls'
      }
    },
    {
      id: '4',
      label: '清空基础数据',
      permission: permission.empty,
      fun: 'clearAllData',
      size: 'mini',
      className: [],
      icon: 'delete',
      type: 'danger',
      query: {
        message: '基础数据',
        type: '1'
      }
    },
    {
      id: '5',
      label: '清空标签数据',
      permission: permission.empty,
      fun: 'clearAllData',
      size: 'mini',
      className: [],
      icon: 'delete',
      type: 'danger',
      query: {
        message: '标签数据',
        type: '2'
      }
    },
    {
      id: '6',
      label: '预览所有数据',
      permission: permission.upload,
      fun: 'exportAllProject',
      size: 'mini',
      className: [],
      icon: 'view',
      type: 'success',
      query: {
        fileType: 'html'
      }
    },
    {
      id: '7',
      label: '导出所有数据',
      permission: permission.upload,
      fun: 'exportAllProject',
      size: 'mini',
      className: [],
      icon: 'download',
      type: 'primary',
      query: {
        fileType: 'xls'
      }
    },
    {
      id: '8',
      label: '预览筛选数据',
      permission: permission.upload,
      fun: 'exportFilterProject',
      size: 'mini',
      className: [],
      icon: 'view',
      type: 'success',
      query: {
        fileType: 'html'
      }
    },
    {
      id: '9',
      label: '导出筛选数据',
      permission: permission.upload,
      fun: 'exportFilterProject',
      size: 'mini',
      className: [],
      icon: 'download',
      type: 'primary',
      query: {
        fileType: 'xls'
      }
    }
    // {
    //   id: '10',
    //   label: '清空标签队列',
    //   permission: permission.list,
    //   fun: 'queueEmptyTag',
    //   size: 'mini',
    //   className: [],
    //   icon: 'delete',
    //   type: 'danger'
    // }
  ]
  return updateButtonsLists;
}

// 联合key
export const allKey = {
  '1': {
    target: '2',
    comType: 'fv4',
    targetType: 'title',
    list: 'pointList'
  },
  '2': {
    target: '1',
    comType: 'title',
    targetType: 'fv4',
    list: 'positionList'
  }
}

export const tagDrawerOptions = {
  title: '',
  direction: 'rtl',
  size: '40%'
}
export const tagSelectOptions = {
  'type.fieldName': 'fv1',
  'type.values': '',
  'top.fieldName': 'fv10'
}
