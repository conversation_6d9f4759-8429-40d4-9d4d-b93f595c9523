<template>
  <el-drawer
    :before-close="beforeClose"
    :close-on-press-escape="false"
    :destroy-on-close="true"
    :visible.sync="selectVisible"
    :wrapper-closable="false"
    size="85%"
    title="选择项目权限!"
  >
    <div class="select-project">
      <el-row :gutter="20">
        <!--项目成员-->
        <el-col :lg="16" :md="16" :sm="16" :xl="16" :xs="16">
          <el-table
            ref="usertable"
            :data="userList"
            highlight-current-row
            row-key="id"
            style="width: 100%;"
            @current-change="handleCurrentChange"
          >
            <el-table-column :show-overflow-tooltip="true" fixed="left" label="用户名" prop="username" />
            <el-table-column :show-overflow-tooltip="true" fixed="left" label="姓名" prop="nickName" />
            <el-table-column :show-overflow-tooltip="true" label="手机号" prop="phone" />
            <el-table-column :show-overflow-tooltip="true" label="邮箱" prop="email" />
            <!-- <el-table-column
:show-overflow-tooltip="true"
label="角色"
prop="roles"
>
<template slot-scope="scope">
{{ scope.row.roles.map((itme) => { return itme.name }).join(',') }}
</template>
</el-table-column> -->
            <el-table-column label="性别" prop="gender" />
            <el-table-column :show-overflow-tooltip="true" label="部门" prop="dept">
              <template slot-scope="scope">
                <div>{{ scope.row.dept ? scope.row.dept.name : '暂无部门' }}</div>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" label="创建日期" prop="createTime" width="135" />
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
        <el-col :lg="8" :md="8" :sm="8" :xl="8" :xs="8">
          <!--项目-->

          <el-card class="box-card" shadow="never">
            <div slot="header" class="clearfix">
              <el-tooltip class="item" content="选择指定人员分配权限" effect="dark" placement="top">
                <span class="role-span">权限分配</span>
              </el-tooltip>
              <el-button
                :disabled="!showButton"
                :loading="loading"
                icon="el-icon-check"
                size="mini"
                style="float: right; padding: 6px 9px"
                type="primary"
                @click="onsubmit"
              >保存
              </el-button>
            </div>
            <el-tree
              ref="project"
              :data="projectList"
              :default-checked-keys="projectIds"
              :props="defaultProps"
              accordion
              check-strictly
              node-key="id"
              show-checkbox
              @check="menuChange"
            >
              <span slot-scope="{ node, data }" class="custom-tree-node">
                <span class="node-label">{{ node.label }}({{ data.fv1 }})</span>

              </span>
            </el-tree>
          </el-card>
        </el-col>

      </el-row>
    </div>

  </el-drawer>

</template>

<script>
import CRUD, { crud, form, header, presenter } from '@crud/crud'
import oaPmTree from '@/api/oaWorkOrder/oaPmTree';
import oaPmMember from '@/api/oaWorkOrder/oaPmMember';
import pagination from '@crud/Pagination.vue'

const defaultForm = {
  id: null
}
export default {
  name: 'SelectFile',
  components: { pagination },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: false,
      title: '用户',
      url: 'api/oaPmMember',
      query: { enabled: 1 },
      crudMethod: { ...oaPmMember }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      deptName: '',
      selectVisible: false,
      defaultProps: { children: 'children', label: 'label', isLeaf: 'leaf' },
      deptDatas: [],
      projectInfo: {},
      userList: [],
      projectList: [],
      tableData: [],
      projectIds: [],
      showButton: false,
      loading: false,
      bindId: ''
    }
  },
  watch: {
    'crud.data': {
      handler(val, oldVal) {
        if (val && val.length && val.length > 0) {
          const newVal = val;
          const tableData = [];
          newVal.map(item => {
            tableData.push(item.user)
          })
          this.userList = tableData;
        } else {
          this.userList = [];
        }
      },
      deep: true
    }
  },
  methods: {
    openSelectFile(data, bindId) {
      this.bindId = bindId;
      this.selectVisible = true;
      this.projectInfo = data;
      this.crud.query.pmId = this.projectInfo.id;
      this.getMenuDatas();
      this.crud.toQuery();
    },
    // 获取右侧侧部门数据
    getMenuDatas() {
      const { id } = this.projectInfo
      const params = {
        enabled: 1,
        ids: [id],
        fv1: ['项目', '目录', '子项目']
        // bindId: this.bindId
      }
      oaPmTree.getChildren(params).then(res => {
        const data = res.content;
        // const topId = findTopLevelId(data, this.projectInfo.id);
        // this.addDisabled(data, topId)
        this.projectList = data
      })
    },
    // 统一加disabled
    // addDisabled(data, topId) {
    //   for (const item of data) {
    //     if (item.id !== topId) {
    //       item.disabled = true;
    //       if (item.hasChildren && item.children.length !== 0) {
    //         this.addDisabled(item.children, topId);
    //       }
    //     }
    //   }
    // },
    handleCurrentChange(val) {
      if (val) {
        // 清空菜单的选中
        this.$refs.project.setCheckedKeys([])
        this.projectIds = []
        this.currentId = val.id
        const { id } = this.projectInfo
        oaPmTree.getAllPmTree({ enabled: 1, uid: this.currentId, rootId: id }).then(res => {
          this.projectIds = res.map(item => item.id);
          res.forEach((element) => {
            var node = this.$refs.project.getNode(element);
            if (node.isLeaf) {
              this.$refs.project.setChecked(node, true);
            }
          });
        })
        // uid
        this.showButton = true
      } else {
        this.showButton = false
      }
    },
    onsubmit() {
      this.loading = true
      oaPmTree.editMember(this.formatData()).then(() => {
        this.$notify({
          title: '添加成功',
          type: 'success',
          duration: 2500
        });
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    formatData() {
      const CheckedNodes = this.$refs.project.getCheckedNodes(false, true)
      const { id } = this.projectInfo
      return CheckedNodes.map(item => {
        const obj = {
          items: [
            {
              pm: { id: item.id, enabled: 1 },
              user: { id: this.currentId, enabled: 1 },
              enabled: 1,
              rootId: id
            }
          ],
          enabled: 1
        }
        if (item.pid) {
          obj.pid = item.pid;
        }
        return obj
      })
      // const halfKeys = this.$refs.project.getHalfCheckedKeys()
      // const checkedKeys = this.$refs.project.getCheckedKeys()
      // const allKeys = halfKeys.concat(checkedKeys);
      // return allKeys.map(item => {
      //   console.log(item, '<===>', 'item')
      //   const obj = {
      //     items: [
      //       {
      //         pm: { id: item, enabled: 1 },
      //         user: { id: this.currentId, enabled: 1 },
      //         enabled: 1
      //       }
      //     ],
      //     enabled: 1
      //   }
      //   return obj
      // })
    },
    menuChange(currentObj, treeStatus) {
      const selected = treeStatus.checkedKeys.indexOf(currentObj.id) // -1未选中
      console.log(selected, '<===>', 'selected')
      // 选中
      if (selected !== -1) {
        // 子节点只要被选中父节点就被选中
        this.selectedParent(currentObj)
        // 统一处理子节点为相同的勾选状态
        this.uniteChildSame(currentObj, true)
      } else {
        // 未选中 处理子节点全部未选中
        if (currentObj.hasChildren && currentObj.children.length !== 0) {
          this.uniteChildSame(currentObj, false)
        }
      }
    },
    // 统一处理子节点为相同的勾选状态
    uniteChildSame(treeList, isSelected) {
      this.$refs.project.setChecked(treeList.id, isSelected)
      console.log(treeList, '<===>', 'treeList')
      if (treeList.hasChildren && treeList.children.length !== 0) {
        for (let i = 0; i < treeList.children.length; i++) {
          this.uniteChildSame(treeList.children[i], isSelected)
        }
      }
    },
    // 统一处理父节点为选中
    selectedParent(currentObj) {
      const currentNode = this.$refs.project.getNode(currentObj)
      if (currentNode.parent.key !== undefined) {
        this.$refs.project.setChecked(currentNode.parent, true)
        this.selectedParent(currentNode.parent)
      }
    },
    beforeClose() {
      this.$emit('succeSubmit', { type: 'file' });
      this.selectVisible = false;
      // 恢复data中的初始值
      Object.assign(this.$data, this.$options.data.call(this));
    }
  }
}

</script>
<style lang="scss" rel="stylesheet/scss" scoped>
.select-project {
	padding: 20px;
}

.project-users {
	min-height: 200px;

	.user-lable {
		line-height: 20px;
		font-size: 18px;
		margin-top: 20px;
	}

	.user-list {
		padding: 20px 20px 20px 0;
		display: flex;
		align-items: center;
		flex-wrap: wrap;

		.el-tag {
			margin: 0 10px 10px 0;
		}

	}
}
</style>
