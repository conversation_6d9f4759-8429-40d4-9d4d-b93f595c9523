// 项目列表所有权限
export const permission = {
  add: ['admin', 'oaPmTree:add'],
  edit: ['admin', 'oaPmTree:edit'],
  del: ['admin', 'oaPmTree:del'],
  addMember: ['admin', 'oaPmMember:edit'],
  editMember: ['admin', 'oaPmTreeMember:add'],
  updateT: ['admin', 'oaPmTree:updateFormStruct'],
  updateR: ['admin', 'oaPmTree:updateRelation'],
  cateNamesToIds: ['admin', 'oaPmTree:cateNamesToIds'],
  addToMemberAndAuth: ['admin', 'oaPmTree:addToMemberAndAuth'],
  oneLevelSubproject: ['admin', 'oaPmTree:oneLevelSubproject'],
  importXlsWithRule: ['admin', 'oaPmTree:importXlsWithRule'],
  updateFieldsByRule: ['admin', 'oaPmTree:updateFieldsByRule'],
  seeRemarks: ['admin', 'oaPmTree:seeRemarks'],
  delRemarks: ['admin', 'oaPmTree:delRemarks'],
  addRemarks: ['admin', 'oaPmTree:addRemarks'],
  replyRemarks: ['admin', 'oaPmTree:replyRemarks'],
  seeProjectLog: ['admin', 'oaPmTree:seeProjectLog'],
  delProjectLog: ['admin', 'oaPmTree:delProjectLog'],
  addProjectLog: ['admin', 'oaPmTree:addProjectLog'],
  exportProject: ['admin', 'oaPmTree:exportProject'],
  previewProject: ['admin', 'oaPmTree:previewProject']
}

export const manyOption = [
  { name: '新建子项目', command: '1', permission: permission.add, fun: 'addProject', always: true },
  // { name: '新建子目录', command: '2', permission: permission.add, fun: 'addProject', always: true },
  { name: '项目成员', command: '3', permission: permission.addMember, fun: 'handleUsers', always: false },
  { name: '项目权限', command: '4', permission: permission.editMember, fun: 'handleAuth', always: false },
  { name: '项目派单', command: '5', permission: permission.edit, fun: 'onDispatch', always: true }
  // { name: '项目阶段', command: '6', permission: ['admin', 'oaPmTree:list'], fun: 'onDrawer', always: true }
]

// 项目列表表头
export const tableHeader = [
  { label: '项目名称', prop: 'name', fixed: 'left', align: 'left', width: 300 },
  { label: '部门', prop: 'ft4', align: 'center', width: 150, type: 'select', dict: 'department', showTip: true },
  { label: '整改', prop: 'ft1', align: 'center', width: 50 },
  { label: '日志', prop: 'ft3', align: 'center', width: 50 },
  // { label: '年份', prop: '1', width: 70, align: 'center' },
  { label: '项目类型', prop: 'fv2', width: 70, align: 'left', type: 'select', dict: 'project_info_type' },
  { label: '项目负责人', prop: 'createBy', width: 85, align: 'left', type: 'selectSearch' },
  // { label: '项目编号', prop: '4', width: 130, align: 'center' },
  { label: '项目分类(一)', prop: `cateNamesOne`, width: 90, align: 'left', type: 'cascader', mapKey: 'fv6' },
  { label: '项目分类(二)', prop: `cateNamesTwo`, width: 90, align: 'left', type: 'cascader', mapKey: 'fv6' },
  { label: '项目阶段', prop: 'fv3', width: 80, align: 'left', type: 'select', dict: 'project_info_stage' },
  { label: '项目状态', prop: 'fv4', width: 70, align: 'left', type: 'select', dict: 'project_info_status' },
  { label: '毛利率', prop: 'fv14', width: 150, sortable: 'custom', align: 'right' }, // 不编辑
  // { label: '是否立项', prop: '10', width: 70, align: 'center' },
  // { label: '立项日期', prop: '11', width: 150, sortable: 'custom', align: 'center' },
  {
    label: '收入(元)',
    prop: 'fv7',
    width: 130,
    sortable: 'custom',
    align: 'right',
    type: 'input',
    regular: '/^-?\\d*\\.?\\d+$/'
  },
  // { label: '总金额(元)', prop: '14', width: 120, sortable: 'custom', align: 'center' },
  {
    label: '已收入金额(元)',
    prop: 'fv8',
    width: 150,
    sortable: 'custom',
    align: 'right',
    type: 'input',
    regular: '/^-?\\d*\\.?\\d+$/'
  },
  { label: '未收入金额(元)', prop: 'fv9', width: 150, sortable: 'custom', align: 'right' }, // 不编辑
  {
    label: '采购预算(元)',
    prop: 'fv11',
    width: 150,
    sortable: 'custom',
    align: 'right',
    type: 'input',
    regular: '/^-?\\d*\\.?\\d+$/'
  },
  {
    label: '费用预算(元)',
    prop: 'fv23',
    width: 150,
    sortable: 'custom',
    align: 'right',
    type: 'input',
    regular: '/^-?\\d*\\.?\\d+$/'
  },
  {
    label: '已支出采购合同额(元)',
    prop: 'fv12',
    width: 160,
    sortable: 'custom',
    align: 'right',
    type: 'input',
    regular: '/^-?\\d*\\.?\\d+$/'
  },
  {
    label: '未支出采购合同额(元)',
    prop: 'fv21',
    width: 160,
    sortable: 'custom',
    align: 'right',
    type: 'input',
    regular: '/^-?\\d*\\.?\\d+$/'
  },
  {
    label: '采购签订合同额(元)',
    prop: 'fv24',
    width: 150,
    sortable: 'custom',
    align: 'right'
  }, // 不编辑
  {
    label: '预算费用支出',
    prop: 'fv25',
    width: 150,
    sortable: 'custom',
    align: 'right',
    type: 'input',
    regular: '/^-?\\d*\\.?\\d+$/'
  },
  // { label: '未支出(元)', prop: '18', width: 150, sortable: 'custom', align: 'center' },
  { label: '预算结余', prop: 'fv13', width: 150, sortable: 'custom', align: 'right' }, // 不编辑
  { label: '合同开始日期', prop: 'fv15', width: 150, sortable: 'custom', align: 'center', type: 'date' },
  { label: '合同结束日期', prop: 'fv16', width: 150, sortable: 'custom', align: 'center', type: 'date' },
  { label: '终验日期', prop: 'fv17', width: 150, sortable: 'custom', align: 'center', type: 'date' },
  { label: '质保日期', prop: 'fv20', width: 150, sortable: 'custom', align: 'center', type: 'date' },
  { label: '创建日期', prop: 'createTime', width: 150 }
]

// 定义按钮组
export const updateButtonsLists = [
  {
    id: '1',
    label: '导入项目',
    permission: permission.importXlsWithRule,
    fun: 'importProject',
    size: 'mini',
    className: [],
    icon: 'plus',
    type: 'primary'
  },
  {
    id: '2',
    label: '更新key',
    permission: permission.cateNamesToIds,
    fun: 'updateProjectKeys',
    size: 'mini',
    className: [],
    icon: 'refresh',
    type: 'success'
  },
  {
    id: '3',
    label: '更新项目权限',
    permission: permission.addToMemberAndAuth,
    fun: 'updateProjectAuth',
    size: 'mini',
    className: [],
    icon: 'refresh',
    type: 'primary'
  },
  {
    id: '4',
    label: '更新子项目信息',
    permission: permission.oneLevelSubproject,
    fun: 'updateSubProjects',
    size: 'mini',
    className: [],
    icon: 'refresh',
    type: 'success'
  },
  {
    id: '5',
    label: '更新项目金额',
    permission: permission.updateFieldsByRule,
    fun: 'updateProjectMoneys',
    size: 'mini',
    className: [],
    icon: 'refresh',
    type: 'success'
  },
  {
    id: '6',
    label: '导出项目',
    permission: permission.exportProject,
    fun: 'exportProject',
    size: 'mini',
    className: [],
    icon: 'download',
    type: 'primary',
    query: {
      fileType: 'xls'
    }
  },
  {
    id: '7',
    label: '预览项目',
    permission: permission.previewProject,
    fun: 'exportProject',
    size: 'mini',
    className: [],
    icon: 'view',
    type: 'primary',
    query: {
      fileType: 'html'
    }
  }
]
