<template>
  <span class="see-cell">
    <template v-if="header.prop === 'name'">
      <span style="cursor: pointer;color: #2476F8" @click="goDetail(currentCell.row)">
        {{ currentCell.row[header.prop] }}
      </span>
    </template>
    <template v-else-if="header.prop === 'ft1'">
      <el-popover
        :content="iconList(currentCell.row).title"
        placement="top"
        popper-class="iconListName"
        trigger="hover"
      >
        <i
          slot="reference"
          v-permission="permission.seeRemarks"
          :class="[iconList(currentCell.row).className]"
          @click="handleOperateRemarks(currentCell.row)"
        />
      </el-popover>

    </template>
    <template v-else-if="header.prop === 'ft3'">
      <i
        v-permission="permission.seeProjectLog"
        :class="iconClass(currentCell.row)"
        class="el-icon-document"
        @click="handleOperateLog(currentCell.row)"
      />
    </template>
    <template v-else-if="header.prop=='fv14'">
      <span class="money-span">
        {{ currentCell.row[header.prop] ? currentCell.row[header.prop] : '0.00%' }}
      </span>
    </template>
    <template v-else-if="isStatistic(header)">
      <span>
        <el-statistic
          :precision="2"
          :value="formatterMoney(currentCell.row[header.prop], currentCell.row)"
          :value-style="{ 'font-size': '14px',color: '#606266', 'text-align': 'right' }"
          group-separator=","
        />
      </span>
    </template>
    <span v-else>{{ currentCell.row[header.prop] }}</span>
  </span>
</template>

<script>
export default {
  name: 'SeeCell',
  components: {},

  props: {
    header: {
      type: Object,
      default: () => {
        return {}
      }
    },
    currentCell: {
      type: Object,
      default: () => {
        return {}
      }
    },
    permission: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  computed: {
    formatterMoney() {
      return (val) => {
        if (val && val !== '' && !isNaN(val)) {
          return parseFloat(val)
        } else {
          // return 0.00 // 注意这里返回了一个数字类型的0.00, 而不是字符串 '0.00'
        }
      }
    },
    iconList() {
      return (val) => {
        const { ft2 } = val;
        const iconMap = {
          '': { className: 'el-icon-info infoIcon', title: '暂无批注' },
          '0': { className: 'el-icon-warning errIcon', title: '整改要求' },
          '1': { className: 'el-icon-success successIcon', title: '整改合格' },
          '2': { className: 'el-icon-warning replyIcon', title: '整改回复' }
        };
        return iconMap[ft2] || iconMap[''];
      }
    },
    iconClass() {
      return row => {
        const ft3Array = row.ft3 ? JSON.parse(row.ft3) : [];
        return ft3Array.length ? 'successIcon' : 'infoIcon';
      };
    },
    isStatistic() {
      const list = ['fv7', 'fv21', 'fv11', 'fv8', 'fv9', 'fv12', 'fv13', 'fv23', 'fv24', 'fv25']
      return (header) => {
        return list.includes(header.prop)
      }
    }
  },
  watch: {},
  created() {
  },
  methods: {
    // 去项目详情页
    goDetail(data) {
      const { otherInfo } = this.$config.projects_keys
      const { id: projectId, bindId, name, fv4, categoryId } = data
      this.$router.push({
        name: 'OaPproject',
        query: {
          projectId,
          id: String(bindId),
          docId: otherInfo,
          name,
          status: fv4,
          categoryId
        }
      })
    },
    // 点击批注
    handleOperateRemarks(row) {
      this.$emit('handleOperateRemarks', row)
    },
    handleOperateLog(row) {
      this.$emit('handleOperateLog', row)
    }
  }
}
</script>
<style>
.iconListName {
	min-width: 60px !important;
	padding: 10px;
	text-align: center;
}
</style>
<style currentCelld lang="scss" rel="stylesheet/scss">
// 钱的样式
.money-span {
	font-size: 14px;
	color: #606266;
	text-align: right;
	font-family: Sans-serif;
}

.errIcon {
	color: #ff0000;
	font-size: 22px;
}

.successIcon {
	color: #13CE66;
	font-size: 22px;
}

.infoIcon {
	color: #a1aabb;
	font-size: 22px;
}

.replyIcon {
	color: #FFC125;
	font-size: 22px;
}

::v-deep.el-statistic {
	text-align: right !important;

	.con {
		text-align: right !important;
		display: flex;
		align-items: flex-end !important;
		justify-content: flex-end !important;
	}
}
</style>
