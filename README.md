### 任务字段说明

```angular2html
新建任务使用字段
fv7: '', // 里程碑  备注：传中文(title)
fv8: '', // 优先级  备注：传中文
fv9: '', // 处理人  备注：远程联动搜索 传数组（名称）
fv13: '', // 预计开始时间 废弃
fv11: '', // 预计结束时间
fv12: '', // 状态 备注：传中文
ft1: '', // 任务详情 备注
```

### 注意

```angular2html
更新模板id值 一定要更新全局配置
```

### 全局基本配置参数获取

```
有模板相关的配置都将在登录之后加载 菜单接口加载之前

// 在普通vue文件中通过
this.$config[key] // 获取到对应的配置 例如this.$config.milestone_keys 获取里程碑的配置
this.$configData[key] // 获取子表单外的配置 例如 this.$configData.dirKey

在js文件中获取 需要通过vue的原型上去获取
Vue.prototype.$config[key] // 获取到对应的配置 例如Vue.prototype.$config.milestone_keys 获取里程碑的配置
Vue.prototype.$configData[key] // 获取子表单外的配置 例如 Vue.prototype.$configData.dirKey

```

### 全局组件

```
1. 图片预览
urls 是数组多个地址 thUrl 是单个地址 显示缩略图
<file-thumb :preview-list="urls" :url="thUrl" />


```

### 控制操作列是否显示

```
const permission = {
  add: ['admin', 'oaPmTree:add'],
  edit: ['admin', 'oaPmTree:edit'],
  del: ['admin', 'oaPmTree:del'],
  addMember: ['admin', 'oaPmMember:edit'],
  updateT: ['admin', 'oaPmTree:updateFormStruct'],
  updateR: ['admin', 'oaPmTree:updateRelation']
}
// 计算属性中使用 只调用一次 函数中的话会多次调用
// 传参就是第一个参数是要判断的所有的key 第二个就是定义的权限对象
computed: {
    setOperateShow() {
      return this.$setArrPermission(['add', 'del', 'addMember'], this.permission)
    }
},

// 页面中这样使用
<el-table-column
    v-if="checkPer(setOperateShow)"
    align="left"
    fixed="right"
    label="操作"
    width="240"
>
    
 </el-table-column>
```

2024年4月3日 项目列表添加了24列 25列
24列是毛利率 25列是质保日期
