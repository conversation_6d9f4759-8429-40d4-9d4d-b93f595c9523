<template>
  <div id="drawer-container" ref="drawerContainer">
    <el-drawer
      ref="drawer"
      :append-to-body="false"
      :direction="drawerOptions.direction"
      :modal="false"
      :size="drawerOptions.size"
      :title="drawerOptions.title"
      :visible.sync="labelVisible"
      :wrapper-closable="true"
    >
      <!--<multi-tag-selector-->
      <!--  ref="multiCategorySelector"-->
      <!--  :categories="categoryData"-->
      <!--  @update:selectedCategories="updateSelectedCategories(...arguments)"-->
      <!--/>-->
      <info-tag-select
        ref="infoTagSelect"
        :categories="categoryData"
        @resetTag="resetTag"
        @update:selectedCategories="updateSelectedCategories(...arguments)"
      />
    </el-drawer>
  </div>
</template>

<script>
// import MultiTagSelector from '@/views/components/InfoTagSelect/MultiTagSelect';
import InfoTagSelect from '@/views/components/InfoTagSelect/InfoTagSelect.vue';
import {
  // getCategoryList,
  checkAndTagToQuery,
  formatRequestJson,
  getConfigData,
  initLabelData
} from '../utils/commonFun';
import { findFilterItem } from '@/api/safeNav/omAssetTag'

export default {
  name: 'LabelDrawer',
  // components: { MultiTagSelector },
  components: { InfoTagSelect },
  props: {
    drawerOptions: {
      type: Object,
      default: () => ({
        title: '标签选择',
        direction: 'ltr',
        size: '35%'
      })
    },
    crud: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      selectedCategories: [],
      categoryData: [],
      labelVisible: false,
      labelConfig: {}
    };
  },
  async created() {
    this.labelConfig = await getConfigData();
    this.getAllTags();
  },
  methods: {
    async getAllTags() {
      const query = {
        'type.values': this.labelConfig.topName,
        'top.fieldName': 'fv10',
        'type.fieldName': 'fv1'
      }
      findFilterItem(query).then(res => {
        const data = res || []
        this.categoryData = initLabelData(data)
        this.$emit('update:showTgaDisabled')
      })
    },
    resetTag() {
      this.categoryData = initLabelData(this.categoryData)
    },
    updateSelectedCategories(selectArr, selectObject, isFromClear) {
      if (isFromClear) return;// 如果是主动关闭抽屉，不去重新请求
      this.selectedCategories = selectArr;
      const requestJson = formatRequestJson(selectArr, selectObject);
      this.crud.query = { ...this.crud.query, ...requestJson };
      checkAndTagToQuery(this.crud, this.selectedCategories, ['fv7', 'fv6', 'fv4OrTitle'], this.labelConfig); // 调用检查和更新API的方法
    },
    disableBodyScroll() {
      document.body.style.overflow = 'hidden';
    },
    enableBodyScroll() {
      document.body.style.overflow = '';
    },
    closeDrawer() {
      // clearTagAssetToQuery(this.crud);
      // this.$refs.multiCategorySelector.resetAllCategories();
    }

  }
};
</script>

<style scoped>
#drawer-container {
	position: absolute;
	left: 0;
	top: 0;
}

::v-deep .el-drawer__header {
	margin-bottom: 10px !important;
}

::v-deep .el-drawer__body {
	padding: 0 20px !important;
}

::v-deep .el-drawer__header {
	font-size: 17px !important;
	font-weight: bold !important;
	color: #000 !important;
}
</style>
