<template>
  <div v-if="crud.props.searchToggle">
    <!-- 搜索 -->
    <el-input
      v-model="query.basicNo"
      class="filter-item"
      clearable
      placeholder="请输入资产编号搜索"
      size="small"
      style="width: 200px;"
    />
    <!-- <el-input
      v-model="query.orderNo"
      class="filter-item"
      clearable
      placeholder="请输入订单编号"
      size="small"
      style="width: 200px;"
      @keyup.enter.native="crud.toQuery"
    /> -->
    <el-select v-model="query.depotId" class="filter-item" clearable placeholder="请选择仓库" style="width: 230px;">
      <el-option v-for="item in storeroomList" :key="item.id" :label="item.title" :value="item.id" />
    </el-select>
    <el-select
      ref="elselect"
      v-model="query.pmId"
      :collapse-tags="true"
      :loading="selectLoading"
      :remote-method="remoteSelectProject"
      class="filter-item"
      clearable
      debounce="500"
      filterable
      :multiple="false"
      placeholder="请输入请项目名称"
      remote
      reserve-keyword
      size="small"
      style="width: 250px"
      @keyup.enter.native="handleEnter"
    >

      <el-option v-for="item in projectList" :key="item.id" :label="item.name" :value="item.id" />
    </el-select>
    <el-cascader
      v-model="categoryValue"
      class="filter-item"
      :options="Devices"
      :props="cascaderProps"
      :show-all-levels="true"
      clearable
      placeholder="选择资产类别"
      style="width: 250px"
      @change="handleCategoryChange"
    />
    <rrOperation />
  </div>
</template>

<script>
import CRUD, { header } from '@crud/crud';
import rrOperation from '@crud/RR.operation';
import amDepots from '@/api/property/amDepot'
import crudAmCategory from '@/api/property/amCategory'
import { getPmTreeSmall } from '@/api/oaWorkOrder/oaPmTree'
import { getByName } from '@/api/system/user';

export default {
  components: { rrOperation },
  mixins: [header()],
  props: {

  },

  data() {
    return {
      selectLoading: false,
      projectList: [],
      storeroomList: [],
      categoryValue: [],
      Devices: [],
      cascaderProps: {
        lazy: true, // 开启懒加载
        value: 'id', // 将每个选项的值设置为 'id' 属性
        label: 'label', // 用于显示选项名称的属性
        children: 'children', // 包含子选项的属性
        isLeaf: 'isLeaf', // 指示选项是否为叶子节点的属性
        lazyLoad: this.lazyLoad
      }
    }
  },
  created() {
    this.initStoreroomList()
  },
  methods: {

    [CRUD.HOOK.beforeResetQuery]() {
      this.categoryValue = []
    },
    // 获取仓库列表
    async initStoreroomList() {
      try {
        const json = {
          enabled: 1,
          page: 0,
          size: 99999,
          sort: 'createTime,desc',
          bindId: this.$config.storeroom_key.bindId
        }
        const res = await amDepots.get(json);
        if (res && res.content) {
          this.storeroomList = res.content;
        }
      } catch (error) {
        console.error('获取仓库列表失败:', error)
      }
    },
    initDevices() {
      crudAmCategory.getAmCategory({ enabled: '1', pid: null }).then(res => {
        this.Devices = res.content.map(item => ({
          ...item,
          leaf: !item.hasChildren
        }))
        console.log(this.Devices, '<===>', 'Devices')
      })
    },
    // 懒加载数据
    lazyLoad(node, resolve) {
      if (node.isLeaf) {
        // 如果是叶子节点，不需要加载数据
        resolve([]);
      } else {
        const parentId = node.value; // 假设当前节点的 'id' 是父节点的 ID
        crudAmCategory.getAmCategory({ enabled: '1', pid: parentId || null }).then((children) => {
          resolve(children.content);
        });
      }
    },
    handleCategoryChange(value) {
      console.log(value, '<===>', 'value')
      // 根据选择层级动态赋值
      const levels = [
        { level: 0, property: 'deviceId' },
        { level: 1, property: 'brandId' },
        { level: 2, property: 'modelId' }
      ];

      levels.forEach(({ level, property }) => {
        if (value && value.length > level) {
          if (value[level]) {
            this.query[property] = value[level];
          } else {
            delete this.query[property];
          }
        } else {
          delete this.query[property];
        }
      });
    },
    async remoteSelectProject(query, type) {
      console.log(query, '<===>', 'query')
      const { bindId } = this.$config.projects_keys;
      const parameters = {
        enabled: 1,
        name: query,
        size: 99,
        bindId,
        fv1: '项目'
      }
      const result = await this.publicRemote(query, 'projectType', parameters)
      const content = result.content || [];
      if (type) {
        const obj = content.find(item => item.name === query)
        this.query.pmId = [obj.id];
      }
      this.projectList = content || [];
    },
    async publicRemote(query, type, parameters) {
      const QueryTypes = {
        'projectType': { loadingProp: 'selectLoading', remoteMethod: getPmTreeSmall },
        'userType': { loadingProp: 'selectLoading', remoteMethod: getByName }
      }
      const typeMap = QueryTypes[type];
      if (query !== '') {
        this[typeMap.loadingProp] = true;
        try {
          const res = await typeMap.remoteMethod(parameters);
          return res || [];
        } catch (error) {
          return [];
        } finally {
          this[typeMap.loadingProp] = false;
        }
      } else {
        return [];
      }
    },
    handleEnter(val) {
      this.crud.query.pmId = this.projectList.map(item => item.id);
    }
  }
}
</script>
<style lang="scss" rel="stylesheet/scss" scoped>
::v-deep .vue-treeselect__placeholder {
  line-height: 28px;
  font-size: 14px;
  margin-left: 5px;
}

::v-deep .vue-treeselect__control {
  height: 28px;

  .vue-treeselect__single-value {
    margin-left: 5px;
    line-height: 28px;
    color: #606266;
    font-size: 14px;
  }
}

::v-deep .vue-treeselect__input-container {
  line-height: 30px;
  height: 28px;
}
</style>
