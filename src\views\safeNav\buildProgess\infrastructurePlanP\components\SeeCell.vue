<template>
  <span class="see-cell">
    <template v-if="header.prop === 'title'">
      <span style="cursor: pointer;color: #2476F8" @click="goDetail(currentScope.row)">
        {{ currentScope.row[header.prop] }}
      </span>
    </template>
    <template v-else-if="isDynamicHeader(header.prop) && currentScope.row[header.prop]">
      <span class="tag-list">
        <el-tag
          class="custom-tag"
          style="margin-right:8px;width: auto;cursor:pointer;"
          type="success"
        >{{ currentScope.row[header.prop] }}</el-tag>
      </span>
    </template>
    <span v-else>{{ currentScope.row[header.prop] }}</span>
  </span>
</template>

<script>
export default {
  name: 'SeeCell',
  components: {},

  props: {
    header: {
      type: Object,
      default: () => {
        return {}
      }
    },
    currentScope: {
      type: Object,
      default: () => {
        return {}
      }
    },
    crud: {
      type: Object,
      default: () => {
        return {}
      }
    },
    permission: {
      type: Object,
      default: () => {
        return {}
      }
    },
    isShowTag: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  computed: {
    getDescribe() {
      return (data) => {
        return data.join(',')
      }
    }
    // getTagName() {
    //   return (data) => {
    //     if (this.isShowTag) {
    //       return data
    //     } else {
    //
    //     }
    //   }
    // }
  },
  watch: {},
  created() {
  },
  methods: {
    // 去资产详情页
    goDetail(data) {
      const query = {
        name: 'AutoPilot3Form',
        query: {
          id: data.id,
          type: 1
        }
      }
      this.$router.push(query)
    },
    isDynamicHeader(prop) {
      const dynamicHeader = this.crud?.metaData?.tableHeader;
      if (dynamicHeader && dynamicHeader.length) {
        return dynamicHeader.some(element => element.prop === prop);
      }
      return false;
    }
  }
}
</script>

<style currentScoped lang="scss" rel="stylesheet/scss">
.tag-list {
	display: flex;
	flex-wrap: wrap;

	.custom-tag {
		margin-right: 8px;
		padding: 0 2px;
		text-align: center;
		white-space: normal; /* 允许文本折行 */
		word-break: break-all; /* 在任意字符间折行，适用于没有自然折行点的长单词或URL等 */
		/* 或者使用 word-break: break-word; 保持英文单词和中文句子的完整性 */
		min-width: 100px;
		height: auto;
		//height: 44px;
		height: auto;
		vertical-align: top;
		margin-bottom: 3px;
	}

}
</style>
