import store from '../store'
import { getToken } from '@/utils/auth'
function codeLOGIN() {
  if (getToken()) return
  if (window.location.search) {
    const search = window.location.search;
    const code = search.substring(search.indexOf('code=') + 5, search.indexOf('&state='));
    if (code) {
      store.dispatch('CodeLogin', { code }).then(() => {
        window.location.href = window.location.href.replace(`?code=${code}&state=`, '')
        this.$router.push({ path: '/' || '' });
      })
    }
  }
}
codeLOGIN();
