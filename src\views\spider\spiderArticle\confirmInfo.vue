<template>
  <div class="app-container spider-detail">
    <!--项目基础信息-->
    <BaseInfo />
    <template v-for="item in detailInfo">
      <el-card :key="item.id" class="box-card">
        <div slot="header" class=" box-card-cus">
          <span>{{ dict.label.announcement_type[item.fv3] }}</span>
          <div>
            <el-button style="padding: 3px 0;margin-right: 10px;" type="text" @click="goSpider(item)">原文链接</el-button>
          </div>
        </div>
        <!--固定好的字段信息-->
        <div class="text item">
          <el-form ref="currentProject" :model="currentProject" :rules="elRule" label-width="130px">
            <el-row :gutter="20">
              <template v-for="filedItem in field[item.fv3].fields">
                <el-col :key="filedItem.id" :span="12">
                  <el-form-item :label="`${filedItem.label}:`" :prop="filedItem.value">
                    <el-input v-model="item[filedItem.value]" :disabled="item.value == 'fv1'" style="width: 50%;" />
                  </el-form-item>
                </el-col>
              </template>
            </el-row>
          </el-form>
          <fm-generate-form
            v-if="showFormData"
            :key="item.fv3"
            :ref="`${item.fv3}generateForm`"
            :data="formStruct"
            :preview="viewOrEdit"
            :remote="remoteFunc"
            :value="item.ft2"
          />
        </div>
        <!--补充的字段信息-->
        <el-descriptions :column="Column" size="medium">
          <el-descriptions-item
            v-for="otherfiledItem in item.ft2"
            :key="otherfiledItem.id"
            :label="otherfiledItem.label"
            :label-style="{width: '130px',textAlign: 'right'}"
          >
            {{ otherfiledItem.value }}
          </el-descriptions-item>
        </el-descriptions>
        <!--附件信息-->
        <el-descriptions :column="Column" size="medium">
          <el-descriptions-item :label-style="{width: '130px',textAlign: 'right'}" label="附件信息">
            <div class="attachment-list">
              <p v-for="subItem in item.attachment" :key="subItem.name" @click="downLoadAttach(subItem)">
                <el-link type="success">
                  {{ subItem.name }}
                </el-link>
              </p>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </template>

    <el-row>
      <el-button type="primary" @click="handleSave">保存</el-button>
      <el-button type="primary" @click="handleCancel">取消</el-button>
    </el-row>

  </div>
</template>

<script>
import Vue from 'vue'
import { GenerateForm } from '@/components/VueFormMaking'
import 'form-making/dist/FormMaking.css'

Vue.component(GenerateForm.name, GenerateForm)
import spiderArticleApi from '@/api/spider/spiderArticle'
import { downloadUrl } from '@/utils/index'
import { field } from '@/views/spider/utils/field';
import { mapGetters } from 'vuex';
import extendTpl from '@/api/system/extendTpl.js'
import { getToken } from '@/utils/auth';
import { affirm } from '@/api/spider/spiderReadAttention';
import BaseInfo from '@/views/spider/spiderArticle/components/baseInfo.vue';

const Column = 2
export default {
  name: 'ConfirmInfo',
  components: { BaseInfo },
  dicts: ['announcement_type'],
  data() {
    return {
      detailInfo: [],
      Column,
      field,
      elRule: {},
      formStruct: {},
      processStructureValue: {},
      formData: {},
      remoteFunc: {
        funcGetToken(resolve) {
          resolve(getToken())
        }
      },
      viewOrEdit: false,
      showFormData: false,
      correctionFormVisible: false,
      detailType: 1
    };
  },
  computed: {
    filedShowText() {
      return function(value) {
        if (value === null || value === undefined || value === 'null') {
          return ''
        }
        return value
      }
    },
    ...mapGetters([
      'currentProject',
      'projectLabels',
      'user'
    ])
  },
  created() {
    this.getProcessNodeList()
    this.getDetail()
  },
  methods: {
    getDetail() {
      const query = {
        fv1: this.currentProject.projectName,
        enabled: 1,
        sort: 'fv11,desc'
      }
      spiderArticleApi.get(query).then(res => {
        const data = res.content || [];
        this.detailInfo = data.map(item => {
          item.attachment = JSON.parse(item.formData)?.attachment || [];
          item.ft2 = item.ft2 ? JSON.parse(item.ft2).custom : [];
          return item
        })
      })
    },
    // 获取模板
    getProcessNodeList() {
      this.loading = true;
      const { otherInfo } = this.$config['custom_key']
      const data = { id: otherInfo, enabled: 1 }
      extendTpl.get(data).then(res => {
        if (res && res.content && res.content.length) {
          this.processStructureValue = res.content[0];
          const FormStruct = JSON.parse(this.processStructureValue.formStruct);
          this.formStruct = FormStruct;
        }
        this.showFormData = true;
      }).finally(() => {
        this.loading = false;
      });
    },
    goSpider(data) {
      window.open(data.fv4, '_blank');
    },
    downLoadAttach(item) {
      downloadUrl(item.href, item.name)
    },
    handleCancel() {
      this.$router.go(-1);
    },
    async handleSave() {
      try {
        const generateData = await this.submitGenerateForm();
        const editPromises = generateData.map(item => {
          return spiderArticleApi.edit(item);
        });
        await Promise.all(editPromises);
        this.infoSure();
        console.log('All items edited successfully.');
      } catch (error) {
        this.failedEdit()
      }
    },
    // 提交获取模板数据
    async submitGenerateForm() {
      const data = JSON.parse(JSON.stringify(this.detailInfo));
      try {
        const newData = await Promise.all(data.map(async item => {
          const values = await this.$refs[`${item.fv3}generateForm`][0].getData();
          const subData = JSON.stringify(values);
          item.ft2 = subData;
          return {
            ...item
          };
        }));
        return newData;
      } catch (error) {
        this.failedEdit()
      }
    },

    infoSure() {
      const data = {
        fv1: this.currentProject.projectName,
        enabled: 1,
        fv2: this.user.username
      }
      affirm(data).then(res => {
        this.successEdit()
      })
    },
    successEdit() {
      this.$notify({
        title: '操作成功',
        type: 'success',
        duration: 2500
      })
      this.handleCancel()
    },
    failedEdit() {
      this.$notify({
        title: '操作失败',
        type: 'error',
        duration: 2500
      })
    }
  }
};
</script>

<style lang="scss" scoped>
.spider-detail {
	padding-left: 40px;

	.box-card {
		margin-bottom: 20px;

		.box-card-cus {
			padding: 0 16px;
			display: flex;
			justify-content: space-between;
		}
	}
}

.attachment-list {
	display: flex;
	flex-direction: column;
}

</style>
