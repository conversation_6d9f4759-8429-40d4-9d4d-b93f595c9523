<template>
  <div class="app-container spider-detail">
    <!--项目基础信息-->
    <BaseInfo />
    <template v-for="item in detailInfo">
      <el-card :key="item.id" class="box-card">
        <div slot="header" class=" box-card-cus">
          <span>{{ dict.label.announcement_type[item.fv3] }}</span>
          <div>
            <el-button style="padding: 3px 0;margin-right: 10px;" type="text" @click="goSpider(item)">原文链接</el-button>
            <el-button style=" padding: 3px 0" type="text" @click="updateInfo(item)">更正/补充</el-button>
          </div>
        </div>
        <!--固定好的字段信息-->
        <el-descriptions :column="Column" size="medium">
          <el-descriptions-item v-for="filedItem in field[item.fv3].fields" :key="filedItem.id" :label="filedItem.label">
            {{ filedShowText(item[filedItem.value]) }}
          </el-descriptions-item>
        </el-descriptions>
        <!--补充的字段信息-->
        <el-descriptions :column="Column" size="medium">
          <el-descriptions-item v-for="otherfiledItem in item.ft2" :key="otherfiledItem.id" :label="otherfiledItem.label">
            {{ otherfiledItem.value }}
          </el-descriptions-item>
        </el-descriptions>
        <!--附件信息-->
        <el-descriptions :column="Column" size="medium">
          <el-descriptions-item label="附件信息">
            <div class="attachment-list">
              <p v-for="subItem in item.attachment" :key="subItem.name" @click="downLoadAttach(subItem)">
                <el-link type="success">
                  {{ subItem.name }}
                </el-link>
              </p>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </template>

    <template v-if="detailType == 2">
      <el-card class="box-card">
        <div slot="header" class=" box-card-cus">
          <span>项目已有标签</span>
        </div>
        <!--固定好的字段信息-->
        <el-descriptions :column="1" size="medium">
          <!--<el-descriptions-item v-for="filedItem in field[item.fv3].fields" :key="filedItem.id" :label="filedItem.label">{{ filedShowText(item[filedItem.value]) }}</el-descriptions-item>-->
        </el-descriptions>
        <!--补充的字段信息-->
        <label-tree-tranfer :init-label="currentProject.label" @saveLabel="saveLabelPre" />
      </el-card>
    </template>
    <!--其余信息表单更正确认弹框-->
    <correction-form v-if="correctionFormVisible" ref="correctionFormRef" @success="successEdit" />
  </div>
</template>

<script>
import spiderArticleApi from '@/api/spider/spiderArticle'
import { downloadUrl } from '@/utils/index'
import { field, baseProjectFiled } from '@/views/spider/utils/field';
import CorrectionForm from '@/views/spider/spiderArticle/components/correctionForm.vue';
import LabelTreeTranfer from '@/views/spider/spiderArticle/components/labelTreeTranfer.vue';
import BaseInfo from '@/views/spider/spiderArticle/components/baseInfo.vue';
import { mapGetters } from 'vuex';
import spiderLabel from '@/api/spider/spiderLabel';

const Column = 2
export default {
  name: 'SpiderWebAdminDetail',
  dicts: ['announcement_type'],
  components: {
    CorrectionForm,
    LabelTreeTranfer,
    BaseInfo
  },
  data() {
    return {
      detailInfo: [],
      Column,
      field,
      baseProjectFiled,
      correctionFormVisible: false,
      detailType: 1
    };
  },
  computed: {
    filedShowText() {
      return function(value) {
        if (value === null || value === undefined || value === 'null') {
          return ''
        }
        return value
      }
    },
    ...mapGetters([
      'currentProject',
      'projectLabels'
    ])
  },
  created() {
    this.detailType = this.$route.query.detailType
    this.getDetail()
  },
  methods: {
    // 获取详情
    getDetail() {
      const query = {
        fv1: this.currentProject.projectName,
        enabled: 1,
        sort: 'fv11,desc'
      }
      spiderArticleApi.get(query).then(res => {
        const data = res.content || [];
        this.detailInfo = data.map(item => {
          item.attachment = JSON.parse(item.formData)?.attachment || [];

          item.ft2 = item.ft2 ? JSON.parse(item.ft2).custom : [];
          return item
        })
        if (this.detailType == 2) {
          this.detailInfo.length = 1
        }
      })
    },
    goSpider(data) {
      window.open(data.fv4, '_blank');
    },
    downLoadAttach(item) {
      downloadUrl(item.href, item.name)
    },
    async updateInfo(item) {
      this.correctionFormVisible = true
      await this.$nextTick();
      this.$refs.correctionFormRef.initData(item, field)
    },
    successEdit() {
      this.correctionFormVisible = false;
      this.getDetail()
    },
    saveLabelPre(data) {
      let messageContent = '确定将当前项目从标签中移除吗？'
      let confirmType = 'warning';
      if (data.length > 0) {
        const names = data.map((item, index) => `<span>${item.label}${index !== data.length - 1 ? '、' : ''}</span>`).join(''); // 遍历数组对象，构建 HTML 字符串
        messageContent = `<div>${names}</div>`; // 构建包含所有 name 值的 HTML 内容
        confirmType = 'info';
        this.showConfirm(messageContent, '保存标签', confirmType, () => {
          this.saveLabel(data);
        });
      } else {
        this.showConfirm(messageContent, '提示', confirmType, () => {
          this.saveLabel(data);
        });
      }
    },
    showConfirm(messageContent, title, type, callback) {
      this.$confirm(messageContent, title, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: type,
        dangerouslyUseHTMLString: true // 使用 HTML 字符串
      }).then(callback);
    },
    saveLabel(info) {
      const data = {
        ft1: JSON.stringify(info),
        enabled: 1,
        fv1: this.currentProject.projectName
      }
      spiderLabel.rewriteEdit(data).then(res => {
        this.$message.success('保存成功');
      })
    }
  }
};
</script>

<style lang="scss" scoped>
.spider-detail {
	padding-left: 40px;

	.box-card {
		margin-bottom: 20px;

		.box-card-cus {
			padding: 0 16px;
			display: flex;
			justify-content: space-between;
		}
	}
}

.attachment-list {
	display: flex;
	flex-direction: column;
}

</style>
