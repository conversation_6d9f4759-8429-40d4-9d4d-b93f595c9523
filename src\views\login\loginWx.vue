<template>
  <div :style="'background-image:url(' + Background + ');'" class="login">

    <div class="login-center">
      <img
        v-show="isShowLoginType"
        :src="require(`@/assets/images/${loginType[currentLoginType].img}.png`)"
        alt=""
        class="login-type"
        @click="clickLoginType"
      >
      <h3 class="login-title">
        {{ title }}
      </h3>
      <div class="login-box">
        <account-login v-show="currentLoginType === 'account'" />
        <code-login v-show="currentLoginType === 'code'" />
      </div>
    </div>
    <!--  底部  -->
    <div v-if="$store.state.settings.showFooter" id="el-login-footer">
      <span v-html="$store.state.settings.footerTxt" />
      <span> ⋅ </span>
      <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">{{
        $store.state.settings.caseNumber
      }}</a>
    </div>
  </div>
</template>

<script>
import Background from '@/assets/images/background.jpg';
import settings from '../../settings';
import AccountLogin from '@/views/login/components/AccountLogin.vue';
import CodeLogin from '@/views/login/components/CodeLogin.vue';

export default {
  name: 'Login',
  components: {
    AccountLogin,
    CodeLogin
  },
  data() {
    return {
      Background: Background,
      title: settings.title,
      currentLoginType: 'code',
      loginType: {
        code: {
          type: 'code',
          name: '扫码登录',
          img: 'codeLogin'
        },
        account: {
          type: 'account',
          name: '账号登录',
          img: 'accountLogin'
        }
      },
      isShowLoginType: true
    };
  },
  created() {
    this.init()
  },
  methods: {
    clickLoginType() {
      this.currentLoginType == 'code' ? this.currentLoginType = 'account' : this.currentLoginType = 'code'
    },
    init() {
      if (process.env.NODE_ENV === 'production') {
        this.isShowLoginType = false
        this.currentLoginType = 'code'
      } else {
        this.isShowLoginType = true
        this.currentLoginType = 'account'
      }
    }
  }
};
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.login {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100%;
	background-size: cover;
	position: relative;
	overflow: hidden;

	.login-center {
		position: absolute;
		top: 50%;
		left: 50%;
		width: 380px;
		height: 380px;
		transform: translate(-50%, -50%);
		display: inline-block;
		background: #fff;
		padding: 0 30px;
		box-shadow: 0 1px 12px 0 rgba(0, 0, 0, .2);
		border-radius: 6px;
		box-sizing: border-box;

		.login-type {
			position: absolute;
			width: 48px;
			height: 48px;
			right: 8px;
			top: 8px;
			clip-path: polygon(0 0, 100% 0, 100% 100%,)
		}

		.login-title {
			margin: 20px auto 0;
			font-size: 20px;
			text-align: center;
			color: #707070;
		}

		.login-box {
			margin-top: 30px;
			//display: flex;
			//justify-content: space-around;
			overflow: hidden;

			//.prn-line {
			//	width: 1px;
			//	height: 300px;
			//	background: #e5e5e5;
			//	margin: 0 30px;
			//}
		}
	}

	#el-login-footer {
		position: absolute;
		bottom: 20px;
		left: 50%;
		transform: translateX(-50%);
		font-size: 12px;
		color: #707070;
		display: flex;
	}
}

</style>
