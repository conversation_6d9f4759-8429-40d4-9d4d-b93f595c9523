<template>
  <div>
    <!-- <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :before-close="concelForm"
      :visible.sync="visible"
      :title="title"
      width="1200px"
      class="scrollable-dialog"
    > -->
    <div>
      <!-- <h3 class="date-title">{{ currentData.text }}周报</h3> -->
      <el-row :gutter="20">
        <!--项目数据-->
        <el-col :lg="14" :md="14" :sm="14" :xl="14" :xs="14">
          <div class="week-box">
            <div class="con-box">
              <h4 v-if="thisWeeklyTypeLabel">【{{ thisWeeklyTypeLabel }}】</h4>
              <template>
                <ul v-if="weekThisList && weekThisList.length" class="week-list">
                  <li v-for="(item,index) in weekThisList" :key="index">
                    <p style="font-weight: 500;margin-bottom: 8px;" v-html="item.fv7+'：'" />
                    <p style="white-space: pre-wrap;color:#444" v-html="item.fv8" />
                  </li>
                </ul>
                <p v-else style="font-size: 16px;margin-left:10px;">无</p>
              </template>
              <div v-for="(item,index) in weekOther" :key="index" class="need-coordinate">
                <h4 v-if="needCoordinate">【{{ needCoordinate }}】</h4>
                <p style="white-space: pre-wrap;color:#444">{{ item.fv8 }}</p>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :lg="8" :md="8" :sm="8" :xl="8" :xs="8">
          <div class="week-box">
            <div class="con-box">
              <h4 v-if="nextWeeklyTypeLabel">【{{ nextWeeklyTypeLabel }}】</h4>
              <template>
                <ul v-if="weekNextList && weekNextList.length" class="week-list">
                  <li v-for="(item,index) in weekNextList" :key="index">
                    <p style="font-weight: 500;margin-bottom: 8px;" v-html="item.fv7+'：'" />
                    <p style="white-space: pre-wrap;color:#444" v-html="item.fv8" />
                  </li>
                </ul>
                <p v-else style="font-size: 16px;margin-left:10px;">无</p>
              </template>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <!-- <div slot="footer" class="dialog-footer" style="text-align: center;">
      <el-button type="primary" @click="concelForm(false)">关闭</el-button>
    </div> -->
    <!-- </el-dialog> -->
  </div>
</template>

<script>
import { get } from '@/api/oaWorkOrder/oaPmWeeklyReport'

export default {
  dicts: ['weekly_work_classify', 'weekly_type'],
  // props: {
  //   weekData: {
  //     type: Object,
  //     required: true
  //   }
  // },
  data() {
    return {
      visible: false,
      title: '查看周报',
      currentData: {},
      weekNextList: [],
      weekThisList: [],
      weekOther: []
    }
  },
  computed: {
    thisWeeklyTypeLabel() {
      if (this.dict && this.dict.weekly_type && this.dict.weekly_type.length > 0) {
        return this.dict.weekly_type[0].label;
      }
      return ''; // 本周工作标题
    },
    nextWeeklyTypeLabel() {
      if (this.dict && this.dict.weekly_type && this.dict.weekly_type.length > 0) {
        return this.dict.weekly_type[1].label;
      }
      return ''; // 下周计划标题
    },
    needCoordinate() {
      if (this.dict && this.dict.weekly_type && this.dict.weekly_type.length > 0) {
        return this.dict.weekly_type[2].label;
      }
      return ''; // 需要协调的问题标题
    }
  },
  created() {
  },
  methods: {
    init(info) {
      this.bindId = this.$config['pm_weekly']?.bindId;
      this.visible = true;
      this.currentData = info;
      if (this.currentData) {
        this.getContent(this.currentData);
      }
    },
    getContent(info) {
      const json = {
        page: 0,
        size: 999999,
        // sort: 'id,desc',
        enabled: 1,
        bindId: this.bindId,
        fv1: this.$route.query.name,
        pmId: this.$route.query.projectId, // 项目id
        fv3: info.s,
        fv4: info.e
      }
      get(json).then(res => {
        if (res && res.content && res.content.length) {
          const weeklyList = res.content;
          this.weekNextList = weeklyList.filter(item => item.fv6 == this.nextWeeklyTypeLabel);
          this.weekThisList = weeklyList.filter(item => item.fv6 == this.thisWeeklyTypeLabel);
          this.weekOther = weeklyList.filter(item => item.fv6 == this.needCoordinate);
        } else {
          this.weekNextList = [];
          this.weekThisList = [];
          this.weekOther = [];
        }
      });
    },

    concelForm(action, data) {
      this.visible = false;
      this.currentData = {};
      this.weekNextList = [];
      this.weekThisList = [];
      this.weekOther = [];
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
::v-deep .el-input-number .el-input__inner {
  text-align: left;
}
.date-title{
  font-size: 22px;
  font-weight: bold;
  margin-bottom:15px;
  color:#000;
  text-align: center;
}
.week-box{
  .title{
    font-size: 18px;font-weight: bold;margin-bottom:15px;
  }
  .con-box{
    color:#000;
    margin-bottom:20px;
    >h4{
      margin-bottom: 10px;
      font-size: 16px;
    }
    .week-list{
      >li{
        margin-bottom:20px;
        p{
          line-height: 30px;
          font-size: 16px;
          padding-left: 14px;
        }
      }
    }
    .need-coordinate{
      margin-bottom:8px;
      >h4{
        margin-bottom: 10px;
        font-size: 16px;
      }
      >p{
        line-height: 30px;padding-left: 14px;font-size: 16px;
      }
    }

  }
}

</style>
