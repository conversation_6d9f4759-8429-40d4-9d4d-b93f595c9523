<template>
  <div>
    <el-dialog
      :before-close="handleClose"
      :close-on-click-modal="false"
      :visible.sync="uploadVisible"
      append-to-body
      title="上传文件"
      width="500px"
    >
      <el-form ref="form" :model="uploadData" :rules="rules" label-width="80px" size="small">
        <el-form-item label="表头行数" prop="objectType">
          <el-input
            v-model.number="uploadData.objectType"
            oninput="value=value.replace(/[^0-9]/g,'')"
            style="width: 370px;"
          />
        </el-form-item>
        <!--   上传文件   -->
        <el-form-item label="上传文件">
          <el-upload
            ref="upload"
            :action="baseApi+'/api/omAssetAffiliated/import/xls/rule'"
            :auto-upload="false"
            :before-upload="beforeUpload"
            :data="uploadData"
            :headers="headers"
            :limit="1"
            :on-error="handleError"
            :on-success="handleSuccess"
          >
            <div class="eladmin-upload"><i class="el-icon-upload" /> 添加文件</div>
            <div slot="tip" class="el-upload__tip">可上传exel文件，且不超过10M</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="handleClose">取消</el-button>
        <el-button :loading="loading" type="primary" @click="upload">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { getToken } from '@/utils/auth';

export default {
  name: 'UploadExcel',
  data() {
    return {
      uploadVisible: false,
      loading: false,
      uploadData: {
        objectType: 1,
        objectId: '',
        objectData: '' // 分类
      },
      rules: {
        objectType: [
          { required: true, message: '请输入行数', trigger: ['blur', 'change'] }
        ],
        objectData: [
          { required: true, message: '请选择部件分类', trigger: ['blur', 'change'] }
        ]
      },
      headers: { 'Authorization': getToken() },
      assetsList: []
    }
  },
  computed: {
    ...mapGetters([
      'baseApi'
    ])
  },
  methods: {
    init(data) {
      this.uploadVisible = true;
      const { bindId, categoryId } = data;
      this.uploadData.objectId = bindId;
      this.uploadData.objectData = categoryId;
    },
    handleClose() {
      this.uploadVisible = false;
      this.$refs.upload.clearFiles();
      setTimeout(() => {
        Object.assign(this.$data, this.$options.data.call(this));
      }, 200)
    },
    upload() {
      this.$refs.upload.submit();
    },
    beforeUpload(file) {
      let isLt2M = true
      console.log(file, '<===>', 'file')
      isLt2M = file.size / 1024 / 1024 < 50
      if (!isLt2M) {
        this.loading = false
        this.$message.error('上传文件大小不能超过 50MB!')
      }
      return isLt2M;
    },
    handleSuccess(response, file) {
      // this.crud.notify('上传成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      // this.$refs.upload.clearFiles()
      this.handleClose();
    },
    // 监听上传失败
    handleError(e, file, fileList) {
      const msg = JSON.parse(e.message)
      this.$notify({
        title: msg.message,
        type: 'error',
        duration: 2500
      })
      this.loading = false
    }
  }
}
</script>

<style>

</style>
