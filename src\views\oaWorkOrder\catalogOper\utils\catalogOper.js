import oaPmTree from '@/api/oaWorkOrder/oaPmTree'
import Vue from 'vue';

export const permission = {
  del: ['admin', 'oaPmCatalogRelation:del'],
  add: ['admin', 'oaPmCatalogRelation:add'],
  edit: ['admin', 'oaPmCatalogRelation:edit'],
  updateT: ['admin', 'oaPmCatalogRelation:updateFormStruct'],
  updateR: ['admin', 'oaPmCatalogRelation:updateRelation']
}

export function formatterTableData(val) {
  let tableData = [];
  if (!val && !val.length) {
    return tableData
  }
  const mapItem = (item) => {
    return {
      ...item
    };
  };
  tableData = val.map(mapItem);
  return tableData
}

export function formatterTableHeader(val) {
  const tableHeader = [
    // { prop: 'status', label: '是否执行', align: 'center' },
    { prop: 'createBy', label: '创建人', align: 'center' },
    { prop: 'createTime', label: '创建时间', align: 'center' }
  ]
  const otherHeader = [];
  return [...tableHeader, ...otherHeader];
}

export function findSelectList(arr, val) {
  let ft1 = []
  const obj = arr.find(item => item.id === val)
  if (obj) {
    ft1 = JSON.parse(obj.ft1)
  }
  console.log(ft1, '<===>', 'ft1')
  return ft1
}

export function findSelectObj(arr, val) {
  const obj = arr.find(item => item.indexSort === val)
  return obj || {} // 防止未找到时返回 undefined
}

/**
 *
 */

export async function getAllproject() {
  const query = {
    fv1: ['项目', '子项目'],
    enabled: 1,
    pidIsNull: true,
    sort: ['createTime,desc'],
    size: 9999,
    bindId: Vue.prototype.$config.projects_keys.bindId
  }
  const queryResult = await oaPmTree.getPmTreeSmall(query)
  return queryResult.content || []
}

/**
 * 设置数组对象指定属性的值
 * @param array
 * @param props
 * @returns {*}
 */
export function clearArrObjProps(array, props) {
  array.forEach((item, index) => {
    props.forEach(prop => {
      Vue.set(item, prop, '');
    });
  });
  return array;
}

/**
 *
 * @param array
 * @param direction 'toNew' or 'toOld'
 * @returns {*}
 */
export function transformFt1(array, direction) {
  const mappings = {
    oldIndexSort: 'o1',
    oldName: 'o2',
    newIndexSort: 'n1',
    newName: 'n2'
  };

  return array.map(item => {
    const transformedItem = {};
    for (const [oldKey, newKey] of Object.entries(mappings)) {
      if (direction === 'toNew') {
        transformedItem[newKey] = item[oldKey];
      } else {
        transformedItem[oldKey] = item[newKey];
      }
    }
    return transformedItem;
  });
}

/**
 * 选项禁用
 * @param selectedIds
 * @param arr
 * @returns {*}
 */
export function updateOptionsDisabled(selectedIds, arr) {
  return arr.map(option => {
    if (selectedIds.includes(option.id)) {
      return {
        ...option,
        disabled: true
      };
    }
    return option;
  });
}

export function updateOptionsDisabledFalse(arr) {
  return arr.map(option => {
    return {
      ...option,
      disabled: false
    };
  });
}
