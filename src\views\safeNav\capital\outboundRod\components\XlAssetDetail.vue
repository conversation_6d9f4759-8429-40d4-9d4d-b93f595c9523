<template>
  <el-dialog
    v-dialog-drag="{minHeight:'800px'}"
    :before-close="cancel"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :modal="false"
    :modal-append-to-body="false"
    :title="fullTitle"
    :visible.sync="visible"
    append-to-body
    width="800px"
  >
    <el-form ref="form" label-position="left" label-width="90px">
      <el-row :gutter="20" class="asset-detail-top">
        <el-col :span="12">
          <el-form-item label="点位编号:">
            {{ detail.fv4 }}
          </el-form-item>
          <el-form-item label="立杆位置:">
            {{ detail.title }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="no-label" label="">
            <el-button size="mini" type="success" @click="toInspects()">巡检记录</el-button>
          </el-form-item>
          <el-form-item label="最近巡检:">
            {{ detail.fv19 }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" class="asset-detail-top">
        <el-col :span="24">
          <el-form-item label="位置" />
        </el-col>
        <!--<template v-for="item in detail.location">-->
        <el-col :span="12">
          <el-form-item label="经度:">
            {{ detail.location.fv2 }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="维度:">
            {{ detail.location.fv3 }}
          </el-form-item>
        </el-col>
        <!--</template>-->

      </el-row>
      <el-row :gutter="20" class="asset-detail-top">
        <el-col :span="12">
          <el-form-item label="派出所:">
            {{ detail.fv8 }}
          </el-form-item>
          <el-form-item label="总摄像头数:">
            {{ detail.fv7 }}
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-table
      ref="table"
      :data="detail.detail"
      class="table-fixed"
      row-key="id"
    >
      <el-table-column
        v-for="(item) in tableHeaderDetail"
        :key="item.prop"
        :align="item.align"
        :fixed="item.fixed || false"
        :label="item.label"
        :min-width="item.minWidth"
        :prop="item.prop"
        :width="item.width"
      >
        <template slot-scope="scope">
          <template v-if="item.prop == 'filePre'">
            <file-thumb
              :file-ext="scope.row.fileExt"
              :preview-list="[scope.row.url]"
              :url="scope.row.thUrl"
              @preView="preView"
            />
          </template>
          <span v-else>{{ scope.row[item.prop] }}</span>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script>
import crudTable from '@/api/parts/assets'
import {
  tableHeaderDetail
} from '@/views/xl/xlAsset/utils/field'

export default {
  name: 'XlAssetDetail',

  props: {},
  data() {
    return {
      visible: false,
      fullTitle: '点位信息',
      tableHeaderDetail,
      detail: {
        location: {}
      },
      currentId: null
    }
  },
  computed: {},
  watch: {},
  created() {
  },
  methods: {
    init(row) {
      this.visible = true
      const { fv4, bindId, categoryId } = row
      const query = {
        fv4,
        bindId,
        categoryId,
        enabled: 1,
        size: 999
      }
      crudTable.getDetailWithX(query).then(res => {
        const data = res.content[0] || {}
        this.detail = data
        this.detail.detail = JSON.parse(data.detail)
        this.detail.location = JSON.parse(data.location)
        console.log(this.detail, '<===>', 'this.detail')
      })
    },

    // 获取详情信息
    cancel() {
      this.visible = false
    },
    toInspects() {
      this.$router.push({
        name: 'XlInspects',
        query: {
          fv4: this.detail.fv4,
          omAssetID: this.detail.id,
          omAssetTitle: this.detail.title
        }
      })
    }
  }
}
</script>
<style>
.asset-detail-top .no-label .el-form-item__content {
	margin-left: 0 !important; /* 确认无单位 */
}
</style>

<style lang="scss" rel="stylesheet/scss">
.asset-detail-top {
	width: 100%;

	.el-form-item {
		margin-bottom: 10px;
	}

	.asset-detail-point {
		margin-right: 20px;
	}

	.asset-detail-button {

	}

	.no-label {
		::v-deep .el-form-item__label {
			display: none;
		}
	}

}

.table-fixed {
	margin-top: 20px;
}
</style>
