// 项目列表所有权限
export const permission = {
  del: ['admin', 'amStockIn:del'],
  edit: ['admin', 'amStockIn:edit'],
  updateT: ['admin', 'amStockIn:updateFormStruct'],
  updateR: ['admin', 'amStockIn:updateRelation']
}

// 入库类型映射
export const orderTypeMap = {
  1: '新设备',
  2: '拆回',
  3: '维修返回',
  4: '后期接收'
}
// 项目列表表头
export const tableHeader = [
  { label: '资产编号', prop: 'basicNo', fixed: 'left', headerAlign: 'left', align: 'left', width: 200 },
  { label: '订单编号', prop: 'orderNo', fixed: 'left', headerAlign: 'left', align: 'left', width: 200 },
  { label: '入库类型', prop: 'orderType', align: 'left', headerAlign: 'left' },
  { label: '项目', prop: 'pmName', align: 'left', headerAlign: 'left', width: 200 },
  { label: '库房', prop: 'depotTitle', headerAlign: 'left', align: 'left' },
  { label: '数量', prop: 'amount', align: 'left', headerAlign: 'left' },
  { label: '单价(元)', prop: 'showPrice', align: 'left', headerAlign: 'left' },
  { label: '设备名称', prop: 'deviceName', align: 'left', headerAlign: 'left' },
  { label: '品牌', prop: 'brandName', align: 'left', headerAlign: 'left' },
  { label: '型号', prop: 'modelName', align: 'left', headerAlign: 'left' },
  { label: '入库时间', prop: 'inDate', align: 'left', headerAlign: 'left' },
  { label: '入库人', prop: 'createBy', align: 'left', headerAlign: 'left' }
]

// 定义按钮组
export const updateButtonsLists = [
  {
    label: '导出入库数据',
    icon: 'download',
    type: 'primary',
    size: 'mini',
    fun: 'showExportDialog',
    permission: ['admin']
  }
]

