export const carStatus1 = {
  '车网': {
    '1': {
      'status': ['完成'],
      'key': '车网数据回传',
      'tipTitle': '车网数据回传(完成)',
      'color': 'autoPilot3Red'
    },
    '2': {
      'status': ['施工中'],
      'key': '车网数据回传',
      'tipTitle': '车网数据回传(施工中)',
      'color': 'autoPilot3Purple'
    },
    '3': {
      'status': ['完成', '施工中'],
      'key': '杆体通网',
      'tipTitle': '杆体通网(完成+施工中)',
      'color': 'autoPilot3Success'
    },
    '4': {
      'status': ['完成'],
      'key': '进度-已通电',
      'tipTitle': '进度-已通电(完成)',
      'color': 'autoPilot3Yellow'
    },
    'other': {
      'status': ['机场', '未开始'],
      'key': '',
      'tipTitle': '机场、未开始路口',
      'color': 'autoPilot3Grey'
    }
  },
  '京智网': {
    '1': {
      'status': [
        '完成'
      ],
      'tipTitle': '京智网数据回传(完成)',
      'key': '京智网数据回传',
      'color': 'autoPilot3Red'
    },
    '2': {
      'status': [
        '施工中'
      ],
      'tipTitle': '京智网数据回传(施工中)',
      'key': '京智网数据回传',
      'color': 'autoPilot3Purple'
    },
    '3': {
      'status': [
        '完成',
        '施工中'
      ],
      'tipTitle': '杆体通网(完成+施工中)',
      'key': '杆体通网',
      'color': 'autoPilot3Success'
    },
    '4': {
      'status': [
        '完成'
      ],
      'tipTitle': '进度-已通电(完成)',
      'key': '进度-已通电',
      'color': 'autoPilot3Yellow'
    },
    'other': {
      'status': [
        '机场',
        '未开始'
      ],
      'tipTitle': '机场、未开始路口',
      'key': '',
      'color': 'autoPilot3Grey'
    }
  }
}
export const carStatus = {
  '车网': [
    {
      'status': ['完成'],
      'key': '车网数据回传',
      'tipTitle': '车网数据回传(完成)',
      'sort': 1,
      'finalStatus': '',
      'color': 'autoPilot3Red'
    },
    {
      'status': ['施工中'],
      'key': '车网数据回传',
      'finalStatus': '',
      'tipTitle': '车网数据回传(施工中)',
      'sort': 2,
      'color': 'autoPilot3Purple'
    },
    {
      'status': ['完成', '施工中'],
      'key': '杆体通网',
      'finalStatus': '',
      'sort': 3,
      'tipTitle': '杆体通网(完成+施工中)',
      'color': 'autoPilot3Success'
    },
    {
      'status': ['完成'],
      'key': '进度-已通电',
      'finalStatus': '',
      'sort': 4,
      'tipTitle': '进度-已通电(完成)',
      'color': 'autoPilot3Yellow'
    },
    {
      'status': ['机场', '未开始'],
      'key': '',
      'finalStatus': '',
      'sort': 5,
      'tipTitle': '机场、未开始路口',
      'color': 'autoPilot3Grey'
    }
  ],
  '京智网': [
    {
      'status': [
        '完成'
      ],
      'finalStatus': '',
      'sort': 1,
      'tipTitle': '京智网数据回传(完成)',
      'key': '京智网数据回传',
      'color': 'autoPilot3Red'
    },
    {
      'status': [
        '施工中'
      ],
      'finalStatus': '',
      'sort': 2,
      'tipTitle': '京智网数据回传(施工中)',
      'key': '京智网数据回传',
      'color': 'autoPilot3Purple'
    },
    {
      'status': [
        '完成',
        '施工中'
      ],
      'finalStatus': '',
      'sort': 3,
      'tipTitle': '杆体通网(完成+施工中)',
      'key': '杆体通网',
      'color': 'autoPilot3Success'
    },
    {
      'status': [
        '完成'
      ],
      'finalStatus': '',
      'sort': 4,
      'tipTitle': '进度-已通电(完成)',
      'key': '进度-已通电',
      'color': 'autoPilot3Yellow'
    },
    {
      'status': [
        '机场',
        '未开始'
      ],
      'finalStatus': '',
      'sort': 5,
      'tipTitle': '机场、未开始路口',
      'key': '',
      'color': 'autoPilot3Grey'
    }
  ]
}
export const carStatusNew = {
  '数基建设备': [
    {
      'status': ['全部上线'],
      'key': '状态-数基建设备',
      'tipTitle': '状态-数基建设备(全部上线)',
      'sort': 1,
      'finalStatus': '',
      'color': 'autoPilot3Purple'
    },
    {
      'status': ['部分上线'],
      'key': '状态-数基建设备',
      'finalStatus': '',
      'tipTitle': '状态-数基建设备(部分上线)',
      'sort': 2,
      'color': 'autoPilot3Success'
    },
    {
      'status': ['离线'],
      'key': '状态-数基建设备',
      'finalStatus': '',
      'sort': 3,
      'tipTitle': '状态-数基建设备(离线)',
      'color': 'autoPilot3Red'
    },
    {
      'status': ['未上线'],
      'key': '状态-数基建设备',
      'finalStatus': '',
      'sort': 4,
      'tipTitle': '状态-数基建设备(未上线)',
      'color': 'autoPilot3Grey'
    }
  ],
  '车网设备': [
    {
      'status': ['全部上线'],
      'key': '状态-车网设备',
      'tipTitle': '状态-车网设备(全部上线)',
      'sort': 1,
      'finalStatus': '',
      'color': 'autoPilot3Purple'
    },
    {
      'status': ['部分上线'],
      'key': '状态-车网设备',
      'finalStatus': '',
      'tipTitle': '状态-车网设备(部分上线)',
      'sort': 2,
      'color': 'autoPilot3Success'
    },
    {
      'status': ['离线'],
      'key': '状态-车网设备',
      'finalStatus': '',
      'sort': 3,
      'tipTitle': '状态-车网设备(离线)',
      'color': 'autoPilot3Red'
    },
    {
      'status': ['未上线'],
      'key': '状态-车网设备',
      'finalStatus': '',
      'sort': 4,
      'tipTitle': '状态-车网设备(未上线)',
      'color': 'autoPilot3Grey'
    }
  ],
  '京智网设备': [
    {
      'status': ['全部上线'],
      'key': '状态-京智网设备',
      'tipTitle': '状态-京智网设备(全部上线)',
      'sort': 1,
      'finalStatus': '',
      'color': 'autoPilot3Purple'
    },
    {
      'status': ['部分上线'],
      'key': '状态-京智网设备',
      'finalStatus': '',
      'tipTitle': '状态-京智网设备(部分上线)',
      'sort': 2,
      'color': 'autoPilot3Success'
    },
    {
      'status': ['离线'],
      'key': '状态-京智网设备',
      'finalStatus': '',
      'sort': 3,
      'tipTitle': '状态-京智网设备(离线)',
      'color': 'autoPilot3Red'
    },
    {
      'status': ['未上线'],
      'key': '状态-京智网设备',
      'finalStatus': '',
      'sort': 4,
      'tipTitle': '状态-京智网设备(未上线)',
      'color': 'autoPilot3Grey'
    }
  ]
}
