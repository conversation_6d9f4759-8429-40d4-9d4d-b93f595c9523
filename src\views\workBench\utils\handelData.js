// 处理数据
import { parseJsonOrReturnOriginal } from '@/utils/index'

// 处理待办任务列表显示数据
export function mapTaskItem(item) {
  const { name, fv9, fv12, fv7, fv8, fv11, fv13, ft1, id: taskId, attachment, createBy, createTime } = item.task;

  return {
    original: item,
    status: item.status || '',
    id: item.id || '',
    createTime: item.createTime,
    userName: item.userName || '',
    taskCreateBy: createBy || '',
    taskCreateTime: createTime || '',
    taskId,
    name: name,
    fv9: parseJsonOrReturnOriginal(fv9),
    fv12,
    fv7,
    fv8,
    fv11,
    fv13,
    ft1,
    attachment: attachment ? JSON.parse(attachment) : [],
    currentStatusVal: currentStatus(fv12)
  };
}

export function mapPmTree(item) {
  const { id, name, fv9, fv12, fv7, fv8, fv11, fv18, fv13, ft1, attachment, createBy, createTime } = item;
  return {
    original: item,
    taskId: id,
    name: name,
    createBy,
    createTime,
    fv9: parseJsonOrReturnOriginal(fv9),
    fv12,
    fv7,
    fv8,
    fv11,
    fv13,
    ft1,
    fv18,
    attachment: attachment ? JSON.parse(attachment) : [],
    currentStatusVal: currentStatus(fv12)
  }
}

/**
 * 获取当前状态
 * @param val
 * @returns {*|boolean}
 */
function currentStatus(val) {
  const statusMap = {
    '已审核': true
  };
  return statusMap[val] || false
}

// function formatName(fv16, name) {
//   return fv16 ? `${fv16} ${name}` : name;
// }
