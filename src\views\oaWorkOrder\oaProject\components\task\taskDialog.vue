<template>
  <div class="task-dialog">
    <el-dialog
      :before-close="concelForm"
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="visible"
      append-to-body
      width="800px"
    >

      <div v-if="showFormData" class="text item task-content">
        <fm-generate-form
          :ref="'generateForm'"
          :data="formStruct"
          :preview="viewOrEdit"
          :remote="remoteFunc"
          :value="formData"
        />
        <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="130px">
          <!--<el-form-item label="里程碑:" prop="fv7">-->
          <!--  <el-select-->
          <!--    v-model="ruleForm.fv7"-->
          <!--    class="filter-item"-->
          <!--    clearable-->
          <!--    placeholder="请选择里程碑"-->
          <!--    size="small"-->
          <!--    style="width: 100%"-->
          <!--  >-->
          <!--    <el-option-->
          <!--      v-for="(item) in milestoneList"-->
          <!--      :key="item.id"-->
          <!--      :label="item.title"-->
          <!--      :value="item.title"-->
          <!--    />-->
          <!--  </el-select>-->
          <!--</el-form-item>-->
          <!--<el-form-item label="优先级:" prop="fv8">-->
          <!--  <el-select-->
          <!--    v-model="ruleForm.fv8"-->
          <!--    class="filter-item"-->
          <!--    clearable-->
          <!--    placeholder="请选择优先级"-->
          <!--    size="small"-->
          <!--    style="width: 100%"-->
          <!--  >-->
          <!--    <el-option-->
          <!--      v-for="item in dict.task_priority"-->
          <!--      :key="item.id"-->
          <!--      :label="item.label"-->
          <!--      :value="item.label"-->
          <!--    />-->
          <!--  </el-select>-->
          <!--</el-form-item>-->
          <el-form-item label="处理人:" prop="fv9">
            <el-select
              v-model="ruleForm.fv9"
              :loading="selectLoading"
              :remote-method="remoteSelectUsers"
              class="filter-item"
              clearable
              debounce="500"
              filterable
              placeholder="请输入处理人"
              remote
              reserve-keyword
              size="small"
              style="width: 100%"
            >
              <el-option
                v-for="item in userList"
                :key="item.id"
                :label="item.username"
                :value="item.username"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="开始时间:" prop="fv13">
            <el-date-picker
              v-model="ruleForm.fv13"
              :picker-options="pickerOptionsE"
              clearable
              format="yyyy-MM-dd HH:mm:ss"
              placeholder="请选择预计开始时间"
              popper-class="no-atTheMoment"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </el-form-item>
          <el-form-item label="预计结束时间:" prop="fv11">
            <el-date-picker
              v-model="ruleForm.fv11"
              :append-to-body="false"
              :picker-options="pickerOptionsS"
              class="date-picker"
              clearable
              format="yyyy-MM-dd HH:mm:ss"
              placeholder="请选择预计结束时间"
              popper-class="no-atTheMoment"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </el-form-item>
        </el-form>

      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="concelForm">取消</el-button>
        <el-button
          :disabled="submitDisabled"
          type="primary"
          @click="submitAction"
        >
          {{ ADDTYPE[type] }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth';
import Vue from 'vue'
import { GenerateForm } from '@/components/VueFormMaking'
import 'form-making/dist/FormMaking.css'

Vue.component(GenerateForm.name, GenerateForm)
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import extendBindTpl from '@/api/system/extendBindTpl';
import crudUser from '@/api/system/user';
import oaPmMilestone from '@/api/oaWorkOrder/oaPmMilestone'
import oaPmTree from '@/api/oaWorkOrder/oaPmTree';
import { mapGetters } from 'vuex';

const ADDTYPE = {
  1: '创建任务',
  2: '修改任务',
  3: '创建子任务',
  4: ''
}
export default {
  components: {},
  data() {
    return {
      visible: false,
      ruleForm: {
        fv1: '任务',
        // fv7: '', // 里程碑
        fv8: '', // 优先级
        fv9: '', // 处理人
        fv13: '', // 预计开始时间
        fv11: '', // 预计结束时间
        fv12: '', // 状态
        enabled: 1,
        pid: '',
        fv18: '', // 项目名称
        fv17: '' // 目录id
      },
      rules: {
        // fv7: [
        //   { required: true, message: '请选择里程碑', trigger: 'change' }
        // ],
        // fv8: [
        //   { required: true, message: '请选择优先级', trigger: 'change' }
        // ],
        fv9: [
          { required: true, message: '请输入处理人', trigger: 'change' }
        ],
        fv13: [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        fv11: [
          { required: true, message: '请选择结束时间', trigger: 'change' }
        ],
        fv12: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      },
      title: '添加任务',
      formStruct: {},
      formData: {},
      jsonData: {},
      submitDisabled: false,
      viewOrEdit: false,
      showFormData: false,
      remoteFunc: {
        funcGetToken(resolve) {
          resolve(getToken())
        }
      },
      selectLoading: false,
      userList: [], // 处理人
      milestoneList: [], // 项目里程碑
      projectId: '', // 项目ID
      type: 1,
      ADDTYPE,
      pickerOptionsS: {
        disabledDate: (time) => {
          if (!this.ruleForm.fv13) {
            return;
          }
          return time.getTime() < new Date(this.ruleForm.fv13).valueOf();
        }
      },
      pickerOptionsE: {
        disabledDate: (time) => {
          if (!this.ruleForm.fv11) {
            return;
          }
          return time.getTime() > new Date(this.ruleForm.fv11).valueOf();
        }
      }
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  // dicts: ['task_status', 'task_priority'],
  dicts: ['task_status'],
  methods: {
    init(info) {
      this.visible = true;
      const { data, type, currentProjectName, projectId, directoryId } = info;
      this.projectId = projectId
      this.type = type;
      this.title = `${ADDTYPE[type]}--${data.name}`;
      this.bindId = data.bindId
      if (type == 1 || type == 3) {
        this.ruleForm.pid = data.id;
        this.ruleForm.fv17 = directoryId;
        this.ruleForm.fv18 = currentProjectName;
        this.ruleForm.bindId = data.bindId;
        this.getProcessNodeList(this.bindId);
      } else if (type == 2) {
        this.getContent(data.id);
      }
      // this.getProjectMilestone(info)
    },
    // 处理人搜索
    remoteSelectUsers(query) {
      if (query !== '') {
        this.selectLoading = true;
        const data = {
          enabled: 1,
          userName: query,
          size: 99
        }
        crudUser.getByName(data).then(res => {
          if (res) {
            this.userList = res || [];
            this.selectLoading = false;
          } else {
            this.userList = [];
          }
        })
      } else {
        this.userList = [];
      }
    },
    // 获取该项目的里程牌
    getProjectMilestone(info) {
      const { currentProjectName: fv18, projectId: pmId } = info;
      const data = {
        pmId,
        fv18,
        fv12: '已审核',
        fv1: '任务',
        enabled: 1,
        size: 999
      }
      oaPmMilestone.getSmall(data).then(res => {
        if (res && res.content) {
          this.milestoneList = res.content;
        }
      })
    },
    getProcessNodeList(id) {
      this.loading = true;
      const data = { id, enabled: 1 }
      extendBindTpl.get(data).then(res => {
        if (res && res.content && res.content.length) {
          this.processStructureValue = res.content[0];
          const FormStruct = JSON.parse(this.processStructureValue.extendTpl.formStruct);
          this.formStruct = FormStruct;
        }
        this.showFormData = true;
      }).finally(() => {
        this.loading = false;
      });
    },
    getContent(id) {
      oaPmTree.getPmTree({ id, enabled: 1 }).then(res => {
        if (res && res.content) {
          const jsonData = res.content[0];
          this.jsonData = jsonData;
          this.processStructureValue = jsonData;
          this.formStruct = JSON.parse(jsonData.formStruct);
          this.formData = JSON.parse(jsonData.formData);
          this.ruleForm = jsonData;
          this.ruleForm.fv9 = JSON.parse(jsonData.fv9);
          this.showFormData = true;
        }
      })
    },
    submitAction() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          if (this.type == 1 || this.type == 3) {
            this.ruleForm.fv12 = this.dict['task_status'][0].value;
            // this.ruleForm.milestoneId = this.milestoneList.find(item => item.title == this.ruleForm.fv7).id;
            this.ruleForm['items'] = [
              {
                user: { id: this.user.id, enabled: 1 },
                enabled: 1
              }
            ]
          } else {
            // 编辑做什么处理
          }
          this.checkModule().then(res => {
            if (!res.flag) {
              return false;
            } else {
              const subData = { ...this.ruleForm, ...res.subData };
              let request = oaPmTree.createTask;
              let title = '添加';
              if (subData.id) {
                // 需要判断 是流转还是编辑接口
                request = oaPmTree.edit;
                title = '修改'
              }
              request(subData).then(response => {
                this.$notify({
                  title: `${title}成功`,
                  type: 'success',
                  duration: 2500
                })
                this.concelForm();
              }).catch((e) => {
                console.log(e);
              })
            }
          })
        } else {
          this.submitDisabled = false
          return false
        }
      });
    },

    async checkModule() {
      const subData = {
        bindId: this.bindId,
        formData: null,
        formStruct: null,
        formBindToVar: true,
        relation: this.processStructureValue.relation
      };
      if (this.showFormData) {
        const p = await this.$refs['generateForm'].getData().then(values => {
          subData.formData = JSON.stringify(values);
          subData.formStruct = JSON.stringify(this.formStruct);
          return {
            flag: true,
            subData
          };
        }).catch(() => {
          return {
            flag: false
          };
        })
        return p;
      } else {
        return Promise.resolve({
          subData,
          flag: true
        });
      }
    },
    concelForm() {
      this.visible = false;
      const data = {
        type: this.type
      }
      if (this.type == 1) {
        data.id = this.ruleForm.pid;
      }

      this.$emit('successAction', data)
    }
    // loadOrgan
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.el-picker-panel__footer .el-button--text.el-picker-panel__link-btn {
	display: none;
}
</style>

<style lang="scss" rel="stylesheet/scss">

.no-atTheMoment {
	.el-picker-panel__footer .el-button--text.el-picker-panel__link-btn {
		display: none;
	}
}
</style>
