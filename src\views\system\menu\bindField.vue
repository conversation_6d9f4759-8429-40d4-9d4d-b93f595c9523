<template>
  <div class="app-container">
    <!--表格渲染-->
    <el-row :gutter="15">
      <el-col style="margin-bottom: 10px">
        <el-card class="box-card" shadow="never">
          <div slot="header" class="clearfix">
            <span class="role-span">字段绑定关系</span>
            <el-button
              :loading="columnLoading"
              icon="el-icon-check"
              size="mini"
              style="float: right; padding: 6px 9px;margin-right: 9px"
              type="primary"
              @click="confirm"
            >保存</el-button>
            <el-button
              icon="el-icon-refresh-left"
              style="float: right; padding: 6px 9px;margin-right: 9px"
              @click="goBack"
            >
              返回
            </el-button>
          </div>
          <el-form size="small" label-width="90px">
            <el-table v-loading="tableLoading" :data="tableData" size="small" style="width: 100%;margin-bottom: 15px">
              <el-table-column prop="prop" label="模板字段" align="center" />
              <el-table-column prop="label" label="标题" align="center" />
              <el-table-column prop="tplName" label="模板名称" align="center" />
              <el-table-column prop="menuName" label="菜单名称" align="center" />
              <!-- <el-table-column prop="relation" label="绑定字段名" :show-overflow-tooltip="true" align="center">
                <template slot-scope="scope">
                  <span>{{ formatRelation(scope.row) }}</span>
                </template>
              </el-table-column> -->
              <el-table-column label="数据库字段" align="center">
                <!-- eslint-disable-next-line -->
                <template slot="header" slot-scope="scope">
                  <el-select v-model="entityInfo" filterable class="edit-input" clearable size="mini" style="width: 200px" placeholder="请选择数据库表" value-key="entityName" @change="findAllField(entityInfo,1)">
                    <el-option
                      v-for="item in entityList"
                      :key="item.entityName"
                      :label="item.comment"
                      :value="item"
                    />
                  </el-select>
                </template>
                <template slot-scope="scope">
                  <el-select v-model="tableData[scope.$index].field" style="width: 200px" filterable class="edit-input" clearable size="mini" placeholder="请选择" @change="fieldChange(scope.$index)">
                    <el-option
                      v-for="item in tableData[scope.$index].fieldList"
                      :key="item.attribute"
                      :label="item.attribute"
                      :value="item.attribute"
                    />
                  </el-select>
                </template>
              </el-table-column>
            </el-table>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { get, edit, findAllEntity, findAllField } from '@/api/system/extendBindTpl'
export default {
  name: 'BindField',
  data() {
    return {
      tableLoading: false,
      columnLoading: false,
      entityInfo: {},
      tableData: [],
      entityList: [],
      fieldList: [],
      bindTpl: {},
      tpl: {}
    }
  },
  created() {
    const menuId = this.$route.query.id || '';
    this.getList(menuId);
    this.findAllEntity();
  },
  methods: {
    // 获取列表
    getList(menuId) {
      this.tableLoading = true;
      const data = {
        page: 0,
        size: ************,
        enabled: 1,
        sort: 'id,desc',
        menuId: menuId
      }
      get(data).then(res => {
        if (!res.content || res.content.length == 0) {
          this.tableLoading = false;
          return;
        }
        const data = res.content[0];
        this.bindTpl = data;
        if (data.relation) {
          const relation = JSON.parse(data.relation);
          this.bindTpl.relation = relation.relation || {};
          this.entityInfo = relation.entityAttribute;
          this.findAllField(this.entityInfo);
        }
        const header = data.extend.tableHeader;
        this.tableData = header.map((item, index) => {
          item.tplName = data.extendTpl.title;
          item.menuName = data.menu.label;
          item.relation = data.relation;
          item.fieldList = [];
          item.entity = '';
          item.field = this.formatRelation(item);
          return item;
        })
        this.tableLoading = false;
      })
    },
    findAllEntity() {
      findAllEntity().then(res => {
        this.entityList = res;
      })
    },
    findAllField(json, type) {
      if (type) {
        for (let j = 0; j < this.tableData.length; j++) {
          this.$set(this.tableData[j], 'field', '');
        }
        this.tableData = Object.assign([], this.tableData);
      }
      // 输入表信息
      if (!json) {
        this.findAllFieldChange();
        return
      }
      findAllField(json).then(res => {
        this.findAllFieldChange(res);
      })
    },
    findAllFieldChange(data) {
      for (let j = 0; j < this.tableData.length; j++) {
        this.$set(this.tableData[j], 'fieldList', data);

        this.fieldChange(j);
      }
      this.tableData = Object.assign([], this.tableData);
    },
    fieldChange(index) {
      this.$set(this.tableData, index, this.tableData[index]);
    },
    confirm() {
      const relation = {};
      this.tableData && this.tableData.length && this.tableData.forEach(item => {
        if (item.field) {
          relation[item.prop] = item.field;
        }
      })
      const json = {
        ...this.bindTpl,
        relation: { relation, entityAttribute: this.entityInfo }
      }
      this.columnLoading = true;
      edit(json).then((res) => {
        this.$notify({
          title: '绑定成功',
          type: 'success',
          duration: 2500
        });
        const menuId = this.$route.query.id || '';
        this.getList(menuId);
        this.entityInfo = {};
        this.columnLoading = false;
      }).catch(e => {
        this.columnLoading = false;
      });
    },
    formatRelation(row) {
      const json = this.bindTpl.relation;
      let res = '';
      if (json) {
        for (const name in json) {
          if (name == row.prop) {
            res = json[name];
          }
        }
      }
      return res;
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>
<style>
.tableImg {
  width: 50px;height: 50px;
}
</style>
