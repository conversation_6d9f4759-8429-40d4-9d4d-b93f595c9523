<template>
  <div class="app-container">
    <el-card v-loading="loading" class="box-card" style="margin-top: 10px">
      <div slot="header" class="clearfix">
        <span>表单信息</span>
      </div>
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="120px">
        <el-form-item label="部件名称:" prop="assetTitle">
          <el-autocomplete
            v-if="!viewOrEdit"
            v-model="ruleForm.assetTitle"
            :fetch-suggestions="querySearchAsync"
            placeholder="请输入部件名称"
            style="width:600px"
            :debounce="800"
            :disabled="disabledAsset"
            @select="handleSelect"
          />
          <span v-else>{{ ruleForm.assetTitle }}</span>
        </el-form-item>
        <el-form-item label="巡检结果:" prop="status">
          <el-select
            v-if="!viewOrEdit"
            v-model="ruleForm.status"
            clearable
            size="small"
            placeholder="请选择巡检结果"
            class="filter-item"
            style="width:600px"
            @change="statusChange"
          >
            <el-option
              v-for="item in dict.inspects_results"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <span v-else>{{ ruleForm.status }}</span>
        </el-form-item>
        <el-form-item v-if="showFaultLevel" label="故障等级:" prop="fv2">
          <el-select
            v-if="!viewOrEdit"
            v-model="ruleForm.fv2"
            style="width:600px"
            clearable
            size="small"
            placeholder="请选择故障等级"
            class="filter-item"
          >
            <el-option
              v-for="item in dict.fault_level"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <span v-else>{{ ruleForm.fv2 }}</span>
        </el-form-item>
      </el-form>
      <div v-if="showFormData" class="text item">
        <fm-generate-form
          :ref="'generateForm'"
          :data="formStruct"
          :remote="remoteFunc"
          :value="formData"
          :preview="viewOrEdit"
        />
      </div>
      <hr style="background-color: #d9d9d9; border:0; height:1px;">
      <div class="text item" style="text-align: center;margin-top:18px">
        <el-button v-if="!viewOrEdit" type="primary" :disabled="submitDisabled" @click="submitAction">提交</el-button>
        <el-button @click="concelForm">{{ viewOrEdit == false ? '取 消' :'返回' }}</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>

import Vue from 'vue'
import { GenerateForm } from '@/components/VueFormMaking'
import 'form-making/dist/FormMaking.css'
Vue.component(GenerateForm.name, GenerateForm)
import crudDictDetail from '@/api/system/dictDetail'
import { get, add, edit } from '@/api/parts/inspect'
import omAsset from '@/api/parts/assets'
import extendBindTpl from '@/api/system/extendBindTpl'
import { getConfig } from '@/utils/getConfigData'
export default {
  name: 'RodInspectsCreate',
  dicts: ['inspects_results', 'fault_level'],
  data() {
    return {
      loading: false,
      viewOrEdit: false, // 默认是编辑
      submitDisabled: false,
      processStructureValue: {},
      ruleForm: {
        assetTitle: '',
        status: '',
        fv2: ''
      },
      rules: {
        assetTitle: [{ required: true, message: '请选择', trigger: 'change' }],
        status: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      jsonData: {},
      formStruct: {},
      formData: {},
      showFormData: false,
      remoteFunc: {
        // 获取字典详情
        getDictDetail(resolve, dictName) {
          crudDictDetail.get(dictName).then(response => {
            const options = response.content
            resolve(options)
          })
        },
        // 获取故障等级
        getFaultLevel(resolve) {
          this.getDictDetail(resolve, 'fault_level');
        },
        // 获取巡检结果
        getInspectsResults(resolve) {
          this.getDictDetail(resolve, 'inspects_results');
        },
        // 获取部件种类
        // async getParts(resolve) {
        //   const data = { key: 'Asset_class' }
        //   await getConfig(data).then(res => {
        //     const pid = res.extend.data.categoryId
        //     const bindId = res.extend.data.bindId
        //     crudCategory.getCategory({ pid, bindId }).then(resCategory => {
        //       const options = resCategory.content
        //       resolve(options)
        //     })
        //   })
        // },
        // 获取路口部件列表name
        // async getPartsNames(resolve) {
        //   const data = { key: 'Junction_list' }
        //   await getConfig(data).then(res => {
        //     const categoryId = res.extend.data.categoryId
        //     const bindId = res.extend.data.bindId
        //     const pageInfo = {
        //       page: 0,
        //       size: 9999
        //     }
        //     const data = {
        //       categoryId,
        //       bindId,
        //       size: pageInfo.size,
        //       enabled: 1,
        //       page: pageInfo.page++
        //     }
        //     omAsset.getOmAssetSmall(data).then(resOmAsset => {
        //       const options = resOmAsset.content || []
        //       resolve(options)
        //     })
        //   })
        // },
        funcGetToken(resolve) {
        }
      },
      partsTypes: [],
      partsNames: [],
      categoryId: '',
      bindId: '',
      asset: {},
      showFaultLevel: false,
      disabledAsset: false
    }
  },
  created() {
    this.getInitData();
    this.getConfigData();
  },
  methods: {
    getConfigData() {
      const data = { key: 'rod_list' }
      getConfig(data).then(res => {
        this.categoryId = res.extend.data.categoryId
        this.bindId = res.extend.data.bindId
      });
    },
    getInitData() {
      if (this.$route.query && this.$route.query.rowId) {
        this.getContent();
        this.viewOrEdit = this.$route.query.type == 'see';
      } else {
        // 创建的时候
        this.getProcessNodeList();
        if (this.$route.query.omAssetTitle && this.$route.query.omAssetID) {
        // 有路口名字的时候
          this.ruleForm.assetTitle = this.$route.query.omAssetTitle;
          this.asset = {
            id: this.$route.query.omAssetID,
            title: this.$route.query.omAssetTitle
          };
          this.disabledAsset = true;
        }
      }
    },
    getContent() {
      this.loading = true;
      get({ id: this.$route.query.rowId }).then(res => {
        if (res && res.content && res.content.length) {
          const jsonData = res.content[0];
          this.processStructureValue = jsonData;
          this.formStruct = JSON.parse(jsonData.formStruct);
          this.formData = this.formatArr(JSON.parse(jsonData.formData));
          this.jsonData = jsonData;
          this.ruleForm.assetTitle = jsonData.asset.title;
          this.ruleForm.status = jsonData.status;
          this.ruleForm.fv2 = jsonData.fv2 || '';
          this.statusChange(jsonData.status);
          this.asset = jsonData.asset;
          this.disabledAsset = true;
          this.showFormData = true;
        }
      }).finally(() => {
        this.loading = false;
      });
    },
    getProcessNodeList() {
      this.loading = true;
      const data = { id: this.$route.query.id, enabled: 1 }
      extendBindTpl.get(data).then(res => {
        if (res && res.content && res.content.length) {
          this.processStructureValue = res.content[0];
          this.formStruct = JSON.parse(this.processStructureValue.extendTpl.formStruct);
        }
        this.showFormData = true;
      }).finally(() => {
        this.loading = false;
      });
    },
    submitAction() {
      // if (!this.asset.id) {
      //   this.$notify({
      //     title: '请选择部件名称',
      //     type: 'info',
      //     duration: 2500
      //   });
      //   return;
      // }
      let isSubmit = false;
      this.$refs['ruleForm'].validate((valid) => {
        if (!valid) {
          isSubmit = true;
          return false;
        }
      })
      this.submitDisabled = true;
      const subData = {
        bindId: Number(this.$route.query.id),
        enabled: 1,
        formData: '',
        formStruct: JSON.stringify(this.formStruct),
        formBindToVar: true,
        relation: this.processStructureValue.relation,
        asset: {
          id: this.asset.id
        },
        categoryId: this.$route.query.categoryID,
        status: this.ruleForm.status,
        fv2: this.ruleForm.fv2
      };
      this.$refs['generateForm'].getData().then(values => {
        subData.formData = JSON.stringify(values);
      }).catch(() => {
        isSubmit = true;
      })
      setTimeout(() => {
        if (isSubmit) {
          this.submitDisabled = false
          this.$notify({
            title: '请根据提示填写表单信息',
            type: 'info',
            duration: 2500
          });
        } else {
          let request = add;
          if (this.jsonData && this.jsonData.id) {
            subData.id = this.jsonData.id;
            request = edit;
          }
          request(subData).then(response => {
            this.concelForm();
          }).catch(() => {
            this.submitDisabled = false
          })
        }
      })
    },
    concelForm() {
      this.$router.go(-1);
    },
    querySearchAsync(queryString, cb) {
      if (queryString) {
        const data = {
          categoryId: this.categoryId,
          enabled: 1,
          title: queryString
        }
        omAsset.getOmAssetSmall(data).then(resOmAsset => {
          let options = resOmAsset.content || [];
          if (options && options.length) {
            options = options.map(item => {
              item.value = item.title;
              return item;
            })
          } else {
            this.ruleForm.assetTitle = '';
            this.asset = {};
          }
          cb(options)
        });
      }
    },
    handleSelect(item) {
      this.asset = item;
    },
    statusChange(val) {
      if (val === '故障') {
        this.showFaultLevel = true;
      } else {
        this.showFaultLevel = false;
      }
    },
    formatArr(obj) {
      for (var name in obj) {
        if (typeof obj[name] == 'string') {
          try {
            obj[name] = JSON.parse(obj[name])
          } catch (e) {
            // console.log(e);
          }
        }
      }
      return obj;
    }
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
  ::v-deep .vue-treeselect__control,::v-deep .vue-treeselect__placeholder,::v-deep .vue-treeselect__single-value {
    height: 30px;
    line-height: 30px;
  }
</style>
