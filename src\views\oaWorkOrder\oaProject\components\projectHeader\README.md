# ProjectHeader 组件地图功能更新

## 更新内容

已成功实现通过项目ID获取节点列表的功能，并筛选出 `type=='point'` 的节点传递给 `mapDialog` 组件。

## 主要修改

### 1. showMapDialog() 方法重构

- **原来**: 使用硬编码的模拟数据
- **现在**: 通过 `getCatalogTree(projectId)` API 获取真实数据

### 2. 新增 extractPointNodes() 方法

递归遍历树形结构，提取所有 `type === 'point'` 的节点。

```javascript
extractPointNodes(nodes) {
  const pointNodes = []
  
  const traverse = (nodeList) => {
    if (!Array.isArray(nodeList)) return
    
    nodeList.forEach(node => {
      // 如果节点类型是'point'，添加到结果数组
      if (node.type === 'point') {
        pointNodes.push(node)
      }
      
      // 如果有子节点，递归遍历
      if (node.children && Array.isArray(node.children)) {
        traverse(node.children)
      }
    })
  }
  
  traverse(nodes)
  return pointNodes
}
```

### 3. 数据格式转换

将提取的点位节点转换为地图组件需要的格式：

```javascript
this.locationPoints = pointNodes.map(point => ({
  name: point.name || point.title || '未命名点位',
  longitude: point.longitude || point.lng || point.lon,
  latitude: point.latitude || point.lat,
  id: point.id,
  ...point // 保留原始数据
})).filter(point => point.longitude && point.latitude)
```

## API 依赖

- `getCatalogTree(projectId)`: 从 `@/api/oaWorkOrder/oaPmProgress` 导入
- 返回项目的目录树结构，包含各种类型的节点

## 数据结构

### getCatalogTree 返回的数据结构示例：

```javascript
[
  {
    id: 1,
    name: '项目根目录',
    type: 'catalog',
    children: [
      {
        id: 2,
        name: '施工区域A',
        type: 'catalog',
        children: [
          {
            id: 3,
            name: '监测点A1',
            type: 'point',
            longitude: '116.816054',
            latitude: '40.154257'
          }
        ]
      }
    ]
  }
]
```

### 转换后传递给地图组件的数据格式：

```javascript
[
  {
    id: 3,
    name: '监测点A1',
    longitude: '116.816054',
    latitude: '40.154257'
  }
]
```

## 错误处理

- 项目ID不存在时显示警告
- API调用失败时显示错误信息
- 自动过滤掉没有坐标信息的点位

## 使用方式

点击"地图展示"按钮时，组件会：

1. 获取当前项目ID
2. 调用 `getCatalogTree` API
3. 递归遍历返回的树形数据
4. 筛选出所有 `type === 'point'` 的节点
5. 转换数据格式并传递给地图组件
6. 显示地图弹窗

## 测试

可以使用 `test-example.js` 文件中的模拟数据来测试 `extractPointNodes` 函数的逻辑。
