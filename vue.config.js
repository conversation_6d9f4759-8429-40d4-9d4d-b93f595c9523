'use strict'
const path = require('path')
const defaultSettings = require('./src/settings.js')
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

function resolve(dir) {
  return path.join(__dirname, dir)
}

const Timestamp = new Date().getTime();
const Version = `${process.env.VUE_APP_Version}.${Timestamp}`;
const name = defaultSettings.title // 网址标题
const port = 8020 // 端口配置

// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
  // hash 模式下可使用
  // publicPath: process.env.NODE_ENV === 'development' ? '/' : './',
  publicPath: '/',
  outputDir: 'dist',
  assetsDir: 'static',
  // lintOnSave: process.env.NODE_ENV === 'development',
  lintOnSave: false,
  productionSourceMap: false,
  devServer: {
    port: port,
    // host: '*************',、
    disableHostCheck: true,
    open: false,
    overlay: {
      warnings: false,
      errors: true
    },
    proxy: {
      '/api': {
        target: process.env.VUE_APP_BASE_API,
        changeOrigin: true,
        onProxyRes(proxyRes, req, res) {
          const realUrl = new URL(req.url || '', process.env.VUE_APP_BASE_API || '')
          proxyRes.headers['x-real-url1'] = realUrl
        },
        pathRewrite: {
          '^/api': 'api'
        }
      },
      '/auth': {
        target: process.env.VUE_APP_BASE_API,
        changeOrigin: true,
        onProxyRes(proxyRes, req, res) {
          const realUrl = new URL(req.url || '', process.env.VUE_APP_BASE_API || '')
          proxyRes.headers['x-real-url1'] = realUrl
        },
        pathRewrite: {
          '^/auth': 'auth'
        }
      }
    }
  },
  configureWebpack: {
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    name: name,
    resolve: {
      alias: {
        '@': resolve('src'),
        '@crud': resolve('src/components/Crud')
      }
    },
    output: { // 输出重构  打包编译后的 文件名称  【模块名称.版本号.时间戳】
      filename: `static/js/[name].${Version}.js`,
      chunkFilename: `static/js/[name].${Version}.js`
    },
    plugins: [
      new MiniCssExtractPlugin({
        // 修改打包后css文件名
        filename: `static/css/[name].${Version}.css`,
        chunkFilename: `static/css/[name].${Version}.css`
      })
    ],
    performance: {
      hints: 'warning', // 枚举 false关闭
      maxEntrypointSize: 100000000, // 最大入口文件大小
      maxAssetSize: 100000000, // 最大资源文件大小
      assetFilter: function (assetFilename) { // 只给出js文件的性能提示
        return assetFilename.endsWith('.js');
      }
    }
  },
  css: {
    extract: {
      filename: `static/css/[name].${Version}.css`,
      chunkFilename: `static/css/[name].${Version}.css`
    }
  },
  chainWebpack(config) {
    config.plugins.delete('preload') // TODO: need test
    config.plugins.delete('prefetch') // TODO: need test

    config.module
      .rule('images')
      .use('url-loader')
      .tap(options => {
        options.name = `static/img/[name].${Version}.[ext]`;
        options.fallback = {
          loader: 'file-loader',
          options: {
            name: `static/img/[name].${Version}.[ext]`
          }
        };
        return options;
      });
    // set svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(resolve('src/assets/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()

    // set preserveWhitespace
    config.module
      .rule('vue')
      .use('vue-loader')
      .loader('vue-loader')
      .tap(options => {
        options.compilerOptions.preserveWhitespace = true
        return options
      })
      .end()

    config
      // https://webpack.js.org/configuration/devtool/#development
      .when(process.env.NODE_ENV === 'development',
        config => config.devtool('cheap-source-map')
      )

    config
      .when(process.env.NODE_ENV !== 'development',
        config => {
          config
            .plugin('ScriptExtHtmlWebpackPlugin')
            .after('html')
            .use('script-ext-html-webpack-plugin', [{
              // `runtime` must same as runtimeChunk name. default is `runtime`
              inline: /runtime\..*\.js$/
            }])
            .end()
          config.optimization.splitChunks({
            chunks: 'all',
            maxInitialRequests: Infinity,
            minSize: 30000, // 依赖包超过bit将被单独打包
            maxSize: 450000,
            automaticNameDelimiter: '-',
            cacheGroups: {
              vendor: {
                test: /[\\/]node_modules[\\/]/,
                name(module) {
                  const packageName = module.context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/)[1];
                  return `chunk.${packageName.replace('@', '')}`;
                },
                priority: 10
              }
            }
          })
          config.optimization.runtimeChunk('single')
        }
      )
  },
  transpileDependencies: [
    'resize-detector'
  ]
}
