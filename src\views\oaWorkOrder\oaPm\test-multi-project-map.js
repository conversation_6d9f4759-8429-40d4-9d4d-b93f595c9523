// 测试示例：多项目地图点位数据获取和合并

// 模拟选中的项目数据
const mockSelectedProjects = [
  { id: 1, name: '北京市教育设施建设项目' },
  { id: 2, name: '上海市基础设施改造项目' },
  { id: 3, name: '广州市智慧城市建设项目' }
]

// 模拟 getCatalogTree API 返回的不同项目数据
const mockApiResponses = {
  1: [ // 项目1的目录树
    {
      id: 101,
      name: '北京项目根目录',
      type: 'catalog',
      children: [
        {
          id: 102,
          name: '教学楼建设区域',
          type: 'catalog',
          children: [
            {
              id: 103,
              name: '监测点BJ-A1',
              type: 'point',
              longitude: '116.816054',
              latitude: '40.154257',
              description: '北京教学楼A区监测点'
            },
            {
              id: 104,
              name: '监测点BJ-A2',
              type: 'point',
              longitude: '116.825623',
              latitude: '40.110006',
              description: '北京教学楼B区监测点'
            }
          ]
        }
      ]
    }
  ],
  2: [ // 项目2的目录树
    {
      id: 201,
      name: '上海项目根目录',
      type: 'catalog',
      children: [
        {
          id: 202,
          name: '道路改造区域',
          type: 'catalog',
          children: [
            {
              id: 203,
              name: '监测点SH-R1',
              type: 'point',
              longitude: '121.473701',
              latitude: '31.230416',
              description: '上海道路改造监测点1'
            }
          ]
        },
        {
          id: 204,
          name: '桥梁建设区域',
          type: 'catalog',
          children: [
            {
              id: 205,
              name: '监测点SH-B1',
              type: 'point',
              longitude: '121.480000',
              latitude: '31.240000',
              description: '上海桥梁建设监测点'
            }
          ]
        }
      ]
    }
  ],
  3: [ // 项目3的目录树
    {
      id: 301,
      name: '广州项目根目录',
      type: 'catalog',
      children: [
        {
          id: 302,
          name: '智慧设备安装区域',
          type: 'catalog',
          children: [
            {
              id: 303,
              name: '监测点GZ-S1',
              type: 'point',
              longitude: '113.280637',
              latitude: '23.125178',
              description: '广州智慧设备监测点1'
            },
            {
              id: 304,
              name: '监测点GZ-S2',
              type: 'point',
              longitude: '113.290000',
              latitude: '23.135000',
              description: '广州智慧设备监测点2'
            }
          ]
        }
      ]
    }
  ]
}

// 模拟 getCatalogTree API 函数
function mockGetCatalogTree(projectId) {
  return new Promise((resolve) => {
    // 模拟网络延迟
    setTimeout(() => {
      resolve(mockApiResponses[projectId] || [])
    }, Math.random() * 1000 + 500) // 500-1500ms 随机延迟
  })
}

// 递归提取type=='point'的节点（与实际代码相同）
function extractPointNodes(nodes) {
  const pointNodes = []

  const traverse = (nodeList) => {
    if (!Array.isArray(nodeList)) return

    nodeList.forEach(node => {
      // 如果节点类型是'point'，添加到结果数组
      if (node.type === 'point') {
        pointNodes.push(node)
      }

      // 如果有子节点，递归遍历
      if (node.children && Array.isArray(node.children)) {
        traverse(node.children)
      }
    })
  }

  traverse(nodes)
  return pointNodes
}

// 模拟 handleShowMapView 方法的核心逻辑
async function testMultiProjectMapView() {
  console.log('开始测试多项目地图点位数据获取...')

  const projectIds = mockSelectedProjects.map(project => project.id)
  console.log('选中的项目ID列表:', projectIds)

  const allLocationPoints = []

  for (const projectId of projectIds) {
    try {
      console.log(`正在获取项目 ${projectId} 的目录树数据...`)
      const response = await mockGetCatalogTree(projectId)

      if (response) {
        const pointNodes = extractPointNodes(response)

        const projectPoints = pointNodes.map(point => ({
          name: point.name || point.title || '未命名点位',
          longitude: point.longitude || point.lng || point.lon,
          latitude: point.latitude || point.lat,
          id: point.id,
          projectId: projectId,
          projectName: mockSelectedProjects.find(p => p.id === projectId)?.name || '未知项目',
          ...point
        })).filter(point => point.longitude && point.latitude)

        allLocationPoints.push(...projectPoints)
        console.log(`项目 ${projectId} 获取到 ${projectPoints.length} 个点位`)
      }
    } catch (error) {
      console.error(`获取项目 ${projectId} 的目录树数据失败:`, error)
    }
  }

  console.log(`总共获取到 ${allLocationPoints.length} 个点位数据:`)
  console.table(allLocationPoints.map(point => ({
    项目名称: point.projectName,
    点位名称: point.name,
    经度: point.longitude,
    纬度: point.latitude,
    描述: point.description
  })))

  return allLocationPoints
}

// 运行测试
testMultiProjectMapView().then(result => {
  console.log('测试完成，最终结果:', result)
})

export { testMultiProjectMapView, extractPointNodes, mockGetCatalogTree }
