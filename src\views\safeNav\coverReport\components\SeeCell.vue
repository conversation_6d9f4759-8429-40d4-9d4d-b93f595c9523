<template>
  <span class="see-cell">
    <template v-if="header.prop === 'title'">
      <span style="cursor: pointer;color: #2476F8" @click="goDetail(currentScope.row)">
        {{ currentScope.row[header.prop] }}
      </span>
    </template>
    <template v-else-if="header.prop === 'ft1File'">
      <file-thumb-list
        :preview-list="currentScope.row.ft1File.previewList"
        :urls="currentScope.row.ft1File.urls"
      />
    </template>
    <!-- <template v-else-if="header.prop === 'ft2File'">
      <file-thumb
        :preview-list="currentScope.row.ft2File.url"
        :url="currentScope.row.ft2File.thurl"
      />
    </template> -->
    <template v-else-if="header.prop === 'fv9'">
      <el-radio-group v-model="currentScope.row.fv9" @change="changeStatus(currentScope.row)">
        <el-radio label="合格">合格</el-radio>
        <el-radio label="不合格">不合格</el-radio>
      </el-radio-group>
    </template>
    <span v-else>{{ currentScope.row[header.prop] }}</span>
  </span>
</template>

<script>
import crudTable from '@/api/safeNav/omAssetAffiliated'
export default {
  name: 'SeeCell',
  components: {},

  props: {
    header: {
      type: Object,
      default: () => {
        return {}
      }
    },
    currentScope: {
      type: Object,
      default: () => {
        return {}
      }
    },
    permission: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
    }
  },
  computed: {},
  watch: {},
  created() {
  },
  methods: {
    // 详情页
    goDetail(data) {
      const query = {
        name: 'CoverReportForm',
        query: {
          id: data.id,
          type: 1
        }
      }
      this.$router.push(query)
    },
    changeStatus(row) {
      crudTable.edit(row).then((response) => {
        this.$notify({
          title: '设置成功',
          type: 'success',
          duration: 2500
        });
      }).catch((e) => {
        // 回滚状态
        row.fv9 = (row.fv9 === '合格') ? '不合格' : '合格';
        this.$notify({
          title: '设置失败',
          type: 'error',
          duration: 2500
        });
      });
    }
  }
}
</script>

<style currentScoped lang="scss" rel="stylesheet/scss">

</style>
