<template>
  <div class="amap-page-container">
    <el-amap :zoom="zoom" :center="center" class="amap-demo">
      <el-amap-layer-district
        :map-style="mapStyle"
        :visible="visible"
        type="Province"
        adcode="110113"
        :depth="2"
        :styles="styles"
        @click="amapClick"
      />
      <el-amap-marker v-for="(marker, index) in markers" :key="index" :icon="marker.icon.image" :position="marker.position" />
    </el-amap>

    <el-button type="success" @click="start()">开始</el-button>
    <el-button type="primary" @click="stop()">暂停</el-button>
  </div>
</template>

<script>
// 颜色辅助方法
module.exports = {
  name: 'amap-page-box',
  data() {
    return {
      zoom: 11,
      center: [116.653, 40.128],
      visible: true,
      mapStyle: 'amap://styles/blue',
      styles: {
        'fill': 'transparent',
        'stroke-width': '1.5',
        'county-stroke': 'blue'
      },
      markers: [
        {
          position: [116.653, 40.128],
          id: 1,
          icon: '//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-red.png'
        },
        {
          position: [116.85, 40.128],
          id: 1,
          icon: '//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-red.png'
        }
      ],
      time1: null,
      time2: null,
      a: 116.653,
      b: 40.13,
      c: 116.85
    };
  },
  mounted() {
    // this.start();
    const a = [1, 2, 3, 4];
    delete a[1];
    console.log(a.length);
    console.log(a[1]);
  },
  methods: {
    amapClick(e) {
      console.log(e);
      const marker = {
        position: [e.props.x, e.props.y],
        id: new Date().getTime(),
        icon: {
          image: '//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-red.png',
          imageSize: (1, 1)
        }
      };
      this.markers.push(marker);
    },
    start() {
      this.time1 = setInterval(() => {
        this.addMarker(this.a + 0.001, this.b + 0.001)
      }, 10);
      this.time2 = setInterval(() => {
        this.addMarker(this.c + 0.001, this.b + 0.001)
      }, 10);
    },
    stop() {
      clearInterval(this.time1);
      clearInterval(this.time2);
    },
    addMarker(a, b) {
      const marker = {
        position: [a + (Math.random() - 0.5) * 0.1, b + (Math.random() - 0.5) * 0.2],
        id: new Date().getTime(),
        icon: {
          image: '//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-red.png',
          imageSize: (1, 1)
        }
      };
      this.markers.push(marker);
    },
    toggleVisible() {
      this.visible = !this.visible;
    }
  }

};
</script>

<style lang="scss" scoped>
  .amap-demo {
    height: 700px;
    ::v-deep .amap-icon {
      img {
        width: 4px;height: 4px;
      }
    }
    ::v-deep .amap-marker {
      img {
        width: 4px;height: 4px;
      }
    }
}

</style>
