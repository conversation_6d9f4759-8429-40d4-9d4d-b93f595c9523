/**
 * 防抖
 * @param func
 * @param delay
 * @returns {(function(...[*]): void)|*}
 */
const debounce = (func, delay) => {
  let timeout;
  return function(...args) {
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  };
};
/**
 * 防抖指令
 */
export default {
  bind(el, binding) {
    if (typeof binding.value !== 'function') {
      console.warn(`Expect a function, got ${typeof binding.value}`);
      return;
    }
    const delay = parseInt(binding.arg) || 300; // 默认防抖时间为 300ms
    el.__debounceHandler__ = debounce(binding.value, delay);
    el.addEventListener('click', el.__debounceHandler__);
  },
  unbind(el) {
    el.removeEventListener('click', el.__debounceHandler__);
    delete el.__debounceHandler__;
  }
};
