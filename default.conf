server
    {
        listen 80;
        listen  [::]:80;
        server_name  localhost;
        index index.html;
        root   /usr/share/nginx/html;  #dist上传的路径
        client_max_body_size 100m;
        # 开启gzip    
        gzip  on;
        # 低于1kb的资源不压缩
        gzip_min_length 1k;
        # 设置压缩所需要的缓冲区大小
        gzip_buffers 4 16k;
        # 压缩级别[1-9]，越大压缩率越高，同时消耗cpu资源也越多，建议设置在4左右。
        gzip_comp_level 4;
        # 需要压缩哪些响应类型的资源，缺少自己补。
        gzip_types text/plain application/x-javascript text/css application/xml text/javascript application/javascript application/json;
        # 配置禁用gzip条件，支持正则。此处表示ie6及以下不启用gzip（因为ie低版本不支持）
        gzip_disable "MSIE [1-6]\.";
        # 是否添加“Vary: Accept-Encoding”响应头，
        gzip_vary on;
        # 避免访问出现 404 错误
        location / {
          try_files $uri $uri/ @router;
          index  index.html;
        }
        location @router {
          rewrite ^.*$ /index.html last;
        }
        add_header X-XSS-Protection "1;mode=block";
        add_header X-Frame-Options "DENY";
        add_header X-Content-Type-Options "nosniff";
        server_tokens off; 
              # 接口
        location /api {
          proxy_pass http://**************:9000;
          proxy_set_header X-Forwarded-Proto $scheme;
          proxy_set_header X-Forwarded-Port $server_port;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "upgrade";
          proxy_buffering off;
          proxy_send_timeout 600;
          proxy_read_timeout 600;
        }

        # 授权接口
        location /auth {
          proxy_pass http://**************:9000;
          proxy_set_header X-Forwarded-Proto $scheme;
          proxy_set_header X-Forwarded-Port $server_port;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "upgrade";
        }

        # WebSocket 服务
        location /webSocket {
          proxy_pass http://**************:9000;
          proxy_set_header X-Forwarded-Proto $scheme;
          proxy_set_header X-Forwarded-Port $server_port;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "upgrade";
        }

        # 头像
        location /avatar {
          proxy_pass http://**************:9000;
        }

        # 文件
        location /file {
          proxy_pass http://**************:9000;
          proxy_buffering off;
          proxy_send_timeout 600;
          proxy_read_timeout 600;
        } 

        # wx portal
        location /wx {
          proxy_pass http://**************:9000;
          proxy_set_header X-Forwarded-Proto $scheme;
          proxy_set_header X-Forwarded-Port $server_port;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "upgrade";
          proxy_buffering off;
          proxy_send_timeout 600;
          proxy_read_timeout 600;
        }
        # themes
        location /themes {
          proxy_pass http://**************:9000;
          proxy_set_header X-Forwarded-Proto $scheme;
          proxy_set_header X-Forwarded-Port $server_port;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "upgrade";
          proxy_buffering off;
          proxy_send_timeout 600;
          proxy_read_timeout 600;
        }
        # template
        location /template {
          proxy_pass http://**************:9000;
          proxy_set_header X-Forwarded-Proto $scheme;
          proxy_set_header X-Forwarded-Port $server_port;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "upgrade";
          proxy_buffering off;
          proxy_send_timeout 600;
          proxy_read_timeout 600;
        }

    } 