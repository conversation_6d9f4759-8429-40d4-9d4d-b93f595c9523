<template>
  <span class="see-cell">
    <template v-if="header.prop === 'title'">
      <span style="cursor: pointer;color: #2476F8" @click="goDetail(currentScope.row)">
        {{ currentScope.row[header.prop] }}
      </span>
    </template>
    <template v-else-if="isDynamicHeader(header.prop) && currentScope.row[header.prop]">
      <!--<div v-if="currentStatus(currentScope.row[header.prop])" class="auto-stauts">-->
      <!--  <img-->
      <!--    :src="getImagePath(currentStatus(currentScope.row[header.prop]).value)"-->
      <!--    class="auto-status-img"-->
      <!--  >-->
      <!--</div>-->
      <div class="custom-tag">
        <span>{{ currentScope.row[header.prop] }}</span>
        <!--<el-popconfirm-->
        <!--  ref="popconfirm"-->
        <!--  title="确定删除该标签吗？"-->
        <!--  @confirm="closeTag(currentScope.row, header)"-->
        <!--&gt;-->
        <!--  <i slot="reference" class="el-icon-circle-close del-icon" />-->
        <!--</el-popconfirm>-->
      </div>
    </template>
    <span v-else>{{ currentScope.row[header.prop] }}</span>
  </span>
</template>

<script>
export default {
  name: 'SeeCell',
  components: {},

  props: {
    dict: {
      type: Object,
      required: true
    },
    header: {
      type: Object,
      default: () => {
        return {}
      }
    },
    currentScope: {
      type: Object,
      default: () => {
        return {}
      }
    },
    crud: {
      type: Object,
      default: () => {
        return {}
      }
    },
    permission: {
      type: Object,
      default: () => {
        return {}
      }
    },
    isShowTag: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isHighlighted: false // 用于标记当前标签是否高亮
    }
  },
  computed: {
    getDescribe() {
      return (data) => {
        return data.join(',')
      }
    }
    // getImagePath() {
    //   return (img) => {
    //     return require(`@/assets/images//${img}.png`);
    //   };
    // },
    // currentStatus({ dict }) {
    //   // const statusList = this.dict['status_icon_3.0'];
    //   return (val) => {
    //     // const current = statusList.find(item => item.label === val)
    //     return false
    //   }
    // }
  },
  watch: {},
  created() {
  },
  methods: {
    closeTag(row, herder,) {
      const fv11 = row[herder.prop]
      const fv10 = herder.label
      const tagList = row.tagList;
      const objectTag = tagList.find(tag => tag.fv10 === fv10 && tag.fv11 === fv11);
      console.log(row, '<===>', 'row')
      console.log(herder, '<===>', 'herder')
      console.log(objectTag, '<===>', 'objectTag')
    },
    async confirmDelete(event) {
      // console.log(666, '<===>', '666')
      // console.log(this.$refs, '<===>', 'this.$refs')
      event.preventDefault(); // 阻止默认的关闭行为
      this.popconfirmVisible = true;
      this.$nextTick(() => {
        this.$refs.popconfirm.$el.querySelector('.el-popover__reference').click();
      });
      await this.$nextTick()
      this.$refs.popconfirm.confirmVisible = true;
      this.$refs.popconfirm.visible = true;
    },
    // 鼠标移入、移出事件 设置是否展示删除icon
    setTag(flag) {
      this.isHighlighted = flag;
    },
    // 去详情页
    goDetail(data) {
      const query = {
        name: 'AutoPilot4Form',
        query: {
          id: data.id,
          type: 1
        }
      }
      this.$router.push(query)
    },
    isDynamicHeader(prop) {
      const dynamicHeader = this.crud?.metaData?.tableHeader;
      if (dynamicHeader && dynamicHeader.length) {
        return dynamicHeader.some(element => element.prop === prop);
      }
      return false;
    }
  }
}
</script>

<style currentScoped lang="scss" rel="stylesheet/scss">
//.tag-list {
//	display: flex;
//	flex-wrap: wrap;
//
//	.custom-tag {
//		margin-right: 8px;
//		padding: 0 2px;
//		text-align: center;
//		white-space: normal; /* 允许文本折行 */
//		word-break: break-all; /* 在任意字符间折行，适用于没有自然折行点的长单词或URL等 */
//		/* 或者使用 word-break: break-word; 保持英文单词和中文句子的完整性 */
//		min-width: 100px;
//		height: auto;
//		//height: 44px;
//		height: auto;
//		vertical-align: top;
//		margin-bottom: 3px;
//	}
//
//}
.auto-stauts {
	display: flex;
	align-items: center;
	justify-content: center;

	.auto-status-img {
		width: 24px;
		height: 24px;
	}
}

.custom-tag {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100px;
	height: auto;
	background: #E7FAF0;
	border-radius: 4px;
	border: 1px solid #d0f5e0;
	padding: 0 2px;

	//&:hover {
	//	background: #FEF0F0;
	//	border: 1px solid #FDE2E2;
	//
	//	span {
	//		color: #f56c6c;
	//	}
	//
	//	.del-icon {
	//		opacity: 1;
	//	}
	//}

	span {
		color: #13ce66;
		line-height: 18px;
		font-size: 14px;
		text-align: center;
		white-space: normal; /* 允许文本折行 */
		word-break: break-all; /* 在任意字符间折行，适用于没有自然折行点的长单词或URL等 */
		/* 或者使用 word-break: break-word; 保持英文单词和中文句子的完整性 */
	}

	.del-icon {
		font-size: 14px;
		line-height: 18px;
		color: #f56c6c;
		opacity: 0;
		cursor: pointer;
		margin-left: 5px;
	}

}
</style>
