<template>
  <span class="see-cell">
    <template v-if="header.prop === 'title'">
      <span style="cursor: pointer;color: #2476F8" @click="goDetail(currentScope.row)">
        {{ currentScope.row[header.prop] }}
      </span>
    </template>
    <span v-else>{{ currentScope.row[header.prop] }}</span>
  </span>
</template>

<script>
export default {
  name: 'SeeCell',
  components: {},

  props: {
    header: {
      type: Object,
      default: () => {
        return {}
      }
    },
    currentScope: {
      type: Object,
      default: () => {
        return {}
      }
    },
    permission: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  computed: {},
  watch: {},
  created() {
  },
  methods: {
    // 去资产详情页
    goDetail(data) {
      const query = {
        name: 'AutoPilot3Form',
        query: {
          id: data.id,
          type: 1
        }
      }
      this.$router.push(query)
    }
  }
}
</script>

<style currentScoped lang="scss" rel="stylesheet/scss">

</style>
