<template>
  <div class="app-container map-box">
    <mapContainer ref="map-container" :bus-event-name="busEventName" />
    <markerList ref="marker-list" :map="map" />
  </div>
</template>

<script>
import mapContainer from '@/components/map'
import markerList from './components/marker'
import Bus from '@/utils/bus';

export default {
  name: 'NetWorkMap',
  components: {
    markerList,
    mapContainer
  },
  data() {
    return {
      map: {},
      busEventName: 'networkMap'
    }
  },
  mounted() {
    Bus.$on(this.busEventName, data => {
      this.map = data;
    });
  }
}
</script>

<style scoped>
#mapContainer {
	height: calc(100vh - 150px);
}

.map-box {
	position: relative;
}

.search-box {
	position: absolute;
	width: 100%;
	left: 20px;
	top: 20px;
	z-index: 2000;
	padding: 10px 0 0 10px;
}
</style>
