import { LOAD_CHILDREN_OPTIONS } from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';

export const treeFlatTrees = [];

export function getTreeLists({ apiMethod, queryParams }) {
  return fetchData(apiMethod, queryParams);
}

export function lazyTrees({ action, parentNode, callback, apiMethod, otherParams }) {
  if (action === LOAD_CHILDREN_OPTIONS) {
    const queryParams = { enabled: 1, pid: parentNode.id, sort: 'sort,asc', ...otherParams };
    fetchData(apiMethod, queryParams).then(data => {
      parentNode.children = hanleChildren(data);
      setTimeout(callback, 200);
    });
  }
}

// 处理接口
export function fetchData(apiMethod, ...args) {
  return apiMethod(...args).then(res => {
    const data = res?.content;
    return hanleChildren(data);
  });
}

// 对有子节点的组织进行处理
function hanleChildren(data) {
  const set = new Set(treeFlatTrees.map(item => item.id));
  data.map(item => {
    if (!set.has(item.id)) {
      treeFlatTrees.push(item);
      set.add(item.id);
    }
    if (item.children) {
      hanleChildren(item.children)
    }
    if (item.hasChildren && !item.children) {
      item.children = null
    }
  })
  return data
}
