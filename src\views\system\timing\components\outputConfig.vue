<template>
  <div class="app-container">
    <el-form ref="form" :model="formData" label-width="270px">
      <el-form-item label="要写入输出的文件">
        <el-input v-model="formData.output_file" placeholder="要写入输出的文件" style="width:60%" />
      </el-form-item>
      <el-form-item label="存储HTTP请求/响应">
        <el-radio-group v-model="formData.store_response">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="将HTTP请求/响应存储到自定义目录">
        <el-input v-model="formData.store_response_dir" placeholder="将HTTP请求/响应存储到自定义目录" style="width:60%" />
      </el-form-item>
      <el-form-item label="在jsonl输出中省略原始请求/响应">
        <el-radio-group v-model="formData.omit_raw">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="在jsonl输出中省略响应主体">
        <el-radio-group v-model="formData.omit_body">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="以jsonl格式编写输出">
        <el-radio-group v-model="formData.json">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="禁用输出内容着色（ANSI 转义码）">
        <el-radio-group v-model="formData.no_colors">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="仅显示输出">
        <el-radio-group v-model="formData.silent">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="显示详细输出">
        <el-radio-group v-model="formData.verbose">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="显示项目版本">
        <el-radio-group v-model="formData.version">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { delArrInvalid } from '@/views/system/timing/utils/formate'
export default {
  name: 'OutputConfig',

  data() {
    return {
      formData: {
        'output_file': null,
        'store_response': null,
        'store_response_dir': null,
        'omit_raw': null,
        'omit_body': null,
        'json': null,
        'no_colors': null,
        'silent': null,
        'verbose': null,
        'version': null
      }
    };
  },

  mounted() {

  },

  methods: {
    getFormData() {
      return {
        output: delArrInvalid(this.formData)
      };
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
