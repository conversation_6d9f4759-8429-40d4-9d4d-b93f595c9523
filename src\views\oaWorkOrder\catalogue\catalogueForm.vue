<template>
  <div class="app-container catalogue">
    <el-card class="box-card" style="margin-top: 10px">
      <div slot="header" class="clearfix">
        <span>目录模版信息</span>
        <el-button :disabled="btnDisabled" size="mini" style="float: right" type="primary" @click="saveData">保存
        </el-button>
      </div>
      <el-form ref="elForm" :model="elForm" :rules="elFormRules" label-width="100px">
        <el-form-item label="模板名称" prop="title">
          <el-input
            v-model.trim="elForm.title"
            class="edit-input"
            placeholder="请输入名称"
            size="mini"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="elForm.status">
            <el-radio
              v-for="item in statusList"
              :key="item.id"
              :label="item.value"
            >{{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="模板描述" prop="description">
          <el-input
            v-model.trim="elForm.description"
            class="edit-input"
            placeholder="请输入名称"
            size="mini"
          />
        </el-form-item>
        <el-form-item label="模板详细信息" prop="ft1">
          <el-table :data="elForm.ft1" border style="width: 100%">

            <el-table-column
              fixed
              width="50"
            >
              <template slot="header">
                <i
                  class="el-icon-circle-plus"
                  style="font-size: 25px; color: #409EFF;cursor:pointer;"
                  @click="addSubformCol()"
                />
              </template>
              <template slot-scope="scope">
                <i
                  class="el-icon-remove"
                  style="font-size: 25px; color: red"
                  @click="delSubformCol(scope)"
                />
              </template>
            </el-table-column>
            <el-table-column label="序号">
              <template slot-scope="scope">
                <el-form-item
                  :prop="`ft1.${scope.$index}.indexSort`"
                  :rules="elFormRules.indexSort"
                  class="table-form-item"
                  label-width="0"
                >
                  <el-input
                    v-model.trim="scope.row.indexSort"
                    class="edit-input"
                    placeholder="索引格式1.1.1"
                    size="mini"
                  />
                </el-form-item>

              </template>
            </el-table-column>
            <el-table-column label="名称">
              <template slot-scope="scope">
                <el-form-item :prop="`ft1.${scope.$index}.name`" class="table-form-item">
                  <el-input
                    v-model.trim="scope.row.name"
                    class="edit-input"
                    placeholder="请输入名称"
                    size="mini"
                  />
                </el-form-item>

              </template>
            </el-table-column>

          </el-table>
        </el-form-item>
        <el-form-item />
      </el-form>

    </el-card>
  </div>
</template>

<script>
import oaPmCatalog from '@/api/oaWorkOrder/oaPmCatalog';
import { getOaPmCatalogData, statusList } from '@/views/oaWorkOrder/catalogue/utils/catalogue';

export default {
  name: 'CatalogueForm',
  components: {},

  props: {},
  data() {
    const isValidVersion = (version) => {
      const versionPattern = /^\d+(\.\d+)*$/;
      return versionPattern.test(version);
    };
    const validFv16 = (rule, value, callback) => {
      if (!value) {
        callback()
      } else if (!isValidVersion(value)) {
        callback(new Error('请输入正确索引'))
      } else {
        callback()
      }
    };
    return {
      btnDisabled: false,
      elForm: {
        title: '',
        enabled: 1,
        description: '',
        status: true,
        ft1: []
      },
      elFormRules: {
        title: [
          { required: true, message: '请输入模板名称', trigger: 'blur' }
        ],
        indexSort: [
          { required: true, message: '请输入索引', trigger: ['blur', 'change'], validator: validFv16 }
        ]
      },
      list: [],
      validFv16,
      statusList
    }
  },
  created() {
    this.initData()
  },
  methods: {
    async initData() {
      const { id } = this.$route.query
      if (id) {
        const dataArr = await getOaPmCatalogData({ id, enabled: 1 });

        if (dataArr.length > 0) {
          const { ft1 } = dataArr[0]
          this.elForm = {
            ...dataArr[0],
            ft1: JSON.parse(ft1)
          };
        } else {
          this.elForm = {
            title: '',
            enabled: 1,
            description: '',
            ft1: []
          };
        }
      }
    },
    addSubformCol(data) {
      const obj = {
        indexSort: '',
        name: ''
      }
      // const rule = { message: '请输入模板名称', trigger: 'blur', validator: validFv16 }
      this.elForm.ft1.push(obj)
    },
    delSubformCol(data) {
      const { $index } = data
      this.elForm.ft1.splice($index, 1)
    },
    saveData() {
      this.$refs.elForm.validate(valid => {
        if (!valid) {
          return
        } else {
          this.btnDisabled = true
          let title = '添加成功'
          let request = oaPmCatalog.add
          if (this.elForm.id) {
            // 编辑
            title = '编辑成功'
            request = oaPmCatalog.edit
          } else {
            // 新增
          }
          const data = {
            ...this.elForm,
            ft1: JSON.stringify(this.elForm.ft1)
          }
          request(data).then(res => {
            this.$message({
              message: title,
              type: 'success'
            })
            this.goBack()
            this.btnDisabled = false
          })
        }
      })
    },
    goBack() {
      this.$router.push({ name: 'Catalogue' });
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.save-btn {
	margin-top: 20px;
}

// 表格内嵌表单样式
.table-form-item {
	margin-bottom: 0 !important;
}
</style>
