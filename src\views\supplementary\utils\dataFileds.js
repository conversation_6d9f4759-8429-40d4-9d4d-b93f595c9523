/** 京智网-恒华湖-实时中心环-1 */
export const ji<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ake = [
  // 1：主干1-环1
  {
    fv4: 'SY-480',
    status: '问题',
    fv4Style: {
      left: '525px',
      top: '107px'
    }
  },
  {
    fv4: 'SY-481',
    status: '问题',
    fv4Style: {
      left: '525px',
      top: '169px',
      width: '76px',
      height: '83px'
    }
  },
  {
    fv4: 'SY-483',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '169px'
    }
  },
  // {
  //   // 突出去的
  //   fv4: 'SYXZ-02',
  //   status: '问题',
  //   fv4Style: {
  //     left: '',
  //     top: ''
  //   }
  //   // mark: 'long'
  // },
  {
    fv4: 'SY-484',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '169px'
    }
  },
  {
    fv4: 'SY-485',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '169px'
    }
  },
  {
    fv4: 'SY-502',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '200px'
    }
  },
  {
    fv4: 'SY-497',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '262px'
    }
  },
  {
    fv4: 'SY-498',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '293px'
    }
  },
  {
    fv4: 'SY-499',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '293px'
    }
  },
  {
    fv4: 'SY-500',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '293px'
    }
  },
  // 2：主干1-环2
  {
    fv4: 'SY-501',
    status: '问题',
    fv4Style: {
      left: '525px',
      top: '293px',
      width: '76px',
      height: '124px'
    }
  },
  {
    fv4: 'SY-503',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '375px'
    }
  },
  {
    fv4: 'SY-504',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '375px'
    }
  },
  {
    fv4: 'SY-508',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '375px'
    }
  },
  {
    fv4: 'SY-507',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '375px'
    }
  },
  {
    fv4: 'SY-506',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '437px'
    }
  },
  {
    fv4: 'SY-505',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '499px'
    }
  },
  {
    fv4: 'SY-509',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '499px'
    }
  },
  {
    fv4: 'SY-510',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '499px'
    }
  },
  {
    fv4: 'SY-511',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '499px'
    }
  },
  // 3：主干1-环3
  {
    fv4: 'SY-512',
    status: '问题',
    fv4Style: {
      left: '525px',
      top: '499px',
      width: '76px',
      height: '124px'
    }
  },
  {
    fv4: 'SY-486',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '581px'
    }
  },
  {
    fv4: 'SY-487',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '581px'
    }
  },
  {
    fv4: 'SY-488',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '581px'
    }
  },
  {
    fv4: 'SY-489',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '581px'
    }
  },
  {
    fv4: 'SY-490',
    status: '问题',
    fv4Style: {
      left: '45px',
      top: '581px'
    }
  },
  {
    fv4: 'SY-491',
    status: '问题',
    fv4Style: {
      left: '45px',
      top: '643px'
    }
  },
  {
    fv4: 'SY-492',
    status: '问题',
    fv4Style: {
      left: '45px',
      top: '705px'
    }
  },
  {
    fv4: 'SY-493',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '705px'
    }
  },
  {
    fv4: 'SY-494',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '705px'
    }
  },
  {
    fv4: 'SY-495',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '705px'
    }
  },
  {
    fv4: 'SY-496',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '705px'
    }
  },
  {
    fv4: 'SYXZ-01',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '643px'
    },
    mark: 'long'
  },
  // 4：主干1-环4
  {
    fv4: 'SY-100',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '787px',
      width: '124px',
      height: '76px'
    }
  },
  {
    fv4: 'SY-046',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '787px'
    }
  },
  {
    fv4: 'SY-094',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '788px'
    }
  },
  {
    fv4: 'SY-093',
    status: '问题',
    fv4Style: {
      left: '45px',
      top: '788px'
    }
  },
  {
    fv4: 'SY-089',
    status: '问题',
    fv4Style: {
      left: '45px',
      top: '850px'
    }
  },
  {
    fv4: 'SY-085',
    status: '问题',
    fv4Style: {
      left: '45px',
      top: '912px'
    }
  },
  {
    fv4: 'SY-080',
    status: '问题',
    fv4Style: {
      left: '45px',
      top: '974px'
    }
  },
  {
    fv4: 'SY-045',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '974px'
    }
  },
  {
    fv4: 'SY-073',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '974px'
    }
  },
  {
    fv4: 'SY-072',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '974px'
    }
  },
  // 5：主干2-环1
  {
    fv4: 'SY-062',
    status: '问题',
    fv4Style: {
      left: '568px',
      top: '787px',
      width: '124px',
      height: '76px'
    }
  },
  {
    fv4: 'SY-044',
    status: '问题',
    fv4Style: {
      left: '568px',
      top: '883px'
    }
  },
  {
    fv4: 'SY-065',
    status: '问题',
    fv4Style: {
      left: '568px',
      top: '945px'
    }
  },
  {
    fv4: 'SY-066',
    status: '问题',
    fv4Style: {
      left: '568px',
      top: '1007px'
    }
  },
  {
    fv4: 'SY-070',
    status: '问题',
    fv4Style: {
      left: '664px',
      top: '1007px'
    }
  },
  {
    fv4: 'SY-069',
    status: '问题',
    fv4Style: {
      left: '760px',
      top: '1008px'
    }
  },
  {
    fv4: 'SY-071',
    status: '问题',
    fv4Style: {
      left: '760px',
      top: '945px'
    }
  },
  {
    fv4: 'SY-074',
    status: '问题',
    fv4Style: {
      left: '760px',
      top: '883px'
    }
  },
  // 5：主干2-环1

  // 6：主干2-环2
  {
    fv4: 'SY-076',
    status: '问题',
    fv4Style: {
      left: '876px',
      top: '787px',
      width: '124px',
      height: '76px'
    }
  },
  {
    fv4: 'SY-522',
    status: '问题',
    fv4Style: {
      left: '876px',
      top: '883px'
    }
  },
  {
    fv4: 'SY-078',
    status: '问题',
    fv4Style: {
      left: '876px',
      top: '945px'
    }
  },
  {
    fv4: 'SY-079',
    status: '问题',
    fv4Style: {
      left: '876px',
      top: '1007px'
    }
  },
  {
    fv4: 'SY-084',
    status: '问题',
    fv4Style: {
      left: '972px',
      top: '1007px'
    }
  },
  {
    fv4: 'SY-083',
    status: '问题',
    fv4Style: {
      left: '1068px',
      top: '1008px'
    }
  },
  {
    fv4: 'SY-082',
    status: '问题',
    fv4Style: {
      left: '1068px',
      top: '945px'
    }
  },
  {
    fv4: 'SY-077',
    status: '问题',
    fv4Style: {
      left: '1068px',
      top: '883px'
    }
  },
  {
    fv4: 'SY-081',
    status: '问题',
    fv4Style: {
      left: '1068px',
      top: '821px'
    }
  },
  // 6：主干2-环2
  // 7：主干2-环3
  {
    fv4: 'SY-086',
    status: '问题',
    fv4Style: {
      left: '1184px',
      top: '787px',
      width: '124px',
      height: '76px'
    }
  },
  {
    fv4: 'SY-087',
    status: '问题',
    fv4Style: {
      left: '1184px',
      top: '883px'
    }
  },
  {
    fv4: 'SY-088',
    status: '问题',
    fv4Style: {
      left: '1184px',
      top: '945px'
    }
  },
  {
    fv4: 'SY-092',
    status: '问题',
    fv4Style: {
      left: '1184px',
      top: '1007px'
    }
  },
  {
    fv4: 'SY-091',
    status: '问题',
    fv4Style: {
      left: '1280px',
      top: '1007px'
    }
  },
  {
    fv4: 'SY-090',
    status: '问题',
    fv4Style: {
      left: '1376px',
      top: '1008px'
    }
  },
  {
    fv4: 'SY-095',
    status: '问题',
    fv4Style: {
      left: '1376px',
      top: '945px'
    }
  },
  {
    fv4: 'SY-096',
    status: '问题',
    fv4Style: {
      left: '1376px',
      top: '883px'
    }
  },
  {
    fv4: 'SY-097',
    status: '问题',
    fv4Style: {
      left: '1376px',
      top: '821px'
    }
  },
  // 7：主干2-环3

  // 8：主干3-环1
  {
    fv4: 'SY-068',
    status: '问题',
    fv4Style: {
      left: '1492px',
      top: '787px',
      width: '124px',
      height: '76px'
    }
  },
  {
    fv4: 'SY-061',
    status: '问题',
    fv4Style: {
      left: '1492px',
      top: '883px'
    }
  },
  {
    fv4: 'SY-075',
    status: '问题',
    fv4Style: {
      left: '1492px',
      top: '945px'
    }
  },
  {
    fv4: 'SY-523',
    status: '问题',
    fv4Style: {
      left: '1492px',
      top: '1007px'
    }
  },
  {
    fv4: 'SY-051',
    status: '问题',
    fv4Style: {
      left: '1588px',
      top: '1007px'
    }
  },
  {
    fv4: 'SY-050',
    status: '问题',
    fv4Style: {
      left: '1684px',
      top: '1008px'
    }
  },
  {
    fv4: 'SY-049',
    status: '问题',
    fv4Style: {
      left: '1684px',
      top: '945px'
    }
  },
  {
    fv4: 'SY-048',
    status: '问题',
    fv4Style: {
      left: '1684px',
      top: '883px'
    }
  },
  {
    fv4: 'SY-052',
    status: '问题',
    fv4Style: {
      left: '1684px',
      top: '821px'
    }
  },
  // 8：主干3-环1

  // 9：主干3-环2
  {
    fv4: 'SY-047',
    status: '问题',
    fv4Style: {
      left: '1319px',
      top: '664px',
      width: '76px',
      height: '83px'
    }
  },
  {
    fv4: 'SY-053',
    status: '问题',
    fv4Style: {
      left: '1415px',
      top: '705px'
    }
  },
  {
    fv4: 'SY-054',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '705px'
    }
  },
  {
    fv4: 'SY-055',
    status: '问题',
    fv4Style: {
      left: '1607px',
      top: '705px'
    }
  },
  {
    fv4: 'SY-056',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '705px'
    }
  },
  {
    fv4: 'SY-124',
    status: '问题',
    fv4Style: {
      left: '1799px',
      top: '674px'
    }
  },
  {
    fv4: 'SY-123',
    status: '问题',
    fv4Style: {
      left: '1799px',
      top: '612px'
    }
  },
  {
    fv4: 'SY-125',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '581px'
    }
  },
  {
    fv4: 'SY-126',
    status: '问题',
    fv4Style: {
      left: '1607px',
      top: '581px'
    }
  },
  {
    fv4: 'SY-127',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '581px'
    }
  },
  {
    fv4: 'SY-118',
    status: '问题',
    fv4Style: {
      left: '1415px',
      top: '581px'
    }
  },
  {
    fv4: 'SY-101',
    status: '问题',
    fv4Style: {
      left: '1319px',
      top: '499px',
      width: '76px',
      height: '124px '
    }
  },
  // 9：主干3-环2

  // 10：主干3-环3
  {
    fv4: 'SY-107',
    status: '问题',
    fv4Style: {
      left: '1415px',
      top: '499px'
    }
  },
  {
    fv4: 'SY-106',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '499px'
    }
  },
  {
    fv4: 'SY-111',
    status: '问题',
    fv4Style: {
      left: '1607px',
      top: '499px'
    }
  },
  {
    fv4: 'SY-115',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '499px'
    }
  },
  {
    fv4: 'SY-116',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '437px'
    }
  },
  {
    fv4: 'SY-114',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '375px'
    }
  },
  {
    fv4: 'SY-119',
    status: '问题',
    fv4Style: {
      left: '1607px',
      top: '375px'
    }
  },
  {
    fv4: 'SY-120',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '375px'
    }
  },
  {
    fv4: 'SY-117',
    status: '问题',
    fv4Style: {
      left: '1415px',
      top: '375px'
    }
  },
  {
    fv4: 'SY-113',
    status: '问题',
    fv4Style: {
      left: '1319px',
      top: '293px',
      width: '76px',
      height: '124px'
    }
  },
  // 10：主干3-环3
  // 11：主干3-环4
  {
    fv4: 'SY-110',
    status: '问题',
    fv4Style: {
      left: '1415px ',
      top: '293px'
    }
  },
  {
    fv4: 'SY-112',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '293px'
    }
  },
  {
    fv4: 'SY-108',
    status: '问题',
    fv4Style: {
      left: '1607px',
      top: '293px'
    }
  },
  {
    fv4: 'SY-102',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '293px'
    }
  },
  {
    fv4: 'SY-103',
    status: '问题',
    fv4Style: {
      left: '1799px',
      top: '293px'
    }
  },
  {
    fv4: 'SY-104',
    status: '问题',
    fv4Style: {
      left: '1799px',
      top: '231px'
    }
  },
  {
    fv4: 'SY-109',
    status: '问题',
    fv4Style: {
      left: '1799px',
      top: '169px'
    }
  },
  {
    fv4: 'SY-520',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '169px'
    }
  },
  {
    fv4: 'SY-105',
    status: '问题',
    fv4Style: {
      left: '1607px',
      top: '169px'
    }
  },
  {
    fv4: 'SY-099',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '169px'
    }
  },
  {
    fv4: 'SY-098',
    status: '问题',
    fv4Style: {
      left: '1415px',
      top: '169px'
    }
  }
]

/** 京智网-正商雅筑-实时中心环-1 */
export const jingzhizhengshangyazhu1 = [
  // 1：主干1-环1
  {
    fv4: 'SY-254',
    status: '问题',
    fv4Style: {
      left: '525px',
      top: '111px',
      width: '76px',
      height: '124px'
    }
  },
  {
    fv4: 'SY-253',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '152px'
    }
  },
  {
    fv4: 'SY-479',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '152px'
    }
  },
  {
    fv4: 'SY-252',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '152px'
    }
  },
  {
    fv4: 'SY-251',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '152px'
    }
  },
  {
    fv4: 'SY-263',
    status: '问题',
    fv4Style: {
      left: '45px',
      top: '152px'
    }
  },
  {
    fv4: 'SY-266',
    status: '问题',
    fv4Style: {
      left: '45px',
      top: '214px'
    }
  },
  {
    fv4: 'SY-271',
    status: '问题',
    fv4Style: {
      left: '45px',
      top: '276px'
    }
  },
  {
    fv4: 'SY-519',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '276px'
    }
  },
  {
    fv4: 'SY-262',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '276px'
    }
  },
  {
    fv4: 'SY-243',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '276px'
    }
  },
  // 2：主干1-环2
  {
    fv4: 'SY-250',
    status: '问题',
    fv4Style: {
      left: '525px',
      top: '276px',
      width: '76px',
      height: '124px'
    }
  },
  {
    fv4: 'SY-516',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '358px'
    }
  },
  {
    fv4: 'SY-517',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '358px'
    }
  },
  {
    fv4: 'SY-518',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '358px'
    }
  }, {
    fv4: 'SY-268',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '358px'
    }
  }, {
    fv4: 'SY-535',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '420px'
    }
  }, {
    fv4: 'SY-265',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '482px'
    }
  }, {
    fv4: 'SY-532',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '482px'
    }
  }, {
    fv4: 'SY-261',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '482px'
    }
  }, {
    fv4: 'SY-264',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '482px'
    }
  },
  // 3：主干1-环3
  {
    fv4: 'SY-248',
    status: '问题',
    fv4Style: {
      left: '525px',
      top: '482px',
      width: '76px',
      height: '124px'
    }
  }, {
    fv4: 'SY-533',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '564px'
    }
  }, {
    fv4: 'SY-249',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '564px'
    }
  }, {
    fv4: 'SY-242',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '626px'
    }
  }, {
    fv4: 'SY-241',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '688px'
    }
  }, {
    fv4: 'SY-228',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '688px'
    }
  },
  // 4：主干2-环1
  {
    fv4: 'SY-276',
    status: '问题',
    fv4Style: {
      left: '525px',
      top: '799px',
      width: '124px',
      height: '76px'
    }
  }, {
    fv4: 'SY-274',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '799px'
    }
  }, {
    fv4: 'SY-273',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '799px'
    }
  }, {
    fv4: 'SY-272',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '839px'
    }
  }, {
    fv4: 'SY-260',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '901px'
    }
  }, {
    fv4: 'SY-259',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '942px'
    }
  }, {
    fv4: 'SY-258',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '942px'
    }
  },
  // 5：主干2-环2
  {
    fv4: 'SY-238',
    status: '问题',
    fv4Style: {
      left: '703px',
      top: '799px',
      width: '124px',
      height: '76px'
    }
  },
  {
    fv4: 'SY-245',
    status: '问题',
    fv4Style: {
      left: '703px',
      top: '895px'
    }
  },
  {
    fv4: 'SY-277',
    status: '问题',
    fv4Style: {
      left: '703px',
      top: '957px'
    }
  },
  {
    fv4: 'SY-237',
    status: '问题',
    fv4Style: {
      left: '703px',
      top: '1019px'
    }
  },
  {
    fv4: 'SY-257',
    status: '问题',
    fv4Style: {
      left: '799px',
      top: '1019px'
    }
  },
  {
    fv4: 'SY-236',
    status: '问题',
    fv4Style: {
      left: '895px',
      top: '1019px'
    }
  },
  {
    fv4: 'SY-256',
    status: '问题',
    fv4Style: {
      left: '991px',
      top: '1019px'
    }
  },
  {
    fv4: 'SY-255',
    status: '问题',
    fv4Style: {
      left: '1087px',
      top: '1019px'
    }
  },
  {
    fv4: 'SY-244',
    status: '问题',
    fv4Style: {
      left: '1183px',
      top: '1019px'
    }
  },
  {
    fv4: 'SY-235',
    status: '问题',
    fv4Style: {
      left: '1183px',
      top: '957px'
    }
  },
  {
    fv4: 'SY-233',
    status: '问题',
    fv4Style: {
      left: '1183px',
      top: '895px'
    }
  },
  {
    fv4: 'SY-223',
    status: '问题',
    fv4Style: {
      left: '1183px',
      top: '833px '
    }
  },
  // 5：主干2-环2
  // 6：主干2-环3
  {
    fv4: 'SY-224',
    status: '问题',
    fv4Style: {
      left: '1319px',
      top: '799px',
      width: '124px',
      height: '76px'
    }
  },
  {
    fv4: 'SY-222',
    status: '问题',
    fv4Style: {
      left: '1319px',
      top: '895px'
    }
  },
  {
    fv4: 'SY-215',
    status: '问题',
    fv4Style: {
      left: '1319px',
      top: '957px'
    }
  },
  {
    fv4: 'SY-216',
    status: '问题',
    fv4Style: {
      left: '1318px',
      top: '1019px'
    }
  },
  {
    fv4: 'SY-209',
    status: '问题',
    fv4Style: {
      left: '1414px',
      top: '1019px'
    }
  },
  {
    fv4: 'SY-208',
    status: '问题',
    fv4Style: {
      left: '1510px',
      top: '1019px'
    }
  },
  {
    fv4: 'SY-207',
    status: '问题',
    fv4Style: {
      left: '1606px',
      top: '1019px'
    }
  },
  {
    fv4: 'SY-206',
    status: '问题',
    fv4Style: {
      left: '1702px',
      top: '1019px'
    }
  },
  // 6：主干2-环3
  // 7：主干2-环4
  {
    fv4: 'SY-197',
    status: '问题',
    fv4Style: {
      left: '1319px',
      top: '647px',
      width: '76px',
      height: '124px'
    }
  },
  {
    fv4: 'SY-196',
    status: '问题',
    fv4Style: {
      left: '1415px',
      top: '688px'
    }
  },
  {
    fv4: 'SY-195',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '688px'
    }
  },
  {
    fv4: 'SY-183',
    status: '问题',
    fv4Style: {
      left: '1607px',
      top: '688px'
    }
  },
  {
    fv4: 'SY-184',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '626px'
    }
  },
  {
    fv4: 'SY-180',
    status: '问题',
    fv4Style: {
      left: '1607px',
      top: '564px'
    }
  },
  {
    fv4: 'SY-185',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '564px'
    }
  },
  {
    fv4: 'SY-186',
    status: '问题',
    fv4Style: {
      left: '1415px',
      top: '564px'
    }
  },
  {
    fv4: 'SY-175',
    status: '问题',
    fv4Style: {
      left: '1319px',
      top: '482px',
      width: '76px',
      height: '124px'
    }
  },
  // 7：主干2-环4
  // 8：主干2-环5
  {
    fv4: 'SY-174',
    status: '问题',
    fv4Style: {
      left: '1415px',
      top: '482px'
    }
  },
  {
    fv4: 'SY-156',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '482px'
    }
  },
  {
    fv4: 'SY-158',
    status: '问题',
    fv4Style: {
      left: '1607px',
      top: '482px'
    }
  },
  {
    fv4: 'SY-164',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '482px'
    }
  },
  {
    fv4: 'SY-063',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '420px'
    }
  },
  {
    fv4: 'SY-160',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '358px'
    }
  },
  {
    fv4: 'SY-159',
    status: '问题',
    fv4Style: {
      left: '1607px',
      top: '358px'
    }
  },
  {
    fv4: 'SY-157',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '358px'
    }
  },
  {
    fv4: 'SY-148',
    status: '问题',
    fv4Style: {
      left: '1415px',
      top: '358px'
    }
  },
  {
    fv4: 'SY-149',
    status: '问题',
    fv4Style: {
      left: '1319px',
      top: '276px ',
      width: '76px',
      height: '124px'
    }
  },
  // 8：主干2-环5
  // 9：主干2-环6
  {
    fv4: 'SY-150',
    status: '问题',
    fv4Style: {
      left: '1415px',
      top: '276px'
    }
  },
  {
    fv4: 'SY-138',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '276px'
    }
  },
  {
    fv4: 'SY-137',
    status: '问题',
    fv4Style: {
      left: '1607px',
      top: '276px'
    }
  },
  {
    fv4: 'SY-139',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '276px'
    }
  },
  {
    fv4: 'SY-147',
    status: '问题',
    fv4Style: {
      left: '1799px',
      top: '245px'
    }
  },
  {
    fv4: 'SY-136',
    status: '问题',
    fv4Style: {
      left: '1799px',
      top: '183px'
    }
  },
  {
    fv4: 'SY-525',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '152px'
    }
  },
  {
    fv4: 'SY-128',
    status: '问题',
    fv4Style: {
      left: '1607px',
      top: '152px'
    }
  },
  {
    fv4: 'SY-129',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '152px'
    }
  },
  {
    fv4: 'SY-130',
    status: '问题',
    fv4Style: {
      left: '1415px',
      top: '152px'
    }
  }
  // 9：主干2-环6
]

/** 京智网-正商雅筑-实时中心环-2 */
export const jingzhizhengshangyazhu2 = [
  // 1：主干3-环1
  {
    fv4: 'SY-231',
    status: '问题',
    fv4Style: {
      left: '525px',
      top: '128px',
      width: '76px',
      height: '124px'
    }
  },
  {
    fv4: 'SY-230',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '169px'
    }
  },
  {
    fv4: 'SY-526',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '169px'
    }
  },
  {
    fv4: 'SY-229',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '169px'
    }
  },
  {
    fv4: 'SY-213',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '200px'
    }
  },
  {
    fv4: 'SY-212',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '262px'
    }
  },
  {
    fv4: 'SY-211',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '293px'
    }
  },
  {
    fv4: 'SY-220',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '293px'
    }
  },
  {
    fv4: 'SY-221',
    status: '问题',
    fv4Style: {
      left: '430px',
      top: '293px'
    }
  },
  // 2：主干3-环2
  {
    fv4: 'SY-227',
    status: '问题',
    fv4Style: {
      left: '525px',
      top: '293px',
      width: '76px',
      height: '124px'
    }
  },
  {
    fv4: 'SY-240',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '375px'
    }
  },
  {
    fv4: 'SY-247',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '375px'
    }
  },
  {
    fv4: 'SY-218',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '375px'
    }
  },
  {
    fv4: 'SY-217',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '406px'
    }
  },
  {
    fv4: 'SY-246',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '468px'
    }
  },
  {
    fv4: 'SY-239',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '499px'
    }
  },
  {
    fv4: 'SY-234',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '499px'
    }
  },
  {
    fv4: 'SY-226',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '499px'
    }
  },
  // 3：主干3-环3
  {
    fv4: 'SY-225',
    status: '问题',
    fv4Style: {
      left: '525px',
      top: '499px',
      width: '76px',
      height: '124px'
    }
  },
  {
    fv4: 'SY-534',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '581px'
    }
  },
  {
    fv4: 'SY-219',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '581px'
    }
  },
  {
    fv4: 'SY-210',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '581px'
    }
  },
  {
    fv4: 'SY-200',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '581px'
    }
  },
  {
    fv4: 'SY-199',
    status: '问题',
    fv4Style: {
      left: '45px',
      top: '581px'
    }
  },
  {
    fv4: 'SY-198',
    status: '问题',
    fv4Style: {
      left: '45px',
      top: '643px'
    }
  },
  {
    fv4: 'SY-187',
    status: '问题',
    fv4Style: {
      left: '45px',
      top: '705px'
    }
  },
  {
    fv4: 'SY-188',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '705px'
    }
  },
  {
    fv4: 'SY-189',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '705px'
    }
  },
  {
    fv4: 'SY-182',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '705px'
    }
  },
  // 4：主干3-环4
  {
    fv4: 'SY-176',
    status: '问题',
    fv4Style: {
      left: '501px',
      top: '787px',
      width: '124px',
      height: '76px'
    }
  },
  {
    fv4: 'SY-165',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '835px'
    }
  },
  {
    fv4: 'SY-064',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '897px'
    }
  },
  {
    fv4: 'SY-132',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '959px'
    }
  },
  {
    fv4: 'SY-161',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '1007px'
    }
  },
  {
    fv4: 'SY-162',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '1007px'
    }
  },
  {
    fv4: 'SY-152',
    status: '问题',
    fv4Style: {
      left: '525px',
      top: '1007px'
    }
  },
  {
    fv4: 'SY-151',
    status: '问题',
    fv4Style: {
      left: '621px',
      top: '1007px'
    }
  },
  {
    fv4: 'SY-145',
    status: '问题',
    fv4Style: {
      left: '717px',
      top: '1007px'
    }
  },
  {
    fv4: 'SY-140',
    status: '问题',
    fv4Style: {
      left: '717px',
      top: '945px'
    }
  },
  // 5：主干3-环5
  {
    fv4: 'SY-141',
    status: '问题',
    fv4Style: {
      left: '853px',
      top: '787px',
      width: '124px',
      height: '76px'
    }
  },
  {
    fv4: 'SY-146',
    status: '问题',
    fv4Style: {
      left: '877px',
      top: '883px'
    }
  },
  {
    fv4: 'SY-142',
    status: '问题',
    fv4Style: {
      left: '877px',
      top: '945px'
    }
  },
  {
    fv4: 'SY-133',
    status: '问题',
    fv4Style: {
      left: '877px',
      top: '1007px'
    }
  },
  {
    fv4: 'SY-122',
    status: '问题',
    fv4Style: {
      left: '973px',
      top: '1008px'
    }
  },
  {
    fv4: 'SY-121',
    status: '问题',
    fv4Style: {
      left: '1069px',
      top: '1007px'
    }
  },
  {
    fv4: 'SY-135',
    status: '问题',
    fv4Style: {
      left: '1165px',
      top: '1008px'
    }
  },
  {
    fv4: 'SY-134',
    status: '问题',
    fv4Style: {
      left: '1261px',
      top: '1007px'
    }
  },
  {
    fv4: 'SY-143',
    status: '问题',
    fv4Style: {
      left: '1261px',
      top: '945px'
    }
  },
  {
    fv4: 'SY-155',
    status: '问题',
    fv4Style: {
      left: '1261px',
      top: '883px'
    }
  },
  // 6：主干3-环6
  {
    fv4: 'SY-169',
    status: '问题',
    fv4Style: {
      left: '1319px',
      top: '664px',
      width: '76px',
      height: '124px'
    }
  },
  {
    fv4: 'SY-154',
    status: '问题',
    fv4Style: {
      left: '1415px',
      top: '705px'
    }
  },
  {
    fv4: 'SY-131',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '705px'
    }
  },
  {
    fv4: 'SY-153',
    status: '问题',
    fv4Style: {
      left: '1607px',
      top: '705px'
    }
  },
  {
    fv4: 'SY-163',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '674px'
    }
  },
  {
    fv4: 'SY-166',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '612px'
    }
  },
  {
    fv4: 'SY-067',
    status: '问题',
    fv4Style: {
      left: '1607px',
      top: '581px'
    }
  },
  {
    fv4: 'SY-167',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '581px'
    }
  },
  {
    fv4: 'SY-168',
    status: '问题',
    fv4Style: {
      left: '1415px',
      top: '581px'
    }
  },
  // 7：主干3-环7
  {
    fv4: 'SY-170',
    status: '问题',
    fv4Style: {
      left: '1319px',
      top: '499px',
      width: '76px',
      height: '124px'
    }
  },
  {
    fv4: 'SY-173',
    status: '问题',
    fv4Style: {
      left: '1415px',
      top: '499px'
    }
  },
  {
    fv4: 'SY-179',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '499px'
    }
  },
  {
    fv4: 'SY-193',
    status: '问题',
    fv4Style: {
      left: '1607px',
      top: '499px'
    }
  },
  {
    fv4: 'SY-531',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '499px'
    }
  },
  {
    fv4: 'SY-192',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '437px'
    }
  },
  {
    fv4: 'SY-181',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '375px'
    }
  },
  {
    fv4: 'SY-178',
    status: '问题',
    fv4Style: {
      left: '1607px',
      top: '375px'
    }
  },
  {
    fv4: 'SY-172',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '375px'
    }
  },
  {
    fv4: 'SY-171',
    status: '问题',
    fv4Style: {
      left: '1415px',
      top: '375px'
    }
  },
  // 8：主干3-环8
  {
    fv4: 'SY-177',
    status: '问题',
    fv4Style: {
      left: '1319px',
      top: '293px',
      width: '76px',
      height: '124px'
    }
  },
  {
    fv4: 'SY-191',
    status: '问题',
    fv4Style: {
      left: '1415px',
      top: '293px'
    }
  },
  {
    fv4: 'SY-190',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '293px'
    }
  },
  {
    fv4: 'SY-201',
    status: '问题',
    fv4Style: {
      left: '1607px',
      top: '293px'
    }
  },
  {
    fv4: 'SY-202',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '293px'
    }
  },
  {
    fv4: 'SY-205',
    status: '问题',
    fv4Style: {
      left: '1799px',
      top: '262px'
    }
  },
  {
    fv4: 'SY-214',
    status: '问题',
    fv4Style: {
      left: '1799px',
      top: '200px'
    }
  },
  {
    fv4: 'SY-204',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '169px'
    }
  },
  {
    fv4: 'SY-060',
    status: '问题',
    fv4Style: {
      left: '1607px',
      top: '169px'
    }
  },
  {
    fv4: 'SY-194',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '169px'
    }
  },
  {
    fv4: 'SY-203',
    status: '问题',
    fv4Style: {
      left: '1415px',
      top: '169px'
    }
  }
]

/** 京智网-空港机房-实时中心环-1 */
export const jingzhikonggangjifang1 = [
  // 1：主干1-环1
  {
    fv4: 'SY-346',
    status: '问题',
    fv4Style: {
      left: '525px',
      top: '128px',
      width: '76px',
      height: '124px'
    }
  },
  {
    fv4: 'SY-342',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '169px'
    }
  },
  {
    fv4: 'SY-335',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '169px'
    }
  },
  {
    fv4: 'SY-336',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '169px'
    }
  },
  {
    fv4: 'SY-337',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '169px'
    }
  },
  {
    fv4: 'SY-333',
    status: '问题',
    fv4Style: {
      left: '45px',
      top: '200px'
    }
  },
  {
    fv4: 'SY-339',
    status: '问题',
    fv4Style: {
      left: '45px',
      top: '262px'
    }
  },
  {
    fv4: 'SY-338',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '293px'
    }
  },
  {
    fv4: 'SY-343',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '293px'
    }
  },
  {
    fv4: 'SY-344',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '293px'
    }
  },
  {
    fv4: 'SY-347',
    status: '问题',
    fv4Style: {
      left: '430px',
      top: '293px'
    }
  },
  // 2：主干1-环2
  {
    fv4: 'SY-351',
    status: '问题',
    fv4Style: {
      left: '525px',
      top: '293px',
      width: '76px',
      height: '124px'
    }
  },
  {
    fv4: 'SY-353',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '375px'
    }
  },
  {
    fv4: 'SY-341',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '375px'
    }
  },
  {
    fv4: 'SY-334',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '375px'
    }
  },
  {
    fv4: 'SY-340',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '375px'
    }
  },
  {
    fv4: 'SY-345',
    status: '问题',
    fv4Style: {
      left: '45px',
      top: '375px'
    }
  },
  {
    fv4: 'SY-370',
    status: '问题',
    fv4Style: {
      left: '45px',
      top: '437px'
    }
  },
  {
    fv4: 'SY-380',
    status: '问题',
    fv4Style: {
      left: '45px',
      top: '499px'
    }
  },
  {
    fv4: 'SY-381',
    status: '问题',
    fv4Style: {
      left: '45px',
      top: '561px'
    }
  },
  {
    fv4: 'SY-382',
    status: '问题',
    fv4Style: {
      left: '45px',
      top: '623px'
    }
  },
  {
    fv4: 'SY-383',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '623px'
    }
  },
  {
    fv4: 'SY-348',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '623px'
    }
  },
  {
    fv4: 'SY-349',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '623px'
    }
  },
  {
    fv4: 'SY-350',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '623px'
    }
  },
  // 3：主干1-环3
  {
    fv4: 'SY-458',
    status: '问题',
    fv4Style: {
      left: '525px',
      top: '623px',
      width: '76px',
      height: '124px'
    }
  },
  {
    fv4: 'SY-459',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '705px'
    }
  },
  {
    fv4: 'SY-460',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '705px'
    }
  },
  {
    fv4: 'SY-453',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '705px'
    }
  },
  {
    fv4: 'SY-452',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '705px'
    }
  },
  {
    fv4: 'SY-451',
    status: '问题',
    fv4Style: {
      left: '45px',
      top: '736px'
    }
  },
  {
    fv4: 'SY-448',
    status: '问题',
    fv4Style: {
      left: '45px',
      top: '798px'
    }
  },
  {
    fv4: 'SY-446',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '829px'
    }
  },
  {
    fv4: 'SY-447',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '829px'
    }
  },
  {
    fv4: 'SY-450',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '829px'
    }
  },
  {
    fv4: 'SY-455',
    status: '问题',
    fv4Style: {
      left: '430px',
      top: '829px'
    }
  },
  // 4：主干1-环4
  {
    fv4: 'SY-465',
    status: '问题',
    fv4Style: {
      left: '525px',
      top: '795px',
      width: '124px',
      height: '76px'
    }
  },
  {
    fv4: 'SY-466',
    status: '问题',
    fv4Style: {
      left: '526px',
      top: '891px'
    }
  },
  {
    fv4: 'SY-467',
    status: '问题',
    fv4Style: {
      left: '526px',
      top: '953px'
    }
  },
  {
    fv4: 'SY-469',
    status: '问题',
    fv4Style: {
      left: '622px',
      top: '953px'
    }
  },
  {
    fv4: 'SY-471',
    status: '问题',
    fv4Style: {
      left: '718px',
      top: '953px'
    }
  },
  {
    fv4: 'SY-470',
    status: '问题',
    fv4Style: {
      left: '814px',
      top: '953px'
    }
  },
  {
    fv4: 'SY-468',
    status: '问题',
    fv4Style: {
      left: '814px',
      top: '891px'
    }
  },
  // 5：主干1-环5
  {
    fv4: 'SY-454',
    status: '问题',
    fv4Style: {
      left: '930px',
      top: '795px',
      width: '124px',
      height: '76px'
    }
  },
  {
    fv4: 'SY-456',
    status: '问题',
    fv4Style: {
      left: '954px',
      top: '891px'
    }
  },
  {
    fv4: 'SY-417',
    status: '问题',
    fv4Style: {
      left: '954px',
      top: '953px'
    }
  },
  {
    fv4: 'SY-379',
    status: '问题',
    fv4Style: {
      left: '954px',
      top: '1015px'
    }
  },
  {
    fv4: 'SY-402',
    status: '问题',
    fv4Style: {
      left: '1050px',
      top: '1015px'
    }
  },
  {
    fv4: 'SY-401',
    status: '问题',
    fv4Style: {
      left: '1146px',
      top: '1016px'
    }
  },
  {
    fv4: 'SY-431',
    status: '问题',
    fv4Style: {
      left: '1242px',
      top: '1015px'
    }
  },
  {
    fv4: 'SY-439',
    status: '问题',
    fv4Style: {
      left: '1338px',
      top: '1016px'
    }
  },
  {
    fv4: 'SY-524',
    status: '问题',
    fv4Style: {
      left: '1434px',
      top: '1015px'
    }
  },
  {
    fv4: 'SY-445',
    status: '问题',
    fv4Style: {
      left: '1434px',
      top: '953px'
    }
  },
  {
    fv4: 'SY-449',
    status: '问题',
    fv4Style: {
      left: '1434px',
      top: '891px'
    }
  },
  {
    fv4: 'SY-457',
    status: '问题',
    fv4Style: {
      left: '1434px',
      top: '829px'
    }
  },
  // 5：主干1-环5
  // 6：主干1-环6
  {
    fv4: 'SY-463',
    status: '问题',
    fv4Style: {
      left: '1319px',
      top: '664px',
      width: '76px',
      height: '124px'
    }
  },
  {
    fv4: 'SY-444',
    status: '问题',
    fv4Style: {
      left: '1415px',
      top: '705px'
    }
  },
  {
    fv4: 'SY-442',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '705px'
    }
  },
  {
    fv4: 'SY-441',
    status: '问题',
    fv4Style: {
      left: '1607px',
      top: '705px'
    }
  },
  {
    fv4: 'SY-440',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '674px'
    }
  },
  {
    fv4: 'SY-419',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '613px'
    }
  },
  {
    fv4: 'SY-443',
    status: '问题',
    fv4Style: {
      left: '1607px',
      top: '582px'
    }
  },
  {
    fv4: 'SY-461',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '581px'
    }
  },
  {
    fv4: 'SY-462',
    status: '问题',
    fv4Style: {
      left: '1415px',
      top: '581px'
    }
  },
  // 6：主干1-环6
  // 7：主干1-环7
  {
    fv4: 'SY-513',
    status: '问题',
    fv4Style: {
      left: '1319px',
      top: '499px',
      width: '76px',
      height: '124px'
    }
  },
  {
    fv4: 'SY-404',
    status: '问题',
    fv4Style: {
      left: '1415px',
      top: '499px'
    }
  },
  {
    fv4: 'SY-403',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '499px'
    }
  },
  {
    fv4: 'SY-405',
    status: '问题',
    fv4Style: {
      left: '1607px',
      top: '499px'
    }
  },
  {
    fv4: 'SY-406',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '499px'
    }
  },
  {
    fv4: 'SY-407',
    status: '问题',
    fv4Style: {
      left: '1799px',
      top: '468px'
    }
  },
  {
    fv4: 'SY-408',
    status: '问题',
    fv4Style: {
      left: '1799px',
      top: '406px'
    }
  },
  {
    fv4: 'SY-409',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '375px'
    }
  },
  {
    fv4: 'SY-410',
    status: '问题',
    fv4Style: {
      left: '1607px',
      top: '375px'
    }
  },
  {
    fv4: 'SY-411',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '375px'
    }
  },
  {
    fv4: 'SY-514',
    status: '问题',
    fv4Style: {
      left: '1415px',
      top: '375px'
    }
  },
  // 7：主干1-环7
  // 8：主干1-环8
  {
    fv4: 'SY-422',
    status: '问题',
    fv4Style: {
      left: '1319px',
      top: '293px',
      width: '76px',
      height: '124px'
    }
  },
  {
    fv4: 'SY-421',
    status: '问题',
    fv4Style: {
      left: '1415px',
      top: '293px'
    }
  },
  {
    fv4: 'SY-420',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '293px'
    }
  },
  {
    fv4: 'SY-418',
    status: '问题',
    fv4Style: {
      left: '1607px ',
      top: '293px'
    }
  },
  {
    fv4: 'SY-412',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '293px'
    }
  },
  {
    fv4: 'SY-413',
    status: '问题',
    fv4Style: {
      left: '1799px',
      top: '293px'
    }
  },
  {
    fv4: 'SY-424',
    status: '问题',
    fv4Style: {
      left: '1799px',
      top: '231px'
    }
  },
  {
    fv4: 'SY-423',
    status: '问题',
    fv4Style: {
      left: '1799px',
      top: '169px'
    }
  },
  {
    fv4: 'SY-427',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '169px'
    }
  },
  {
    fv4: 'SY-428',
    status: '问题',
    fv4Style: {
      left: '1607px',
      top: '169px'
    }
  },
  {
    fv4: 'SY-435',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '169px'
    }
  },
  {
    fv4: 'SY-434',
    status: '问题',
    fv4Style: {
      left: '1415px',
      top: '169px'
    }
  }
  // 8：主干1-环8
]

/** 京智网-空港机房-实时中心环-2 */
export const jingzhikonggangjifang2 = [
  // 1：主干2-环1
  {
    fv4: 'SY-416',
    status: '问题',
    fv4Style: {
      left: '573px',
      top: '160px',
      width: '76px',
      height: '124px'
    }
  },
  {
    fv4: 'SY-415',
    status: '问题',
    fv4Style: {
      left: '477px',
      top: '201px'
    }
  }, {
    fv4: 'SY-426',
    status: '问题',
    fv4Style: {
      left: '381px',
      top: '201px'
    }
  }, {
    fv4: 'SY-430',
    status: '问题',
    fv4Style: {
      left: '285px',
      top: '201px'
    }
  }, {
    fv4: 'SY-433',
    status: '问题',
    fv4Style: {
      left: '189px',
      top: '201px'
    }
  }, {
    fv4: 'SY-438',
    status: '问题',
    fv4Style: {
      left: '93px',
      top: '201px'
    }
  }, {
    fv4: 'SY-437',
    status: '问题',
    fv4Style: {
      left: '93px',
      top: '263px'
    }
  }, {
    fv4: 'SY-436',
    status: '问题',
    fv4Style: {
      left: '93px',
      top: '325px'
    }
  }, {
    fv4: 'SY-432',
    status: '问题',
    fv4Style: {
      left: '189px',
      top: '325px'
    }
  }, {
    fv4: 'SY-429',
    status: '问题',
    fv4Style: {
      left: '285px',
      top: '325px'
    }
  }, {
    fv4: 'SY-425',
    status: '问题',
    fv4Style: {
      left: '381px',
      top: '325px'
    }
  }, {
    fv4: 'SY-414',
    status: '问题',
    fv4Style: {
      left: '478px',
      top: '325px'
    }
  },
  // 2：主干2-环2
  {
    fv4: 'SY-388',
    status: '问题',
    fv4Style: {
      left: '573px',
      top: '325px',
      width: '76px',
      height: '124px'
    }
  },
  {
    fv4: 'SY-389',
    status: '问题',
    fv4Style: {
      left: '477px',
      top: '407px'
    }
  }, {
    fv4: 'SY-390',
    status: '问题',
    fv4Style: {
      left: '381px',
      top: '407px'
    }
  }, {
    fv4: 'SY-377',
    status: '问题',
    fv4Style: {
      left: '285px',
      top: '407px'
    }
  }, {
    fv4: 'SY-378',
    status: '问题',
    fv4Style: {
      left: '189px',
      top: '407px'
    }
  }, {
    fv4: 'SY-391',
    status: '问题',
    fv4Style: {
      left: '93px',
      top: '407px'
    }
  }, {
    fv4: 'SY-400',
    status: '问题',
    fv4Style: {
      left: '93px',
      top: '469px'
    }
  }, {
    fv4: 'SY-399',
    status: '问题',
    fv4Style: {
      left: '93px',
      top: '531px'
    }
  }, {
    fv4: 'SY-398',
    status: '问题',
    fv4Style: {
      left: '189px',
      top: '531px'
    }
  }, {
    fv4: 'SY-397',
    status: '问题',
    fv4Style: {
      left: '285px',
      top: '531px'
    }
  }, {
    fv4: 'SY-396',
    status: '问题',
    fv4Style: {
      left: '381px',
      top: '531px'
    }
  }, {
    fv4: 'SY-387',
    status: '问题',
    fv4Style: {
      left: '477px',
      top: '531px'
    }
  },
  // 3：主干2-环3
  {
    fv4: 'SY-375',
    status: '问题',
    fv4Style: {
      left: '573px',
      top: '531px',
      width: '76px',
      height: '124px'
    }
  }, {
    fv4: 'SY-374',
    status: '问题',
    fv4Style: {
      left: '477px',
      top: '613px'
    }
  }, {
    fv4: 'SY-373',
    status: '问题',
    fv4Style: {
      left: '381px',
      top: '613px'
    }
  },
  {
    fv4: 'SY-384',
    status: '问题',
    fv4Style: {
      left: '285px',
      top: '613px'
    }
  }, {
    fv4: 'SY-393',
    status: '问题',
    fv4Style: {
      left: '189px',
      top: '613px'
    }
  }, {
    fv4: 'SY-392',
    status: '问题',
    fv4Style: {
      left: '93px',
      top: '613px'
    }
  }, {
    fv4: 'SY-395',
    status: '问题',
    fv4Style: {
      left: '93px',
      top: '675px'
    }
  }, {
    fv4: 'SY-394',
    status: '问题',
    fv4Style: {
      left: '93px',
      top: '737px'
    }
  }, {
    fv4: 'SY-385',
    status: '问题',
    fv4Style: {
      left: '189px',
      top: '737px'
    }
  }, {
    fv4: 'SY-386',
    status: '问题',
    fv4Style: {
      left: '285px',
      top: '737px'
    }
  }, {
    fv4: 'SY-376',
    status: '问题',
    fv4Style: {
      left: '381px',
      top: '737px'
    }
  }, {
    fv4: 'SY-529',
    status: '问题',
    fv4Style: {
      left: '477px',
      top: '737px'
    }
  },
  // 4：主干2-环4
  {
    fv4: 'SY-372',
    status: '问题',
    fv4Style: {
      left: '549px',
      top: '819px',
      width: '124px',
      height: '76px'
    }
  }, {
    fv4: 'SY-371',
    status: '问题',
    fv4Style: {
      left: '573px',
      top: '915px'
    }
  }, {
    fv4: 'SY-356',
    status: '问题',
    fv4Style: {
      left: '573px',
      top: '977px'
    }
  }, {
    fv4: 'SY-355',
    status: '问题',
    fv4Style: {
      left: '669px',
      top: '977px'
    }
  }, {
    fv4: 'SY-530',
    status: '问题',
    fv4Style: {
      left: '765px',
      top: '977px'
    }
  },
  {
    fv4: 'SY-329',
    status: '问题',
    fv4Style: {
      left: '861px',
      top: '977px'
    }
  }, {
    fv4: 'SY-330',
    status: '问题',
    fv4Style: {
      left: '957px',
      top: '977px'
    }
  }, {
    fv4: 'SY-357',
    status: '问题',
    fv4Style: {
      left: '1053px',
      top: '977px'
    }
  }, {
    fv4: 'SY-361',
    status: '问题',
    fv4Style: {
      left: '1149px',
      top: '977px'
    }
  }, {
    fv4: 'SY-364',
    status: '问题',
    fv4Style: {
      left: '1149px',
      top: '915px'
    }
  },
  // 5：主干2-环5
  {
    fv4: 'SY-362',
    status: '问题',
    fv4Style: {
      left: '1367px',
      top: '696px',
      width: '76px',
      height: '124px '
    }
  }, {
    fv4: 'SY-363',
    status: '问题',
    fv4Style: {
      left: '1463px',
      top: '737px'
    }
  }, {
    fv4: 'SY-358',
    status: '问题',
    fv4Style: {
      left: '1559px',
      top: '737px'
    }
  }, {
    fv4: 'SY-359',
    status: '问题',
    fv4Style: {
      left: '1655px',
      top: '737px'
    }
  }, {
    fv4: 'SY-360',
    status: '问题',
    fv4Style: {
      left: '1751px',
      top: '737px'
    }
  }, {
    fv4: 'SY-368',
    status: '问题',
    fv4Style: {
      left: '1751px',
      top: '675px'
    }
  }, {
    fv4: 'SY-369',
    status: '问题',
    fv4Style: {
      left: '1751px',
      top: '613px'
    }
  }, {
    fv4: 'SY-367',
    status: '问题',
    fv4Style: {
      left: '1655px',
      top: '613px'
    }
  }, {
    fv4: 'SY-366',
    status: '问题',
    fv4Style: {
      left: '1559px',
      top: '613px'
    }
  },
  {
    fv4: 'SY-365',
    status: '问题',
    fv4Style: {
      left: '1463px',
      top: '613px'
    }
  },
  // 6：主干2-环6
  {
    fv4: 'SY-323',
    status: '问题',
    fv4Style: {
      left: '1367px',
      top: '531px',
      width: '76px',
      height: '124px'
    }
  }, {
    fv4: 'SY-324',
    status: '问题',
    fv4Style: {
      left: '1463px',
      top: '531px'
    }
  }, {
    fv4: 'SY-325',
    status: '问题',
    fv4Style: {
      left: '1559px',
      top: '531px'
    }
  }, {
    fv4: 'SY-332',
    status: '问题',
    fv4Style: {
      left: '1655px',
      top: '531px'
    }
  }, {
    fv4: 'SY-328',
    status: '问题',
    fv4Style: {
      left: '1751px',
      top: '531px'
    }
  }, {
    fv4: 'SY-327',
    status: '问题',
    fv4Style: {
      left: '1751px',
      top: '469px'
    }
  }, {
    fv4: 'SY-326',
    status: '问题',
    fv4Style: {
      left: '1751px',
      top: '407px'
    }
  }, {
    fv4: 'SY-321',
    status: '问题',
    fv4Style: {
      left: '1655px',
      top: '407px'
    }
  }, {
    fv4: 'SY-331',
    status: '问题',
    fv4Style: {
      left: '1559px',
      top: '407px'
    }
  }, {
    fv4: 'SY-322',
    status: '问题',
    fv4Style: {
      left: '1463px',
      top: '407px'
    }
  },
  // 7：主干2-环7
  {
    fv4: 'SY-317',
    status: '问题',
    fv4Style: {
      left: '1367px',
      top: '325px',
      width: '76px',
      height: '124px'
    }
  }, {
    fv4: 'SY-354',
    status: '问题',
    fv4Style: {
      left: '1559px',
      top: '325px'
    }
  }, {
    fv4: 'SY-320',
    status: '问题',
    fv4Style: {
      left: '1655px',
      top: '263px'
    }
  },
  {
    fv4: 'SY-319',
    status: '问题',
    fv4Style: {
      left: '1559px',
      top: '201px'
    }
  }
]

/** 京智网-半壁店-实时中心环-1 */
export const jingzhibanbidian1 = [
  // 1：主干1-环1
  {
    fv4: 'SY-309',
    status: '问题',
    fv4Style: {
      left: '621px',
      top: '160px',
      width: '76px',
      height: '124px'
    }
  },
  {
    fv4: 'SY-528',
    status: '问题',
    fv4Style: {
      left: '525px',
      top: '201px'
    }
  },
  {
    fv4: 'SY-310',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '201px'
    }
  },
  {
    fv4: 'SY-297',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '201px'
    }
  },
  {
    fv4: 'SY-284',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '201px'
    }
  },
  {
    fv4: 'SY-278',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '201px'
    }
  },
  {
    fv4: 'SY-270',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '263px'
    }
  },
  {
    fv4: 'SY-269',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '325px'
    }
  },
  {
    fv4: 'SY-281',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '325px'
    }
  },
  {
    fv4: 'SY-280',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '325px'
    }
  },
  {
    fv4: 'SY-279',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '325px'
    }
  },
  {
    fv4: 'SY-307',
    status: '问题',
    fv4Style: {
      left: '526px',
      top: '325px'
    }
  },
  // 2：主干1-环2
  {
    fv4: 'SY-285',
    status: '问题',
    fv4Style: {
      left: '621px',
      top: '325px',
      width: '76px',
      height: '124px'
    }
  },
  {
    fv4: 'SY-289',
    status: '问题',
    fv4Style: {
      left: '525px',
      top: '407px'
    }
  },
  {
    fv4: 'SY-293',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '407px'
    }
  },
  {
    fv4: 'SY-298',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '407px'
    }
  },
  {
    fv4: 'SY-302',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '407px'
    }
  },
  {
    fv4: 'SY-306',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '438px'
    }
  },
  {
    fv4: 'SY-303',
    status: '问题',
    fv4Style: {
      left: '141px',
      top: '500px'
    }
  },
  {
    fv4: 'SY-299',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '531px'
    }
  },
  {
    fv4: 'SY-294',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '531px'
    }
  },
  {
    fv4: 'SY-290',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '531px'
    }
  },
  {
    fv4: 'SY-286',
    status: '问题',
    fv4Style: {
      left: '525px',
      top: '531px'
    }
  },
  // 3：主干1-环3
  {
    fv4: 'SY-287',
    status: '问题',
    fv4Style: {
      left: '621px',
      top: '531px',
      width: '76px',
      height: '124px'
    }
  },
  {
    fv4: 'SY-291',
    status: '问题',
    fv4Style: {
      left: '525px',
      top: '613px'
    }
  },
  {
    fv4: 'SY-295',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '613px'
    }
  },
  {
    fv4: 'SY-300',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '613px'
    }
  },
  {
    fv4: 'SY-304',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '613px'
    }
  },
  {
    fv4: 'SY-305',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '675px'
    }
  },
  {
    fv4: 'SY-301',
    status: '问题',
    fv4Style: {
      left: '237px',
      top: '737px'
    }
  },
  {
    fv4: 'SY-296',
    status: '问题',
    fv4Style: {
      left: '333px',
      top: '737px'
    }
  },
  {
    fv4: 'SY-292',
    status: '问题',
    fv4Style: {
      left: '429px',
      top: '737px'
    }
  },
  {
    fv4: 'SY-288',
    status: '问题',
    fv4Style: {
      left: '525px',
      top: '737px'
    }
  },
  // 3：主干1-环3
  // 4：主干1-环4
  {
    fv4: 'SY-521',
    status: '问题',
    fv4Style: {
      left: '597px',
      top: '819px',
      width: '124px',
      height: '76px'
    }
  },
  {
    fv4: 'SY-059',
    status: '问题',
    fv4Style: {
      left: '621px',
      top: '915px'
    }
  },
  {
    fv4: 'SY-283',
    status: '问题',
    fv4Style: {
      left: '717px',
      top: '977px '
    }
  },
  {
    fv4: 'SY-315',
    status: '问题',
    fv4Style: {
      left: '813px',
      top: '977px'
    }
  },
  {
    fv4: 'SY-314',
    status: '问题',
    fv4Style: {
      left: '909px',
      top: '977px'
    }
  },
  {
    fv4: 'SY-058',
    status: '问题',
    fv4Style: {
      left: '1005px',
      top: '977px'
    }
  },
  {
    fv4: 'SY-312',
    status: '问题',
    fv4Style: {
      left: '1101px ',
      top: '977px'
    }
  },
  {
    fv4: 'SY-313',
    status: '问题',
    fv4Style: {
      left: '1197px',
      top: '977px'
    }
  },
  {
    fv4: 'SY-311',
    status: '问题',
    fv4Style: {
      left: '1293px',
      top: '977px'
    }
  },
  {
    fv4: 'SY-308',
    status: '问题',
    fv4Style: {
      left: '1389px',
      top: '977px'
    }
  },
  // 4：主干1-环4
  // 5：主干2-环1
  {
    fv4: 'SY-478',
    status: '问题',
    fv4Style: {
      left: '1415px',
      top: '696px',
      width: '76px',
      height: '124px'
    }
  },
  {
    fv4: 'SY-477',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '737px'
    }
  },
  {
    fv4: 'SY-476',
    status: '问题',
    fv4Style: {
      left: '1607px',
      top: '737px'
    }
  },
  {
    fv4: 'SY-475',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '706px'
    }
  },
  {
    fv4: 'SY-474',
    status: '问题',
    fv4Style: {
      left: '1703px',
      top: '644px'
    }
  },
  {
    fv4: 'SY-473',
    status: '问题',
    fv4Style: {
      left: '1607px',
      top: '613px'
    }
  },
  {
    fv4: 'SY-472',
    status: '问题',
    fv4Style: {
      left: '1511px',
      top: '613px'
    }
  }
]

/** 车网-恒华湖-实时中心环-1 */
export const chewanghenghuaLake1 = [
  // 1：主干1-环1
  {
    fv4: 'SY-480',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '292px'
    }
  },
  {
    fv4: 'SY-481',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '354px'
    }
  },
  {
    fv4: 'SY-483',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '354px'
    }
  },
  {
    fv4: 'SY-484',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '354px'
    }
  },
  {
    fv4: 'SY-485',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '354px'
    }
  },
  {
    fv4: 'SY-502',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '416px'
    }
  },
  {
    fv4: 'SY-497',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '478px'
    }
  },
  {
    fv4: 'SY-498',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '478px'
    }
  },
  {
    fv4: 'SY-499',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '478px'
    }
  },
  {
    fv4: 'SY-500',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '478px'
    }
  },
  // 2：主干1-环2
  {
    fv4: 'SY-501',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-503',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-504',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-508',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-507',
    status: '问题',
    fv4Style: {
      left: '162px',
      top: '591px'
    }
  },
  {
    fv4: 'SY-506',
    status: '问题',
    fv4Style: {
      left: '162px',
      top: '653px'
    }
  },
  {
    fv4: 'SY-505',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '684px'
    }
  },
  {
    fv4: 'SY-509',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '684px'
    }
  },
  {
    fv4: 'SY-510',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '684px'
    }
  },
  {
    fv4: 'SY-511',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '684px'
    }
  },
  {
    fv4: 'SYXZ-01',
    status: '问题',
    fv4Style: {
      left: '1202px',
      top: '746px'
    },
    mark: 'long'
  },
  {
    fv4: 'SY-496',
    status: '问题',
    fv4Style: {
      left: '1202px',
      top: '684px'
    }
  },
  {
    fv4: 'SY-495',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '684px'
    }
  },
  {
    fv4: 'SY-494',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '684px'
    }
  },
  {
    fv4: 'SY-493',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '684px'
    }
  },
  {
    fv4: 'SY-492',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '684px'
    }
  },
  {
    fv4: 'SY-491',
    status: '问题',
    fv4Style: {
      left: '1682px',
      top: '653px'
    }
  },
  {
    fv4: 'SY-490',
    status: '问题',
    fv4Style: {
      left: '1682px',
      top: '591px'
    }
  },
  {
    fv4: 'SY-489',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-488',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-487',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-486',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-512',
    status: '问题',
    fv4Style: {
      left: '1202px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-072',
    status: '问题',
    fv4Style: {
      left: '1202px',
      top: '478px'
    }
  },
  {
    fv4: 'SY-073',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '478px'
    }
  },
  {
    fv4: 'SY-045',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '478px'
    }
  },
  {
    fv4: 'SY-080',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '478px'
    }
  },
  {
    fv4: 'SY-085',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '447px'
    }
  },
  {
    fv4: 'SY-089',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '385px'
    }
  },
  {
    fv4: 'SY-093',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '354px'
    }
  },
  {
    fv4: 'SY-094',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '354px'
    }
  },
  {
    fv4: 'SY-046',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '354px'
    }
  },
  {
    fv4: 'SY-100',
    status: '问题',
    fv4Style: {
      left: '1202px',
      top: '354px'
    }
  }
]

/** 车网-恒华湖-实时中心环-2 */
export const chewanghenghuaLake2 = [
  // 1：主干2-环1
  {
    fv4: 'SY-062',
    status: '问题',
    fv4Style: {
      left: '594px',
      top: '354px'
    }
  },
  {
    fv4: 'SY-044',
    status: '问题',
    fv4Style: {
      left: '498px',
      top: '354px'
    }
  },
  {
    fv4: 'SY-065',
    status: '问题',
    fv4Style: {
      left: '402px',
      top: '354px'
    }
  },
  {
    fv4: 'SY-066',
    status: '问题',
    fv4Style: {
      left: '306px',
      top: '385px'
    }
  },
  {
    fv4: 'SY-070',
    status: '问题',
    fv4Style: {
      left: '306px',
      top: '447px'
    }
  },
  {
    fv4: 'SY-069',
    status: '问题',
    fv4Style: {
      left: '402px',
      top: '478px'
    }
  },
  {
    fv4: 'SY-071',
    status: '问题',
    fv4Style: {
      left: '498px',
      top: '478px'
    }
  },
  {
    fv4: 'SY-074',
    status: '问题',
    fv4Style: {
      left: '594px',
      top: '478px'
    }
  },
  // 2：主干2-环2
  {
    fv4: 'SY-076',
    status: '问题',
    fv4Style: {
      left: '594px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-522',
    status: '问题',
    fv4Style: {
      left: '498px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-078',
    status: '问题',
    fv4Style: {
      left: '402px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-079',
    status: '问题',
    fv4Style: {
      left: '306px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-084',
    status: '问题',
    fv4Style: {
      left: '306px',
      top: '622px'
    }
  },
  {
    fv4: 'SY-083',
    status: '问题',
    fv4Style: {
      left: '306px',
      top: '684px'
    }
  },
  {
    fv4: 'SY-082',
    status: '问题',
    fv4Style: {
      left: '402px',
      top: '684px'
    }
  },
  {
    fv4: 'SY-077',
    status: '问题',
    fv4Style: {
      left: '498px',
      top: '684px'
    }
  },
  {
    fv4: 'SY-081',
    status: '问题',
    fv4Style: {
      left: '594px',
      top: '684px'
    }
  },
  {
    fv4: 'SY-086',
    status: '问题',
    fv4Style: {
      left: '1250px',
      top: '457px'
    }
  },
  {
    fv4: 'SY-087',
    status: '问题',
    fv4Style: {
      left: '1346px',
      top: '457px'
    }
  },
  {
    fv4: 'SY-088',
    status: '问题',
    fv4Style: {
      left: '1442px',
      top: '457px'
    }
  },
  {
    fv4: 'SY-092',
    status: '问题',
    fv4Style: {
      left: '1538px',
      top: '457px'
    }
  },
  {
    fv4: 'SY-091',
    status: '问题',
    fv4Style: {
      left: '1538px',
      top: '519px'
    }
  },
  {
    fv4: 'SY-090',
    status: '问题',
    fv4Style: {
      left: '1538px',
      top: '581px'
    }
  },
  {
    fv4: 'SY-095',
    status: '问题',
    fv4Style: {
      left: '1442px',
      top: '581px'
    }
  },
  {
    fv4: 'SY-096',
    status: '问题',
    fv4Style: {
      left: '1346px',
      top: '581px'
    }
  },
  {
    fv4: 'SY-097',
    status: '问题',
    fv4Style: {
      left: '1250px',
      top: '581px'
    }
  }
]

/** 车网-恒华湖-实时中心环-3 */
export const chewanghenghuaLake3 = [
  // 1：主干3-环1
  {
    fv4: 'SY-068',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '354px'
    }
  },
  {
    fv4: 'SY-061',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '354px'
    }
  },
  {
    fv4: 'SY-075',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '354px'
    }
  },
  {
    fv4: 'SY-523',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '354px'
    }
  },
  {
    fv4: 'SY-051',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '416px'
    }
  },
  {
    fv4: 'SY-050',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '478px'
    }
  },
  {
    fv4: 'SY-049',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '478px'
    }
  },
  {
    fv4: 'SY-048',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '478px'
    }
  },
  {
    fv4: 'SY-052',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '478px'
    }
  },
  {
    fv4: 'SY-047',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-053',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-054',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-055',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-056',
    status: '问题',
    fv4Style: {
      left: '162px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-124',
    status: '问题',
    fv4Style: {
      left: '162px',
      top: '622px'
    }
  },
  {
    fv4: 'SY-123',
    status: '问题',
    fv4Style: {
      left: '162px',
      top: '684px'
    }
  },
  {
    fv4: 'SY-125',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '684px'
    }
  },
  {
    fv4: 'SY-126',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '684px'
    }
  },
  {
    fv4: 'SY-127',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '684px'
    }
  },
  {
    fv4: 'SY-118',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '684px'
    }
  },
  // 3：主干3-环3
  {
    fv4: 'SY-101',
    status: '问题',
    fv4Style: {
      left: '1202px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-107',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-106',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-111',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-115',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '591px'
    }
  },
  {
    fv4: 'SY-116',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '653px'
    }
  },
  {
    fv4: 'SY-114',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '684px'
    }
  },
  {
    fv4: 'SY-119',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '684px'
    }
  },
  {
    fv4: 'SY-120',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '684px'
    }
  },
  {
    fv4: 'SY-117',
    status: '问题',
    fv4Style: {
      left: '1202px',
      top: '684px'
    }
  },
  // 4：主干3-环4
  {
    fv4: 'SY-113',
    status: '问题',
    fv4Style: {
      left: '1202px',
      top: '354px'
    }
  },
  {
    fv4: 'SY-110',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '354px'
    }
  },
  {
    fv4: 'SY-112',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '354px'
    }
  },
  {
    fv4: 'SY-108',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '354px'
    }
  },
  {
    fv4: 'SY-102',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '354px'
    }
  },
  {
    fv4: 'SY-103',
    status: '问题',
    fv4Style: {
      left: '1682px',
      top: '385px'
    }
  },
  {
    fv4: 'SY-104',
    status: '问题',
    fv4Style: {
      left: '1682px',
      top: '447px'
    }
  },
  {
    fv4: 'SY-109',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '478px'
    }
  },
  {
    fv4: 'SY-520',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '478px'
    }
  },
  {
    fv4: 'SY-105',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '478px'
    }
  },
  {
    fv4: 'SY-099',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '478px'
    }
  },
  {
    fv4: 'SY-098',
    status: '问题',
    fv4Style: {
      left: '1202px',
      top: '478px'
    }
  }
]

/** 车网-正商雅筑-实时中心环-1 */
export const chewangzhengshangyazhu1 = [
  // 1：主干1-环1
  {
    fv4: 'SY-254',
    status: '问题',
    fv4Style: {
      left: '690px',
      top: '354px'
    }
  },
  {
    fv4: 'SY-253',
    status: '问题',
    fv4Style: {
      left: '594px',
      top: '354px'
    }
  },
  {
    fv4: 'SY-479',
    status: '问题',
    fv4Style: {
      left: '498px',
      top: '354px'
    }
  },
  {
    fv4: 'SY-252',
    status: '问题',
    fv4Style: {
      left: '402px',
      top: '354px'
    }
  },
  {
    fv4: 'SY-251',
    status: '问题',
    fv4Style: {
      left: '306px',
      top: '354px'
    }
  },
  {
    fv4: 'SY-263',
    status: '问题',
    fv4Style: {
      left: '306px',
      top: '416px'
    }
  },
  {
    fv4: 'SY-266',
    status: '问题',
    fv4Style: {
      left: '306px',
      top: '478px'
    }
  },
  {
    fv4: 'SY-271',
    status: '问题',
    fv4Style: {
      left: '402px',
      top: '478px'
    }
  },
  {
    fv4: 'SY-519',
    status: '问题',
    fv4Style: {
      left: '498px',
      top: '478px'
    }
  },
  {
    fv4: 'SY-262',
    status: '问题',
    fv4Style: {
      left: '594px',
      top: '478px'
    }
  },
  {
    fv4: 'SY-243',
    status: '问题',
    fv4Style: {
      left: '690px',
      top: '478px'
    }
  },
  // 1：主干1-环1
  // 2：主干1-环2
  {
    fv4: 'SY-250',
    status: '问题',
    fv4Style: {
      left: '690px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-516',
    status: '问题',
    fv4Style: {
      left: '594px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-517',
    status: '问题',
    fv4Style: {
      left: '498px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-518',
    status: '问题',
    fv4Style: {
      left: '402px',
      top: '560px'
    }
  },
  {
    fv4: 'SY-268',
    status: '问题',
    fv4Style: {
      left: '306px',
      top: '591px'
    }
  },
  {
    fv4: 'SY-535',
    status: '问题',
    fv4Style: {
      left: '306px',
      top: '653px'
    }
  },
  {
    fv4: 'SY-265',
    status: '问题',
    fv4Style: {
      left: '402px',
      top: '684px'
    }
  },
  {
    fv4: 'SY-532',
    status: '问题',
    fv4Style: {
      left: '498px',
      top: '684px'
    }
  },
  {
    fv4: 'SY-261',
    status: '问题',
    fv4Style: {
      left: '594px',
      top: '684px'
    }
  },
  {
    fv4: 'SY-264',
    status: '问题',
    fv4Style: {
      left: '690px ',
      top: '684px'
    }
  },
  // 2：主干1-环2
  // 3：主干1-环3
  {
    fv4: 'SY-228',
    status: '问题',
    fv4Style: {
      left: '1346px',
      top: '581px'
    }
  },
  {
    fv4: 'SY-241',
    status: '问题',
    fv4Style: {
      left: '1442px',
      top: '581px'
    }
  },
  {
    fv4: 'SY-242',
    status: '问题',
    fv4Style: {
      left: '1538px',
      top: '550px'
    }
  },
  {
    fv4: 'SY-249',
    status: '问题',
    fv4Style: {
      left: '1538px',
      top: '488px'
    }
  },
  {
    fv4: 'SY-533',
    status: '问题',
    fv4Style: {
      left: '1442px',
      top: '457px'
    }
  },
  {
    fv4: 'SY-248',
    status: '问题',
    fv4Style: {
      left: '1346px',
      top: '457px'
    }
  }
]

/** 车网-正商雅筑-实时中心环-2 */
export const chewangzhengshangyazhu2 = [
  // 1：主干2-环1
  {
    fv4: 'SY-276',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '300px'
    }
  },
  {
    fv4: 'SY-274',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '300px'
    }
  },
  {
    fv4: 'SY-273',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '300px'
    }
  },
  {
    fv4: 'SY-272',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '362px'
    }
  },
  {
    fv4: 'SY-260',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '424px'
    }
  },
  {
    fv4: 'SY-259',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '424px'
    }
  },
  {
    fv4: 'SY-258',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '424px'
    }
  },
  // 2：主干2-环2
  {
    fv4: 'SY-238',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '506px'
    }
  },
  {
    fv4: 'SY-245',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '506px'
    }
  },
  {
    fv4: 'SY-277',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '506px'
    }
  },
  {
    fv4: 'SY-237',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '506px'
    }
  },
  {
    fv4: 'SY-257',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '506px'
    }
  },
  {
    fv4: 'SY-236',
    status: '问题',
    fv4Style: {
      left: '162px',
      top: '537px'
    }
  },
  {
    fv4: 'SY-256',
    status: '问题',
    fv4Style: {
      left: '162px',
      top: '599px'
    }
  },
  {
    fv4: 'SY-255',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '630px'
    }
  },
  {
    fv4: 'SY-244',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '630px'
    }
  },
  {
    fv4: 'SY-235',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '630px'
    }
  },
  {
    fv4: 'SY-233',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '630px'
    }
  },
  {
    fv4: 'SY-223',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '630px'
    }
  },
  // 3：主干2-环3
  {
    fv4: 'SY-224',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '712px'
    }
  },
  {
    fv4: 'SY-222',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '712px'
    }
  },
  {
    fv4: 'SY-215',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '712px'
    }
  },
  {
    fv4: 'SY-216',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '743px'
    }
  },
  {
    fv4: 'SY-209',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '805px'
    }
  },
  {
    fv4: 'SY-208',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '836px'
    }
  },
  {
    fv4: 'SY-207',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '836px'
    }
  },
  {
    fv4: 'SY-206',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '836px'
    }
  },
  // 4：主干2-环4
  {
    fv4: 'SY-186',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '836px'
    }
  },
  {
    fv4: 'SY-185',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '836px'
    }
  },
  {
    fv4: 'SY-180',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '836px'
    }
  },
  {
    fv4: 'SY-184',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '805px'
    }
  },
  {
    fv4: 'SY-183',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '743px'
    }
  },
  {
    fv4: 'SY-195',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '712px'
    }
  },
  {
    fv4: 'SY-196',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '712px'
    }
  },
  {
    fv4: 'SY-197',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '712px'
    }
  },
  // 4：主干2-环4
  // 5：主干2-环5
  {
    fv4: 'SY-148',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '630px'
    }
  },
  {
    fv4: 'SY-157',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '630px'
    }
  },
  {
    fv4: 'SY-159',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '630px'
    }
  },
  {
    fv4: 'SY-160',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '630px'
    }
  },
  {
    fv4: 'SY-063',
    status: '问题',
    fv4Style: {
      left: '1682px',
      top: '599px'
    }
  },
  {
    fv4: 'SY-164',
    status: '问题',
    fv4Style: {
      left: '1682px',
      top: '537px'
    }
  },
  {
    fv4: 'SY-158',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '506px'
    }
  },
  {
    fv4: 'SY-156',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '506px'
    }
  },
  {
    fv4: 'SY-174',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '506px '
    }
  },
  {
    fv4: 'SY-175',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '506px'
    }
  },
  // 5：主干2-环5
  // 6：主干2-环6
  {
    fv4: 'SY-130',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '424px'
    }
  },
  {
    fv4: 'SY-129',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '424px'
    }
  },
  {
    fv4: 'SY-128',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '424px'
    }
  },
  {
    fv4: 'SY-525',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '424px'
    }
  },
  {
    fv4: 'SY-136',
    status: '问题',
    fv4Style: {
      left: '1682px',
      top: '424px'
    }
  },
  {
    fv4: 'SY-147',
    status: '问题',
    fv4Style: {
      left: '1682px',
      top: '362px'
    }
  },
  {
    fv4: 'SY-139',
    status: '问题',
    fv4Style: {
      left: '1682px',
      top: '300px'
    }
  },
  {
    fv4: 'SY-137',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '300px'
    }
  },
  {
    fv4: 'SY-138',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '300px'
    }
  },
  {
    fv4: 'SY-150',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '300px'
    }
  },
  {
    fv4: 'SY-149',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '300px'
    }
  }
  // 6：主干2-环6

]

/** 车网-正商雅筑-实时中心环-3 */
export const chewangzhengshangyazhu3 = [
  // 1：主干3-环1
  {
    fv4: 'SY-231',
    status: '问题',
    fv4Style: {
      left: '594px',
      top: '197px'
    }
  },
  {
    fv4: 'SY-230',
    status: '问题',
    fv4Style: {
      left: '498px',
      top: '197px'
    }
  },
  {
    fv4: 'SY-526',
    status: '问题',
    fv4Style: {
      left: '402px',
      top: '197px'
    }
  },
  {
    fv4: 'SY-229',
    status: '问题',
    fv4Style: {
      left: '306px',
      top: '197px'
    }
  },
  {
    fv4: 'SY-213',
    status: '问题',
    fv4Style: {
      left: '306px',
      top: '259px'
    }
  },
  {
    fv4: 'SY-212',
    status: '问题',
    fv4Style: {
      left: '306px',
      top: '321px'
    }
  },
  {
    fv4: 'SY-211',
    status: '问题',
    fv4Style: {
      left: '402px',
      top: '321px'
    }
  },
  {
    fv4: 'SY-220',
    status: '问题',
    fv4Style: {
      left: '498px',
      top: '321px'
    }
  },
  {
    fv4: 'SY-221',
    status: '问题',
    fv4Style: {
      left: '594px',
      top: '321px'
    }
  },
  // 2：主干3-环2
  {
    fv4: 'SY-227',
    status: '问题',
    fv4Style: {
      left: '594px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-240',
    status: '问题',
    fv4Style: {
      left: '498px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-247',
    status: '问题',
    fv4Style: {
      left: '402px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-218',
    status: '问题',
    fv4Style: {
      left: '306px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-217',
    status: '问题',
    fv4Style: {
      left: '306px',
      top: '465px'
    }
  },
  {
    fv4: 'SY-246',
    status: '问题',
    fv4Style: {
      left: '306px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-239',
    status: '问题',
    fv4Style: {
      left: '402px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-234',
    status: '问题',
    fv4Style: {
      left: '498px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-226',
    status: '问题',
    fv4Style: {
      left: '594px',
      top: '527px'
    }
  },
  // 3：主干3-环3
  {
    fv4: 'SY-225',
    status: '问题',
    fv4Style: {
      left: '594px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-534',
    status: '问题',
    fv4Style: {
      left: '498px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-219',
    status: '问题',
    fv4Style: {
      left: '402px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-210',
    status: '问题',
    fv4Style: {
      left: '306px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-200',
    status: '问题',
    fv4Style: {
      left: '210px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-199',
    status: '问题',
    fv4Style: {
      left: '210px',
      top: '671px'
    }
  },
  {
    fv4: 'SY-198',
    status: '问题',
    fv4Style: {
      left: '210px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-187',
    status: '问题',
    fv4Style: {
      left: '306px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-188',
    status: '问题',
    fv4Style: {
      left: '402px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-189',
    status: '问题',
    fv4Style: {
      left: '498px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-182',
    status: '问题',
    fv4Style: {
      left: '594px',
      top: '733px'
    }
  },
  // 4：主干3-环4
  {
    fv4: 'SY-176',
    status: '问题',
    fv4Style: {
      left: '594px',
      top: '815px'
    }
  },
  {
    fv4: 'SY-165',
    status: '问题',
    fv4Style: {
      left: '498px',
      top: '815px'
    }
  },
  {
    fv4: 'SY-064',
    status: '问题',
    fv4Style: {
      left: '402px',
      top: '815px'
    }
  },
  {
    fv4: 'SY-132',
    status: '问题',
    fv4Style: {
      left: '306px',
      top: '815px'
    }
  },
  {
    fv4: 'SY-161',
    status: '问题',
    fv4Style: {
      left: '210px',
      top: '846px'
    }
  },
  {
    fv4: 'SY-162',
    status: '问题',
    fv4Style: {
      left: '210px',
      top: '908px'
    }
  },
  {
    fv4: 'SY-152',
    status: '问题',
    fv4Style: {
      left: '306px',
      top: '939px'
    }
  },
  {
    fv4: 'SY-151',
    status: '问题',
    fv4Style: {
      left: '402px',
      top: '939px'
    }
  },
  {
    fv4: 'SY-145',
    status: '问题',
    fv4Style: {
      left: '498px',
      top: '939px'
    }
  },
  {
    fv4: 'SY-140',
    status: '问题',
    fv4Style: {
      left: '594px',
      top: '939px'
    }
  },
  // 5：主干3-环5
  {
    fv4: 'SY-155',
    status: '问题',
    fv4Style: {
      left: '1250px',
      top: '939px'
    }
  },
  {
    fv4: 'SY-143',
    status: '问题',
    fv4Style: {
      left: '1346px',
      top: '939px'
    }
  },
  {
    fv4: 'SY-134',
    status: '问题',
    fv4Style: {
      left: '1442px',
      top: '939px'
    }
  },
  {
    fv4: 'SY-135',
    status: '问题',
    fv4Style: {
      left: '1538px',
      top: '939px'
    }
  },
  {
    fv4: 'SY-121',
    status: '问题',
    fv4Style: {
      left: '1634px',
      top: '908px'
    }
  },
  {
    fv4: 'SY-122',
    status: '问题',
    fv4Style: {
      left: '1634px',
      top: '846px'
    }
  },
  {
    fv4: 'SY-133',
    status: '问题',
    fv4Style: {
      left: '1538px',
      top: '815px'
    }
  },
  {
    fv4: 'SY-142',
    status: '问题',
    fv4Style: {
      left: '1442px',
      top: '815px'
    }
  },
  {
    fv4: 'SY-146',
    status: '问题',
    fv4Style: {
      left: '1346px',
      top: '815px'
    }
  },
  {
    fv4: 'SY-141',
    status: '问题',
    fv4Style: {
      left: '1250px ',
      top: '815px'
    }
  },
  // 5：主干3-
  // 6：主干3-环6
  {
    fv4: 'SY-168',
    status: '问题',
    fv4Style: {
      left: '1250px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-167',
    status: '问题',
    fv4Style: {
      left: '1346px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-067',
    status: '问题',
    fv4Style: {
      left: '1442px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-166',
    status: '问题',
    fv4Style: {
      left: '1538px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-163',
    status: '问题',
    fv4Style: {
      left: '1538px',
      top: '671px'
    }
  },
  {
    fv4: 'SY-153',
    status: '问题',
    fv4Style: {
      left: '1538px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-131',
    status: '问题',
    fv4Style: {
      left: '1442px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-154',
    status: '问题',
    fv4Style: {
      left: '1346px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-169',
    status: '问题',
    fv4Style: {
      left: '1250px',
      top: '609px'
    }
  },
  // 6：主干3-环6
  // 7：主干3-环7
  {
    fv4: 'SY-171',
    status: '问题',
    fv4Style: {
      left: '1250px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-172',
    status: '问题',
    fv4Style: {
      left: '1346px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-178',
    status: '问题',
    fv4Style: {
      left: '1442px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-181',
    status: '问题',
    fv4Style: {
      left: '1538px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-192',
    status: '问题',
    fv4Style: {
      left: '1634px',
      top: '496px'
    }
  },
  {
    fv4: 'SY-531',
    status: '问题',
    fv4Style: {
      left: '1634px',
      top: '434px'
    }
  },
  {
    fv4: 'SY-193',
    status: '问题',
    fv4Style: {
      left: '1538px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-179',
    status: '问题',
    fv4Style: {
      left: '1442px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-173',
    status: '问题',
    fv4Style: {
      left: '1346px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-170',
    status: '问题',
    fv4Style: {
      left: '1250px',
      top: '403px'
    }
  },
  // 7：主干3-环7
  // 8：主干3-环8
  {
    fv4: 'SY-203',
    status: '问题',
    fv4Style: {
      left: '1250px',
      top: '321px'
    }
  },
  {
    fv4: 'SY-194',
    status: '问题',
    fv4Style: {
      left: '1346px',
      top: '321px'
    }
  },
  {
    fv4: 'SY-060',
    status: '问题',
    fv4Style: {
      left: '1442px',
      top: '321px'
    }
  },
  {
    fv4: 'SY-204',
    status: '问题',
    fv4Style: {
      left: '1538px',
      top: '321px'
    }
  },
  {
    fv4: 'SY-214',
    status: '问题',
    fv4Style: {
      left: '1634px',
      top: '321px'
    }
  },
  {
    fv4: 'SY-205',
    status: '问题',
    fv4Style: {
      left: '1634px',
      top: '259px'
    }
  },
  {
    fv4: 'SY-202',
    status: '问题',
    fv4Style: {
      left: '1634px',
      top: '197px'
    }
  },
  {
    fv4: 'SY-201',
    status: '问题',
    fv4Style: {
      left: '1538px',
      top: '197px'
    }
  },
  {
    fv4: 'SY-190',
    status: '问题',
    fv4Style: {
      left: '1442px',
      top: '197px'
    }
  },
  {
    fv4: 'SY-191',
    status: '问题',
    fv4Style: {
      left: '1346px',
      top: '197px'
    }
  },
  {
    fv4: 'SY-177',
    status: '问题',
    fv4Style: {
      left: '1250px',
      top: '197px'
    }
  }
  // 8：主干3-环8
]

/** 车网-空港-实时中心环-1 */
export const chewangkonggang1 = [
  // 1：主干1-环1
  {
    fv4: 'SY-346',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '197px'
    }
  },
  {
    fv4: 'SY-342',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '197px'
    }
  },
  {
    fv4: 'SY-335',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '197px'
    }
  },
  {
    fv4: 'SY-336',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '197px'
    }
  },
  {
    fv4: 'SY-337',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '197px'
    }
  },
  {
    fv4: 'SY-333',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '259px'
    }
  },
  {
    fv4: 'SY-339',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '321px'
    }
  },
  {
    fv4: 'SY-338',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '321px'
    }
  },
  {
    fv4: 'SY-343',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '321px'
    }
  },
  {
    fv4: 'SY-344',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '321px'
    }
  },
  {
    fv4: 'SY-347',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '321px'
    }
  },
  // 2：主干1-环2
  {
    fv4: 'SY-351',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-353',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-341',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-334',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-340',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-345',
    status: '问题',
    fv4Style: {
      left: '162px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-370',
    status: '问题',
    fv4Style: {
      left: '66px',
      top: '434px'
    }
  },
  {
    fv4: 'SY-380',
    status: '问题',
    fv4Style: {
      left: '66px',
      top: '496px'
    }
  },
  {
    fv4: 'SY-381',
    status: '问题',
    fv4Style: {
      left: '162px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-382',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-383',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-348',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-349',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-350',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '527px'
    }
  },
  // 3：主干1-环3
  {
    fv4: 'SY-458',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-459',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-460',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-453',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-452',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-451',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '671px'
    }
  },
  {
    fv4: 'SY-448',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-446',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-447',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-450',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-455',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '733px'
    }
  },
  // 4：主干1-环4
  {
    fv4: 'SY-465',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '815px'
    }
  },
  {
    fv4: 'SY-466',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '815px'
    }
  },
  {
    fv4: 'SY-467',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '815px'
    }
  },
  {
    fv4: 'SY-469',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '877px'
    }
  },
  {
    fv4: 'SY-471',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '939px'
    }
  },
  {
    fv4: 'SY-470',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '939px'
    }
  },
  {
    fv4: 'SY-468',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '939px'
    }
  },
  // 5：主干1-环5
  {
    fv4: 'SY-457',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '939px'
    }
  },
  {
    fv4: 'SY-449',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '939px'
    }
  },
  {
    fv4: 'SY-445',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '939px'
    }
  },
  {
    fv4: 'SY-524',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '939px'
    }
  },
  {
    fv4: 'SY-439',
    status: '问题',
    fv4Style: {
      left: '1682px',
      top: '939px'
    }
  },
  {
    fv4: 'SY-431',
    status: '问题',
    fv4Style: {
      left: '1778px',
      top: '908px'
    }
  },
  {
    fv4: 'SY-401',
    status: '问题',
    fv4Style: {
      left: '1778px',
      top: '846px'
    }
  },
  {
    fv4: 'SY-402',
    status: '问题',
    fv4Style: {
      left: '1682px',
      top: '815px'
    }
  },
  {
    fv4: 'SY-379',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '815px'
    }
  },
  {
    fv4: 'SY-417',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '815px'
    }
  },
  {
    fv4: 'SY-456',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '815px'
    }
  },
  {
    fv4: 'SY-454',
    status: '问题',
    fv4Style: {
      left: '1298px ',
      top: '815px'
    }
  },
  // 5：主干1-环5
  // 6：主干1-环6
  {
    fv4: 'SY-462',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-461',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-443',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-419',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-440',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '671px'
    }
  },
  {
    fv4: 'SY-441',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-442',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-444',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-463',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '609px'
    }
  },
  // 6：主干1-环6
  // 7：主干1-环7
  {
    fv4: 'SY-514',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '527px'
    },
    mark: 'long'
  },
  {
    fv4: 'SY-411',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-410',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-409',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-408',
    status: '问题',
    fv4Style: {
      left: '1682px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-407',
    status: '问题',
    fv4Style: {
      left: '1682px',
      top: '465px'
    }
  },
  {
    fv4: 'SY-406',
    status: '问题',
    fv4Style: {
      left: '1682px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-405',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-403',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-404',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-513',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '403px'
    }
  },
  // 7：主干1-环7
  // 8：主干1-环8
  {
    fv4: 'SY-434',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '321px'
    }
  },
  {
    fv4: 'SY-435',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '321px'
    }
  },
  {
    fv4: 'SY-428',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '321px'
    }
  },
  {
    fv4: 'SY-427',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '321px'
    }
  },
  {
    fv4: 'SY-423',
    status: '问题',
    fv4Style: {
      left: '1682px',
      top: '321px'
    }
  },
  {
    fv4: 'SY-424',
    status: '问题',
    fv4Style: {
      left: '1778px',
      top: '290px'
    }
  },
  {
    fv4: 'SY-413',
    status: '问题',
    fv4Style: {
      left: '1778px',
      top: '228px'
    }
  },
  {
    fv4: 'SY-412',
    status: '问题',
    fv4Style: {
      left: '1682px',
      top: '197px'
    }
  },
  {
    fv4: 'SY-418',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '197px'
    }
  },
  {
    fv4: 'SY-420',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '197px'
    }
  },
  {
    fv4: 'SY-421',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '197px'
    }
  },
  {
    fv4: 'SY-422',
    status: '问题',
    fv4Style: {
      left: '1298px ',
      top: '197px'
    }
  }
  // 8：主干1-环8

]

/** 车网-空港-实时中心环-2 */
export const chewangkonggang2 = [
  // 1：主干2-环1
  {
    fv4: 'SY-416',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '197px'
    }
  },
  {
    fv4: 'SY-415',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '197px'
    }
  },
  {
    fv4: 'SY-426',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '197px'
    }
  },
  {
    fv4: 'SY-430',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '197px'
    }
  },
  {
    fv4: 'SY-433',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '197px'
    }
  },
  {
    fv4: 'SY-438',
    status: '问题',
    fv4Style: {
      left: '162px',
      top: '228px'
    }
  },
  {
    fv4: 'SY-437',
    status: '问题',
    fv4Style: {
      left: '162px',
      top: '290px'
    }
  },
  {
    fv4: 'SY-436',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '321px'
    }
  },
  {
    fv4: 'SY-432',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '321px'
    }
  },
  {
    fv4: 'SY-429',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '321px'
    }
  },
  {
    fv4: 'SY-425',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '321px'
    }
  },
  {
    fv4: 'SY-414',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '321px'
    }
  },
  // 2：主干2-环2
  {
    fv4: 'SY-388',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-389',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-390',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-377',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-378',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-391',
    status: '问题',
    fv4Style: {
      left: '162px',
      top: '434px'
    }
  },
  {
    fv4: 'SY-400',
    status: '问题',
    fv4Style: {
      left: '162px',
      top: '496px'
    }
  },
  {
    fv4: 'SY-399',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-398',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-397',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-396',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-387',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '527px'
    }
  },
  // 3：主干2-环3
  {
    fv4: 'SY-375',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-374',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-373',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-384',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-393',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-392',
    status: '问题',
    fv4Style: {
      left: '162px',
      top: '640px'
    }
  },
  {
    fv4: 'SY-395',
    status: '问题',
    fv4Style: {
      left: '162px',
      top: '702px'
    }
  },
  {
    fv4: 'SY-394',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-385',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-386',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-376',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-529',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '733px'
    }
  },
  // 4：主干2-环4
  {
    fv4: 'SY-372',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '815px'
    }
  },
  {
    fv4: 'SY-371',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '815px'
    }
  },
  {
    fv4: 'SY-356',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '815px'
    }
  },
  {
    fv4: 'SY-355',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '815px'
    }
  },
  {
    fv4: 'SY-530',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '846px'
    }
  },
  {
    fv4: 'SY-329',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '908px'
    }
  },
  {
    fv4: 'SY-330',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '939px'
    }
  },
  {
    fv4: 'SY-357',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '939px'
    }
  },
  {
    fv4: 'SY-361',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '939px'
    }
  },
  {
    fv4: 'SY-364',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '939px'
    }
  },
  // 4：主干2-环4
  // 5：主干2-环5
  {
    fv4: 'SY-365',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '836px'
    }
  },
  {
    fv4: 'SY-366',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '836px'
    }
  },
  {
    fv4: 'SY-367',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '836px'
    }
  },
  {
    fv4: 'SY-369',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '836px'
    }
  },
  {
    fv4: 'SY-368',
    status: '问题',
    fv4Style: {
      left: '1682px',
      top: '805px'
    }
  },
  {
    fv4: 'SY-360',
    status: '问题',
    fv4Style: {
      left: '1682px',
      top: '743px'
    }
  },
  {
    fv4: 'SY-359',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '712px'
    }
  },
  {
    fv4: 'SY-358',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '712px'
    }
  },
  {
    fv4: 'SY-363',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '712px'
    }
  },
  {
    fv4: 'SY-362',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '712px'
    }
  },
  // 5：主干2-环5
  // 6：主干2-环6
  {
    fv4: 'SY-322',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '630px'
    }
  },
  {
    fv4: 'SY-331',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '630px'
    }
  },
  {
    fv4: 'SY-321',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '630px'
    }
  },
  {
    fv4: 'SY-326',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '630px'
    }
  },
  {
    fv4: 'SY-327',
    status: '问题',
    fv4Style: {
      left: '1682px',
      top: '599px'
    }
  },
  {
    fv4: 'SY-328',
    status: '问题',
    fv4Style: {
      left: '1682px',
      top: '537px'
    }
  },
  {
    fv4: 'SY-332',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '506px'
    }
  },
  {
    fv4: 'SY-325',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '506px'
    }
  },
  {
    fv4: 'SY-324',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '506px'
    }
  },
  {
    fv4: 'SY-323',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '506px'
    }
  },
  // 6：主干2-环6
  // 7：主干2-环7
  {
    fv4: 'SY-319',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '424px'
    }
  },
  {
    fv4: 'SY-320',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '424px'
    }
  },
  {
    fv4: 'SY-354',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '300px'
    }
  },
  {
    fv4: 'SY-317',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '300px'
    }
  }
  // 7：主干2-环7
]

export const chewangbanbidian1 = [
  // 1：主干1-环1
  {
    fv4: 'SY-309',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-528',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-310',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-297',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-284',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-278',
    status: '问题',
    fv4Style: {
      left: '162px',
      top: '434px'
    }
  },
  {
    fv4: 'SY-270',
    status: '问题',
    fv4Style: {
      left: '162px',
      top: '496px'
    }
  },
  {
    fv4: 'SY-269',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-281',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-280',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-279',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-307',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '527px'
    }
  },
  // 2：主干1-环2
  {
    fv4: 'SY-285',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-289',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-293',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-298',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-302',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-306',
    status: '问题',
    fv4Style: {
      left: '162px',
      top: '671px'
    }
  },
  {
    fv4: 'SY-303',
    status: '问题',
    fv4Style: {
      left: '258px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-299',
    status: '问题',
    fv4Style: {
      left: '354px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-294',
    status: '问题',
    fv4Style: {
      left: '450px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-290',
    status: '问题',
    fv4Style: {
      left: '546px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-286',
    status: '问题',
    fv4Style: {
      left: '642px',
      top: '733px'
    }
  },
  // 3：主干1-环3
  {
    fv4: 'SY-288',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-292',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-296',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-301',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '733px'
    }
  },
  {
    fv4: 'SY-305',
    status: '问题',
    fv4Style: {
      left: '1682px',
      top: '702px'
    }
  },
  {
    fv4: 'SY-304',
    status: '问题',
    fv4Style: {
      left: '1682px',
      top: '640px'
    }
  },
  {
    fv4: 'SY-300',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-295',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-291',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '609px'
    }
  },
  {
    fv4: 'SY-287',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '609px'
    }
  },
  // 3：主干1-环3
  // 4：主干1-环4
  {
    fv4: 'SY-308',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-311',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-313',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-312',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '527px'
    }
  },
  {
    fv4: 'SY-058',
    status: '问题',
    fv4Style: {
      left: '1682px',
      top: '496px'
    }
  },
  {
    fv4: 'SY-314',
    status: '问题',
    fv4Style: {
      left: '1682px',
      top: '434px '
    }
  },
  {
    fv4: 'SY-315',
    status: '问题',
    fv4Style: {
      left: '1586px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-283',
    status: '问题',
    fv4Style: {
      left: '1490px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-059',
    status: '问题',
    fv4Style: {
      left: '1394px',
      top: '403px'
    }
  },
  {
    fv4: 'SY-521',
    status: '问题',
    fv4Style: {
      left: '1298px',
      top: '403px'
    }
  }
  // 4：主干1-环4
]

export const chewangbanbidian2 = [
  // 1：主干2-环1
  {
    fv4: 'SY-478',
    status: '问题',
    fv4Style: {
      left: '988px',
      top: '506px'
    }
  },
  {
    fv4: 'SY-477',
    status: '问题',
    fv4Style: {
      left: '1084px',
      top: '506px'
    }
  },
  {
    fv4: 'SY-476',
    status: '问题',
    fv4Style: {
      left: '1180px',
      top: '506px'
    }
  },
  {
    fv4: 'SY-475',
    status: '问题',
    fv4Style: {
      left: '1276px',
      top: '568px'
    }
  },
  {
    fv4: 'SY-474',
    status: '问题',
    fv4Style: {
      left: '1180px',
      top: '630px'
    }
  },
  {
    fv4: 'SY-473',
    status: '问题',
    fv4Style: {
      left: '1084px',
      top: '630px'
    }
  },
  {
    fv4: 'SY-472',
    status: '问题',
    fv4Style: {
      left: '988px',
      top: '630px'
    }
  }
]
