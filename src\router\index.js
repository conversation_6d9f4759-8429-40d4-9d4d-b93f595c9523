import router from './routers'
import store from '@/store'
import Config from '@/settings'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css'// progress bar style
import { getToken } from '@/utils/auth' // getToken from cookie
import { buildMenus } from '@/api/system/menu'
import { filterAsyncRouter } from '@/store/modules/permission'
import { homeRouter } from '@/router/homeRouter';
import { setNormalConfig } from '@/utils/normalConfig';
import Vue from 'vue'

NProgress.configure({ showSpinner: false })// NProgress Configuration

const whiteList = ['/login']// no redirect whitelist
router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    document.title = to.meta.title + ' - ' + Config.title
  }
  NProgress.start()
  if (getToken()) {
    // 已登录且要跳转的页面是登录页
    if (to.path === '/login') {
      next({ path: '/' })
      NProgress.done()
    } else {
      if (store.getters.roles.length === 0) { // 判断当前用户是否已拉取完user_info信息
        store.dispatch('GetInfo').then(() => { // 拉取user_info
          // 动态路由，拉取菜单
          loadMenus(next, to)
        }).catch(() => {
          store.dispatch('LogOut').then(() => {
            location.reload() // 为了重新实例化vue-router对象 避免bug
          })
        })
        // 登录时未拉取 菜单，在此处拉取
      } else if (store.getters.loadMenus) {
        // 修改成false，防止死循环
        store.dispatch('updateLoadMenus')
        loadMenus(next, to)
      } else {
        next()
      }
    }
  } else {
    /* has no token*/
    if (whiteList.indexOf(to.path) !== -1) { // 在免登录白名单，直接进入
      next()
    } else {
      next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
      NProgress.done()
    }
  }
})

export const loadMenus = async(next, to) => {
  await setNormalConfig({ key: 'devops_keys' })
  const isProjectRoles = handleRoles()
  buildMenus().then(async res => {
    const sdata = JSON.parse(JSON.stringify(res))
    const rdata = JSON.parse(JSON.stringify(res))
    const sidebarRoutes = filterAsyncRouter(sdata)
    const rewriteRoutes = filterAsyncRouter(rdata, false, true)
    rewriteRoutes.push({ path: '*', redirect: '/404', hidden: true })
    // 判断当前角色是项目经理、项目成员
    if (isProjectRoles) {
      rewriteRoutes.push(homeRouter.projectHome);
    } else {
      rewriteRoutes.push(homeRouter.publicHome);
      // sidebarRoutes.unshift(homeRouter.publicHome)
    }

    // 后续代码也必须在接口获取到数据之后在执行
    store.dispatch('GenerateRoutes', rewriteRoutes).then(() => { // 存储路由
      store.dispatch('SetSidebarRouters', sidebarRoutes)
      router.addRoutes(rewriteRoutes) // 动态添加可访问路由表
      next({ ...to, replace: true })
    })
  })
}

export function handleRoles() {
  const { roles_keys } = Vue.prototype.$config
  const rolesString = roles_keys.otherInfo;
  const projectAboutRoles = rolesString.split(',').map(Number);
  const roles = store.getters.user.roles.map(item => item.id);
  const isProjectRoles = roles.some(element => projectAboutRoles.includes(element));
  return isProjectRoles
}

router.afterEach(() => {
  NProgress.done() // finish progress bar
})
