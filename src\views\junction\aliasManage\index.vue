<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input v-model="query.formData" clearable size="small" placeholder="输入关键字搜索" style="width: 200px;" class="filter-item" />
        <rrOperation>
          <el-button
            slot="right"
            class="filter-item"
            type="primary"
            icon="el-icon-eleme"
            size="mini"
            @click="preView"
          >
            预览
          </el-button>
          <el-button
            slot="right"
            class="filter-item"
            type="primary"
            icon="el-icon-download"
            size="mini"
            @click="downLoad"
          >
            导出
          </el-button>
        </rrOperation>
      </div>
      <crudOperation :permission="permission">
        <el-button
          slot="left"
          v-permission="permission.add"
          class="filter-item"
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="addTable"
        >
          新建
        </el-button>
        <el-button
          slot="left"
          v-permission="permission.upload"
          size="mini"
          icon="el-icon-plus"
          type="success"
          class="filter-item"
          @click="addFile"
        >上传</el-button>
        <update-button
          slot="left"
          :permission="permission"
          :bind-id="bindId"
          :enabled="[1]"
        />
      </crudOperation>
    </div>
    <el-table
      ref="table"
      v-loading="crud.loading"
      :data="tableData"
      row-key="id"
      @select="crud.selectChange"
      @select-all="crud.selectAllChange"
      @selection-change="crud.selectionChangeHandler"
    >
      <el-table-column
        v-for="item in tableHeader"
        :key="item.prop"
        :show-overflow-tooltip="true"
        :prop="item.prop"
        :label="item.label"
        :width="item.width || ''"
        :align="item.align || 'center'"
      >
        <template slot-scope="scope">
          <template v-if="item.prop.indexOf('img') !='-1'">
            <template v-for="(item,index) in scope.row[item.prop]">
              <el-image
                v-if="index == 0"
                :key="item.id"
                :src="item.url"
                :preview-src-list="[item.url]"
                fit="contain"
                lazy
                class="el-avatar"
              >
                <div slot="error">
                  <i class="el-icon-document" />
                </div>
              </el-image>
            </template>

          </template>
          <!-- <template v-else-if="item.prop.indexOf('cascader') !='-1'">
            <span>{{ scope.row[item.prop] ? scope.row[item.prop].join('-') : '' }}</span>
          </template> -->
          <span v-else>{{ scope.row[item.prop] }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="checkPer(['admin','omAlias:edit','omAlias:del'])" label="操作" width="250" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button v-permission="permission.edit" type="primary" size="mini" @click="editItem(scope.row)">编辑</el-button>
          <el-popconfirm
            confirm-button-text="确认"
            :hide-icon="true"
            cancel-button-text="取消"
            icon-color="red"
            title="确认要删除该条数据？"
            @confirm="deleteItem(scope.row)"
          >
            <el-button slot="reference" v-permission="permission.del" type="danger" size="mini">删除</el-button>
          </el-popconfirm>
          <!-- <el-button type="success" size="mini" @click="detail(scope.row)">查看</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination />
    <uploadExcel ref="uploadExcel" @getlist="crud.refresh()" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { downloadUrl } from '@/utils/index'
import uploadExcel from './components/uploadExcel'
import crudTable from '@/api/parts/alias'
import CRUD, { presenter, header, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import updateButton from '@/components/UpdateButton/index'
export default {
  name: 'AliasManage',
  components: { crudOperation, rrOperation, pagination, uploadExcel, updateButton },
  cruds() {
    return CRUD({ title: '别名管理', url: 'api/omAlias', query: { enabled: 1 }, crudMethod: { ...crudTable }})
  },
  mixins: [presenter(), header(), crud()],
  data() {
    return {
      nofistLoad: false,
      tableData: [],
      tableHeader: [],
      permission: {
        add: ['admin', 'omAlias:add'],
        edit: ['admin', 'omAlias:edit'],
        del: ['admin', 'omAlias:del'],
        upload: ['admin', 'omAlias:importXlsWithRule'],
        updateT: ['admin', 'omAlias:updateFormStruct'],
        updateR: ['admin', 'omAlias:updateRelation'],
        updateA: ['admin', 'omAlias:updateAssetId']
      },
      props: {
        value: 'id',
        label: 'label',
        emitPath: false
      },
      bindId: ''
    }
  },
  computed: {
    ...mapGetters([
      'baseApi',
      'omInspectAPi'
    ])
  },
  watch: {
    'crud.data': {
      handler(val, oldVal) {
        if (val && val.length && val.length > 0) {
          const newVal = val;
          const publicfilters = ['text_', 'divider_', 'editor_', 'subform'];
          const uniqueFilters = ['file_'];
          const publicLable = ['栅格布局']
          const uniqueLable = ['序号']
          const filterTableHeader = [...publicfilters, ...uniqueFilters]
          const filterLable = [...publicLable, ...uniqueLable]
          const tableHeader = newVal[0].extend.tableHeader.filter((item) => {
            return filterLable.every(subItem => item.label !== subItem) && filterTableHeader.every(subItem => item.prop.indexOf(subItem) == '-1')
          });
          const otherTableHeader = [
            // { prop: 'createBy', label: '发布人员' },
            // { prop: 'createTime', label: '发布时间' }
          ];
          this.tableHeader = [...tableHeader, ...otherTableHeader];
          let tableData = [];
          tableData = newVal.map(item => {
            const json = item.extend.data;
            json.id = item.id;
            json.createBy = item.createBy;
            json.createTime = item.createTime;
            return json;
          });
          this.tableData = tableData;
        } else {
          this.tableHeader = [];
          this.tableData = [];
        }
      },
      deep: true
    }
  },
  created() {
    this.crud.optShow = { add: false, edit: false, del: false, download: false, reset: true }
  },
  activated() {
    if (this.nofistLoad) {
      this.crud.refresh();
    }
    this.nofistLoad = false
  },
  methods: {
    [CRUD.HOOK.beforeRefresh]() {
      this.crud.query.bindId = this.$route.query.id;
      this.crud.query.categoryId = this.$route.query.category;
      this.bindId = this.$route.query.id;
    },
    preView() {
      // const params = qs.stringify(data, { indices: false })
      const params = `page=0&size=99999&alias.enabled=1&alias.formData=${this.query.formData || ''}`
      const url = `${this.omInspectAPi}/alias/html?${params}`;
      window.open(url)
    },
    downLoad() {
      const params = `page=0&size=99999&alias.enabled=1&alias.formData=${this.query.formData || ''}&fileName=别名表`
      const url = `${this.omInspectAPi}/alias/xls?${params}`;
      downloadUrl(url)
    },
    addFile() {
      this.$refs.uploadExcel.init();
    },
    addTable() {
      this.$router.push({ name: 'AliasCreate', query: { id: this.$route.query.id }});
      this.nofistLoad = true;
    },
    editItem(row) {
      this.$router.push({ name: 'AliasCreate', query: { id: this.$route.query.id, rowId: row.id, type: 'edit' }});
      this.nofistLoad = true;
    },
    detail(row) {
      this.$router.push({ name: 'AliasCreate', query: { id: this.$route.query.id, rowId: row.id, type: 'see' }})
    },
    deleteItem(row) {
      crudTable.del([row.id]).then(response => {
        this.crud.refresh();
        this.$notify({
          title: '数据已删除',
          type: 'success',
          duration: 2500
        })
      })
    },
    getJson(item) {
      const json = {}
      item.forEach(i => {
        for (const name in i) {
          json[name] = i[name];
        }
      })
      return json;
    },
    getArr(item) {
      const arr = [];
      item.forEach(i => {
        i.forEach(j => {
          arr.push(j)
        })
      })
      return arr;
    }
  }
}
</script>
<style>
.tableImg {
  width: 50px;height: 50px;
}
</style>
