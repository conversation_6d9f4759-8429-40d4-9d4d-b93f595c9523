<template>
  <div class="document-info">
    <!--任务俩表-->
    <div class="head-container">
      <e-header :permission="permission" />
      <crudOperation :permission="permission">

        <update-button
          v-if="bindId"
          slot="right"
          :bind-id="bindId"
          :enabled="[1]"
          :permission="permission"
        />
      </crudOperation>
    </div>
    <div class="body-box">
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="tableData"
        :load="getMenus"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        lazy
        row-key="id"
        style="width: 100%;"
        @select="crud.selectChange"
        @select-all="crud.selectAllChange"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column :fixed="true" type="selection" width="55" />
        <el-table-column
          v-for="item in tableHeader"
          :key="item.prop"
          :align="item.align || 'center'"
          :fixed="item.fixed || false"
          :label="item.label"
          :prop="item.prop"
          :show-overflow-tooltip="true"
          :width="item.width"
        >
          <template slot-scope="scope">
            <template v-if="item.label == '预览图'">
              <el-image
                :key="item.id"
                :preview-src-list="[scope.row.ft1[0].url]"
                :src="scope.row.ft1[0].url"
                class="el-avatar"
                fit="contain"
                lazy
              >
                <div slot="error">
                  <i class="el-icon-document" />
                </div>
              </el-image>
            </template>
            <span
              v-else-if="item.prop === 'name'"
              style="cursor: pointer;color: #2476F8"
              @click="goDetail(scope.row)"
            >
              {{ scope.row.fv16 }}  {{ scope.row[item.prop] }}
            </span>

            <span v-else>{{ scope.row[item.prop] }}</span>
          </template>
        </el-table-column>
        <!--   编辑与删除   -->
        <el-table-column
          align="center"
          fixed="right"
          label="操作"
          width="240"
        >
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
              msg="确定删除吗,如果存在下级节点则一并删除，此操作不能撤销！"
            />
            <el-button type="primary" @click="createTask(scope.row)">创建子任务</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import eHeader from '@/views/oaWorkOrder/oaProject/components/task/TaskHeader.vue';
import oaPmTree from '@/api/oaWorkOrder/oaPmTree'
import CRUD, { crud, form, presenter } from '@crud/crud';
import crudOperation from '@crud/CRUD.operation.vue';
import pagination from '@crud/Pagination.vue';
import updateButton from '@/components/UpdateButton/index.vue'
import udOperation from '@crud/UD.operation.vue';
import { parseJsonOrReturnOriginal } from '@/utils'

const defaultForm = {
  id: null
}
export default {
  name: 'OaProject',
  components: { eHeader, crudOperation, pagination, updateButton, udOperation },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: false,
      title: '任务',
      url: 'api/oaPmTree/task/small',
      sort: ['createTime,desc'],
      query: { enabled: 1, fv1: '任务' },
      crudMethod: { ...oaPmTree },
      optShow: {
        add: false,
        edit: false,
        del: true,
        download: false,
        reset: false,
        rightGroup: false
      }
    })
  },
  mixins: [presenter(), form(defaultForm), crud()],
  data() {
    return {
      permission: {
        del: ['admin', 'oaDocument:del'],
        add: ['admin', 'oaDocument:add'],
        updateT: ['admin', 'oaPm:updateFormStruct'],
        updateR: ['admin', 'oaPm:updateRelation']
      },
      tableData: [],
      tableHeader: [],
      bindId: '',
      taskDialogVisible: false,
      directoryInfo: {}
    }
  },
  computed: {},
  watch: {
    'crud.data': {
      handler(val) {
        this.tableHeader = this.formatterTableHeader(val)
        this.tableData = this.formatterTableData(val)
      },
      deep: true
    }
  },
  methods: {
    initData() {
      const { bindId } = this.$config.task_keys;
      this.bindId = bindId;
      // this.crud.query.bindId = bindId;
    },
    goDetail(data) {
      this.$router.push({
        name: 'DoTask',
        query: {
          id: data.id,
          type: 2
        }
      })
    },
    createTask(data) {
      const info = {
        data,
        type: 3,
        directoryId: this.directoryInfo.id,
        projectId: this.$route.query.projectId,
        currentProjectName: this.$route.query.name
      }
      this.$emit('toEdit', info)
    },
    [CRUD.HOOK.beforeToEdit](crud, form) {
      const info = {
        data: form,
        type: 2,
        projectId: this.$route.query.projectId,
        currentProjectName: this.$route.query.name
      }
      this.$emit('toEdit', info)
    },
    getMenus(tree, treeNode, resolve) {
      const params = { pid: tree.id, enabled: 1, fv1: '任务', size: 999 }
      setTimeout(() => {
        oaPmTree.getPmTreeSmall(params).then(res => {
          const tableData = res.content;

          resolve(this.formatterTableData(tableData))
        })
      }, 100)
    },
    async upDataTableData(data) {
      this.initData()
      if (data) {
        this.directoryInfo = data;
        this.crud.query.pid = data.id;
      } else {
        this.directoryInfo = {}
        this.crud.query.pid = this.$route.query.projectId;
        this.crud.data = []
      }
      this.crud.toQuery();
    },

    /**
		 * 格式化表头数据
		 * @param val
		 * @returns {*[]}
		 */
    formatterTableData(val) {
      let tableData = [];
      if (val && val.length) {
        tableData = val.map(item => {
          const data = {
            ...item
          }
          data.fv9 = parseJsonOrReturnOriginal(data.fv9);
          return data;
        });
      } else {
        tableData = [];
      }
      return tableData;
    },
    /**
		 * 格式化表头数据
		 * @param val
		 * @returns {*[]}
		 */
    formatterTableHeader(val) {
      const tableHeader = [
        { prop: 'name', label: '任务名称' },
        { prop: 'fv9', label: '处理人' },
        { prop: 'createTime', label: '创建时间' },
        { prop: 'fv13', label: '开始时间' },
        { prop: 'fv11', label: '预计结束时间' },
        { prop: 'fv12', label: '当前状态' }
      ]
      const otherHeader = [];
      return [...tableHeader, ...otherHeader];
    }
  }

}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>

</style>
